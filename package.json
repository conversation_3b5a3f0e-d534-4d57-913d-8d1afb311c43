{"name": "TestFiesta", "version": "0.120.5", "private": true, "scripts": {"serve": "vite --port 8082", "serve:preview": "vite preview --port 8082", "build:staging": "npx vite build --mode staging", "build:dev": "npx vite build --mode development", "build:production": "npx vite build", "lint": "npx eslint src --ext .js,.ts,.vue", "coverage": "npx nyc report --reporter=text-summary", "i18n:report": "vite i18n:report --src \"./src/**/*.?(js|vue)\" --locales \"./src/locales/**/*.json\"", "test:e2e": "vite test:e2e", "test:unit": "vite test:unit", "ci:runtests": "start-server-and-test serve http://localhost:8080 test:all"}, "dependencies": {"@coreui/icons": "^2.1.0", "@coreui/icons-vue": "^2.0.0", "@johmun/vue-tags-input": "^2.1.0", "@sentry/vue": "^8.30.0", "@tiptap/core": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/vue-2": "^2.12.0", "@vue-stripe/vue-stripe": "^4.5.0", "apexcharts": "^3.36.3", "axios": "^1.2.1", "date-fns": "^2.28.0", "date-fns-tz": "^1.3.0", "dayjs": "^1.11.13", "debounce": "^1.2.1", "dompurify": "^3.2.4", "fast-xml-parser": "^5.2.0", "file-saver": "^2.0.5", "html-to-adf-converter": "^1.0.2", "idb": "^7.0.1", "jsonschema": "^1.4.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "mammoth": "^1.9.0", "marked": "^15.0.7", "node-forge": "^1.3.1", "papaparse": "^5.4.1", "pdfjs-dist": "2.0.943", "portal-vue": "2", "sweetalert2": "^11.16.0", "tiny-uid": "^1.1.2", "tiptap-extension-global-drag-handle": "^0.1.18", "tiptap-extension-upload-image": "^1.0.1", "tiptap-markdown": "^0.8.10", "validator": "^13.7.0", "vee-validate": "^3.4.9", "viewer-vue": "^0.1.2", "viewerjs": "^1.11.6", "vite-svg-loader": "^5.1.0", "vue": "^2.7.11", "vue-advanced-cropper": "^1.11.3", "vue-apexcharts": "^1.6.2", "vue-burger-menu": "^2.0.5", "vue-grid-layout": "^2.3.12", "vue-gtag": "^1.16.1", "vue-i18n": "8.28.2", "vue-i18n-bridge": "^9.2.0-beta.10", "vue-input-autowidth": "^1.0.11", "vue-pdf-embed": "1", "vue-router": "^3.6.5", "vue-sweetalert2": "^5.0.5", "vue-uuid": "^3.0.0", "vue2-datepicker": "^3.10.4", "vuedraggable": "^2.24.3", "vuetify": "^2.7.1", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0", "wrangler": "^4.18.0", "xlsx": "^0.18.5", "xlsx-to-csv": "^2.0.0"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@intlify/unplugin-vue-i18n": "^6.0.3", "@intlify/vue-i18n-loader": "^3.2.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^1.1.3", "autoprefixer": "^10.4.20", "chai": "^4.3.7", "eslint": "8.46.0", "eslint-config-google": "^0.14.0", "eslint-plugin-vue": "^8.4.1", "istanbul-lib-coverage": "^3.2.2", "sass": "^1.49.8", "sass-loader": "^10.2.0", "start-server-and-test": "^2.0.2", "unplugin-vue-components": "^28.0.0", "vite": "^6.1.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-vue-devtools": "^7.7.1", "vite-plugin-vue2": "^2.0.3", "vue-template-compiler": "^2.7.16"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "resolutions": {"eslint": "8.46.0"}}