| File | Has Migrated |
|------|:------------:|
| src/views/404.vue | No |
| src/views/Admin/Configurations/ConfigurationDialog.vue | No |
| src/views/Admin/Configurations/ConfirmDialog.vue | No |
| src/views/Admin/Configurations/Index.vue | No |
| src/views/Admin/CustomFields/Index.vue | No |
| src/views/Admin/CustomTemplates/Index.vue | No |
| src/views/Admin/Index.vue | No |
| src/views/Admin/Resource/Edit.vue | No |
| src/views/Admin/Resource/Index.vue | No |
| src/views/Admin/Roles/Index.vue | No |
| src/views/Admin/Roles/RolesCreateView.vue | No |
| src/views/Admin/Roles/RolesEditView.vue | No |
| src/views/Admin/Roles/RolesMemberView.vue | No |
| src/views/Admin/SharedSteps/ConfirmDialog.vue | No |
| src/views/Admin/SharedSteps/Index.vue | No |
| src/views/Admin/Tags/Index.vue | No |
| src/views/Admin/Users/<USER>
| src/views/Auth/ConfirmEmailPage.vue | No |
| src/views/Auth/ContinueWithSSOPage.vue | No |
| src/views/Auth/CreatePasswordPage.vue | No |
| src/views/Auth/ForgotPasswordPage.vue | No |
| src/views/Auth/LoginPage.vue | No |
| src/views/Auth/RegisterPage.vue | Yes |
| src/views/Auth/ResetPasswordPage.vue | No |
| src/views/Auth/TwoFactorPage.vue | No |
| src/views/Auth/createOrg.vue | No |
| src/views/Dashboard.vue | No |
| src/views/Integration/AddIntegration.vue | No |
| src/views/Integration/Index.vue | No |
| src/views/Maintenance.vue | No |
| src/views/Org/GetInvite.vue | No |
| src/views/Profile.vue | No |
| src/views/Projects/Defect/Index.vue | No |
| src/views/Projects/ProjectCreateView.vue | No |
| src/views/Projects/ProjectDetailView.vue | No |
| src/views/Projects/index.vue | Yes |
| src/views/Settings/Organization/About.vue | No |
| src/views/Settings/Organization/ApiKeys.vue | No |
| src/views/Settings/Organization/Authentication.vue | No |
| src/views/Settings/Organization/Billing.vue | No |
| src/views/Settings/Organization/Organization.vue | No |
| src/views/Settings/Organization/Personalization.vue | No |
| src/views/Settings/Organization/Storage.vue | No |
| src/views/Settings/Personal/About.vue | No |
| src/views/Settings/Personal/Account.vue | No |
| src/views/Settings/Personal/ApiKeys.vue | No |
| src/views/Settings/Personal/Authentication.vue | No |
| src/views/Settings/Personal/Billing.vue | No |
| src/views/Settings/Personal/Notifications.vue | No |
| src/views/Settings/Personal/Organizations.vue | No |
| src/views/Settings/Personal/Personalization.vue | No |
| src/views/Settings/User/Notifications.vue | No |
| src/views/Settings/User/Organizations.vue | No |
| src/views/Setup.vue | No |
| src/views/Tests/Case/CaseExecutionService.vue | No |
| src/views/Tests/Case/CaseFromRepository.vue | No |
| src/views/Tests/Case/Components/ChildStep.vue | No |
| src/views/Tests/Case/Components/ConfirmBulkDeleteDialog.vue | No |
| src/views/Tests/Case/Components/ConfirmDialog.vue | No |
| src/views/Tests/Case/Components/CustomFieldFromTemplate.vue | Yes |
| src/views/Tests/Case/Components/DefectItem.vue | No |
| src/views/Tests/Case/Components/EditDialog.vue | No |
| src/views/Tests/Case/Components/ExportDialog.vue | Yes |
| src/views/Tests/Case/Components/Filter.vue | No |
| src/views/Tests/Case/Components/StepEditor.vue | No |
| src/views/Tests/Case/Components/StepItem.vue | No |
| src/views/Tests/Case/Components/TestRunItem.vue | No |
| src/views/Tests/Case/Detail/Index.vue | No |
| src/views/Tests/Case/Export/ExportSpreadsheet.vue | No |
| src/views/Tests/Case/Import/ImportCsv.vue | Yes |
| src/views/Tests/Case/Import/ImportSteps/StepFour.vue | Yes |
| src/views/Tests/Case/Import/ImportSteps/StepOne.vue | Yes |
| src/views/Tests/Case/Import/ImportSteps/StepThree.vue | Yes |
| src/views/Tests/Case/Import/ImportSteps/StepTwo.vue | Yes |
| src/views/Tests/Case/Index.vue | Yes |
| src/views/Tests/Case/Tree/Index.vue | Yes |
| src/views/Tests/Milestones/Index.vue | Yes |
| src/views/Tests/Milestones/MilestoneCreateView.vue | Yes |
| src/views/Tests/Milestones/MilestoneEditView.vue | Yes |
| src/views/Tests/Milestones/MilestoneTestActivitiesView.vue | No |
| src/views/Tests/Milestones/MilestoneView.vue | No |
| src/views/Tests/Milestones/RevertChanges.vue | No |
| src/views/Tests/Plans/AddTestRunView.vue | No |
| src/views/Tests/Plans/Components/ConfirmDialog.vue | No |
| src/views/Tests/Plans/Components/DetailSectionHeader.vue | No |
| src/views/Tests/Plans/Components/List/PlansList.vue | Yes |
| src/views/Tests/Plans/Components/List/PlansListFilter.vue | No |
| src/views/Tests/Plans/Components/Placeholder.vue | No |
| src/views/Tests/Plans/Components/SelectTestRunStatusDialog.vue | No |
| src/views/Tests/Plans/Components/TestRunList.vue | Yes |
| src/views/Tests/Plans/CreatePlanView.vue | Yes |
| src/views/Tests/Plans/DetailView.vue | No |
| src/views/Tests/Plans/Duplicate/Index.vue | No |
| src/views/Tests/Plans/Index.vue | Yes |
| src/views/Tests/Plans/RerunView.vue | No |
| src/views/Tests/Runs/Create/RunAddCaseView.vue | No |
| src/views/Tests/Runs/Create/TestRunCreateView.vue | Yes |
| src/views/Tests/Runs/Duplicate/Index.vue | No |
| src/views/Tests/Runs/RunCaseEditView.vue | Yes |
| src/views/Tests/Runs/RunEditView.vue | No |
| src/views/Tests/Runs/RunView.vue | Yes |
| src/views/Workspace/Index.vue | No |
