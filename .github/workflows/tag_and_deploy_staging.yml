name: Tag release to kick off build
on:
  pull_request:
    types: [closed]
    branches:
      - master
jobs:
  tag_release:
    if: ${{ github.event.pull_request.merged }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
    steps:
      - uses: actions/checkout@v3
        with:
          token: ${{ secrets.GH_PUSHER_TOKEN }}

      # setup node
      - name: Use node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Increment the current package version
        run: |
          echo "BACKEND_VERSION=$(echo $(curl --silent https://staging.api.testfiesta.com/version) | awk '{ if (match($0,/[0-9]+\.[0-9]+\.[0-9]/,m)) print m[0] }')" >> $GITHUB_ENV
          echo "PACKAGE_VERSION=$(npm pkg get version | sed 's/\"//g' | awk -F. -v OFS=. 'NF==1{print ++$NF}; NF>1{if(length($NF+1)>length($NF))$(NF-1)++; $NF=sprintf("%0*d", length($NF), ($NF+1)%(10^length($NF))); print}')" >> $GITHUB_ENV
          echo "PACKAGE_VERSION_NAME=$(npm pkg get version | sed 's/\"//g' | awk -F. -v OFS=. 'NF==1{print ++$NF}; NF>1{if(length($NF+1)>length($NF))$(NF-1)++; $NF=sprintf("%0*d", length($NF), ($NF+1)%(10^length($NF))); print}' | sed 's/\./-/g')" >> $GITHUB_ENV

      - name: Update the package.json
        run: |
          npm pkg set version="$PACKAGE_VERSION"

      - name: Update staging system job overlays
        working-directory: "deploy/overlays/staging/jobs/system"
        run: |
          kustomize edit set image backend-image="*:v$BACKEND_VERSION"
          kustomize edit set namesuffix -- "-frontend-v$PACKAGE_VERSION_NAME"
          kustomize edit set label "version:v$PACKAGE_VERSION"

      - name: Update staging user job overlays
        working-directory: "deploy/overlays/staging/jobs/user"
        run: |
          kustomize edit set image backend-image="*:v$BACKEND_VERSION"
          kustomize edit set namesuffix -- "-frontend-v$PACKAGE_VERSION_NAME"
          kustomize edit set label "version:v$PACKAGE_VERSION"

      - name: Update production system job overlays
        working-directory: "deploy/overlays/production/jobs/system"
        run: |
          kustomize edit set image backend-image="*:v$BACKEND_VERSION"
          kustomize edit set namesuffix -- "-frontend-v$PACKAGE_VERSION_NAME"
          kustomize edit set label "version:v$PACKAGE_VERSION"

      - name: Update production user job overlays (for manual runs)
        working-directory: "deploy/overlays/production/jobs/user"
        run: |
          kustomize edit set image backend-image="*:v$BACKEND_VERSION"
          kustomize edit set namesuffix -- "-frontend-v$PACKAGE_VERSION_NAME"
          kustomize edit set label "version:v$PACKAGE_VERSION"


      - name: Update version deploy
        working-directory: "deploy/base/jobs/system"
        run: |
          sed -i -e "s/v[0-9]\+\.[0-9]\+\.[0-9]/v$PACKAGE_VERSION/g" add-app-version-job.yaml

      - name: Update nginx configs
        working-directory: "deploy/base/deployment"
        run: |
          sed -i -e "s/v[0-9]\+\.[0-9]\+\.[0-9]/v$PACKAGE_VERSION/g" default.conf

      - name: Update cloudbuild scripts
        working-directory: "deploy"
        run: |
         sed -i -e "s/v[0-9]\+-[0-9]\+-[0-9]/v$PACKAGE_VERSION_NAME/g" staging-cloudbuild.yaml production-cloudbuild.yaml

      - name: Commit files
        run: |
          git config --local user.email "build-bot[bot]@testfiesta.com"
          git config --local user.name "Build Bot [bot]"
          git commit -a -m "Tag version v$PACKAGE_VERSION"
          echo "NEW_GIT_SHA=$(git rev-parse HEAD)" >> $GITHUB_ENV

      - name: Push changes
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.GH_PUSHER_TOKEN }}
          branch: ${{ github.ref }}

      - name: package-version-to-git-tag
        uses: pkgdeps/git-tag-action@v2
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          github_repo: ${{ github.repository }}
          version: ${{ env.PACKAGE_VERSION }}
          git_commit_sha: ${{ env.NEW_GIT_SHA }}
          git_tag_prefix: "v"

      - name: Extract all commit messages and build m
        uses: sergeysova/jq-action@v2
        id: message
        with:
          cmd: |
            echo -n "FRONTEND: v${{ env.PACKAGE_VERSION }} is being deployed to STAGING. More info at:"
            echo '${{ toJSON(github.event) }}' | jq '.pull_request.html_url' -r

      - name: Discord notification
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK_URL }}
        uses: Ilshidur/action-discord@master
        with: 
          args: "${{ steps.message.outputs.value }}"
