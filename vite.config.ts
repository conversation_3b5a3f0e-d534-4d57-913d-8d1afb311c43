import fs from 'node:fs';
import path from 'node:path';
import { createVuePlugin } from 'vite-plugin-vue2';
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'
import { VuetifyResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
//import vueDevTools from 'vite-plugin-vue-devtools';
import { defineConfig } from 'vite';
import { createRequire } from 'module';

import svgLoader from 'vite-svg-loader';
import autoprefixer from 'autoprefixer';
import eslintPlugin from 'vite-plugin-eslint';

const require = createRequire(import.meta.url);

const packagePath = path.join(process.cwd(), 'package.json');
let version = JSON.parse(fs.readFileSync(packagePath).toString()).version;

export default () => { 
  return defineConfig({
  build: {
    rollupOptions: {
      output: {
        assetFileNames: () => {
          return `v${version}/assets/[name]-[hash][extname]`;
        },
        chunkFileNames: `v${version}/assets/[name]-[hash].js`,
        entryFileNames: `v${version}/assets/[name]-[hash].js`,
      },
    },
    outDir: `dist/v${version}`,
  },
  worker: {
    format: 'es',
    rollupOptions: {
      output: {
        assetFileNames: () => {
          return `v${version}/assets/[name]-[hash][extname]`;
        },
        chunkFileNames: `v${version}/assets/[name]-[hash].js`,
        entryFileNames: `v${version}/assets/[name]-[hash].js`,
      },
    },
  },
  css: {
    postcss: {
      plugins: [ autoprefixer() ],
    },
  },
  server: {
    hmr: {
      overlay: true
    }
  },
  plugins: [
    createVuePlugin(),
    VueI18nPlugin({
      include: [path.resolve(__dirname, './src/locales/**')],
      strictMessage: false,
    }), 
    svgLoader(),
    eslintPlugin({ 
      eslintPath: require.resolve('eslint'),
      failOnError: false,
      failOnWarning: false,
      emitError: true,
      emitWarning: true
    }),
    Components({
      resolvers: [
        VuetifyResolver(),
      ],
    }),
    //vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
    // TODO - remove .vue and add it when importing
    extensions: [ '.js', '.ts', '.json', '.vue' ],
  },
});
};
