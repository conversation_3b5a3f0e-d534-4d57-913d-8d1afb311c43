{"root": true, "env": {"browser": true, "node": true, "es2021": true}, "extends": ["plugin:vue/recommended", "eslint:recommended"], "rules": {"no-debugger": 0, "no-console": 0, "vue/no-template-shadow": 0, "vue/require-prop-types": 0, "vue/require-default-prop": 0, "vue/no-lone-template": 0, "vue/no-v-html": 0, "vue/prop-name-casing": 0, "no-async-promise-executor": 0, "vue/multi-word-component-names": 0, "vue/no-mutating-props": 0}, "settings": {"vue": {"version": "2.7"}}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"mocha": true}}]}