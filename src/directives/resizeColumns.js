// Simple column resize directive for Vuetify v-data-table (Vue 2)
// Attaches draggable handles to header cells and persists widths per-table

const ColumnResizeDirective = {
  inserted(el, binding, vnode) {
    // Delay to allow v-data-table to render
    const initialize = () => {
      const tableWrapper = el.querySelector('.v-data-table__wrapper');
      const table = tableWrapper?.querySelector('table');
      const headerRow = table?.querySelector('thead tr');

      if (!table || !headerRow) return;

      const headerCells = Array.from(headerRow.children);

      // Track active resize state per-table to suppress header sorting
      let isResizing = false;
      let suppressNextClick = false; // short window after mouseup to swallow the synthetic click

      const computeStorageKey = () => {
        const routeKey = window?.location?.pathname || 'default';
        const headerLabels = headerCells
          .map((th) => (th.innerText || '').trim())
          .join('|');
        return (binding?.value && binding.value.storageKey) || `colres:${routeKey}:${headerLabels}`;
      };

      const storageKey = computeStorageKey();

      const loadWidths = () => {
        try {
          const raw = localStorage.getItem(storageKey);
          return raw ? JSON.parse(raw) : {};
        } catch (e) {
          return {};
        }
      };

      const saveWidths = (widthMap) => {
        try {
          localStorage.setItem(storageKey, JSON.stringify(widthMap));
        } catch (e) {
          // ignore quota errors
        }
      };

      const shouldSkip = (th) => {
        const label = (th.innerText || '').trim().toLowerCase();
        if (label === 'actions' || th.classList.contains('action-column')) return true;
        // Skip selection checkbox column
        if (th.querySelector('input[type="checkbox"]') || th.querySelector('.v-input--selection-controls')) return true;
        return false;
      };

      const minWidthPx = 72;

      // Apply any saved widths
      const saved = loadWidths();
      headerCells.forEach((th) => {
        const label = (th.innerText || '').trim();
        const savedWidth = saved[label];
        if (savedWidth) {
          const widthPx = Number(savedWidth);
          th.style.width = `${widthPx}px`;
          th.style.minWidth = `${Math.max(minWidthPx, widthPx)}px`;
        }
        th.style.whiteSpace = 'nowrap';
        th.style.boxSizing = 'border-box';
      });

      // Block header clicks while resizing (capture phase to preempt Vuetify sorting)
      const headerClickBlocker = (e) => {
        const isOnHandle = e.target?.closest?.('.col-resize-handle');
        if (isResizing || suppressNextClick || isOnHandle) {
          e.preventDefault();
          e.stopPropagation();
        }
      };
      headerRow.addEventListener('click', headerClickBlocker, true);

      // Ensure we can cleanup listeners on unbind
      el.__colResizeCleanup = () => {
        try {
          headerRow.removeEventListener('click', headerClickBlocker, true);
        } catch (_) {
          /* no-op: cleanup best-effort */
        }
      };

      // Create handles
      const resizableHeaderCells = headerCells.filter((th) => !shouldSkip(th));
      const lastResizableTh = resizableHeaderCells[resizableHeaderCells.length - 1];

      headerCells.forEach((th) => {
        if (shouldSkip(th)) return;
        // Do not render a handle for the last resizable column
        if (th === lastResizableTh) return;
        th.style.position = 'relative';

        const handle = document.createElement('div');
        handle.className = 'col-resize-handle';
        handle.style.position = 'absolute';
        handle.style.top = '0px';
        handle.style.right = '0';
        handle.style.height = '100%';
        handle.style.width = '1px';
        handle.style.right = '0';
        handle.style.cursor = 'col-resize';
        handle.style.userSelect = 'none';
        handle.style.zIndex = '2';
        handle.style.background = '#E3E3E3';
        handle.style.display = 'flex';
        handle.style.alignItems = 'center';
        handle.style.justifyContent = 'flex-end';
        th.appendChild(handle);

        // Visible indicator bar inside the handle
        const indicator = document.createElement('div');
        indicator.className = 'col-resize-indicator';
        indicator.style.width = '1px';
        indicator.style.height = '100%';
        indicator.style.marginRight = '3px';
        indicator.style.borderRadius = '1px';
        indicator.style.background = '#E3E3E3'; 
        indicator.style.opacity = '0.8';
        indicator.style.transition = 'all 120ms ease-in-out';
        handle.appendChild(indicator);

        let startXClient = 0;
        let startWidth = 0;
        let startRightWidth = 0;
        let rafId = null;
        let dragLine = null;
        let layoutFrozen = false;
        let frozenLeft = null;
        let selectionPreventionActive = false;
        const preventSelection = (ev) => ev.preventDefault();

        const applyWidthTo = (cell, newWidth) => {
          if (!cell) return;
          const width = Math.max(minWidthPx, newWidth);
          cell.style.width = `${width}px`;
          cell.style.minWidth = `${width}px`;
          cell.style.boxSizing = 'border-box';
        };

        // Determine the immediate right resizable header cell for pairwise resizing
        const currentIndex = resizableHeaderCells.indexOf(th);
        const rightResizableTh = resizableHeaderCells[currentIndex + 1] || null;

        let hasMoved = false;
        const onMouseMove = (e) => {
          const run = () => {
            rafId = null;
            const delta = e.clientX - startXClient;
            // Detect actual movement beyond 0 to avoid click-time jump when width is tiny
            if (!hasMoved && Math.abs(delta) < 1) return;
            hasMoved = true;
            // On first real movement, freeze table layout to keep boundary aligned with cursor
            if (!layoutFrozen && table) {
              const cellsToFreeze = Array.from(headerCells);
              cellsToFreeze.forEach((cell) => {
                const w = Math.round(cell.getBoundingClientRect().width);
                cell.style.width = `${w}px`;
                cell.style.minWidth = `${Math.max(minWidthPx, w)}px`;
                cell.style.boxSizing = 'border-box';
              });
              table.style.tableLayout = 'fixed';
              layoutFrozen = true;
              // Cache the left edge of the active column so we can set width exactly to cursorX - left
              frozenLeft = Math.round(th.getBoundingClientRect().left);
            }
            // Compute target width with 1:1 cursor movement relative to start, then clamp by neighbor's min width
            let targetWidth = layoutFrozen && frozenLeft != null
              ? Math.round(e.clientX - frozenLeft)
              : Math.round(startWidth + delta);

            if (rightResizableTh) {
              // Keep total width of the pair constant while dragging
              const maxTargetWidth = Math.max(minWidthPx, startWidth + startRightWidth - minWidthPx);
              if (targetWidth < minWidthPx) targetWidth = minWidthPx;
              if (targetWidth > maxTargetWidth) targetWidth = maxTargetWidth;
              const neighborWidth = startRightWidth - (targetWidth - startWidth);
              applyWidthTo(th, targetWidth);
              applyWidthTo(rightResizableTh, neighborWidth);
            } else {
              // Fallback: only adjust the current column
              if (targetWidth < minWidthPx) targetWidth = minWidthPx;
              applyWidthTo(th, targetWidth);
            }
            // Keep the visual drag line under the cursor
            if (dragLine) {
              // Align to actual column right edge for pixel-perfect alignment
              const right = Math.round(th.getBoundingClientRect().right);
              dragLine.style.left = `${right - 0.5}px`;
            }
          };
          if (!rafId) rafId = window.requestAnimationFrame(run);
        };

        const onMouseUp = () => {
          if (rafId) {
            cancelAnimationFrame(rafId);
            rafId = null;
          }
          document.removeEventListener('mousemove', onMouseMove);
          document.removeEventListener('mouseup', onMouseUp);
          if (selectionPreventionActive) {
            document.removeEventListener('selectstart', preventSelection);
            selectionPreventionActive = false;
          }
          document.body.style.cursor = '';
          // Remove drag line overlay
          if (dragLine && dragLine.parentNode) {
            dragLine.parentNode.removeChild(dragLine);
            dragLine = null;
          }
          // Unfreeze table layout for future renders
          if (layoutFrozen && table) {
            table.style.tableLayout = '';
            layoutFrozen = false;
          }
          isResizing = false;
          // Swallow the subsequent synthetic click that follows drag
          suppressNextClick = true;
          setTimeout(() => {
            suppressNextClick = false;
          }, 250);
          // Build a map of header label -> width
          const labelToWidthMap = {};
          headerCells.forEach((cell) => {
            if (shouldSkip(cell)) return;
            const label = (cell.innerText || '').trim();
            if (label) {
              labelToWidthMap[label] = cell.offsetWidth;
            }
          });
          // Persist to localStorage
          saveWidths(labelToWidthMap);

          // Optionally persist into Vuex headers store when type is provided
          try {
            const store = vnode && vnode.context && vnode.context.$store;
            const type = binding && binding.value && binding.value.type;
            if (store && type) {
              const allHeaders = store.getters['headers/dynamicHeaders'] || {};
              const current = allHeaders[type];
              if (Array.isArray(current) && current.length > 0) {
                const updated = current.map((h) => {
                  const headerText = h && h.text ? String(h.text) : '';
                  const widthForHeader = labelToWidthMap[headerText];
                  if (widthForHeader) {
                    return { ...h, width: `${widthForHeader}px` };
                  }
                  return h;
                });
                store.dispatch('headers/updateHeaders', { type, headers: updated });
              }
            }
          } catch (_) {
            /* best-effort: persist to store is optional */
          }
        };

        const onMouseDown = (e) => {
          // Avoid triggering sort or other header interactions
          e.preventDefault();
          e.stopPropagation();
          isResizing = true;
          startXClient = e.clientX;
          // Record starting width without mutating styles to avoid any click-time jump
          const currentWidth = th.getBoundingClientRect().width;
          startWidth = currentWidth;
          startRightWidth = rightResizableTh ? rightResizableTh.getBoundingClientRect().width : 0;
          // Active styles
          indicator.style.background = '#0C2FF3';
          indicator.style.opacity = '1';
          // keep indicator width constant to avoid micro-jump
          // Visual and UX cues
          document.body.style.cursor = 'col-resize';
          // Prevent text selection while dragging
          document.addEventListener('selectstart', preventSelection);
          selectionPreventionActive = true;
          // Create a fixed-position drag line so the cursor is always aligned with a visible guide
          try {
            const hostRect = (tableWrapper || el).getBoundingClientRect();
            dragLine = document.createElement('div');
            dragLine.style.position = 'fixed';
            dragLine.style.top = `${hostRect.top}px`;
            dragLine.style.left = `${e.clientX - 0.5}px`;
            dragLine.style.height = `${hostRect.height}px`;
            dragLine.style.width = '1px';
            dragLine.style.background = '#0C2FF3';
            dragLine.style.opacity = '0.9';
            dragLine.style.zIndex = '9999';
            dragLine.style.pointerEvents = 'none';
            document.body.appendChild(dragLine);
          } catch (_) {
            // best-effort visual aid
          }
          document.addEventListener('mousemove', onMouseMove);
          document.addEventListener('mouseup', onMouseUp);
        };

        const onMouseEnter = () => {
          indicator.style.background = '#64748b'; // slate-500
          indicator.style.opacity = '1';
        };

        const onMouseLeave = () => {
          indicator.style.background = '#E3E3E3';
          indicator.style.opacity = '0.8';
          indicator.style.width = '1px';
        };

        handle.addEventListener('mousedown', onMouseDown);
        // Ensure clicks on the handle never trigger sort/navigation
        handle.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
        });
        handle.addEventListener('mouseenter', onMouseEnter);
        handle.addEventListener('mouseleave', onMouseLeave);
        document.addEventListener('mouseup', onMouseLeave);
      });
    };

    // Use requestAnimationFrame to ensure DOM ready
    if (typeof window !== 'undefined') {
      window.requestAnimationFrame(() => initialize());
      // Fallback in case raf fires too early
      setTimeout(() => initialize(), 50);
    } else {
      initialize();
    }
  },
  unbind(el) {
    if (el.__colResizeCleanup) {
      try {
        el.__colResizeCleanup();
      } catch (_) {
        /* no-op: cleanup best-effort */
      }
      delete el.__colResizeCleanup;
    }
  },
};

export default ColumnResizeDirective;

