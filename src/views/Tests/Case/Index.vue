<template>
  <div
    id="case-index-container"
    fluid
    class="pl-3 pt-3"
  >
    <TestCasesSectionHeader
      :title="$t('testCases')"
      :action-text="$t('createTestCase')"
      :folder-uid="selectedFolderUid"
      :write-entity="writeEntity"
    />
    <CaseManagement
      :quick-create="true"
      :cases="cases"
      :folders="folders"
      :from-test-case="true"
      :set-selected-folder-uid="initialSelectedFolder"
      :write-entity="writeEntity"
      :delete-entity="deleteEntity"
      :selected-case="selectedCase"
      :pagination="pagination"
      :current-page="currentPage"
      :items-per-page="itemsPerPage"
      :update-pagination="updatePagination"
      :relations-loading="relationsLoading"
      :cases-loading="casesLoading"
      :total-items="totalCases"
      :sort-by="sortBy"
      :sort-desc="sortDesc"
      @refresh-folders="getFolders"
      @folder-select="onSelectFolder"
      @bulkRemove="onBulkRemove"
      @caseRemove="onCaseRemove"
      @update-cases="updateCases"
      @updateCasesHistoryData="updateCasesHistoryData"
      @reload-cases="reloadCases"
      @applySearch="applySearch"
      @applyFilters="applyFilters"
      @clearFilters="clearFilters"
      @save-expanded-tree="saveExpandedFolderTree"
    />
  </div>
</template>

<script>
import TestCasesSectionHeader from '@/components/TestCases/SectionHeader.vue';
import CaseManagement from '@/components/Cases/CaseManagement';
import { useTestCasesIndex } from '@/composables/modules/cases/index';
import { onMounted, computed } from 'vue';

export default {
  name: 'CaseIndex',
  components: {
    TestCasesSectionHeader,
    CaseManagement
  },
  setup() {
    const {
      cases,
      folders,
      selectedFolderUid,
      writeEntity,
      deleteEntity,
      initialSelectedFolder,
      onSelectFolder,
      reloadCases,
      updateCasesHistoryData,
      getFolders,
      onCaseRemove,
      onBulkRemove,
      init,
      updateCases,
      selectedCase,
      currentPage,
      itemsPerPage,
      updatePagination,
      totalCases,
      totalPages,
      relationsLoading,
      casesLoading,
      // Sorting
      sortBy,
      sortDesc,
      // Search and filters
      searchTerm,
      filters,
      applySearch,
      applyFilters,
      clearFilters,
      saveExpandedFolderTree,
    } = useTestCasesIndex();

    // Pagination info (computed from cases or API response)
    const pagination = computed(() => {
      // If cases is an array, fallback to length
      if (cases.value && cases.value.pagination) return cases.value.pagination;
      // fallback: just show 1 page
      return { total: totalCases.value, page: currentPage.value, limit: itemsPerPage.value, totalPages: totalPages.value };
    });

    // Initialize data on component mount
    onMounted(async () => {
      await init();
    });

    return {
      cases,
      folders,
      selectedFolderUid,
      writeEntity,
      deleteEntity,
      initialSelectedFolder,
      onSelectFolder,
      reloadCases,
      updateCasesHistoryData,
      getFolders,
      onCaseRemove,
      onBulkRemove,
      updateCases,
      selectedCase,
      currentPage,
      itemsPerPage,
      updatePagination,
      pagination,
      relationsLoading,
      casesLoading,
      totalCases,
      sortBy,
      sortDesc,
      // Search and filters
      searchTerm,
      filters,
      applySearch,
      applyFilters,
      clearFilters,
      saveExpandedFolderTree,
    };
  }
}
</script>

<style scoped>
.position-relative {
  position: relative !important;
}
.test-case-loader {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  align-items: center;
}
</style>
