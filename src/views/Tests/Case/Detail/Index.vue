<template>
  <!-- header section -->
  <div
    class="card bg-white rounded-lg ml-1 pa-6 app-height-global detail-view-container"
  >
    <div
      class="d-flex justify-space-between align-center mb-5"
    >
      <div>
        <v-btn
          :class="{
            'text-capitalize bg-white f-color-blue px-0 toggle-btn': true,
            'disabled-action': isProjectArchived
          }"
          depressed
          @click="!isProjectArchived && handleEdit()"
        >
          <div class="d-flex align-center">
            <EditBlueIcon />
            <span class="ml-2">{{ $t('edit') }}</span>
          </div>
        </v-btn>
      </div>
      <div class="d-flex align-center">
        <div class="d-flex align-center mr-5">
          <v-btn
            depressed
            icon
            :disabled="isSelectedCaseFirstIndex"
            :class="{ 'disabled-action': isSelectedCaseFirstIndex }"
            @click="viewPreviousCase"
          >
            <span class="cursor-pointer d-flex align-center">
              <ArrowLeftIcon />
            </span>
          </v-btn>
        
          <v-btn
            depressed
            icon
            :disabled="isSelectedCaseLastIndex"
            :class="{ 'disabled-action': isSelectedCaseLastIndex }"
            @click="viewNextCase"
          >
            <span
              class="mx-2 cursor-pointer d-flex align-center"    
            >
              <ArrowRightIcon />
            </span>
          </v-btn>
        </div>
        <span
          class="close-btn"
          @click="$emit('closeDetail')"
        ><v-icon>mdi-close</v-icon></span>
      </div>
    </div>

    <div class="mb-6">
      <h2>
        {{ caseItem.name }}
      </h2>

      <div
        class="d-flex flex-flow-wrap align-center"
        :class="{
          'gap-2': isTagsNotEmpty
        }"
      >
        <div
          v-for="(item, index) in caseItem.tags"
          :key="index"
        >
          <v-chip
            :ripple="false"
            class="chip-theme"
          >
            {{ item.name }}
          </v-chip>
        </div>

        <v-menu
          v-model="tagMenu"
          bottom
          right
          offset-y
          class="rounded-lg"
          :close-on-content-click="false"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              depressed
              plain
              :ripple="false"
              v-bind="attrs"
              class="f-color-white btn-theme text-capitalize rounded-lg btn-plain-theme px-0"
              color="primary"
              height="40px"
              style="order: 1;"
              v-on="on"
              @click="tagMenu = !tagMenu"
            >
              <div class="d-flex align-center">
                <PlusBlueIcon />
                <span class="ml-2 fw-semibold fs-14px">{{ $t('addTags') }}</span>
              </div>
            </v-btn>
          </template>

          <v-list class="pa-0">
            <v-list-item
              class="px-4 py-2"
            >
              <v-text-field
                v-model="tagSearch"
                dense
                hide-details
                prepend-inner-icon="mdi-magnify"
                placeholder="Search"
                class="tag-search rounded-lg"
                background-color="#F9F9FB"
                filled
                rounded
                @click.stop
              />
            </v-list-item>
            <template v-if="filteredTags?.length">
              <v-list-item
                v-for="tag in filteredTags"
                :key="tag.uid"
              >
                <v-list-item-action class="custom-checkbox-container">
                  <v-checkbox
                    :input-value="isTagSelected(tag.uid)"
                    hide-details
                    class="field-theme mt-0 pt-0"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    @change="toggleTag(tag)"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ tag.name }}</span>
                    </template>
                  </v-checkbox>
                </v-list-item-action>
              </v-list-item>
            </template>
            <template v-else>
              <div class="px-4 pb-4">
                <span class="fs-14px">{{ $t('noMatchesFound') }} <b
                  class="text-theme-primary cursor-pointer"
                  @click="onCreateTagClick"
                >{{ $t('createItemTag', { tagName: tagSearch }) }}</b></span>
              </div>
            </template>
          </v-list>
        </v-menu>
      </div>
    </div>

    <v-tabs
      v-model="selectedTab"
      class="tabs-theme"
      color="#667085"
      hide-slider
    >
      <v-tab
        v-for="(tab, index) in tabs"
        :key="index"
        :href="'#tab-' + (index + 1)"
        class="text-capitralize text-theme-secondary font-weight-medium fs-14px"
        :ripple="false"
        active-class="tab-label-active"
      >
        {{ tab }}
      </v-tab>
    </v-tabs>

    <v-tabs-items v-model="selectedTab">
      <v-tab-item
        v-for="(tab, index) in tabs"
        :key="index"
        :value="'tab-' + (index + 1)"
      >
        <div
          v-if="selectedTab == `tab-${ 1 }`"
          class="mt-6"
        >
          <h4 class="fw-semibold fs-16px text-theme-label text-capitalize">
            {{ tab }}
          </h4>

          <v-list
            class="list-theme"
          >
            <v-list-item class="px-0">
              <template>
                <div class="flex justify-between">
                  <div
                    class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                    style="background-color: #F9FAFB"
                  >
                    <h5 class="align-left">
                      {{ $t('template') }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ getTemplates }}
                    </div>
                  </div>
                  <div
                    class="block rounded-lg px-3 py-2 w-50 ml-2 mh-56px"
                    style="background-color: #f9fafb"
                  >
                    <h5 class="align-left">
                      {{ $t('priority') }}
                    </h5>
                    <div
                      class="align-left contents fs-14px fw-semibold"
                      :style="'color: ' + getPriorityColor(caseItem.priority, priorities)"
                    >
                      {{ getPriorityName(caseItem.priority, priorities) }}
                    </div>
                  </div>
                </div>
              </template>
            </v-list-item>
            <v-list-item
              class="px-0 mt-2"
            >
              <template>
                <div class="flex justify-between">
                  <div
                    class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                    style="background-color: #F9FAFB"
                  >
                    <h5 class="align-left">
                      {{ $t('creator') }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ getCreator }}
                    </div>
                  </div>
                  <div
                    class="block rounded-lg px-3 py-2 w-50 ml-2 mh-56px"
                    style="background-color: #F9FAFB"
                  >
                    <h5 class="align-left">
                      {{ $t('id') }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ $route.params.key }} - {{ caseItem.testCaseRef }}
                    </div>
                  </div>
                </div>
              </template>
            </v-list-item>
            <v-list-item
              class="px-0 mt-2"
            >
              <template>
                <div class="flex justify-between">
                  <div
                    class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                    style="background-color: #f9fafb"
                  >
                    <h5 class="align-left">
                      {{ $t('lastUpdate') }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px">
                      {{ lastUpdated }}
                    </div>
                  </div>
                  <div
                    class="block rounded-lg px-3 py-2 w-50 ml-2 mh-56px"
                    style="background-color: #F9FAFB"
                  >
                    <h5 class="align-left">
                      {{ $t('folder') }}
                    </h5>
                    <div class="align-left contents fw-semibold fs-14px break-all">
                      {{ selectedFolderName }}
                    </div>
                  </div>
                </div>
              </template>
            </v-list-item>

            <div class="text-left font-weight-regular fs-14px text-theme-secondary my-3">
              {{ $t('customFields') }}
            </div>
            <div
              v-if="customFieldsGeneral?.length"
              class="grid"
            >
              <div
                v-for="(field, index) in customFieldsGeneral"
                :key="field.id || index"
                class="block rounded-lg px-3 py-2 w-48 mh-56px bg-gray-theme mb-2"
              >
                <div>
                  <h5 class="align-left">
                    {{ field.name }}
                  </h5>
                  <div
                    v-if="field.value"
                    class="align-left contents fw-semibold fs-14px"
                    v-html="formatContent(field.value)"
                  />
                  <div
                    v-else
                    class="align-left contents fw-semibold fs-14px"
                  >
                    {{ $t('none') }}
                  </div>
                </div>
              </div>
            </div>    
            <div
              v-else
              class="pa-6 text-center"
            >
              <span class="font-weight-medium text-theme-secondary fs-14px">{{ $t('noCustomFieldsFound') }}</span>
            </div>

            <fileInputWithSlider
              :files="attachments"
              class="mt-3"
              :hide-add-attachment="true"
              @removeFile="removeFile"
              @download-file="downloadFile"
              @uploadAttachments="uploadAttachments"
            />

            <!-- References Section -->
            <div v-if="caseItem.references && caseItem.references.length > 0">
              <h4 class="custom_field_heading">
                {{ $t('references') }}
              </h4>
              <div class="d-flex flex-wrap gap-2">
                <v-tooltip
                  v-for="(reference, index) in caseItem.references"
                  :key="index"
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <div
                      v-bind="attrs"
                      class="reference-chip d-flex align-center justify-space-between w-full px-2 py-1 rounded-lg mr-2 mb-2"
                      style="background: #F2F4F7; border: 1px solid #E4E7EC; cursor: pointer; max-width: 200px;"
                      v-on="on"
                      @click="window.open(reference.externalLink, '_blank')"
                    >
                      <div
                        class="d-flex align-center"
                        style="min-width: 0; flex: 1;"
                      >
                        <span
                          class="fs-12px text-theme-label mr-1 text-truncate"
                          style="min-width: 0; flex: 1; font-weight: 500;"
                        >{{ reference.name }}</span>
                      </div>
                      <a
                        :href="reference.externalLink"
                        target="_blank"
                        class="reference-link"
                        style="text-decoration: none; color: inherit;"
                        @click.stop
                      >
                        <v-icon
                          size="12"
                          class="text-theme-secondary"
                        >
                          mdi-arrow-top-right
                        </v-icon>
                      </a>
                    </div>
                  </template>
                  <span>{{ reference.name }}</span>
                </v-tooltip>
              </div>
            </div>

            <div
              v-if="caseDefects.length > 0"
              class="mt-6"
            >
              <div class="d-flex align-center mb-3">
                <h4 class="fw-semibold fs-16px text-theme-label text-capitalize">{{ $t('linkedDefects') }}</h4>
              </div>
              <div class="d-flex flex-wrap gap-2">
                <div
                  v-for="defect in caseDefects"
                  :key="defect.uid"
                  class="d-flex align-center justify-space-between w-full px-2 py-3 rounded-lg"
                  style="background: #F9FAFB; height: 40px;"
                >
                  <div class="d-flex justify-space-between align-center w-full">
                    <div class="d-flex align-center gap-2">
                      <DefectIcon />
                      <span class="fs-14px text-theme-secondary">#{{ defect.uid }}</span>
                      <span class="fs-14px text-theme-label">{{ defect.name }}</span>
                    </div>
                    <a
                      :href="defect.externalLink"
                      target="_blank"
                      class="d-flex align-center h-full"
                    >
                      <ChevronRightIcon/>
                    </a>
                  </div>
                </div>
              </div>
            </div>

          </v-list>
        </div>
        <div
          v-if="selectedTab == `tab-${ 2 }`"
          class="mt-6"
        >
          <h4 class="fw-semibold fs-16px text-theme-label text-capitalize">
            {{ tab }}
          </h4>

          <div
            v-if="customFieldsDetails?.length"
            class="d-flex flex-column gap-2 mt-3"
          >
            <div
              v-for="(field, index) in customFieldsDetails"
              :key="field.id || index"
              class="block rounded-lg px-3 py-2 w-full mh-56px bg-gray-theme"
            >
              <div>
                <h5 class="align-left fs-14px fw-semibold text-theme-label mb-2">
                  {{ field.name }}
                </h5>
                <div
                  v-if="field.value"
                  class="align-left contents text-theme-label fs-14px"
                  v-html="formatContent(field.value)"
                />
                <div
                  v-else
                  class="align-left contents fw-semibold fs-14px"
                >
                  {{ $t('none') }}
                </div>
              </div>
            </div>
          </div>    
          <div
            v-else
            class="pa-6 text-center"
          >
            <span class="font-weight-medium text-theme-secondary fs-14px">{{ $t('noCustomFieldsFound') }}</span>
          </div>

          <h4 class="fw-semibold fs-16px text-theme-label text-capitalize mt-6">
            {{ $t('steps') }}
          </h4>

          <v-timeline
            dense
            class="timeline-theme"
          >
            <v-timeline-item
              v-for="step in caseItem.steps"
              :key="step.key"
              right
            >
              <CaseStepItem
                :step-item="step"
                :case-item="caseItem"
              />
            </v-timeline-item>
            <v-timeline-item v-if="!(caseItem?.customFields?.expectedResultByStep)">
              <div class="px-0">
                <h4 class="text-theme-secondary fs-14px fw-semibold mb-3">
                  {{ $t('sharedStepPage.expectedResult') }}
                </h4>
                <div 
                  class="text-theme-label font-weight-regular"
                  v-html="formatContent(caseItem.customFields.expectedResult)"
                />
              </div>
            </v-timeline-item>
          </v-timeline>
        </div>
        <div
          v-if="selectedTab == `tab-${ 3 }`"
          class="mt-6"
        >
          <h4 class="fw-semibold fs-16px text-theme-label text-capitalize">
            {{ tab }}
          </h4>

          <div v-if="caseItem.runs?.length">
            <TestRunItem
              v-for="(run, index) in caseItem.runs"
              :key="index"
              :external-id="run.externalId"
              :run-id="run.runUid"
              :run-status="run.status"
              :run-title="run.name"
            />
          </div>
          <div
            v-else-if="isTestCaseLoading"
            class="d-flex flex-column align-center gap-2"
          >
            <v-skeleton-loader
              v-for="i in 4"
              :key="i"
              height="40"
              width="500"
              type="heading"
            />
          </div>
          <div
            v-else
            class="pa-6 text-center"
          >
            <span class="font-weight-medium text-theme-secondary fs-14px">{{ $t('noTestRuns') }}</span>
          </div>
        </div>
      </v-tab-item>
    </v-tabs-items>

    <CreateUpdateTagDialog 
      v-model="showCreateTagDialog"
      :initial-value="initialValue"
      @create-new-tag="createTag"
      @close-dialog="onCloseTagDialog"
    />
  </div>
</template>
<script>
import CaseStepItem from '@/views/Tests/Case/Components/StepItem.vue';
import TestRunItem from '@/views/Tests/Case/Components/TestRunItem.vue';
import { formatDate } from '@/utils/util';
import EditBlueIcon from '@/assets/svg/edit-blue.svg';
import ArrowRightIcon from '@/assets/svg/arrow-right.svg';
import ArrowLeftIcon from '@/assets/svg/arrow-left.svg';
import DefectIcon from '@/assets/svg/left-menu/defect.svg';
import ChevronRightIcon from '@/assets/svg/chevron-right24px.svg';
import projectStatus from '@/mixins/projectStatus';
import makeTemplateService from "@/services/api/template";
import makeAttachmentService from '@/services/api/attachment'
import makeCasesService from '@/services/api/case';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import fileInputWithSlider from "@/components/base/FileInputWithSlider.vue";
import { getExtension, generateDateUid } from "@/utils/util";
import axios from 'axios'
import { mapActions } from 'vuex';
import makeTagService from '@/services/api/tag';
import CreateUpdateTagDialog from "@/components/Admin/Tag/CreateUpdateTagDialog.vue";
import PlusBlueIcon from '@/assets/svg/plus-blue.svg';
import { formatContent } from '@/utils/markdown';
import { entityTypeNames,entityTypes } from '@/constants/templates';

let templateService;
let attachmentService;
let casesService;
let tagService;

export default {
  name: 'DetailView',
  components: {
    CaseStepItem,
    EditBlueIcon,
    ArrowRightIcon,
    ArrowLeftIcon,
    TestRunItem,
    fileInputWithSlider,
    CreateUpdateTagDialog,
    PlusBlueIcon,
    DefectIcon,
    ChevronRightIcon
  },
  mixins: [projectStatus, colorPreferencesMixin],
  props: {
    caseItem: Object,
    caseDefects: Array,
    isSelectedCaseFirstIndex: Boolean,
    isSelectedCaseLastIndex: Boolean,
    selectedFolder: Array,
    fromRun: Boolean,
    isTestCaseLoading: Boolean,
  },
  data() {
    return {
      descriptionPanel: 0,
      runsPanel: 0,
      defectsPanel: 0,
      commentsPanel: 0,
      runs: [],
      templates: [],
      priorities: [],
      tagMenu: false,
      tagSearch: '',
      availableCasesTags: [],
      showCreateTagDialog: false,
      initialValue: {
        name: '',
      },
      templatesGrouped: {
        testCase: [],
        testResult: [],
      },
      entityTypeNames: entityTypeNames,
      entityTypes: entityTypes,
      originalTags: [],
      hasChanges: false,
      selectedTab: null,
      tabs: [this.$t('overview'), this.$t('integrations.edit_integration.details'), this.$t('testRuns')],
    };
  },

  computed: {
    lastUpdated() {
      return formatDate(this.caseItem.updatedAt, 'MM/dd/yy');
    },
    getCreator() {
      return this.caseItem.creator ? `${this.caseItem.creator?.firstName} ${this.caseItem.creator?.lastName}` : this.$t('none');
    },
    getSeverity(){
      return this.caseItem.customFields.severity || this.$t('none');
    },
    getTemplates()
    {
      const template = this.templates.find(t => t.uid === this.caseItem.testTemplateUid);
      return template ? template.name : this.$t('none');
    },
    selectedFolderName() {
      return this.selectedFolder.map(folder => folder.text).join(' - ');
    },
    attachments() {
      return this.caseItem?.attachments ?? [];
    },
    isTagsNotEmpty(){
      return !!this.caseItem?.tags?.length
    },
    filteredTags() {
      if (!this.tagSearch) return this.availableCasesTags;
      const search = this.tagSearch.toLowerCase();
      return this.availableCasesTags.filter(tag => 
        tag.name.toLowerCase().includes(search)
      );
    },
    customFieldsGeneral() {
      const customFields = this.caseItem?.customFields?.templateFields || [];
      return customFields.filter(field =>
        !field.templateType || field.templateType == "generalInfo"
      );
    },
    customFieldsDetails(){
      const customFields = this.caseItem?.customFields?.templateFields || [];
      return customFields.filter(field => 
        field.templateType == "details"
      );
    }
  },
  watch: {
    tagMenu(newValue, oldValue) {
      if (newValue) {
        // When menu opens, store the original tags
        this.originalTags = [...(this.caseItem?.tags || [])];
        this.hasChanges = false;
      } else if (oldValue && this.hasChanges) {
        // When menu closes and there are changes, emit the update
        this.$emit('updateCase', { property: 'tags', value: this.caseItem.tags });
      }
    }
  },
  created() {
    this.priorities = this.getPriorities("testCase");
    attachmentService = makeAttachmentService(this.$api);
    casesService = makeCasesService(this.$api);
    tagService = makeTagService(this.$api);
    this.fetchCasesTags();
  },
  async mounted() {
    templateService = makeTemplateService(this.$api);
    this.init();
  },
  methods: {
    ...mapActions({
        uploadToServer: 'attachment/uploadToServer',
      }
    ),
    formatContent,
    handleEdit() {

      const editObject = {
        name: 'EditTestCases',
        params: { uid: this.caseItem.testCaseRef },
      }
      if (this.fromRun) {
        editObject.query = {
          redirectTo: 'TestRunEdit',
          runId: this.$route.params.id,
          folderUid: this.$route.params.folderUid,
        };
      }
      if(this.caseItem.executionUid) 
        editObject.query = {
          ...(editObject.query ? { ...editObject.query } : undefined),
          id: this.caseItem.executionUid,
          isExecution: true
        }
      
      this.$router.push(editObject);
    },
    isTagSelected(tagUid) {
      return this.caseItem.tags?.some(tag => tag.uid === tagUid);
    },
    onCreateTagClick() {
      this.showCreateTagDialog = true;
      this.tagMenu = false;
      this.initialValue = {
        name: this.tagSearch,
      }
    },
    toggleTag(tag) {
      const index = this.caseItem?.tags?.findIndex(t => t.uid === tag.uid);
      if (index === -1) {
        this.caseItem?.tags?.push(tag);
      } else {
        this.caseItem?.tags?.splice(index, 1);
      }
      
      // Check if there are actual changes compared to original
      const originalUids = this.originalTags.map(t => t.uid).sort();
      const currentUids = (this.caseItem?.tags || []).map(t => t.uid).sort();
      this.hasChanges = JSON.stringify(originalUids) !== JSON.stringify(currentUids);
    },
    async createTag(tag) {
      
      this.showCreateTagDialog = false;
    
      try {
        const payload = {
          name: tag.name,
          description: tag.description,
          entityTypes: tag.entityTypes,
        }
        const response = await tagService.createTag(this.$route.params.handle, payload);
        if(response.status === 200){
          this.fetchCasesTags();
          showSuccessToast(this.$swal, "createSuccess", { item: "Tag" });
        }
        
      } catch (err) {
        showErrorToast(this.$swal, "createError", { item: "Tag" }, err?.response?.data);
      } finally {
        this.showCreateTagDialog = false;
        this.tagSearch = '';
      }
    },
    async fetchTagsByType(tagType) {
      try {
        const response = await tagService.getTags(this.$route.params.handle, tagType);
        return response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: `${tagType} tags` }, error?.response?.data);
        return [];
      }
    },
    async fetchCasesTags() {
      this.availableCasesTags = await this.fetchTagsByType('cases');
    },
    async uploadAttachments(newFiles){
      const handle = this.$route.params.handle;
      const mediaType = 'attachment';
      const params = {
        handle,
        projectKey: this.$route.params.key,
        caseId: this.caseItem.testCaseRef
      }


      if(newFiles?.length) {
        await Promise.all(newFiles.map(async (file) => {
          await this.uploadToServer( {handle, mediaType, file, apiService: casesService, params})
        })).then(() => {
          this.$emit('updateAttachments');
          showSuccessToast(this.$swal, this.$t('success.uploadedAttachments'))
        }).catch((err) => {
          if (err?.status == 507)
            showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, 'limitReached', handle)
          else
            showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, err?.response?.data)
        });

      } else {
        showSuccessToast(this.$swal, this.$t('noAttachments'))
      }
    },
    onCloseTagDialog() {
      this.showCreateTagDialog = false;
      this.tagSearch = '';
    },
    viewPreviousCase() {
      if (!this.isSelectedCaseFirstIndex) {
        this.$emit('viewPreviousCase');
      }
    },
    viewNextCase() {
      if (!this.isSelectedCaseLastIndex) {
        this.$emit('viewNextCase');
      }
    },
    async init(){
      try {
        const entityTypeKeys = Object.keys(this.entityTypeNames);
        const templatePromises = entityTypeKeys.map(entityType => 
          this.fetchTemplates(entityType)
        );

        const allResults = await Promise.allSettled([
          ...templatePromises,
        ]);

        const templateResults = allResults.slice(0, entityTypeKeys.length);
        templateResults.forEach((result, index) => {
          if (result.status === 'rejected') {
            console.error(`Failed to fetch templates for ${entityTypeKeys[index]}:`, result.reason);
            showErrorToast(this.$swal, 'fetchError', {
              item: `${entityTypeKeys[index]} templates`
            }, result.reason?.response?.data);
          }
        });
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'templates' }, error?.response?.data);
      } 
    },
    async fetchTemplates(entityType) {
      const searchParams = new URLSearchParams();
      searchParams.set('current_page', 1);
      searchParams.set('per_page', 9999);
      searchParams.set('entityType', entityType)
      try {
        const response = await templateService.getTemplates(
          this.$route.params.handle,
          this.$route.params.key,
          searchParams.toString()
        );
        this.templatesGrouped[entityType] = response.data?.templates || [];
        this.templates.push(...this.templatesGrouped[entityType]);
      } catch (error) {
        console.error('Error fetching templates:', error);
      }
    },
    removeFile(file) {
      this.$emit('removeFile', file);
    },
    async downloadFile(file) {
      if (!file.previewUrl) {
        console.error("File URL is missing");
        return;
      }

      try {
        const params = {
          download: true
        }
        const response = await attachmentService.getAttachmentUrl(this.$route.params.handle, "executions", file.uid, params);
        const blob = await axios.get(response.data, { responseType: 'arraybuffer' }).then(res => res.data);
        const uid = generateDateUid();
        const filename = file.name || `${uid}.${getExtension(file.type)}`;
        const url = URL.createObjectURL(new Blob([blob]));
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } catch (err) {
        showErrorToast(this.$swal, this.$t('toast.downloadError', { item: 'Attachment' }), {}, err?.response?.data)
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.reference-chip {
  transition: all 0.2s ease;
  cursor: pointer;
}

.reference-chip:hover {
  background: #E4E7EC !important;
  transform: translateY(-1px);
}

.reference-link {
  text-decoration: none;
  color: inherit;
}

.reference-link:hover {
  opacity: 0.8;
}

.custom_field_heading {
  color: #667085;
  font-weight: 400;
  font-size: 13px;
  margin: 12px 0 4px 0px;
}
</style>
<style scoped>
h2,
h3,
h4 {
  text-align: left;
}
h5 {
  color: #667085;
  font-family: Inter;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  text-align: left;
}
.collapse-btn {
  color: #0c2ff3;
}
p {
  text-align: left;
}
.flex {
  display: flex;
}
.chip-theme {
  background-color: #F2F4F7 !important;
  color: #344054 !important;
  height: 24px !important;
  border-radius: 8px !important;
}
.justify-between {
  justify-content: space-between;
}
.justify-start {
  justify-content: flex-start;
}
.align-center {
  align-items: center;
}
.bg-white {
  background-color: white !important;
}
.close-btn:hover {
  cursor: pointer;
}
.f-color-blue {
  color: #0c2ff3 !important;
}
.w-50 {
  width: 50%;
}
.align-left {
  text-align: left;
}
.align-left .contents {
  font-family: Inter;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
  color: #0c111d;
}
.toggle-btn {
  font-family: Inter;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
  cursor: pointer;
}

.panel-title {
  font-family: Inter;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
  color: #0C111D;
}
.grid{
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.detail-view-container {
  overflow-y: auto;
  max-height: calc(100vh - 120px);
  scrollbar-width: thin;
}
</style>