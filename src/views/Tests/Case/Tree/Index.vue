<template>
  <div class="pa-1 mr-1">
    <v-row align-content="start">
      <v-col
        cols="12"
        align-self="start"
        class="pb-0"
      >
        <v-toolbar
          v-if="isCollapsed"
          flat
          class="toolbar-bg"
        >
          <v-toolbar-title>
            <span
              v-if="!skeletonLoaderState"
              class="fs-18px font-weight-medium text-theme-base"
            >{{ $t('testFolders') }}</span>
            <v-skeleton-loader
              v-else
              height="24"
              width="100"
              type="text"
            />
          </v-toolbar-title>
          <v-spacer />
          <v-tooltip
            v-if="!skeletonLoaderState"
            bottom
          >
            <template #activator="{ on }">
              <v-btn
                v-if="writeEntity"
                icon
                :disabled="!selectedProjectByKey"
                :loading="isFolderLoadingState"
                :class="{ 'disabled-action': isProjectArchived }"
                @click="addTestFolder"
                v-on="on"
              >
                <folderPlusIcon />
              </v-btn>
            </template>
            <span v-if="selectedProjectByKey">{{ $t('info.addFolder') }}</span>
            <span v-else>{{ $t('info.selectProject') }}</span>
          </v-tooltip>
          <v-skeleton-loader
            v-else
            class="primary"
            height="24"
            width="24"
            type="button"
          />
        </v-toolbar>
        <v-toolbar
          v-else
          flat
          class="toolbar-bg item-center"
        >
          <v-btn
            icon
            :disabled="!selectedProjectByKey"
            :loading="isFolderLoadingState"
            :class="{ 'disabled-action': isProjectArchived }"
            @click="addTestFolder"
          >
            <v-icon color="#0C2FF3 !important">
              mdi-folder-plus-outline
            </v-icon>
          </v-btn>
        </v-toolbar>
      </v-col>
      <v-col
        v-if="isCollapsed"
        cols="12"
        align-self="start"
        class="v-padding-0"
      >
        <v-treeview
          v-if="items.length && !skeletonLoaderState"
          :items="items"
          activatable
          item-key="uid"
          item-children="children"
          :open.sync="open"
          transition
          :active.sync="selectedItemsArray"
          rounded
          expand-icon="mdi-chevron-down"
          class="text-left treeview-theme cursor-pointer scrollable-div"
          :class="{
            'has-child': isEditedItemsHasChildren,
            'has-no-child': !isEditedItemsHasChildren,
            'is-scrolled': isScrolled,
            'is-unscroll': !isScrolled,
          }"
          :dense="false"
          :load-children="disableDynamicLoading ? undefined : loadTreeChildren"
          @update:active="setActive"
        >
          <template #prepend="{ item }">
            <div
              :id="item.uid"
              class="d-flex flex-row justify-start align-center cursor-move pr-0"
            >
              <folderActiveIcon v-if="item.active" />
              <folderInActiveIcon v-else />
            </div>
          </template>
          <template #label="{ item, active }">
            <div
              v-if="item != currentEditableItem || item === editingFolder"
              :id="item.uid"
              class="d-flex flex-row justify-start align-center no-select pl-0 w-full"
              @contextmenu.prevent="showContextMenu($event, item)"
            >
              <div
                v-if="item === editingFolder"
                class="pl-0"
              >
                <v-text-field
                  v-model="editingFolder.name"
                  :value="editingFolder.name"
                  type="text"
                  background-color="transparent"
                  class="new-folder-input mt-0 pt-0 pl-0"
                  hide-details
                  autofocus
                  solo
                  flat
                  dense
                  @keyup.enter="updateFolder(item)"
                  @blur="updateFolder(item)"
                />
              </div>
              <div
                v-else
                class="pl-2"
              >
                <v-tooltip bottom>
                  <template #activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      class="font-weight-regular"
                      :class="{ 'text-theme-primary ': active, 'text-theme-label': !active }"
                      v-on="on"
                    >
                      {{ item && item.name ? item.name : $t('unnamedFolder') }}
                    </span>
                  </template>
                  <span>{{ item && item.name ? item.name : $t('unnamedFolder') }}</span>
                </v-tooltip>
              </div>
            </div>
            <div
              v-else-if="item.editable && item !== editingFolder"
              class="d-flex flex-row justify-start align-center pl-0 pr-0 w-full"
            >
              <v-text-field
                v-model="item.name"
                :value="item.name"
                type="text"
                background-color="transparent"
                class="new-folder-input mt-0 pt-0 pl-2"
                hide-details
                autofocus
                solo
                flat
                dense
                @keyup.enter="updateFolder(item)"
                @blur="updateFolder(item)"
                @click.stop
              />
            </div>
          </template>
        </v-treeview>
        <TreeViewSkeleton v-else />
      </v-col>
    </v-row>
    <FolderDeleteDialog
      v-if="folderDeleteDialog"
      :value="folderDeleteDialog"
      :folder_name="folderDeleteName"
      @handleConfirmClick="deleteFolder"
      @close="handleCloseDialog"
    />
    <v-menu
      v-model="showMenu"
      :position-x="menuX"
      :position-y="menuY"
      absolute
      offset-y
    >
      <v-list dense>
        <v-list-item
          :key="1"
          class="action-btn actions-item"
          @click="handleMenuEdit"
        >
          <div class="d-flex align-center">
            <EditIcon />
          </div>
          <v-list-item-title class="pl-3">
            {{ $t('edit') }}
          </v-list-item-title>
        </v-list-item>
        <v-list-item
          :key="2"
          class="action-btn actions-item"
          @click="handleMenuDelete"
        >
          <div class="d-flex align-center">
            <DeleteIcon />
          </div>
          <v-list-item-title class="pl-3 text-theme-danger">
            {{ $t('delete') }}
          </v-list-item-title>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import EditIcon from '@/assets/svg/edit.svg';
import DeleteIcon from '@/assets/svg/delete.svg';
import FolderDeleteDialog from '@/components/TestCases/FolderDeleteDialog.vue';
import folderPlusIcon from '@/assets/svg/folder-plus.svg';
import folderInActiveIcon from '@/assets/svg/folder-yellow.svg';
import folderActiveIcon from '@/assets/svg/folder-blue.svg';
import TreeViewSkeleton from '@/components/Skeletons/TestCases/TreeView.vue';
import { useTreeView } from '@/composables/modules/cases/treeView';
import { useCsvImport } from '@/composables/modules/cases/csvImport';
export default {
  components: {
    FolderDeleteDialog,
    EditIcon,
    DeleteIcon,
    folderPlusIcon,
    folderInActiveIcon,
    folderActiveIcon,
    TreeViewSkeleton,
  },
  props: {
    collapsed: Number,
    items: {
      required: true,
      type: Array,
    },
    selectedFolderUid: {
      type: [String, Number],
      required: false,
      default: null,
    },
    executionFolders: {
      type: Array,
    },
    writeEntity: {
      type: Boolean,
      default: false,
    },
    isImport: {
      type: Boolean,
      default: false,
    },
    disableDynamicLoading: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, context) {
    const {folderCasesMap} = useCsvImport();
    // Use the treeView composable that contains all the logic
    const treeView = useTreeView(props, context);
    
    // Return all properties and methods from the composable
    return {
      folderCasesMap,
      ...treeView
    };
  }
};
</script>

<style>
.is-scrolled {
  max-height: calc(100vh - 10rem);
}
.is-unscroll {
  max-height: calc(100vh - 22rem);
}
.scrollable-div {
  overflow: auto;
  scrollbar-width: none;
  display: flex;
  min-height: calc(100vh - 95vh);
}
.scrollable-div:hover {
  scrollbar-width: thin;
}
.item-center {
  display: flex;
  justify-content: center;
}
.toolbar-bg {
  background-color: transparent !important;
}
.height-parent {
  height: 100%;
}
.float-bottom {
  position: absolute;
  bottom: 0;
}
.position-relative {
  position: relative;
}
.card {
  border-radius: 5px;
}
.v-treeview-node__root {
  position: relative;
}

.v-treeview-node__children {
  margin-left: 10px;
}

.v-treeview-node__append {
  margin-left: 0px;
  padding-left: 0px;
}

.v-treeview-node {
  margin-left: 10px;
}

.v-padding-0 {
  padding-top: 0;
  padding-bottom: 0;
}
.v-treeview-node__children {
  border-left: 1px solid rgb(247, 248, 249);
}
.treeview-theme .v-treeview-node__root {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
  padding-left: 0 !important;
  height: 25px !important;
  min-height: 25px !important;
}

.treeview-theme.has-child .v-treeview-node__root .v-treeview-node__level {
  width: 20px !important;
  left: -12px;
  top: 9px;
  background: url('@/assets/svg/node-line.svg');
  background-size: contain;
  height: 100% !important;
  background-repeat: no-repeat;
  position: absolute;
  background-color: transparent !important;
}
.treeview-theme.has-no-child .v-treeview-node__level {
  width: 0px;
}
.no-select {
  user-select: none;
}
.v-menu {
  display: block;
}
.context-menu-item {
  cursor: pointer;
}
.action-btn .v-list-item__title {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.v-treeview-node__root {
  /* cursor: move; */
  user-select: none;
}

.v-treeview-node__root.dragging {
  opacity: 0.5;
}

.v-treeview-node__root.drag-over {
  border: 2px dashed #0c2ff3;
}
.v-treeview-node__root.drag-over-bottom {
  border-bottom: 2px solid #1976d2 !important;
}
.v-treeview-node__root.drag-over-top {
  border-top: 2px solid #1976d2 !important;
}
</style>
