<template>
  <div class="d-flex flex-column align-center justify-center w-full">
    <v-col
      cols="12"
      class="block main-area mt-4"
      :style="{ 'max-width':'438px' }"
    >
      <h2 class="mb-3">
        {{ $t('importFromCSV') }}
      </h2>
    
      <div class="mb-1 text-left">
        <v-label class="fs-14px text-theme-label font-weight-medium">
          {{ $t('chooseFile') }} <span class="red--text">*</span>
        </v-label>
      </div>
      <div class="text-left text-grey mb-2 fs-12px">
        {{ $t('fileEncodingInfo', { size: '10' }) }}
      </div>
      <fileInput
        v-model="csvFile"
        class="w-full mb-5"
        type="text/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/xml,text/xml"
        :drag-drop-text="$t('dragAndDrop')"
        :browse-text="$t('browseFiles')"
        @update:files="handleFileChange"
      />

      <v-row v-if="isCsvFile">
        <v-col cols="6">
          <div class="mb-1 text-left">
            <v-label class="fs-14px text-theme-label font-weight-medium">
              {{ $t('csvSeparator') }}
            </v-label>
          </div>
          <v-select
            v-model="csvSeparator"
            dense
            height="38px"
            :items="separatorOptions"
            item-value="value"
            item-text="text"
            append-icon="mdi-chevron-down"
            class="rounded-lg field-theme"
            background-color="#F9F9FB"
          />
        </v-col>
        <v-col cols="6">
          <div class="mb-1 text-left">
            <v-label class="fs-14px text-theme-label font-weight-medium">
              {{ $t('firstRow') }}
            </v-label>
          </div>
          <v-text-field
            v-model="firstRow"
            dense
            height="38px"
            class="rounded-lg field-theme"
            background-color="#F9F9FB"
            type="number"
            min="1"
          />
        </v-col>
      </v-row>

      <div
        v-if="isCsvFile"
        class="mb-1 text-left"
      >
        <v-label class="fs-14px text-theme-label font-weight-medium">
          {{ $t('tagsSeparator') }}
        </v-label>
      </div>
      <v-select
        v-if="isCsvFile"
        v-model="tagsSeparator"
        dense
        height="38px"
        :items="tagsSeparatorOptions"
        item-value="value"
        item-text="text"
        append-icon="mdi-chevron-down"
        class="rounded-lg field-theme mb-5"
        background-color="#F9F9FB"
      />

      <div
        v-if="isCsvFile"
        class="mb-2 text-left"
      >
        <v-label class="fs-14px text-theme-label font-weight-medium">
          {{ $t('rowFormat') }}
        </v-label>
      </div>
      <v-radio-group
        v-if="isCsvFile"
        v-model="rowFormat"
        class="mt-0 pt-0 mb-5"
      >
        <v-radio
          :label="$t('singleRowTestCase')"
          value="single"
          color="primary"
        />
        <v-radio
          :label="$t('multiRowTestCase')"
          value="multi"
          color="primary"
        />
      </v-radio-group>
    </v-col>

    <v-col
      cols="12"
      sm="12"
      class="d-flex justify-end py-4 px-6 ma-0 absolute"
    >
      <v-btn
        class="text-capitalize btn-theme px-6 py-2"
        color="primary"
        depressed
        height="40"
        @click="goToMapping"
      >
        {{ $t('goToColumnMapping') }}
      </v-btn>
    </v-col>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';
import fileInput from '@/components/base/FileInput.vue';
import { showErrorToast } from '@/composables/utils/toast';
import { useCsvImport } from '@/composables/modules/cases/csvImport';
import { t } from '@/i18n'
import Swal from 'sweetalert2';

export default {
  name: 'ImportStepOne',
  components: {
    fileInput
  },
  emits: ['next'],
  setup(props, { emit }) {


    // Use the CSV import composable to get shared state and methods
    const {
      csvFile,
      folders,
      handleFileChange,
      currentFileType,
    } = useCsvImport();

    // CSV Configuration
    const csvSeparator = ref(';');
    const firstRow = ref(1);
    const tagsSeparator = ref(',');
    const rowFormat = ref('single');
    
    // Computed property to check if the selected file is a CSV file
    const isCsvFile = computed(() => {
      return currentFileType.value === 'csv';
    });

    // Define all available separators
    const allSeparatorOptions = [
      { text: '; (semicolon)', value: ';' },
      { text: ', (comma)', value: ',' },
      { text: '| (pipe)', value: '|' },
      { text: 'tab', value: '\t' }
    ];

    const allTagsSeparatorOptions = [
      { text: ', (comma)', value: ',' },
      { text: '; (semicolon)', value: ';' },
      { text: '| (pipe)', value: '|' },
      { text: 'space', value: ' ' }
    ];

    // Create computed properties for filtered options
    const separatorOptions = computed(() => {
      return allSeparatorOptions.filter(option => option.value !== tagsSeparator.value);
    });

    const tagsSeparatorOptions = computed(() => {
      return allTagsSeparatorOptions.filter(option => option.value !== csvSeparator.value);
    });

    // Watch for changes in CSV separator and update tags separator if needed
    watch(csvSeparator, (newValue) => {
      // If tags separator is the same as the new CSV separator, change it to a different value
      if (tagsSeparator.value === newValue) {
        // Find the first available option that's not the current CSV separator
        const firstAvailableOption = allTagsSeparatorOptions.find(option => option.value !== newValue);
        if (firstAvailableOption) {
          tagsSeparator.value = firstAvailableOption.value;
        }
      }
    });

    // Watch for changes in Tags separator and update CSV separator if needed
    watch(tagsSeparator, (newValue) => {
      // If CSV separator is the same as the new tags separator, change it to a different value
      if (csvSeparator.value === newValue) {
        // Find the first available option that's not the current tags separator
        const firstAvailableOption = allSeparatorOptions.find(option => option.value !== newValue);
        if (firstAvailableOption) {
          csvSeparator.value = firstAvailableOption.value;
        }
      }
    });

    // Initialize data


    // Handler for going to mapping step
    const goToMapping = () => {
      if (!csvFile.value) {
        showErrorToast(Swal, t('noFileSelected'));
        return;
      }
      
      const selectedData = {
        csvFile: csvFile.value,
      };
      
      // Only include CSV configuration if the file is a CSV file
      if (isCsvFile.value) {
        selectedData.csvSeparator = csvSeparator.value;
        selectedData.firstRow = firstRow.value;
        selectedData.tagsSeparator = tagsSeparator.value;
        selectedData.rowFormat = rowFormat.value;
      }

      emit('next', selectedData);
    };

    return {
      csvFile,

      folders,
      handleFileChange,
      goToMapping,
      // CSV Configuration
      csvSeparator,
      separatorOptions,
      firstRow,
      tagsSeparator,
      tagsSeparatorOptions,
      rowFormat,
      isCsvFile
    };
  }
}
</script>

<style lang="scss" >
.v-input--radio-group__input{
    display: flex !important;
    gap: 8px !important;
    margin-top: 0px;
  }
  .v-label.theme--light{
    font-size: 14px !important;
  }
</style>
