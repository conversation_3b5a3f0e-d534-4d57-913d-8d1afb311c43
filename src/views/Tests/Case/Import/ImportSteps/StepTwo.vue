<template>
  <v-col
    cols="12"
    sm="12"
    class="block px-4"
  >
    <h2 class="mb-5">
      {{ $t('columnMapping') }}
    </h2>
    <div class="mapping-container mb-4">
      <div style="width: 60%;">
        <h3 class="fs-16px font-weight-bold">
          {{ $t('identifiedFields', { name: currentFileType }) }}
        </h3>
      </div>
      <div style="width: 40%;">
        <h3 class="fs-16px font-weight-bold">
          {{ $t('testFiestaFields') }}
        </h3>
      </div>
    </div>
    <div class="mapping-container">
      <!-- Left Panel - Identified CSV fields -->
      <div
        class="panel"
        style="width: 60%;"
      >
        <div class="csv-fields-grid">
          <div
            v-for="(field, index) in csvFields"
            :key="`csv-${index}`"
            class=" csv-field "
            :class="{ 
              'mapped': isCSVFieldMapped(field),
              'drag-over': dragOverField === field 
            }"
            @dragover.prevent="dragOver($event, field)"
            @dragleave="dragLeave"
            @drop="drop($event, field)"
          >
            <div class="holder">
              <div class="card">
                <iconDots style="width: 16px; height: 16px;" />
                <div class="field-text">
                  {{ field }}
                </div>
                <v-btn
                  icon
                  style="width: 22px; height: 22px;"
                  class="delete-field-btn"
                  title="Delete field"
                  @click.stop="handleDeleteCsvField(field, index)"
                >
                  <TrashIcon />
                </v-btn>
              </div>
              <div
                v-if="!isCSVFieldMapped(field)"
                class="card"
                style="background-color: #F2F4F7 !important; border: 1px solid #193BF4; box-shadow: 0px 2px 4px 0px #193BF440;"
              >
                <div class="field-text">
                  <span style="color: #B5C0D0;">Custom field</span>
                </div>
              </div>
              <div
                v-else
                class="card"
              >
                <iconDots />
                <div class="field-text">
                  {{ getMappedTestFiestaField(field) }}
                </div>
              </div>
              <v-btn
                v-if="isCSVFieldMapped(field)"
                icon
                small
                class="remove-mapping"
                @click="removeMapping(field)"
              >
                <v-icon
                  small
                  class="link-icon"
                >
                  mdi-link
                </v-icon>
                <v-icon
                  small
                  class="unlink-icon"
                >
                  mdi-link-variant-off
                </v-icon>
              </v-btn>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - TestFiesta fields -->
      <div
        class="panel"
        style="width: 40%;"
      >
        <!-- General section -->
        <div class="section">
          <div
            class="section-header"
            @click="toggleSection('general')"
          >
            <h4>{{ $t('general') }}</h4>
            <v-icon>{{ generalExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </div>
          
          <div
            v-show="generalExpanded"
            class="section-content-grid"
          >
            <div
              v-for="(field, index) in availableGeneralFields"
              :key="`general-${index}`"
              class="field-item testfiesta-field"
              draggable="true"
              @dragstart="dragStart($event, field)"
              @dragend="dragEnd"
            >
              <div class="field-content">
                <div class="field-icon">
                  <iconDots />
                </div>
                <div class="field-text">
                  {{ field.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Template section -->
        <div class="section">
          <div
            class="section-header"
            @click="toggleSection('template')"
          >
            <h4>{{ $t('template') }}</h4>
            <v-icon>{{ templateExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
          </div>
          
          <div
            v-show="templateExpanded"
            class="section-content"
          >
            <!-- Template change notification -->
            <div
              v-if="showTemplateChangeMessage"
              class="template-change-message"
            >
              {{ templateChangeMessage }}
            </div>
            
            <!-- Template dropdown -->
            <div
              class="template-selector w-full"
            >
              <v-menu
                v-model="openTemplateMenu"
                :close-on-content-click="false"
                offset-y
                bottom
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    color="#F9F9FB"
                    class="w-full d-flex justify-space-between text-body-2 font-weight-regular rounded-lg"
                    depressed
                    height="38px"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <span
                      class="mr-2 text-left text-truncate"
                      style="max-width: 200px;"
                    >
                      {{ selectedTemplateName || $t('selectTemplate') }}
                    </span>
                    <v-icon>mdi-chevron-down</v-icon>
                  </v-btn>
                </template>
                <template #default>
                  <v-card class="py-2">
                    <!-- Search bar -->
                    <v-card-text class="py-2">
                      <v-text-field
                        v-model="templateSearch"
                        class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
                        :placeholder="$t('placeHolder.searchByName')"
                        height="40"
                        clear-icon="mdi-close-circle"
                        clearable
                        background-color="#F9F9FB"  
                        :hide-details="true"
                        @input="filterTemplates"
                      >
                        <template #prepend-inner>
                          <SearchIcon />
                        </template>
                      </v-text-field>
                    </v-card-text>
                    
                    <!-- Templates list -->
                    <v-list
                      dense
                      class="py-0 template-list"
                    >
                      <v-list-item @click="createNewTemplate">
                        <v-list-item-content>
                          <v-list-item-title>
                            {{ $t('templatePage.createCustomTemplate') }}
                          </v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                      <v-list-item-group
                        v-model="selectedTemplateIndex"
                        color="primary"
                      >
                        <v-list-item
                          v-for="template in filteredTemplateOptions"
                          :key="template.id"
                          @click="selectTemplate(template)"
                        >
                          <v-list-item-content>
                            <v-list-item-title>{{ template.name }}</v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list-item-group>
                    </v-list>
                  </v-card>
                </template>
              </v-menu>
            </div>
            
            <!-- Template fields -->
            <div
              v-if="selectedTemplateDropdown && templateFields.length === 0"
              class="no-fields-message"
            >
              This template has no fields configured
            </div>
            <div
              v-else-if="!selectedTemplateDropdown"
              class="no-fields-message"
            >
              Select a template to see available fields
            </div>
            <div
              v-else-if="availableTemplateFields.length === 0"
              class="no-fields-message"
            >
              All fields from this template have been mapped
            </div>
            
            <div
              v-for="(field, index) in availableTemplateFields"
              :key="`template-${index}`"
              class="field-item testfiesta-field"
              draggable="true"
              @dragstart="dragStart($event, field)"
              @dragend="dragEnd"
            >
              <div class="field-content">
                <div class="field-icon">
                  <v-icon small>
                    {{ field.icon }}
                  </v-icon>
                </div>
                <div class="field-text">
                  {{ field.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <v-col
      cols="12"
      sm="12"
      class="d-flex justify-end pa-0 ma-0 mt-4"
    >
      <v-btn
        class="text-capitalize btn-theme px-6 py-2"
        color="primary"
        depressed
        height="40"
        @click="goToPreview"
      >
        {{ $t('goToPreview') }}
      </v-btn>
    </v-col>
    <TemplateCreateUpdateCustomTemplateDialog
      v-model="showCreateUpdateCustomTemplateDialog"
      :data="selectedTemplates"
      :loading.sync="createBtnLoading"
      @create-template="createTemplate"
      @update-template="updateTemplate"
      @close-dialog="showCreateUpdateCustomTemplateDialog = false"
    />
  </v-col>
</template>

<script>
import { ref, onMounted, computed, nextTick, watch } from 'vue';
import { useCsvImport } from '@/composables/modules/cases/csvImport';
import { useRoute } from 'vue-router/composables';
import iconDots from '@/assets/svg/dots-20x20-gray.svg';
import TrashIcon from '@/assets/svg/trash-outline.svg';
import Swal from 'sweetalert2';
import { showErrorToast, showSuccessToast } from '@/composables/utils/toast';
import { t } from '@/i18n';
import SearchIcon from '@/assets/svg/search-icon.svg';
import TemplateCreateUpdateCustomTemplateDialog from '@/components/Admin/CustomTemplates/CreateUpdateCustomTemplateDialog.vue';
import makeTemplateService from '@/services/api/template';
import { $api } from '@/main';

export default {
  name: 'ImportStepTwo',
  components: {
    iconDots, SearchIcon, TemplateCreateUpdateCustomTemplateDialog, TrashIcon
  },
  props: {
    header: {
      type: Array,
      required: true
    }
  },
  emits: ['next', 'update:header'],
  setup(props, { emit }) {
    const route = useRoute();
    
    // Use the CSV import composable
    const { 
      handleNextTwo,
      tagsSeparator,
      rowFormat,
      templates,
      templateFields: templateFieldsMap,
      fetchTemplates,
      selectedTemplateDropdown,
      persistedMappings,
      deleteCsvField,
      currentFileType
    } = useCsvImport();

    // Initialize services
    const templateService = makeTemplateService($api);

    // State for drag and drop
    const csvFields = ref([...props.header]);
    const generalExpanded = ref(true);
    const templateExpanded = ref(true);
    const draggingField = ref(null);
    
    // Using a Map to store mappings for better reactivity
    const mappingsMap = ref(new Map());
    // Counter to track changes and force re-renders
    const updateTrigger = ref(0);
    // Notification message for template change
    const templateChangeMessage = ref('');
    const showTemplateChangeMessage = ref(false);

    // Template menu state
    const openTemplateMenu = ref(false);
    const templateSearch = ref('');
    const selectedTemplateIndex = ref(null);
    const filteredTemplateOptions = ref([]);
    const selectedTemplateName = ref('');

    // Template creation dialog state
    const showCreateUpdateCustomTemplateDialog = ref(false);
    const createBtnLoading = ref(false);
    const selectedTemplates = ref({
      uid: '',
      name: '',
      templateFields: []
    });

    // Template options - computed from the templates fetched
    const templateOptions = computed(() => {
      return templates.value.map(template => ({
        id: template.uid,
        name: template.name
      }));
    });

    // Define general fields
    const generalFields = ref([
      { id: 'title', name: 'Title',  },
      { id: 'id', name: 'ID', },
      {id:'folder', name: 'Folder', },
      { id: 'tags', name: 'Tags',  },
      { id: 'priority', name: 'Priority',  },
      { id: 'status', name: 'Status',  },
      { id: 'creation_date', name: 'Creation date',  },
      { id: 'last_update', name: 'Last update',  },
      { id: 'attachments', name: 'Attachments',  }
    ]);

    // Define template fields based on selected template - now using templateFields from composable
    const templateFields = computed(() => {
      // When a template is selected, get its fields from the templateFieldsMap
      if (selectedTemplateDropdown.value && templateFieldsMap.value[selectedTemplateDropdown.value]) {
        // Get the selected template's fields
        const fields = templateFieldsMap.value[selectedTemplateDropdown.value];
        return fields;
      }
      
      // If no template is selected or no fields are found, return an empty array
      return [];
    });

    // Watch for template selection changes to expand the template section
    const watchTemplateSelection = async (newValue, oldValue) => {
      if (newValue && !templateExpanded.value) {
        templateExpanded.value = true; // Auto-expand template section when a template is selected
      }
      
      // If template changed (not just initial selection), remove mappings from previous template
      if (oldValue && newValue !== oldValue) {
        
        // Get all fields from the old template
        const oldTemplateFields = templateFieldsMap.value[oldValue] || [];
        
        // Check all mappings and remove those from the old template
        const keysToRemove = [];
        
        mappingsMap.value.forEach((value, key) => {
          // Check if this mapped field belongs to the old template (not a general field)
          const isGeneralField = generalFields.value.some(f => f.id === value.id);
          
          if (!isGeneralField) {
            // This is a template field, check if it's from the old template
            const matchesOldTemplate = oldTemplateFields.some(field => 
              field.id === value.id || 
              (field.name === value.name && (field.id === field.name || !field.id))
            );
            
            if (matchesOldTemplate) {
              keysToRemove.push(key);
            }
          }
        });
        
        // Remove all identified mappings
        for (const key of keysToRemove) {
          mappingsMap.value.delete(key);
        }
        
        if (keysToRemove.length > 0) {          
          // Show notification message
          templateChangeMessage.value = `${keysToRemove.length} field${keysToRemove.length > 1 ? 's were' : ' was'} unmapped due to template change`;
          showTemplateChangeMessage.value = true;
          
          // Hide message after 3 seconds
          setTimeout(() => {
            showTemplateChangeMessage.value = false;
          }, 3000);
          
          // Increment the update trigger to force re-render
          updateTrigger.value++;
          // Force the next tick to ensure the DOM updates
          await nextTick();
        }
      }
    };
    
    // Fetch templates on mount and watch for template selection
    onMounted(async () => {
      await fetchTemplates(route.params.handle, route.params.key);
      
      // Restore mappings if navigating back from a later step
      if (persistedMappings.value) {
        // Restore template selection
        selectedTemplateDropdown.value = persistedMappings.value.templateId || selectedTemplateDropdown.value;
        
        // Find the template name for the selected template
        const selectedTemplate = templates.value.find(t => t.uid === selectedTemplateDropdown.value);
        if (selectedTemplate) {
          selectedTemplateName.value = selectedTemplate.name;
        }
        
        // Restore standard mappings
        if (persistedMappings.value.mappings && persistedMappings.value.mappings.length) {
          persistedMappings.value.mappings.forEach(mapping => {
            // Find the corresponding TestFiesta field for this mapping
            let fieldToMap;
            
            // Check general fields first
            if (['name', 'externalId', 'externalCreatedAt', 'externalUpdatedAt', '_steps', 'priority', 'status', 'tags', 'folder'].includes(mapping.biddingValue)) {
              // Map API biddingValues back to field IDs
              let fieldId;
              if (mapping.biddingValue === 'name') {
                fieldId = 'title';
              } else if (mapping.biddingValue === 'externalId') {
                fieldId = 'id';
              } else if (mapping.biddingValue === 'externalCreatedAt') {
                fieldId = 'creation_date';
              } else if (mapping.biddingValue === 'externalUpdatedAt') {
                fieldId = 'last_update';
              } else if (mapping.biddingValue === '_steps') {
                fieldId = 'step_description';
              } else {
                fieldId = mapping.biddingValue;
              }
              
              fieldToMap = generalFields.value.find(f => f.id === fieldId);
            }
            
            if (fieldToMap) {
              // Add mapping to the map
              mappingsMap.value.set(mapping.csvHeader, fieldToMap);
            }
          });
        }
        
        // Restore template field mappings
        if (persistedMappings.value.templateFields) {
          Object.entries(persistedMappings.value.templateFields).forEach(([fieldId, fieldData]) => {
            // Check if we can find this field in the template fields
            const templateField = templateFieldsMap.value[selectedTemplateDropdown.value]?.find(
              f => f.id === fieldId
            );
            
            if (templateField) {
              mappingsMap.value.set(fieldData.csvHeader, templateField);
            }
          });
        }
        
        // Trigger update to refresh the UI
        updateTrigger.value++;
      } else {
        // Set a default template if available and not navigating back
        if (templates.value.length > 0 && !selectedTemplateDropdown.value) {
          selectedTemplateDropdown.value = templates.value[0].uid;
          selectedTemplateName.value = templates.value[0].name;
        }
      }
      
      // Initialize filtered templates
      filteredTemplateOptions.value = templateOptions.value;
      filterTemplates();
    });
    
    // Template menu methods
    const filterTemplates = () => {
      if (!templateOptions.value.length) return;
      
      if (!templateSearch.value) {
        filteredTemplateOptions.value = templateOptions.value;
      } else {
        const searchTerm = templateSearch.value.toLowerCase();
        filteredTemplateOptions.value = templateOptions.value.filter(
          template => template.name.toLowerCase().includes(searchTerm)
        );
      }
    };
    
    const selectTemplate = (template) => {
      selectedTemplateDropdown.value = template.id;
      selectedTemplateName.value = template.name;
      openTemplateMenu.value = false;
      
      // Find the index for the selected template in the original array
      selectedTemplateIndex.value = templateOptions.value.findIndex(t => t.id === template.id);
    };
    
    const createNewTemplate = () => {
      openTemplateMenu.value = false;
      
      // Reset the selected template object for a new creation
      selectedTemplates.value = {
        uid: '',
        name: '',
        templateFields: []
      };
      
      // Open the template creation dialog
      showCreateUpdateCustomTemplateDialog.value = true;
    };
    
    // Watch for template selection changes
    watch(selectedTemplateDropdown, watchTemplateSelection);

    // Drag-and-drop handlers - REVERSED to drag from TestFiesta to CSV
    const dragStart = (event, field) => {
      draggingField.value = field;
      event.dataTransfer.setData('text/plain', JSON.stringify(field));
      event.dataTransfer.effectAllowed = 'move';
    };

    const dragEnd = () => {
      draggingField.value = null;
    };

    // Add new ref for tracking drag over state
    const dragOverField = ref(null);
    
    // Update dragOver handler
    const dragOver = (event, field) => {
      event.preventDefault();
      event.dataTransfer.dropEffect = 'move';
      dragOverField.value = field;
    };
    
    // Add dragLeave handler
    const dragLeave = () => {
      dragOverField.value = null;
    };

    // Update drop handler to clear dragOverField
    const drop = async (event, csvField) => {
      event.preventDefault();
      dragOverField.value = null;
      if (!draggingField.value) return;
      
      const testFiestaField = draggingField.value;
      
      // Check if another CSV field is already mapped to this TestFiesta field
      mappingsMap.value.forEach((value, key) => {
        // Match on ID or name for field identification
        if ((value.id === testFiestaField.id || 
            (value.name === testFiestaField.name && (testFiestaField.id === testFiestaField.name || !testFiestaField.id))) 
            && key !== csvField) {
          // If it is, remove the old mapping
          mappingsMap.value.delete(key);
        }
      });
      
      // Create the new mapping - use map set for reliable reactivity
      mappingsMap.value.set(csvField, testFiestaField);
      
      // Increment the update trigger to force re-render
      updateTrigger.value++;
      
      // Force the next tick to ensure the DOM updates
      await nextTick();
    };

    // Section toggle handlers
    const toggleSection = (section) => {
      if (section === 'general') {
        generalExpanded.value = !generalExpanded.value;
      } else if (section === 'template') {
        templateExpanded.value = !templateExpanded.value;
      }
    };

    // Mapping helpers - UPDATED for reliable reactivity
    const isCSVFieldMapped = (csvField) => {
      // Use the update trigger in computed to ensure reactivity
      updateTrigger.value; // This line ensures the function reruns when trigger changes
      return mappingsMap.value.has(csvField);
    };

    const getMappedTestFiestaField = (csvField) => {
      updateTrigger.value; // This line ensures the function reruns when trigger changes
      return mappingsMap.value.get(csvField)?.name || '';
    };

    const removeMapping = async (csvField) => {
      mappingsMap.value.delete(csvField);
      // Increment the update trigger to force re-render
      updateTrigger.value++;
      // Force the next tick to ensure the DOM updates
      await nextTick();
    };

    // Add the deleteCsvField method to remove a CSV field
    const handleDeleteCsvField = async (field, index) => {
      // If this field is mapped, remove the mapping first
      if (isCSVFieldMapped(field)) {
        mappingsMap.value.delete(field);
      }
      
      // Call the shared delete function to update csvFileData
      await deleteCsvField(field);
      
      // Remove the field from the local csvFields array
      csvFields.value.splice(index, 1);
      
      // Update parent component if needed
      emit('update:header', csvFields.value);
      
      // Increment the update trigger to force re-render
      updateTrigger.value++;
      
      // Force the next tick to ensure the DOM updates
      await nextTick();
    };

    // Convert mappings to format expected by next step
    const formatMappingsForNextStep = () => {
      const formattedMappings = [];
      const templateFieldsMap = {};
      
      mappingsMap.value.forEach((testFiestaField, csvField) => {
        // Check if this is a general field or a template field
        const isGeneralField = generalFields.value.some(f => f.id === testFiestaField.id);
        
        if (isGeneralField) {
          let biddingValue;
          
          // Map TestFiesta field IDs to API expected biddingValues
          if (testFiestaField.id === 'title') {
            biddingValue = 'name';
          } else if (testFiestaField.id === 'id') {
            biddingValue = 'externalId';
          } else if (testFiestaField.id === 'step_description') {
            biddingValue = '_steps';
          } else if (testFiestaField.id === 'creation_date') {
            biddingValue = 'externalCreatedAt';
          } else if (testFiestaField.id === 'last_update') {
            biddingValue = 'externalUpdatedAt';
          } else if (testFiestaField.id === 'folder') {
            biddingValue = 'folder';
          } else {
            biddingValue = testFiestaField.id;
          }
          
          // Add to standard mappings
          formattedMappings.push({
            biddingValue,
            name: testFiestaField.name,
            csvHeader: csvField
          });
        } else {
          // This is a template field - store in templateFields map
          // Use the field id as the key and the csv field as the value
          const fieldId = testFiestaField.id;
          templateFieldsMap[fieldId] = {
            csvHeader: csvField,
            name: testFiestaField.name,
            dataType: testFiestaField.dataType || 'text' // Include dataType if available
          };
        }
      });
      
      // Return both standard mappings and template fields
      return {
        mappings: formattedMappings,
        templateFields: templateFieldsMap,
        templateId: selectedTemplateDropdown.value // Use the shared ref from useCsvImport
      };
    };

    // Go to preview step
    const goToPreview = () => {
      // Check if title is mapped
      let hasTitleMapping = false;
      mappingsMap.value.forEach(field => {
        if (field.id === 'title') {
          hasTitleMapping = true;
        }
      });
      
      if (!hasTitleMapping) {
        // Show error toast or validation message
        showErrorToast(Swal, t('noTitleMapping'))
        return;
      }
      
      // Format mappings for next step
      const { mappings, templateFields } = formatMappingsForNextStep();
      
      // Use the handleNextTwo from the composable, pass both standard mappings and template fields
      handleNextTwo({ mappings, templateFields });
    };

    // Computed properties to filter out already mapped fields
    const availableGeneralFields = computed(() => {
      // Make sure the computed property re-evaluates when updateTrigger changes
      updateTrigger.value;
      
      return generalFields.value.filter(field => {
        // Check if this field is already mapped to any CSV field
        for (const [, mappedField] of mappingsMap.value.entries()) {
          if (mappedField.id === field.id) {
            return false;
          }
        }
        return true;
      });
    });
    
    const availableTemplateFields = computed(() => {
      // Make sure the computed property re-evaluates when updateTrigger changes
      updateTrigger.value;
      
      // Get all template fields for the selected template
      const allFields = templateFields.value;
      
      // Filter out fields that are already mapped
      const available = allFields.filter(field => {
        // Check if this field is already mapped to any CSV field
        for (const [, mappedField] of mappingsMap.value.entries()) {
          // Compare using name if IDs match or if ID equals name (meaning name was used as ID)
          if (mappedField.id === field.id || 
              (mappedField.name === field.name && (field.id === field.name || !field.id))) {
            return false;
          }
        }
        return true;
      });
      return available;
    });



    // Template service methods
    const createTemplate = async (template) => {
      createBtnLoading.value = true;
      try {
       await templateService.createTemplate(
          route.params.handle, 
          route.params.key, 
          template
        );
        
        // Close the dialog
        showCreateUpdateCustomTemplateDialog.value = false;
        
        // Show success message
        showSuccessToast(Swal, t('createSuccess', { item: 'template' }));
        
        // Refresh templates and update dropdown
        await fetchTemplates(route.params.handle, route.params.key);
        
        // If templates are available, select the first one
        if (templates.value.length > 0) {
          selectedTemplateDropdown.value = templates.value[0].uid;
          selectedTemplateName.value = templates.value[0].name;
        }
        
        // Update filtered templates
        filteredTemplateOptions.value = templateOptions.value;
        filterTemplates();
      } catch (error) {
        showErrorToast(Swal, t('createError', { item: 'template' }), error?.response?.data);
      } finally {
        createBtnLoading.value = false;
      }
    };

    const updateTemplate = async (template) => {
      createBtnLoading.value = true;
      try {
        await templateService.updateTemplate(
          route.params.handle,
          route.params.key,
          template.uid,
          template
        );
        
        // Close the dialog
        showCreateUpdateCustomTemplateDialog.value = false;
        
        // Show success message
        showSuccessToast(Swal, t('updateSuccess', { item: 'template' }));
        
        // Refresh templates and update dropdown
        await fetchTemplates(route.params.handle, route.params.key);
        
        // If templates are available, keep the current selection
        const currentTemplate = templates.value.find(t => t.uid === selectedTemplateDropdown.value);
        if (currentTemplate) {
          selectedTemplateName.value = currentTemplate.name;
        }
        
        // Update filtered templates
        filteredTemplateOptions.value = templateOptions.value;
        filterTemplates();
      } catch (error) {
        showErrorToast(Swal, t('updateError', { item: 'template' }), error?.response?.data);
      } finally {
        createBtnLoading.value = false;
      }
    };

    return {
      csvFields,
      generalFields,
      templateFields,
      generalExpanded,
      templateExpanded,
      selectedTemplateDropdown,
      templateOptions,
      tagsSeparator,
      rowFormat,
      updateTrigger, // Expose the trigger to template for reactivity
      templateChangeMessage,
      showTemplateChangeMessage,
      currentFileType,
      // Template menu state
      openTemplateMenu,
      templateSearch,
      selectedTemplateIndex, 
      filteredTemplateOptions,
      selectedTemplateName,
      
      // Template creation dialog state
      showCreateUpdateCustomTemplateDialog,
      createBtnLoading,
      selectedTemplates,
      
      // Template menu methods
      filterTemplates,
      selectTemplate,
      createNewTemplate,
      
      // Drag and drop handlers
      dragStart,
      dragEnd,
      dragOver,
      dragLeave,
      drop,
      
      // Mapping helpers
      isCSVFieldMapped,
      getMappedTestFiestaField,
      removeMapping,
      
      // Section toggle
      toggleSection,
      
      // Navigation
      goToPreview,
      availableGeneralFields,
      availableTemplateFields,
      
      // Template service methods
      createTemplate,
      updateTemplate,
      
      // Add the deleteCsvField method
      handleDeleteCsvField,
      
      // New ref for tracking drag over state
      dragOverField,

    };
  }
}
</script>

<style lang="scss" scoped>
.mapping-container {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
}

.panel {
  background-color: #F2F4F7;
  border-radius: 8px;
  padding: 20px;
  // max-height: 600px;
  overflow-y: auto;
  
  h3 {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
}

.csv-fields-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.section {
  margin-bottom: 16px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background-color: #f0f0f0;
    border-radius: 6px;
    cursor: pointer;
    
    h4 {
      font-size: 16px;
      font-weight: 500;
      margin: 0;
    }
  }
  
  .section-content-grid {
    margin-top: 8px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .section-content {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.field-item {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 4.5px;
  
  border-radius: 6px;
  background-color: white;
  
  &.testfiesta-field {
    cursor: grab;
    transition: all 0.2s;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
  
  
  .field-content {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    gap: 4px;
    
    .field-icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .field-text {
      font-size: 14px;
      font-weight: 600;
    }
  }
}

  .csv-field {
    
    .holder{
      background: #D2D9E3;
      padding: 6px;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      gap: 6px;
      position: relative;
    }
    &.mapped {
      border-color: #2196f3;
      background-color: rgba(33, 150, 243, 0.05);
    }
    &.drag-over .holder {
      border: 2px dashed #D2D9E3;
      background-color: rgba(25, 59, 244, 0.05);
    }
  }

.mapped-value-container {
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px dashed #e0e0e0;
}

.mapped-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  
  .mapped-field {
    background-color: #e3f2fd;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #1976d2;
    font-weight: 500;
  }
}

  .remove-mapping {
    position: absolute !important;
    right: 50%;
    top: 50%;
    transform: translate(50%, -50%);
    background: #D2D9E3;
    transition: background-color 0.3s ease;

    .unlink-icon {
      display: none;
      color: #0C2FF3;
    }

    &:hover {
      background-color: #e8f0fe;

      .link-icon {
        display: none;
      }

      .unlink-icon {
        display: inline-flex;
      }
    }
  }

/* Add styles for the delete field button */
.delete-field-btn {
  position: absolute !important;
  right: -15px;
  top: -3px;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.2s ease;
  background: #D2D9E3;
}

.card {
  position: relative;
  
  &:hover .delete-field-btn {
    opacity: 1;
  }
}

.custom-field-input {
  margin-top: 8px;
  font-size: 12px;
}

.template-selector {
  margin-bottom: 12px;
}

.no-fields-message {
  padding: 12px;
  text-align: center;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 6px;
  margin-bottom: 8px;
  font-size: 14px;
  font-style: italic;
}

.card{
  display: flex;
  align-items: center;
  height: 32px;
  font-size: 14px;
  font-weight: 600;
  gap: 4px;
  padding: 6px;
  border-radius: 6px;
  background-color: white;
  border: 1px solid #e0e0e0;
}

.template-change-message {
  padding: 10px;
  text-align: center;
  color: #e65100;
  background-color: #fff3e0;
  border-radius: 6px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  border-left: 4px solid #ff9800;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.template-list {
  max-height: 300px;
  overflow-y: auto;
}

.v-btn.w-full {
  width: 100%;
  text-align: left;
  justify-content: flex-start;
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-transform: capitalize;
}
</style>
