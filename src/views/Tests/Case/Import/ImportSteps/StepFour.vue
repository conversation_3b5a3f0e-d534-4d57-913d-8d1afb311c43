<template>
  <div>
    <section class="cardHeader">
      <h2>
        {{ $t('importFromCSVDescription') }}
      </h2>
      <span>
        {{ $t('dragAndDropTestCases') }}
      </span>
    </section>
    
    <!-- Show message about pre-assigned folders when applicable -->
    <div
      v-if="hasCasesWithFolderAssignments && casesWithFolderAssignments.length > 0"
      class="alertbox"
    >
      <div class="d-flex flex-column">
        <div
          class="font-weight-medium"
          style="color: #0C2FF3;"
        >
          <span v-if="totalCases > 0">{{ casesWithFolderAssignments.length }} of {{ totalCases }} test cases</span>
          <span v-else>Some test cases</span>
          have pre-assigned folders from your CSV import. These will be automatically placed in those folders.
        </div>
        <div class="d-flex mt-2">
          <v-btn
            class="text-capitalize btn-theme px-6 py-2"
            color="primary"
            depressed
            height="40"
            min-width="140px"
            :loading="preAssignedUploadInProgress"
            :disabled="preAssignedUploadInProgress"
            @click="handleUploadPreAssignedCases"
          >
            <v-icon
              left
              small
            >
              mdi-upload
            </v-icon>
            Upload pre-assigned cases now
          </v-btn>
        </div>
      </div>
    </div>
    
    <v-row
      align-content="start"
      justify="start"
      dense
    >
      <CaseConfirmDialog
        v-model="dialog"
        @edit="openDetailView"
      />
      <div
        :style="{ width: isTreeViewCollapsed ? '5%' : '15%' }"
        class="mt-4"
      >
        <div
          class="d-flex flex-column white card rounded-lg ml-1 mr-2 sticky-on-scroll"
          :class="{ 'is-collapsed-menu': isTreeViewCollapsed }"
        >
          <TreeViewFolder
            :items="computedFolder"
            :selected-folder-uid="selectedFolderUid"
            :collapsed="isTreeViewCollapsed ? 0 : 1"
            :write-entity="writeEntity"
            :is-import="true"
            @folder-selected="selectFolder"
            @folder-delete="deleteFolder"
            @refresh-folders="getFolders"
          />
          <div
            v-if="showCollapse"
            class="collapse-btn"
            @click="toggleMenu"
          >
            <v-icon color="#0C2FF3">
              {{ isTreeViewCollapsed ? 'mdi-arrow-right-bottom' : 'mdi-arrow-left-bottom'
              }}
            </v-icon>
            <span
              v-if="!isTreeViewCollapsed"
              class="collapse-text"
            >{{ $t('collapse') }}
            </span>
          </div>
        </div>
      </div>
      <v-col
        class=""
        :style="{ width: isTreeViewCollapsed ? '95%' : '85%' }"
      >
        <v-row
          align-content="start"
          justify="start"
          dense
        >
          <div
            :class="[isDetailViewCollapsed ? 'col-7' : 'col-12']"
            class="col mt-3"
          >
            <cases-list
              v-if="casesToImportDisplayed.length"
              id="cases-list"
              :is-import="true"
              :case-items="casesToImportDisplayed"
              :initial-selected="runCases"
              :bread-crumbs="breadCrumbs"
              :allow-action="writeEntity && allowAction"
              :write-entity="writeEntity"
              :delete-entity="deleteEntity"
              :is-repository="!quickCreate"
              :from-run="fromRun"
              :selected-case.sync="selectedCase"
              @gotoRepository="gotoRepository"
              @back="$emit('back')"
              @folder-selected="selectFolder"
              @caseRemove="onDeleteCase"
              @bulkCaseRemove="onBulkDeleteCases"
              @submit="submitHandler"
            />
        
            <div class="relative d-flex flex-column align-center pa-6 white justify-center card app-height-global rounded-lg">
              <img
                src="@/assets/png/caseimport.png"
                style="width: 427px;"
                alt="Empty Case"
              >

              <div class="title">
                {{ $t('TestCaseAssignment') }}
              </div>
              <div class="sub-text">
                {{ $t('TestCaseAssignmentSubText') }}
              </div>

              <div
                class="footer-buttons"
              >
                <v-btn

                  depressed
                  background-color="#F2F4F7"
                  class="font-inter text-capitalize black--text mr-4 "
                  height="40"
                  min-width="140px"
                  @click="$emit('back')" 
                >
                  {{ $t('back') }}
                </v-btn>
    
                <v-btn
                  class="text-capitalize btn-theme px-6 py-2"
                  color="primary"
                  depressed
                  height="40"
                  min-width="140px"
                  @click="gotoRepository" 
                >
                  {{ $t('gotoRepository') }}
                </v-btn>
              </div>
            </div>

            <slot name="control-area" />
          </div>
          <div
            v-if="isDetailViewCollapsed"
            class="col-5 mt-3"
          >
            <CaseDetailView
              :case-item="selectedCase"
              :is-selected-case-first-index="isCurrentSelectedCasesFirstIndex"
              :is-selected-case-last-index="isCurrentSelectedCasesLastIndex"
              :selected-folder="breadCrumbs"
              :from-run="fromRun"
              @closeDetail="closeDetailView"
              @viewPreviousCase="viewPreviousCase"
              @viewNextCase="viewNextCase"
            />
          </div>
        </v-row>
      </v-col>
    </v-row>
    <!-- Submission Progress Snackbar -->
    <v-snackbar
      v-model="submissionInProgress"
      :timeout="-1"
      class="alert w-full"
      bottom
      right
    >
      <div class="d-flex align-start gap-4 w-full">
        <SwalAlertIcon />
        <div class="d-flex flex-column w-full">
          <span class="mb-2">{{ $t('assigningTestCasesToFolder', { count: totalSubmissionItems, folderName: submissionFolderName }) }}</span>
          <div class="d-flex justify-center align-center w-full">
            <v-progress-linear
              :value="submissionProgress"
              height="5"
              color="primary"
              class="mb-0 w-full"
              rounded
            />
            <span class="text-right ml-2 mb-0">{{ submissionProgress }}%</span>
          </div>
        </div>
      </div>
    </v-snackbar>
  </div>
</template>
<script>
import CasesList from '@/components/Cases/CaseList.vue';
import TreeViewFolder from '@/views/Tests/Case/Tree/Index.vue';
import CaseDetailView from '@/views/Tests/Case/Detail/Index.vue';   
import { onMounted, computed, ref } from 'vue';
import CaseConfirmDialog from '@/components/TestRuns/ConfirmDialog.vue'
import { useCaseManagement } from '@/composables/modules/cases/management';
import { useTestCasesIndex } from '@/composables/modules/cases/index';
import { useCsvImport } from '@/composables/modules/cases/csvImport';
import { showSuccessToast } from '@/composables/utils/toast';
import SwalAlertIcon from '@/assets/svg/swal_alert.svg';
import { useRoute } from 'vue-router/composables';


export default {
    name: 'CaseManagement',
    components: {
        CasesList,
        TreeViewFolder,
        CaseDetailView,
        CaseConfirmDialog,
      SwalAlertIcon,
    },
    props: {
  
        fromRun: {
            type: Boolean,
            default: false,
        },
        fromTestCase: {
            type: Boolean,
            default: false,
        },
        showCollapse: {
            type: Boolean,
            default: true
        },
        selectOption: {
            type: Boolean,
            default: false,
        },
        allowAction: {
            type: Boolean,
            default: true
        },
        quickCreate: {
            type: Boolean,
            default: false
        },
        

        runCases: {
            type: Array,
            default: () =>
            {
                return []
            }
        },


        setSelectedFolderUid: {
            type: [Number, String],
            default: null
        }
    },
    emits: ['back', 'refresh-folders', 'submit'],
    setup(props, context)
    {
      const route = useRoute();
      
      // Get cache clearing function
      const { clearCache } = useTestCasesIndex();
      
      const computedFolder = computed(() => {
        return folders.value
      });
        const { 
            casesToImport, 
            gotoRepository, 
            submissionInProgress, 
            submissionProgress, 
            totalSubmissionItems,
            submissionFolderName,
            handleSubmit,
            processedItems,
            uploadPreAssignedCases
        } = useCsvImport()
        
        // Computed property to filter cases with folder assignments
        const casesWithFolderAssignments = computed(() => {
          return casesToImport.value.filter(testCase => testCase._hasFolderAssignment);
        });
        
        // Computed property to filter cases without folder assignments (to be displayed for manual assignment)
        const casesWithoutFolderAssignments = computed(() => {
          return casesToImport.value.filter(testCase => !testCase._hasFolderAssignment);
        });
        
        // Computed flag to determine if there are any cases with folder assignments
        const hasCasesWithFolderAssignments = computed(() => {
          return casesWithFolderAssignments.value.length > 0;
        });
        
        // Total number of cases 
        const totalCases = computed(() => {
          return casesToImport.value.length;
        });
        
        // State for tracking pre-assigned test cases upload progress
        const preAssignedUploadInProgress = ref(false);
        
        // Function to handle uploading pre-assigned test cases
        const handleUploadPreAssignedCases = async () => {
          if (!casesWithFolderAssignments.value.length) {
            return;
          }
          
          preAssignedUploadInProgress.value = true;
          
          try {
        
            // Get route params
            const routeParams = {
              params: {
                handle: route.params.handle,
                key: route.params.key
              }
            };
            
            // Call the uploadPreAssignedCases function from the composable
            const result = await uploadPreAssignedCases(routeParams);
            
            if (result.success) {
              // Show success message
              showSuccessToast(
                context.root.$swal, 
                'uploadSuccess', 
                { 
                  count: result.count, 
                  item: result.count === 1 ? 'test case' : 'test cases' 
                }
              );
            }
          } catch (error) {
            console.error('Error uploading pre-assigned cases:', error);
          } finally {
            preAssignedUploadInProgress.value = false;
          }
        };
        
        const {
            isTreeViewCollapsed,
            isDetailViewCollapsed,
            selectedItem,
            breadCrumbs,
            selectedCase,
            selectedCases,
            selectedFolder,
            dialog,
            handleSelected,

            // Computed
            currentAccount,
            selectedFolderUid,
            isCurrentSelectedCasesFirstIndex,
            isCurrentSelectedCasesLastIndex,
            nextCase,
            previousCase,

            // Methods
            confirmEditing,
            toggleMenu,
            changeExpansion,
            closeDetailView,
            handleCases,
            viewPreviousCase,
            viewNextCase,
            selectFolder,
            onCreateCase,
            openDetailView,
            deleteFolder,
            updateSelectedCases,
            getCase,
            onDeleteCase,
            onBulkDeleteCases,
        } = useCaseManagement(props, context);

        const {
            cases,
            folders,

            writeEntity,
            deleteEntity,
            initialSelectedFolder,
            folderUid,
            onSelectFolder,
            getFolders,
            onCaseRemove,
            onBulkRemove,
            init,
            updateCases
        } = useTestCasesIndex();

        onMounted(() =>
        {
            getFolders(true);
        })

        // Handle submit with the correct folder name
        const submitHandler = async (result) => {
            const folder = folders.find(f => f.uid === selectedFolderUid.value);
            if (folder) {
                submissionFolderName.value = folder.name;
            } else {
                submissionFolderName.value = 'selected folder';
            }
            
            // Get route params
            const routeParams = {
                params: {
                    handle: route.params.handle,
                    key: route.params.key
                }
            };
            
            // Combine manually assigned cases with those that have pre-assigned folders
            const allCases = [
              ...result, // Manually assigned cases
              ...casesWithFolderAssignments.value // Cases with pre-assigned folders
            ];
            
            // Call handleSubmit with the route, result and parent folder
            await handleSubmit(routeParams, allCases, selectedFolderUid.value);
            
            // Clear cache after successful CSV import so imported test cases appear immediately
            clearCache();
        };

        return {
            cases,
            folders,
            processedItems,
            writeEntity,
            deleteEntity,
            initialSelectedFolder,
            folderUid,
            onSelectFolder,
            getFolders,
            onCaseRemove,
            onBulkRemove,
            init,
            updateCases,
            computedFolder,
            
            // CSV import data
            casesToImportDisplayed: casesWithoutFolderAssignments, // Only display cases without folder assignments
            casesToImport, // Original full list from the composable
            casesWithFolderAssignments,
            hasCasesWithFolderAssignments,
            totalCases,
            
            //   =======
            isTreeViewCollapsed,
            isDetailViewCollapsed,
            selectedItem,
            breadCrumbs,
            selectedCase,
            selectedCases,
            selectedFolder,
            dialog,
            handleSelected,
            currentAccount,
            selectedFolderUid,
            isCurrentSelectedCasesFirstIndex,
            isCurrentSelectedCasesLastIndex,
            nextCase,
            previousCase,
            confirmEditing,
            toggleMenu,
            changeExpansion,
            closeDetailView,
            handleCases,
            viewPreviousCase,
            viewNextCase,
            selectFolder,
            onCreateCase,
            openDetailView,
            deleteFolder,
            updateSelectedCases,
            getCase,
            onDeleteCase,
            onBulkDeleteCases,
            // ======
            gotoRepository,
            // Add submission progress variables
            submissionInProgress,
            submissionProgress,
            totalSubmissionItems,
            submissionFolderName,
            submitHandler,
            // New upload function and state
            handleUploadPreAssignedCases,
            preAssignedUploadInProgress
        };
    }
}
</script>

<style scoped>
.sticky-on-scroll {
    position: -webkit-sticky;
    position: sticky;
    top: 12px;
    height: calc(100vh - 24px);
}

.cardHeader {
    background-color: #fff;
    /* margin-top: 1rem; */
    padding: 1rem;
    padding-left: 2rem;
    border-radius: 0.5rem;
}

/* Snackbar alert styles */
.alert {
  width: 100%;
}

.gap-4 {
  gap: 8px !important;
}

.w-full {
  width: 100%;
}
.title{
  font-size: 2rem !important;
  font-weight: 600;
  color: #000;
  margin-top: 2.5rem !important;
}
.sub-text{
font-weight: 400;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
margin-top: 12px;

}
.relative{
  position: relative;
}
.footer-buttons{
  display: flex;
  width: 100%;
  justify-content: end;
  align-items: center;
  gap: 12px;
  position: absolute;
  bottom: 0;
  padding: 16px 24px;
}
.alertbox{
  background: #fff;
  margin-top: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  padding: 1rem;
  padding-left: 2rem;
  border-radius: 0.5rem;
}
</style>
