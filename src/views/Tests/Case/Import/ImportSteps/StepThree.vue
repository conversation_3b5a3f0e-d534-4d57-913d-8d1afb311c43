<template>
  <div
    v-if="parseLoading"
    class="d-flex flex-column align-center justify-center w-full h-full"
    style="min-height: calc(100vh - 200px)"
  >
    <div class="d-flex flex-column align-center justify-center text-center">
      <Loader :show-loading-text="false" />
      
      <div class="loadingText">
        {{ processedItems }} of {{ totalItems }}
      </div>
      
      <div
        class="d-flex ga-4 justify-center align-center"
        style="width: 400px;"
      >
        <v-progress-linear
          color="primary"
          :value="processProgress"
          height="8"
          rounded
        />
        <div
          class="text-right ml-3"
          style="font-size: 14px; font-weight: 500;"
        >
          {{ processProgress }}%
        </div>
      </div>
      
      <div class="loadingDescText">
        Please wait while your data is being processed...
      </div>
      
      <v-btn
        depressed
        background-color="#F2F4F7"
        class="font-inter text-capitalize black--text"
        height="40"
        min-width="140px"
        @click="$emit('back')"
      >
        {{ $t('back') }}
      </v-btn>
    </div>
  </div>
  <div
    v-else
    class="d-flex flex-column align-center justify-center w-full h-full"
    style="min-height: calc(100vh - 200px)"
  >
    <div
      cols="12"
      sm="12"
      class="block main-area pa-0"
      :style="{ 'max-width':'1040px' }"
    >
      <section class="d-flex justify-space-between align-center mb-5 py-4 w-full">
        <h2 class="mb-0">
          {{ $t('importPreview') }}
        </h2>
      
        <div class="pagination-controls">
          <v-btn
            icon
            :disabled="currentImportIndex <= 0"
            class="pagination-btn"
            @click="previousImport"
          >
            <v-icon>mdi-chevron-left</v-icon>
          </v-btn>
        
          <span class="pagination-text">{{ currentImportIndex + 1 }} / {{ body.length }}</span>
        
          <v-btn
            icon
            :disabled="currentImportIndex >= body.length - 1"
            class="pagination-btn"
            @click="nextImport"
          >
            <v-icon>mdi-chevron-right</v-icon>
          </v-btn>
        </div>
      </section>



      <div
        v-for="(row, index) in mappedRows.mappings"
        :key="`standard-${index}`"
        class="row-container"
      >
        <span
          class="text-left ma-0 pa-0"
          style="width: 50%;"
        >
          {{ row.name }}
        </span>
  
        <ArrowRightIcon class="icon" />


        <span
          class="text-left ma-0 pa-0"
          style="width: 50%;"
        >
          {{ body[currentImportIndex][row.csvHeader] }}
        </span>
      </div>
    
      <!-- Template field mappings -->
      <template v-if="validTemplateFields && validTemplateFields.length > 0">
        <div
          v-for="(field, index) in validTemplateFields"
          :key="`template-${field.id || index}`"
          class="row-container"
        >
          <span
            class="text-left ma-0 pa-0 font-weight-medium"
            style="width: 50%;"
          >
            {{ field.name || field.id || 'Unknown Field' }}
          </span>
          <ArrowRightIcon class="icon" />

          <span
            class="text-left ma-0 pa-0"
            style="width: 50%;"
          >
            {{ field.csvHeader ? body[currentImportIndex][field.csvHeader] : '-' }}
          </span>
        </div>
      </template>
    </div>
    <div

      class="d-flex justify-end py-4 px-6  w-full mt-auto"
    >
      <v-btn
        depressed
        background-color="#F2F4F7"
        class="font-inter text-capitalize black--text mr-4 "
        height="40"
        min-width="140px"
        @click="$emit('back')" 
      >
        {{ $t('back') }}
      </v-btn>
      <v-btn
        class="text-capitalize btn-theme px-6 py-2"
        color="primary"
        depressed
        height="40"
        min-width="140px"
        :loading="isLoading"
        :disabled="isLoading"
        @click="$emit('submit')" 
      >
        {{ $t('Proceed') }}
      </v-btn>
    </div>
    
    <!-- Submission Progress Snackbar -->
    <v-snackbar
      v-model="submissionInProgress"
      :timeout="-1"
      class="alert w-full"
      bottom
      right
    >
      <div class="d-flex align-start gap-4 w-full">
        <SwalAlertIcon />
        <div class="d-flex flex-column w-full">
          <span class="mb-2">{{ $t('uploadingTestCasesToAssignedFolders') }}</span>
          <div class="d-flex justify-center align-center w-full">
            <v-progress-linear
              :value="submissionProgress"
              height="5"
              color="primary"
              class="mb-0 w-full"
              rounded
            />
            <span class="text-right ml-2 mb-0">{{ submissionProgress }}%</span>
          </div>
        </div>
      </div>
    </v-snackbar>
  </div>
</template>

<script>
import ArrowRightIcon from '@/assets/svg/arrow-right16px.svg';
import Loader from '@/components/base/Loader.vue';
import { ref, computed } from 'vue';
import { useCsvImport } from '@/composables/modules/cases/csvImport';
import SwalAlertIcon from '@/assets/svg/swal_alert.svg';

export default {
  name: 'ImportStepThree',
  components: {
    ArrowRightIcon, Loader, SwalAlertIcon
  },
  props: {
    mappedRows: {
      type: Object,
      required: true
    },
    body: {
      type: Array,
      required: true
    },

  },
  emits: ['submit', 'back'],
  setup(props) {
    const {
      parseLoading,
      processProgress,
      processedItems,
      totalItems,
      submissionInProgress,
      submissionProgress
    } = useCsvImport();
    
    // Add state for the current import index
    const currentImportIndex = ref(0);
    
    // Computed property to show loading during both processing and upload
    const isLoading = computed(() => {
      return parseLoading.value || submissionInProgress.value;
    });
    
    // Navigation methods
    const nextImport = () => {
      if (currentImportIndex.value < props.body.length - 1) {
        currentImportIndex.value++;
      }
    };
    
    const previousImport = () => {
      if (currentImportIndex.value > 0) {
        currentImportIndex.value--;
      }
    };
    
    // Computed property to filter out undefined template fields
    const validTemplateFields = computed(() => {
      if (!props.mappedRows.templateFields) return [];
      
      return Object.entries(props.mappedRows.templateFields)
        .filter(([, field]) => field && typeof field === 'object')
        .map(([fieldId, field]) => ({
          id: fieldId,
          ...field
        }));
    });
    
    return {
      currentImportIndex,
      nextImport,
      previousImport, 
      parseLoading,
      processProgress,
      processedItems,
      totalItems,
      validTemplateFields,
      submissionInProgress,
      isLoading,
      submissionProgress
    };
  }
}
</script>

<style lang="scss" scoped>
.input-field {
  background-color: #F9F9FB;
  padding: 7px 14px;
  border-radius: 5px;
  margin-top: 4px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 4px 8px;
}

.pagination-text {
  font-size: 16px;
  font-weight: 500;
  margin: 0 12px;
  min-width: 48px;
  text-align: center;
}

.pagination-btn {
  background-color: white !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  &:disabled {
    background-color: #f5f5f5 !important;
    color: #c0c0c0 !important;
  }
}

.progress-container {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e0e4e8;
}
.row-container{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  border-radius: 6px;
  background-color: #F9FAFB;
  margin-bottom: 8px;
  font-size: 14px;
  min-width: 1040px;

  .icon{
    margin: 0 16px;
  }
}
.loadingText{
  font-size: 20px;
  font-weight: 700;
  margin: 24px 0;
  min-width: 48px;
}
.loadingDescText{
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 0%;
  text-align: center;
  margin: 24px 0;
}
</style>
