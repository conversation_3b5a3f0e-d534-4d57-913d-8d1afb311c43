<template>
  <div class="  pl-2 pr-4 pt-3 ">
    <section
      class=" app-height-global rounded-lg"
      :class="{ 'bg-white': step < 4 }"
    >
      <div
        v-if="step < 4"
        class="top_section"
      >
        <v-progress-linear
          color="#0C2FF3"
          background-color="#F2F4F7"
          :value="progressValue"
        />
        <div
          class="back-btn mt-4 pl-2"
          @click="goBack"
        >
          <v-icon>mdi-chevron-left</v-icon> {{ $t('backToTestCases') }}
        </div>
      </div>
      <div class="body_section">
        <StepOne
          v-if="step === 1"
          @next="handleNextOne"
        />
        <StepTwo
          v-if="step === 2"
          :header="csvFileData.header"
        />
        <StepThree
          v-if="step === 3"
          :mapped-rows="mappedRows"
          :body="csvFileData.body"
          :loading="createBtnLoading"
          @submit="goToStepFour"
          @back="goBack"
        />
        <StepFour
          v-if="step === 4"
          :mapped-rows="mappedRows"
          :body="csvFileData.body"
          @submit="submitHandler"
          @back="goBack"
        />
      </div>
    </section>
  </div>
</template>

<script>
import { computed, onUnmounted } from 'vue';
import { useRoute } from 'vue-router/composables';
import StepOne from '@/views/Tests/Case/Import/ImportSteps/StepOne.vue';
import StepTwo from '@/views/Tests/Case/Import/ImportSteps/StepTwo.vue';
import StepThree from '@/views/Tests/Case/Import/ImportSteps/StepThree.vue';
import StepFour from '@/views/Tests/Case/Import/ImportSteps/StepFour.vue';
import { useCsvImport } from '@/composables/modules/cases/csvImport';
import { useTestCasesIndex } from '@/composables/modules/cases/index';

export default {
  components: {
    StepOne,
    StepTwo,
    StepThree,
    StepFour
  },
  setup() {
    // Get route params
    const route = useRoute();
    const { handle, key } = route.params;

    // Get cache clearing function
    const { clearCache } = useTestCasesIndex();

    // Use the composable
    const {
      step,
      csvFileData,
      selectedFolder,
      mappedRows,
      createBtnLoading,
      goBack,
      handleNextOne,
      handleSubmit,
      goToStepFour,
      cleanupWorker,
    } = useCsvImport();

    // Calculate progress value based on current step
    const progressValue = computed(() => {
      switch (step.value) {
        case 1:
          return 25;
        case 2:
          return 50;
        case 3:
          return 75;
        case 4:
          return 100;
        default:
          return 0;
      }
    });

    // Handler to go to step four from step three
  

    // Submit handler to pass route params to handleSubmit and clear cache after
    const submitHandler = async (result) => {
      await handleSubmit(handle, key, result);
      // Clear cache after successful CSV import so imported test cases appear immediately
      clearCache();
    };
    
    // Clean up the worker when component is unmounted
    onUnmounted(() => {
      cleanupWorker();
    });

    // Return values used in the template
    return {
      step,
      csvFileData,
      selectedFolder,
      mappedRows,
      createBtnLoading,
      goBack,
      handleNextOne,
      submitHandler,
      goToStepFour,
      progressValue
    };
  }
}
</script>

<style scoped>
.back-btn{
    text-decoration: none;
    color: #0c2ff3 !important;
    font-weight: 600;
    cursor: pointer;
}
h2, h5{
  text-align: left;
}
h2{
  font-size: 24px !important;
  font-weight: 700;
  color: #18181A;
}
h5{
  font-size: 14px !important;
  color: #0C111D;
  font-weight: 500;
}
button {
  font-size: 14px;
}
.v-align-start {
  align-items: flex-start !important;
}
.font-inter{
  font-family: Inter;
}
.card{
  border-radius: 8px;
}
.block{
  display: block;
}
.main-area{
  max-width: 1040px;
  margin-left: auto;
  margin-right: auto;
}
.round-8{
  border-radius: 8px;
}
.relative{
  position: relative;
}
.absolute{
  position: absolute;
}
.bottom-0{
  bottom: 0;
}
.justify-end{
  justify-content: flex-end;
}
.text-white{
  color: white !important;
}
.round-6{
  border-radius: 6px;
}
.flex{
  display: flex;
}
.file-input{
  border:1px solid #D0D5DD;
  border-style:dashed;
}
.bg-white{
  background-color: #fff !important;
}
.top_section{
  width: 100%;
  padding-top: 1.5rem;
  background-color: #fff;
}
.body_section{
  margin-top: 0rem;
}

</style>
