<template>
  <div>
    <v-dialog
      v-model="isOpenStatus"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
      persistent
      @click:outside="$emit('closeDialog')"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6 mb-4">
            <h2 class="black--text">
              {{ $t('testruns.create_testrun.title') }}
            </h2>
            <v-btn
              icon
              @click="$emit('closeDialog')"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <div>
            <v-form
              ref="runForm"
              role="createRunForm"
              class="text-left pt-10"
            >
              <v-row>
                <v-col>
                  <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                    {{ $t('testruns.create_testrun.testrun_name') }}<strong class="red--text text--lighten-1">*</strong>
                  </v-label>
                  <v-text-field
                    v-model="selectedRun.name"
                    type="text"
                    dense
                    :placeholder="$t('testruns.create_testrun.testRunName')"
                    height="38px"
                    :rules="runNameRule"
                    class="rounded-lg field-theme"
                    background-color="#F9F9FB"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col>
                  <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                    {{ $t('testruns.create_testrun.description') }}
                  </v-label>
                  <v-text-field
                    v-model="selectedRun.description"
                    type="text"
                    class="rounded-lg field-theme"
                    background-color="#F9F9FB"
                    dense
                    height="38px"
                    :placeholder="$t('description')"
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col class="pt-0">
                  <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                    {{ $t('testruns.create_testrun.milestone') }}
                  </v-label>
                  <v-select
                    v-model="selectedRun.milestoneUids"
                    type="text"
                    dense
                    background-color="#F9F9FB"
                    multiple
                    :placeholder="$t('milestone.choose')"
                    :items="activeMilestones"
                    class="rounded-lg field-theme combo-box custom-prepend mh-38px"
                    append-icon="mdi-chevron-down"
                    item-text="name"
                    item-value="uid"
                    :menu-props="{ offsetY: true }"
                  >
                    <template #selection="{ item }">
                      <v-tooltip
                        bottom
                        max-width="485px"
                        :disabled="item.name.length < 61"
                        content-class="tooltip-theme"
                      >
                        <template #activator="{ on, attrs }">
                          <div
                            class="d-flex align-center custom-chip-theme mr-1 mb-1"
                            v-bind="attrs"
                            v-on="on"
                          >
                            <div class="text-theme-label label text-truncate mr-1">
                              {{ item.name }}
                            </div>
                            <v-icon
                              size="16px"
                              @click="onRemoveSelectedMilestone(item.uid)"
                            >
                              mdi-close
                            </v-icon>
                          </div>
                        </template>
                        <span>{{ item.name }}</span>
                      </v-tooltip>
                    </template>
                    <template #item="{ item, on, attrs }">
                      <v-list-item
                        :ripple="false"
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item-action>
                          <v-checkbox
                            hide-details
                            :input-value="milestoneSelection(item.uid)"
                            class="field-theme mt-0 pt-0"
                            :ripple="false"
                            off-icon="icon-checkbox-off"
                            on-icon="icon-checkbox-on"
                          >
                            <template #label>
                              <v-tooltip
                                bottom
                                max-width="485px"
                                :disabled="item.name.length < 61"
                                content-class="tooltip-theme"
                              >
                                <template #activator="{ on, attrs }">
                                  <span
                                    class="fs-14px text-theme-label text-truncate"
                                    v-bind="attrs"
                                    v-on="on"
                                  >
                                    {{ item.name }}
                                  </span>
                                </template>
                                <span>{{ item.name }}</span>
                              </v-tooltip>
                            </template>
                          </v-checkbox>
                        </v-list-item-action>
                      </v-list-item>
                    </template>
                  </v-select>
                </v-col>
              </v-row>
              <v-row>
                <v-col class="pt-0">
                  <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                    {{ $t('testruns.create_testrun.priority') }}
                  </v-label>
                  <v-select
                    v-model="selectedRun.priority"
                    type="text"
                    dense
                    background-color="#F9F9FB"
                    height="38px"
                    :placeholder="$t('choosePriority')"
                    :items="priorities"
                    class="rounded-lg field-theme combo-box custom-prepend"
                    append-icon="mdi-chevron-down"
                    item-text="name"
                    item-value="name"
                    return-object
                  />
                </v-col>
              </v-row>
              <v-row>
                <v-col class="pt-0">
                  <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                    {{ $t('testruns.create_testrun.tags') }}
                  </v-label>
                  <v-select
                    v-model="selectedRun.tags"
                    type="text"
                    dense
                    background-color="#F9F9FB"
                    :placeholder="$t('chooseTags')"
                    :items="tags"
                    item-text="name"
                    item-value="uid"
                    class="rounded-lg field-theme custom-prepend mh-38px"
                    append-icon="mdi-chevron-down"
                    :menu-props="{ offsetY: true }"
                    multiple
                    return-object
                  >
                    <template #selection="{ item }">
                      <div class="d-flex align-center custom-chip-theme mr-1 mb-1">
                        <div class="text-theme-label label text-truncate mr-1">
                          {{ item.name }}
                        </div>
                        <v-icon
                          size="16px"
                          @click="onRemoveSelectedTags(item.uid)"
                        >
                          mdi-close
                        </v-icon>
                      </div>
                    </template>

                    <template #item="{ item, on, attrs }">
                      <v-list-item
                        :ripple="false"
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item-action>
                          <v-checkbox
                            hide-details
                            :input-value="tagsSelection(item.uid)"
                            class="field-theme mt-0 pt-0"
                            :ripple="false"
                            off-icon="icon-checkbox-off"
                            on-icon="icon-checkbox-on"
                          >
                            <template #label>
                              <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                            </template>
                          </v-checkbox>
                        </v-list-item-action>
                      </v-list-item>
                    </template>
                  </v-select>
                </v-col>
              </v-row>

              <v-row>
                <v-col class="pt-0">
                  <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                    {{ $t('milestone.create_milestone.dueDate') }}
                  </v-label>
                  <v-menu
                    v-model="menuDueDate"
                    max-width="90"
                    offset-x
                    top
                  >
                    <template #activator="{ on }">
                      <div
                        class="calendar-textbox-container"
                        v-on="on"
                      >
                        <v-text-field
                          dense
                          single-line
                          filled
                          color="blue"
                          class="mr-0 custom_input rounded-lg calendar-textbox"
                          :value="dueDate"
                          placeholder="MM/DD/YY"
                          readonly
                        />
                        <calendarBlueIcon class="calendar-icon" />
                      </div>
                    </template>
                    <v-date-picker
                      v-model="dueDate"
                      @change="onDateChange"
                    />
                  </v-menu>
                </v-col>
              </v-row>
            </v-form>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          full-width
          depressed
          height="40"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="$emit('closeDialog')"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme text-capitalize"
          height="40"
          color="primary"
          depressed
          full-width
          elevation="0"
          :disabled="false"
          @click="createTestRuns"
        >
          {{ $t('add') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import { useCreateTestRun } from '@/composables/modules/testRun/create';
import calendarBlueIcon from '@/assets/svg/calendar-blue.svg';

export default {
  name: 'ExportDialog',
  components: {
    calendarBlueIcon,
  },
  props: {
    isOpen: Boolean,
    selectedRows: Array,
  },
  emits: ['closeDialog'],
  setup() {
    // Additional method for handling back from cases view, if needed

    const {
      runNameRule,
      selectedRun,
      dueDate,
      menuOpen,
      showConfirmBackDialog,
      search,
      selectedCases,
      milestones,
      menuDueDate,
      tags,
      cases,
      showCases,
      onCreateLoading,
      statuses,
      priorities,
      onDateChange,
      activeMilestones,
      isTestPlanCreate,
      createTestRuns,
      handleAddCases,
      handleBackClick,
      onAddCases,
      handleCloseClick,
      handleDuplicateClick,
      handleConfirmClick,
      milestoneSelection,
      onRemoveSelectedMilestone,
      onRemoveSelectedTags,
      tagsSelection,
      updateCases,
      getCases,
      onBackShowCases,
      runForm,
      fileInput,
      isCreateModalOpen,
    } = useCreateTestRun();

    return {
      runNameRule,
      selectedRun,
      dueDate,
      menuOpen,
      showConfirmBackDialog,
      search,
      selectedCases,
      milestones,
      menuDueDate,
      tags,
      cases,
      showCases,
      onCreateLoading,
      onDateChange,
      statuses,
      priorities,
      activeMilestones,
      isTestPlanCreate,
      createTestRuns,
      handleAddCases,
      handleBackClick,
      onAddCases,
      handleCloseClick,
      handleDuplicateClick,
      handleConfirmClick,
      milestoneSelection,
      onRemoveSelectedMilestone,
      onRemoveSelectedTags,
      tagsSelection,
      updateCases,
      getCases,
      onBackShowCases,
      runForm,
      fileInput,
      isCreateModalOpen,
    };
  },

  computed: {
    offset() {
      return (this.pagination.page - 1) * this.pagination.itemsPerPage;
    },
    isOpenStatus() {
      return this.isOpen;
    },
  },
  watch: {
    selectedRows: {
      handler(newSelectedRows) {
        this.selectedCases = newSelectedRows;
      },
      deep: true,
      immediate: true,
    },
    isCreateModalOpen(newValue) {
      if (!newValue) {
        this.$emit('closeDialog')
      }
    },
  },
  mounted() {
    this.isCreateModalOpen = true;
  },
};
</script>

<style scoped>
.test-cases-filter-drawer .v-sheet {
  display: flex;
}

.test-cases-filter-drawer .v-card__text {
  display: flex;
  flex-direction: column;
}

.test-cases-filter-drawer .actions-container {
  margin-top: auto !important;
}

.select-title {
  font-family: Inter;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-align: left;
  z-index: 1;
  top: 0px;
  background: white;
  padding-top: 15px;
}
.full-width-list {
  width: 100%;
}
.full-width-list .v-list-item {
  width: 100%;
}
.calendar-icon {
  position: absolute;
  right: 12px;
  top: 9px;
}
.calendar-textbox-container {
  position: relative;
}
</style>
