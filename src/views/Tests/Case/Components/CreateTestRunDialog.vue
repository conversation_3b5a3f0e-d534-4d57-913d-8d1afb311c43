<template>
  <div>
    <v-dialog
      v-model="isOpenStatus"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
      persistent
      @click:outside="clickOutside"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6 mb-4">
            <h2 class="black--text">
              {{ $t('testruns.test_case.export_test_cases') }} 
            </h2>
            <v-btn
              icon
              @click="$emit('closeDialog')"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <div>
            <div class="select-title mt-4">
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t("exportTo") }}
              </v-label>
            </div>
            <v-radio-group v-model="selectedItem">
              <v-radio
                v-for="(item, index) in exportTypes"
                :key="index"
                :label="item.title"
                :value="item.value"
              />
            </v-radio-group>
            <div class="select-title mt-4">
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('fields') }}
              </v-label>
            </div>
            <v-select
              v-model="selectedFields"
              placeholder="Choose fields"
              :items="fields"
              item-text="title"
              item-value="value"
              multiple
              append-icon="mdi-chevron-down"
              class="mt-0 pt-1 rounded-lg field-theme custom-prepend mh-38px"
              background-color="#F9F9FB"
            >
              <template #prepend-item>
                <v-list-item
                  :ripple="false"
                  @mousedown.prevent
                  @click="toggle"
                >
                  <v-list-item-action>
                    <v-checkbox
                      hide-details
                      class="field-theme"
                      :ripple="false"
                      :value="selectAllFields"
                      :indeterminate="selectSomeFields"
                      indeterminate-icon="icon-indeterminate"
                      off-icon="icon-checkbox-off"
                      on-icon="icon-checkbox-on"
                    >
                      <template #label>
                        <span class="fs-14px text-theme-label">{{ $t("selectAll") }}</span>
                      </template>
                    </v-checkbox>
                  </v-list-item-action>
                </v-list-item>
              </template>
              <template #item="{ item }">
                <v-list-item
                  :ripple="false"
                  @mousedown.prevent
                  @click="item.isSelected = !item.isSelected"
                >
                  <v-list-item-action>
                    <v-checkbox
                      v-model="item.isSelected"
                      hide-details
                      class="field-theme mt-0 pt-0"
                      :ripple="false"
                      off-icon="icon-checkbox-off"
                      on-icon="icon-checkbox-on"
                      :value="item.isSelected"
                    >
                      <template #label>
                        <span class="fs-14px text-theme-label">{{ `${item.title}` }}</span>
                      </template>
                    </v-checkbox>
                  </v-list-item-action>
                </v-list-item>
              </template>
            </v-select>
          </div>
          <div class="mt-4">
            <v-checkbox
              v-model="includeCustomFields"
              hide-details
              class="field-theme"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
            >
              <template #label>
                <span class="fs-14px text-theme-label">{{ $t("includeCustomFields") }}</span>
              </template>
            </v-checkbox>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          full-width
          depressed
          height="40"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="$emit('closeDialog')"
        >
          {{ $t("cancel") }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme text-capitalize"
          height="40"
          color="primary"
          depressed
          full-width
          elevation="0"
          :disabled="selectedFields.length === 0"
          @click="exportData"
        >
          {{ $t("export") }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import { useTestCasesExport } from '@/composables/modules/cases/export';

export default {
  name: "ExportDialog",
  props: {
    isOpen: Boolean,
    selectedRows: Array,
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // Use the composable for export logic
    const {
      selectedFields,
      selectedItem,
      exportTypes,
      isOpenStatus,
      fields,
      selectAllFields,
      selectSomeFields,
      clickOutside,
      toggle,
      exportData,
      includeCustomFields,
    } = useTestCasesExport(props, context);

    // Return everything needed in the template
    return {
      selectedFields,
      selectedItem,
      exportTypes,
      isOpenStatus,
      fields,
      selectAllFields,
      selectSomeFields,
      clickOutside,
      toggle,
      exportData,
      includeCustomFields,
    };
  }
};
</script>

<style scoped>
.test-cases-filter-drawer .v-sheet {
  display: flex;
}

.test-cases-filter-drawer .v-card__text {
  display: flex;
  flex-direction: column;
}

.test-cases-filter-drawer .actions-container {
  margin-top: auto !important;
}

.select-title {
  font-family: Inter;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-align: left;
}
</style>

