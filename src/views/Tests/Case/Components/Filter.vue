<template>
  <div>
    <v-sheet
      v-if="!skeletonLoaderState"
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40px"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-3 py-2 d-flex flex-row">{{ $t('filters') }} <v-icon
        size="16px"
        class="ml-2"
      >mdi-filter-variant</v-icon></span>
    </v-sheet>
    <v-skeleton-loader
      v-else
      class="rounded-lg"
      height="40"
      width="95"
      type="button"
    />
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('filters') }}
            </h2>
            <v-btn
              icon
              @click="showDialog = false"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <v-expansion-panels
            v-model="rolesPanel"
            flat
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                {{ $t('priority') }}
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div
                  v-for="(priority, index) in priorities"
                  :key="index"
                >
                  <v-checkbox
                    v-model="priority.selected"
                    :value="priority.selected"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ priority.name }}</span>
                    </template>
                  </v-checkbox>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>

          <v-expansion-panels
            v-model="tagsPanel"
            flat
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                {{ $t('tags') }}
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-text-field
                  v-model="searchTerm"
                  class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
                  :placeholder="$t('search')"
                  height="40"
                  background-color="#F9F9FB"
                  :hide-details="true"
                >
                  <template #prepend-inner>
                    <SearchIcon />
                  </template>
                </v-text-field>
                <div
                  v-for="(tag, index) in searchedTags"
                  :key="index"
                >
                  <v-checkbox
                    :value="isSelected(tag)"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                    @change="toggleTagSelection(tag)"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ `#${tag.name}` }}</span>
                    </template>
                  </v-checkbox>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          full-width
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="clearFilters"
        >
          {{ $t('clearAll') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          full-width
          elevation="0"
          @click="apply"
        >
          {{ $t('apply') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import SearchIcon from '@/assets/svg/search-icon.svg';
import makeTagService from '@/services/api/tag';
import { showErrorToast } from '@/utils/toast';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import handleLoading from '@/mixins/loader.js'

export default {
  name: 'TestCasesFilter',
  components: {
    SearchIcon
  },
  mixins: [colorPreferencesMixin, handleLoading],
  props: {
    currentFilters: {
      type: Object,
      default: () => ({
        priorities: [],
        tags: []
      })
    }
  },
  data() {
    return {
      rolesPanel: 0,
      projectsPanel: 0,
      tagsPanel: 0,
      rolesMenu: false,
      projectsMenu: false,
      tagsMenu: false,
      showDialog: false,
      priorities: [],
      searchTerm: '',
      tags: [],
      selectedTags: []
    };
  },
  computed: { 
    searchedTags(){
      return this.searchTerm.length ? this.tags.filter(tag => tag.name.toLowerCase().includes(this.searchTerm.toLowerCase())) : this.tags
    }
  },

  watch: {
    currentFilters: {
      handler(newFilters) {
        this.syncWithFilters(newFilters);
      },
      deep: true,
      immediate: true
    }
  },

  async mounted() {
    await this.fetchTags();
    this.priorities = this.getPriorities('testCase');
    
    this.syncWithFilters(this.currentFilters);
  },

  methods: {
    syncWithFilters(filters) {
      if (this.priorities.length > 0) {
        this.priorities = this.priorities.map(priority => ({
          ...priority,
          selected: false
        }));
        
        if (filters.priorities && filters.priorities.length > 0) {
          this.priorities = this.priorities.map(priority => ({
            ...priority,
            selected: filters.priorities.includes(priority.id)
          }));
        }
      }
      
      this.selectedTags = [];
      
      if (filters.tags && filters.tags.length > 0 && this.tags.length > 0) {
        this.selectedTags = this.tags.filter(tag => 
          filters.tags.includes(tag.name)
        );
      }
    },
    
    apply() {
      const selectedPriorities = this.priorities.filter(priority => priority.selected);
      const filters = {
        priorities: selectedPriorities.map(item => item.id),
        tags: this.selectedTags.map(item => item.name),
        tagUids: this.selectedTags.map(item => item.uid), // Add tag UIDs for server-side filtering
        tagObjects: this.selectedTags // Store full tag objects for easier manipulation
      };

      this.$emit('filters', filters);
      this.showDialog = false;
    },

    clearFilters() {
      this.$nextTick(() => {
        this.priorities = this.priorities.map(priority => ({ ...priority, selected: false }));
        this.selectedTags = [];
        this.searchTerm = '';
        
        this.$emit('filters', { priorities: [], tags: [], tagUids: [], tagObjects: [] });
      });
    },

    isSelected(tag) {
      if(this.selectedTags.length === 0) return false;
      return this.selectedTags.some(selectedTag => selectedTag.uid === tag.uid);
    },

    toggleTagSelection(tag) {
      if (this.isSelected(tag)) {
        this.selectedTags = this.selectedTags.filter(
          selectedTag => selectedTag.uid !== tag.uid
        );
      } else {
        this.selectedTags.push(tag);
      }
    },

    async fetchTags() {
      try {
        const tagService = makeTagService(this.$api);
        const response = await tagService.getTags(this.$route.params.handle, 'cases');
        this.tags = response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'tags' }, error?.response?.data);
      }
    },
  }
};
</script>

