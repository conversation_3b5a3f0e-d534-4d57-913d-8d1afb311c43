<template>
  <v-container class="d-row flex-column justify-start pa-0">
    <v-container class="bg-f9faf8 rounded-lg">
      <h3 class="text-theme-label fw-semibold fs-14px pl-3">
        {{ stepItem.title }}
      </h3>
      <v-col class="d-flex flex-row justify-space-between pt-0">
        <v-col class="px-0">
          <h4 class="text-theme-secondary fs-14px fw-semibol mb-3">
            {{ $t('sharedStepPage.stepDescription') }}
          </h4>
          <div 
            class="text-theme-label font-weight-regular"
            v-html="formatContent(stepItem.description)"
          />
        </v-col>
        <v-col
          v-if="expectedResultByStep"
          class="px-0"
        >
          <h4 class="text-theme-secondary fs-14px fw-semibold mb-3">
            {{ $t('sharedStepPage.expectedResult') }}
          </h4>
          <div 
            class="text-theme-label font-weight-regular"
            v-html="formatContent(stepItem.expectedResult)"
          />
        </v-col>
      </v-col>
      <v-col class="d-flex flex-row">
        <div class="w-50">
          <v-select
            v-if="showStatuses"
            id="execution_status"
            :value="Number(stepItem.status)"
            :menu-props="{'offset-y': true}"
            class="rounded-lg field-theme custom-prepend"
            :class="{'disabled-action': isProjectArchived }"
            :items="statuses"
            item-text="name"
            :placeholder="$t('chooseStatus')"
            item-value="id"
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            flat
            :hide-details="true"
            @input="onInputStatus"
          >
            <template #selection="{ item }">
              <span
                class="fs-14px fw-semibold"
                :style="{ color: item.color }"
              >
                {{ item.name }}
              </span>
            </template>
            <template #item="{ item, on, attrs }">
              <v-list-item 
                class="mh-36px cursor-pointer"
                v-bind="attrs" 
                @click="on.click" 
              >
                <v-list-item-content>
                  <v-list-item-title
                    class="fs-14px fw-semibold"
                    :style="{ color: item.color }"
                  >
                    {{ item.name }}
                  </v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </template>
          </v-select>
        </div>
      </v-col>
    </v-container>
    <v-container class="d-flex flex-column justify-start align-end px-0">
      <v-container
        v-for="(item,index) in stepItem.children"
        :key="index"
        class="d-flex flex-row justify-space-between px-0 pt-0"
      >
        <v-container class="bg-f9faf8 rounded-lg w-full ml-12">
          <h3 class="text-theme-label fw-semibold fs-14px pl-3">
            {{ item.title }}
          </h3>
          <div class="flex justify-between pt-0 pl-3">
            <v-col class="px-0">
              <h4 class="text-theme-secondary fs-14px fw-semibold">
                {{ $t('sharedStepPage.stepDescription') }}
              </h4>
              <div 
                class="text-theme-label font-weight-regular"
                v-html="formatContent(item.description)"
              />
            </v-col>
            <v-col
              v-if="expectedResultByStep"
              class="px-0"
            >
              <h4 class="text-theme-secondary fs-14px fw-semibold">
                {{ $t('sharedStepPage.expectedResult') }}
              </h4>
              <div 
                class="text-theme-label font-weight-regular"
                v-html="formatContent(item.expectedResult)"
              />
            </v-col>
          </div>
          <div class="w-65">
            <v-select
              v-if="showStatuses"
              id="execution_status"
              :value="Number(item.status)"
              :menu-props="{'offset-y': true}"
              class="rounded-lg field-theme custom-prepend"
              :class="{'disabled-action': isProjectArchived }"
              :items="statuses"
              item-text="name"
              :placeholder="$t('chooseStatus')"
              item-value="id"
              background-color="#F9F9FB"
              append-icon="mdi-chevron-down"
              flat
              :hide-details="true"
              @input="onInputChildStatus({
                item,
                statusUid: $event
              })"
            >
              <template #selection="{ item }">
                <span
                  class="fs-14px fw-semibold"
                  :style="{ color: item.color }"
                >
                  {{ item.name }}
                </span>
              </template>
              <template #item="{ item, on, attrs }">
                <v-list-item 
                  class="mh-36px cursor-pointer"
                  v-bind="attrs" 
                  @click="on.click" 
                >
                  <v-list-item-content>
                    <v-list-item-title
                      class="fs-14px fw-semibold"
                      :style="{ color: item.color }"
                    >
                      {{ item.name }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </template>
            </v-select>
          </div>
        </v-container>
      </v-container>
    </v-container>
  </v-container>
</template>
<script>
import projectStatus from '@/mixins/projectStatus';
import { formatContent } from '@/utils/markdown';

export default {
  mixins: [projectStatus],
  props: {
    stepItem: Object,
    caseItem: Object,
    statuses: {
      type: Array,
      default: () => []
    },
    showStatuses: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      status: null,
    };
  },
  computed: {
    expectedResultByStep()
    {
      return this.caseItem?.customFields?.expectedResultByStep || this.stepItem?.expectedResult ;
    }
  },
  methods: {
    formatContent,
    onInputStatus(statusUid) {
      this.$emit('updateStatus', {
        statusUid: statusUid,
        stepItemId: this.stepItem?.uid || this.stepItem?.id,
      });
    },
    onInputChildStatus(data) {
      this.$emit('updateStatus', {
        statusUid: data.statusUid,
        stepItemId: data.item?.uid,
        parentStepUid: data.item?.parentStepUid,
      });
    },
  },

}
</script>
<style scoped>
.subtitle{
  font-family: Inter;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
}

.bg-f9faf8 {
  background-color: #F9FAFB;
}

.width-48{
  width: 48px;
  padding: 0px;
  margin: 0px;
}

/* Markdown content styling */
::v-deep .text-theme-label div {
  line-height: 1.5;
}

::v-deep .text-theme-label p {
  margin: 0 0 8px 0;
}

::v-deep .text-theme-label p:last-child {
  margin-bottom: 0;
}

::v-deep .text-theme-label strong {
  font-weight: 600;
}

::v-deep .text-theme-label em {
  font-style: italic;
}

::v-deep .text-theme-label code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

::v-deep .text-theme-label pre {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 8px 0;
}

::v-deep .text-theme-label ul,
::v-deep .text-theme-label ol {
  margin: 8px 0;
  padding-left: 20px;
}

::v-deep .text-theme-label li {
  margin: 4px 0;
}

::v-deep .text-theme-label blockquote {
  border-left: 4px solid #ddd;
  margin: 8px 0;
  padding-left: 12px;
  color: #666;
}

::v-deep .text-theme-label a {
  color: #1976d2;
  text-decoration: none;
}

::v-deep .text-theme-label a:hover {
  text-decoration: underline;
}
</style>
