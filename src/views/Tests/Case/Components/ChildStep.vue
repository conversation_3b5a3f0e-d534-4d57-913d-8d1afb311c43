<template>
  <div class="ml-8">
    <div class="flex justify-between">
      <div class="flex align-center">
        <template>
          <icon-dots />
        </template>
        <v-text-field
          v-model="childStep.title"
          :placeholder="$t('childStepTitlePlaceholder', { index: stepIndex + 1 })"
          class="ml-1 title-input-field"
          dense
          hide-details
          background-color="transparent"
          @input="$emit('title-change-child', childStep.title, stepIndex)"
        />
      </div>
      <v-menu
        offset-y
        bottom
        class="rounded-lg"
      >
        <template #activator="{ on, attrs }">
          <v-btn
            rounded
            depressed
            class="property"
            v-bind="attrs"
            :plain="true"
            icon
            v-on="on"
          >
            <v-icon size="24px">
              mdi-dots-vertical
            </v-icon>
          </v-btn>
        </template>
        <v-card
          rounded="8"
          elevation="0"
        >
          <v-list>
            <v-list-item
              v-if="canDuplicate"
              :key="1"
              @click="$emit('duplicate-child')"
            >
              <div class="d-flex align-center">
                <duplicateIcon />
              </div>
              <v-list-item-title class="pl-3">
                {{ $t('duplicate') }}
              </v-list-item-title>
            </v-list-item>
            <v-list-item
              :key="2"
              @click="$emit('add-child', stepIndex)"
            >
              <div class="d-flex align-center">
                <addChildStepIcon />
              </div>
              <v-list-item-title class="pl-3">
                {{ $t('addChildStep') }}
              </v-list-item-title>
            </v-list-item>
            <v-list-item
              :key="3"
              @click="$emit('delete-child', stepIndex)"
            >
              <div class="d-flex align-center">
                <removeIcon />
              </div>
              <v-list-item-title class="color-red pl-3">
                {{ $t('remove') }}
              </v-list-item-title>
            </v-list-item>
          </v-list>
        </v-card>
      </v-menu>
    </div>
    <div>
      <div class="text-left">
        <v-label class="fs-14px text-theme-label font-weight-medium">
          {{ $t('stepDescription') }} <strong class="red--text text--lighten-1">*</strong>
        </v-label>
      </div>

      <TextareaField
        v-model="childStep.description"
        auto-grow
        placeholder="Step description"
        background-color="#F9F9FB"
        class="field-theme rounded-lg"
        :rules="descriptionValidation"
        :enable-markdown="true"
        :enable-test-rail="isTestRailImport"
        @input="$emit('description-change-child', childStep.description, stepIndex)"
      />

      <div
        v-if="expectedResultByStep"
        class="text-left"
      >
        <v-label class="fs-14px text-theme-label font-weight-medium">
          {{ $t('expectedResult') }} <strong class="red--text text--lighten-1">*</strong>
        </v-label>
      </div>
      <TextareaField
        v-if="expectedResultByStep"
        v-model="childStep.expectedResult"
        auto-grow
        placeholder="Expected result"
        background-color="#F9F9FB"
        class="field-theme rounded-lg"
        :rules="resultValidation"
        :enable-markdown="true"
        :enable-test-rail="isTestRailImport"
        @input="$emit('result-change-child', childStep.expectedResult, stepIndex)"
      />
    </div>
  </div>
</template>
<script>
import iconDots from '@/assets/svg/dots-20x20-gray.svg';
import removeIcon from '@/assets/svg/remove.svg';
import duplicateIcon from '@/assets/svg/duplicate.svg';
import addChildStepIcon from '@/assets/svg/add-child-step.svg';
import { requiredFieldValidationRules } from "@/utils/validation";
import TextareaField from '@/components/base/TextareaField.vue';

export default {
  name: 'ChildStep',
  components: {
    iconDots,
    removeIcon,
    duplicateIcon,
    addChildStepIcon,
    TextareaField
  },
  props: {
    childStep: Object,
    stepIndex: Number,
    canDuplicate: {
      type: Boolean,
      default: true,
    },
    expectedResultByStep: Boolean,
    isTestRailImport: Boolean
  },
  data() {
    return {
      name: "",
      description: "",
      this: "",
      expectedResult: "",
      descriptionValidation: requiredFieldValidationRules(this),
      resultValidation: requiredFieldValidationRules(this),
    }
  },
  created() {
    this.description = this.childStep.description;
    this.expectedResult = this.childStep.expectedResult;
    if (typeof this.childStep.title === 'undefined' || this.childStep.title === '') {
       this.$set(this.childStep, 'title', this.$t('childStepDefaultTitle', { index: this.stepIndex + 1 }));
       this.$emit('title-change-child', this.childStep.title, this.stepIndex);
    }
  },
}
</script>
<style scoped>
.title-input-field.v-text-field >>> .v-input__control > .v-input__slot {
  padding: 0 !important;
}
.title-input-field.v-text-field >>> input {
  font-size: 1.1rem;
  font-weight: 500;
  padding-left: 4px !important;
}
.title-input-field.v-text-field {
  margin-top: 0;
  padding-top: 0;
}
.flex {
  display: flex;
}
.justify-between {
  justify-content: space-between;
}
.align-center {
  align-items: center;
}
.divider-custom {
  border-color: #eaecf0 !important;
}
.stepDescription.v-textarea .v-input__control .v-input__slot .v-input__append-inner,
.expectedResult.v-textarea .v-input__control .v-input__slot .v-input__append-inner{
  position: absolute;
  bottom: 10px;
  right: 10px;
}
.step-container {
  width: 100%;
  max-width: 100%;
  display: flex;
  justify-content: center;
}
.case-contents {
  display: block;
  max-width: 438px;
  width: 438px;
}
.step-header {
  width: 100%;
  max-width: 100%;
  display: flex;
  justify-content: space-between;
}
.title-input-field.v-text-field >>> .v-input__control > .v-input__slot {
  padding: 0 !important;
}
.title-input-field.v-text-field >>> input {
  font-size: 1.1rem;
  font-weight: 500;
  padding-left: 4px !important;
}
.title-input-field.v-text-field {
  margin-top: 0;
  padding-top: 0;
}
</style>
