<template>
  <div>
    <v-dialog
      v-model="isOpenStatus"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
      persistent
      @click:outside="$emit('closeDialog')"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6 mb-4">
            <h2 class="black--text">
              {{ $t('testruns.test_case.add_test_cases_to_test_runs') }}
            </h2>
            <v-btn
              icon
              @click="$emit('closeDialog')"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <div>
            <div class="select-title position-sticky">
              <v-label class="text-left fs-16px text-theme-label font-weight-medium">
                {{ $t('selectTestRuns') }}
              </v-label>
              <v-text-field
                v-model="search"
                class="text-field mt-3 rounded-lg field-theme custom-prepend pa-0"
                :placeholder="$t('searchByName')"
                height="40"
                clearable
                clear-icon="mdi-close-circle"
                background-color="#F9F9FB"
                hide-details
              >
                <template #prepend-inner>
                  <SearchIcon />
                </template>
              </v-text-field>
            </div>
            <div
              class="d-flex flex-column justify-space-between mt-2 position-relative full-width-list"
              style="height: 85vh; background-color: #f9f9fb; border-radius: 8px"
            >
              <div
                v-if="testRunsLoading"
                class="d-flex flex-column justify-center align-center full-width-list full-height-list"
              >
                <v-skeleton-loader
                  v-for="n in 5"
                  :key="n"
                  class="mx-auto rounded-lg full-width-list mb-2 mt-2"
                  height="40"
                  width="90%"
                  type="button"
                />
              </div>
              <v-list
                v-else-if="!testRunsLoading && allTestRuns.length"
                class="d-flex flex-column justify-space-between"
                style="background-color: #f9f9fb; border-radius: 8px; overflow-y: auto"
              >
                <v-list-item
                  v-for="run in allTestRuns"
                  :key="run.id"
                  :ripple="false"
                  @click="toggleSelection(run)"
                >
                  <v-list-item-action>
                    <v-checkbox
                      :input-value="run.selected"
                      class="field-theme"
                      :ripple="false"
                      off-icon="icon-checkbox-off"
                      on-icon="icon-checkbox-on"
                      :hide-details="true"
                      :ripples="false"
                    />
                  </v-list-item-action>

                  <v-list-item-content>
                    <v-list-item-title>{{ run.name }}</v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
              <Pagination
                v-else-if="!testRunsLoading && totalRunsCount"
                :page="pagination.page"
                :items-per-page="pagination.itemsPerPage"
                :total-pages="pagination.length"
                :total-items="totalRunsCount"
                style="position: sticky; bottom: 80px; background-color: #f9f9fb"
                @update:pagination="onUpdatePagination"
              />
              <div
                v-else
                class="d-flex justify-center align-center text-h4 mt-10 grey--text"
              >
                {{ $t('noResults') }}
              </div>
            </div>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          full-width
          depressed
          height="40"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="$emit('closeDialog')"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme text-capitalize"
          height="40"
          color="primary"
          depressed
          full-width
          elevation="0"
          :disabled="!selectedRuns.length"
          @click="addTestCasesToRuns"
        >
          {{ $t('add') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import SearchIcon from '@/assets/svg/search-icon.svg';
import makeRunService from '@/services/api/run';
import Pagination from '@/components/base/Pagination.vue';
import { debounce } from 'debounce';

let runsService;

export default {
  name: 'ExportDialog',
  components: {
    SearchIcon,
    Pagination,
  },
  props: {
    isOpen: Boolean,
    selectedRows: Array,
  },
  emits: ['closeDialog'],
  data() {
    return {
      search: '',
      totalRunsCount: 0,
      pagination: {
        page: 1,
        itemsPerPage: 15,
        length: 0,
      },
      pages: 2,
      selectedLists: [],
      selectedRuns: [],
      testRuns: [],
      testRunsLoading: false,
    };
  },
  computed: {
    allTestRuns() {
      return this.testRuns.map((run) => ({
        ...run,
        selected: this.selectedRuns.includes(run.uid),
      }));
    },
    offset() {
      return (this.pagination.page - 1) * this.pagination.itemsPerPage;
    },
    isOpenStatus() {
      return this.isOpen;
    },
  },
  watch: {
    pagination: {
      handler() {
        this.fetchTestRuns(this.search);
      },
      deep: true,
    },
    search(newSearch) {
      this.testRunsLoading = true;
      this.debounceRunSearch(newSearch);
    },
  },
  created() {
    runsService = makeRunService(this.$api);
  },
  mounted() {
    this.fetchTestRuns();
  },
  methods: {
    toggleSelection(run) {
      const selected = this.selectedRuns.includes(run.uid);
      if (selected) {
        this.selectedRuns = this.selectedRuns.filter((id) => id !== run.uid);
      } else {
        this.selectedRuns = [...this.selectedRuns, run.uid];
      }
    },
    debounceRunSearch: debounce(async function (search) {
      this.fetchTestRuns(search);
    }, 500),
    async fetchTestRuns(search = '') {
      const handle = this.$route.params.handle;
      try {
        const response = await runsService.getRuns(handle, this.$route.params.key, {
          limit: this.pagination.itemsPerPage,
          offset: this.offset,
          q: search.length ? search : undefined,
        });
        if (response.status === 200) {
          this.totalRunsCount = response.data.count;
          this.pagination.length = Math.ceil(this.totalRunsCount / this.pagination.itemsPerPage);
          this.testRuns = response.data.items;
        } else {
          showErrorToast(this.$swal, 'genericError', { message: response.data.message });
        }
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'runs' }, err?.response?.data);
      } finally {
        this.testRunsLoading = false;
      }
    },
    async addTestCasesToRuns() {
      try {
        const response = await runsService.updateTestRuns(this.$route.params.handle, this.$route.params.key, {
          caseUids: this.selectedRows.map((item) => item.uid),
          uids: this.selectedRuns,
          action: 'addCases',
        });
        if (response.status === 200) {
          this.$emit('closeDialog');
          this.$emit('refreshRuns');
          showSuccessToast(this.$swal, 'addSuccess', { item: response.data.message });
        } else {
          showErrorToast(this.$swal, 'genericError', { message: response.data.message });
        }
      } catch (err) {
        showErrorToast(this.$swal, 'addError', { item: 'test cases to runs' }, err?.response?.data);
      }
    },
    onUpdatePagination(options) {
      this.pagination.page = options.page;
      this.pagination.itemsPerPage = options.itemsPerPage;
    },
  },
};
</script>

<style scoped>
.test-cases-filter-drawer .v-sheet {
  display: flex;
}

.test-cases-filter-drawer .v-card__text {
  display: flex;
  flex-direction: column;
}

.test-cases-filter-drawer .actions-container {
  margin-top: auto !important;
}

.select-title {
  font-family: Inter;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-align: left;
  z-index: 1;
  top: 0px;
  background: white;
  padding-top: 15px;
}
.full-width-list {
  width: 100%;
}
.full-width-list .v-list-item {
  width: 100%;
}
</style>
