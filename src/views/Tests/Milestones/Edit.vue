<template>
  <div>
    <v-card

      class="py-6 px-6 mt-3 ml-3"
      rounded="lg"
      elevation="0"
    >
      <v-row>
        <v-col flex>
          <div
            class="back-to-projects"
            @click="handleBackClick"
          >
            <v-icon color="blue">
              mdi-chevron-left
            </v-icon>
            <p class="d-flex-inline justify-center align-center ma-0 blue--text font-weight-bold">
              {{ $t('Back to Milestone Info') }}
            </p>
          </div>
        </v-col>
      </v-row>
      <template>
        <div class="d-flex align-center justify-space-between mt-5">
          <div class="d-flex flex-column justify-start">
            <div
              class="d-flex align-center"
            >
              <template v-if="!skeletonLoaderState">
                <v-hover
                  v-if="!isEditing"
                  v-slot="{ hover }"
                >
                  <div class="d-flex flex-row">
                    <h2 class="edit-plan">
                      {{ selectedMilestone.name }}
                    </h2>
                    <button
                      v-show="hover"
                      class="ml-3 mt-1"
                      @click="editHeader"
                    >
                      <PencilIcon />
                    </button>
                  </div>
                </v-hover>
                <v-responsive v-else>
                  <v-text-field
                    v-model="selectedMilestone.name"
                    dense
                    solo
                    flat
                    class="d-flex rounded-lg my-3 black--text font-weight-bold fs-24px"
                    background-color="#F9FAFB"
                    hide-details
                    :placeholder="$t('templatesPage.enter_default_value')"
                    @keyup.enter="saveHeader"
                    @blur="saveHeader"
                  />
                </v-responsive>
              </template>
              <v-skeleton-loader
                v-else
                class="mb-3"
                height="36"
                width="500"
                type="text"
              />
            </div>
            <div>
              <template v-if="!skeletonLoaderState">
                <p
                  v-if="!isEditing"
                  class="edit-plan text-start"
                  style="color: #667085; font-size: 14px; line-height: 14px"
                >
                  {{ selectedMilestone.description }}
                </p>
                <v-responsive v-else>
                  <v-textarea
                    v-model="selectedMilestone.description"
                    :value="selectedMilestone.description"
                    background-color="#F9FAFB"
                    class="d-flex rounded-lg mb-3"
                    dense
                    solo
                    flat
                    auto-grow
                    hide-details
                    @keyup.enter="saveHeader"
                    @blur="saveHeader"
                  />
                </v-responsive>
              </template>
              <v-skeleton-loader
                v-else
                height="24"
                width="350"
                type="text"
              />
            </div>
          </div>
          <div
            class="options-container"
          >
            <v-row
              v-if="!skeletonLoaderState"
              class="align-center"
            >
              <div class="d-flex align-center">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('tags') }}:
                </v-label>
              </div>
              <v-col
                cols="auto"
                class="pb-4"
              >
                <v-select
                  v-model="selectedMilestone.tags"
                  class="rounded-lg pt-0 field-theme custom-prepend mh-38px"
                  style="max-width: 500px;"
                  append-icon="mdi-chevron-down"
                  background-color="#F9F9FB"
                  multiple
                  hide-details
                  :items="tags"
                  item-text="name"
                  item-value="uid"
                  :placeholder="$t('testruns.selectTags')"
                  :menu-props="{ offsetY: true }"
                >
                  <template #selection="{ item }">
                    <div class="d-flex align-center custom-chip-theme mr-1 mb-1">
                      <div class="text-theme-label label text-truncate mr-1">
                        {{ item.name }}
                      </div>
                      <v-icon
                        size="16px"
                        @click="onRemoveSelectedTags(item.uid)"
                      >
                        mdi-close
                      </v-icon>
                    </div>
                  </template>

                  <template #item="{ item, on, attrs }">
                    <v-list-item
                      :ripple="false"
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-list-item-action class="custom-checkbox-container">
                        <v-checkbox
                          :input-value="tagsSelection(item.uid)"
                          hide-details
                          class="field-theme mt-0 pt-0"
                          :ripple="false"
                          off-icon="icon-checkbox-off"
                          on-icon="icon-checkbox-on"
                        >
                          <template #label>
                            <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                          </template>
                        </v-checkbox>
                      </v-list-item-action>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>
            </v-row>
            <v-skeleton-loader
              v-else
              class="mb-3"
              height="36"
              width="500"
              type="text"
            />
            <v-row
              v-if="!skeletonLoaderState"
              class="align-center ma-0"
            >
              <div
                class="d-flex align-center"
              >
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('status') }}:
                </v-label>
              </div>
              <v-col
                cols="auto"
                class="pb-4"
              >
                <v-select
                  v-model="selectedMilestone.status"
                  :items="statuses"
                  item-text="name"
                  item-value="id"
                  class="rounded-lg select-input-none select-custom-milestone field-theme custom-prepend"
                  background-color="#F9F9FB"
                  :placeholder="$t('status')"
                  append-icon="mdi-chevron-down"
                  :menu-props="{ offsetY: true }"
                />
              </v-col>
            </v-row>
            <v-skeleton-loader
              v-else
              class="mb-3"
              height="36"
              width="500"
              type="text"
            />
          </div>
        </div>
        <div class="d-flex gap-3"> 
          <div class="d-flex flex-column">
            <div
              v-if="!skeletonLoaderState" 
              class="d-flex align-center"
            >
              <p class="text-start fs-16px mb-0 mr-2">
                {{ $t('milestone.create_milestone.startDate') + ':' }}
              </p>
              <v-menu
                v-model="form.startDate"
                :close-on-content-click="false"
                :max-width="'290px'"
              >
                <template #activator="{ on }">
                  <v-text-field
                    color="blue"
                    class="date-picker-field pt-0 mt-0 fs-16px fw-semibold"
                    :value="startDate"
                    readonly
                    :rules="[ruleStartDate]"
                    hide-details
                    placeholder="MM/DD/YY"
                    v-on="on"
                  />
                </template>
                <v-date-picker
                  v-model="startDate"
                  @change="form.startDate = false"
                />
              </v-menu>
            </div>
            <v-skeleton-loader
              v-else
              class="mt-3"
              height="24"
              width="150"
              type="text"
            />
            <p
              v-if="!isStartDateValid && !skeletonLoaderState"
              class="red--text mb-0"
            >
              {{ $t('milestone.create_milestone.startDateError') }}
            </p>
          </div>
          <div class="d-flex flex-column">
            <div
              v-if="!skeletonLoaderState"
              class="d-flex align-center"
            >
              <p class="text-start fs-16px mb-0 mr-2">
                {{ $t('milestone.create_milestone.dueDate') + ':' }}
              </p>
              <v-menu
                v-model="form.dueDate"
                :close-on-content-click="false"
                :max-width="'290px'"
              >
                <template #activator="{ on }">
                  <v-text-field
                    color="blue"
                    class="date-picker-field pt-0 mt-0 fs-16px fw-semibold"
                    :value="dueDate"
                    readonly
                    :rules="[ruleDueDate]"
                    hide-details
                    placeholder="MM/DD/YY"
                    v-on="on"
                  />
                </template>
                <v-date-picker
                  v-model="dueDate"
                  @change="form.dueDate = false"
                />
              </v-menu>
            </div>
            <v-skeleton-loader
              v-else
              class="mt-3"
              height="24"
              width="150"
              type="text"
            />
            <p
              v-if="!isDueDateValid && !skeletonLoaderState"
              class="red--text mb-0"
            >
              {{ $t('milestone.create_milestone.dueDateError') }}
            </p>
          </div>
        </div>
        <div
          v-if="!skeletonLoaderState"
          class="mt-4 d-flex"
        >
          <v-chip
            :class="{ 'blue--text': tableTestType === 'plans' }"
            width="200px"
            :color="tableTestType === 'plans' ? 'blue-light' : 'gray-light'"
            label
            @click="changeTestType('plans')"
          >
            <div class="font-weight-bold px-2">
              {{ $t('testPlans') }} <span class="ml-1">{{ planTotalRows }}</span>
            </div>
          </v-chip>
          <div class="ml-2">
            <v-chip
              :class="{ 'blue--text': tableTestType === 'runs' }"
              width="200px"
              :color="tableTestType === 'runs' ? 'blue-light' : 'gray-light'"
              label
              @click="changeTestType('runs')"
            >
              <div class="font-weight-bold px-2">
                {{ $t('testRuns') }} <span class="ml-1">{{ runTotalRows }}</span>
              </div>
            </v-chip>
          </div>
        </div>
        <div
          v-else
          class="py-4 d-flex"
        >
          <v-skeleton-loader
            class="rounded-sm d-flex gap-2 chip-primary"
            height="32"
            width="200"
            type="button@2"
          />
        </div>
      </template>
    </v-card>

    <v-card
      class="py-6 px-6 mt-3 ml-3"
      rounded="lg"
      elevation="0"
    >
      <div
        v-if="!skeletonLoaderState"
        class="fs-24px text-start font-weight-bold"
      >
        {{ selectedTestType }}
      </div>
      <v-skeleton-loader
        v-else
        height="36"
        width="140"
        type="heading"
      />
      <div 
        v-if="!skeletonLoaderState"
        class="mt-6 d-flex"
      >
        <v-chip
          :class="{ 'blue--text': tableFilter === 'all' }"
          width="200px"
          :color="tableFilter === 'all' ? 'blue-light' : 'gray-light'"
          label
          @click="changeFilter('all')"
        >
          <div class="font-weight-bold px-2">
            {{ $t('testruns.unlinked') }} <span class="ml-1">{{ isTableTypePlans ? planTotalRows : runTotalRows }}</span>
          </div>
        </v-chip>
        <div class="ml-2">
          <v-chip
            :class="{ 'blue--text': tableFilter === 'selected' }"
            width="200px"
            :color="tableFilter === 'selected' ? 'blue-light' : 'gray-light'"
            label
            @click="changeFilter('selected')"
          >
            <div class="font-weight-bold px-2">
              {{ $t('testruns.linked') }} <span class="ml-1">{{ isTableTypePlans ? selectedPlanLength : selectedRunLength }}</span>
            </div>
          </v-chip>
        </div>
      </div>
      <div
        v-else
        class="py-4 d-flex"
      >
        <v-skeleton-loader
          class="rounded-sm d-flex gap-2 chip-primary"
          height="32"
          width="200"
          type="button@2"
        />
      </div>
      <v-row class="align-center mt-3">
        <v-col
          cols="6"
          sm="6"
          class="py-0"
        >
          <div class="d-flex flex-row justify-start align-center">
            <v-responsive
              v-if="!skeletonLoaderState"
              class="ma-0"
              max-width="344"
            >
              <v-text-field
                v-model="searchFilter"
                class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
                :placeholder="$t('searchByName')"
                height="40"
                clear-icon="mdi-close-circle"
                clearable
                background-color="#F9F9FB"
                hide-details
              >
                <template #prepend-inner>
                  <SearchIcon />
                </template>
              </v-text-field>
            </v-responsive>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-3"
              width="344"
              height="40"
              type="button"
            />
            <PlanFilter
              v-if="tableTestType === 'plans'"
              :configurations="configurations"
              :view-type="isTableTypePlans"
              :milestones="activeMilestones"
              :tags="tags"
              @applyFilters="applyPlanFilters"
            />
            <RunFilter
              v-else
              :configurations="configurations"
              :milestones="activeMilestones"
              :tags="tags"
              @applyFilters="applyRunFilters"
            />
          </div>
        </v-col>
        <v-col
          cols="6"
          sm="6"
          class="d-flex justify-end"
        >
          <SettingsMenu
            :key="selectedTableMenu"
            :view-type="isTableTypePlans"
            :table-type="selectedTableMenu"
          />
        </v-col>
      </v-row>
      <MileatoneTestPlanTable
        v-if="tableTestType === 'plans'"
        v-model="selectedPlans"
        :plans-data="tableFilter === 'all' ? filteredTestPlans : selectedPlans"
        :table-filter="tableFilter"
        :filtered-headers="filteredPlanHeaders"
        :filtered-items="tableFilter === 'all' ? filteredTestPlans : selectedPlans"
        :total-items="tableFilter === 'all' ? planTotalRows : selectedPlans.length"
        :current-page="planCurrentPage"
        :items-per-page="planPerPage"
        :relations-loading="relationsLoading"
        :relation-loading-states="relationLoadingStates"
        :sort-by="planSortBy"
        :sort-desc="planSortDesc"
        @update-pagination="onUpdatePlanPagination"
      />
      <MilestoneTestRunTable
        v-if="tableTestType === 'runs'"
        v-model="selectedRuns"
        :runs-data="tableFilter === 'all' ? filteredTestRuns : selectedRuns"
        :table-filter="tableFilter"
        :filtered-headers="filteredRunHeaders"
        :filtered-items="tableFilter === 'all' ? filteredTestRuns : selectedRuns"
        :total-items="tableFilter === 'all' ? runTotalRows : selectedRuns.length"
        :current-page="runCurrentPage"
        :items-per-page="runPerPage"
        :relations-loading="relationsLoading"
        :relation-loading-states="relationLoadingStates"
        :sort-by="runSortBy"
        :sort-desc="runSortDesc"
        @update-pagination="onUpdateRunPagination"
      />

      <v-row
        justify="end"
        class="mt-4"
      >
        <v-col cols="12">
          <v-flex class="mt-6 d-sm-flex justify-end">
            <v-btn
              v-if="!skeletonLoaderState"
              depressed
              color="gray-100"
              height="40"
              class="text-capitalize rounded-lg font-weight-bold black--text mr-4 mt-2"
              :width="$vuetify.breakpoint.smAndDown ? '100%' : '150xp'"
              @click="handleBackClick"
            >
              {{ $t('cancel') }}
            </v-btn>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-4"
              width="75"
              height="40"
              type="button"
            />
            <v-btn
              v-if="!skeletonLoaderState"
              depressed
              color="primary"
              height="40"
              class="text-capitalize btn-theme rounded-lg mt-2"
              :width="$vuetify.breakpoint.smAndDown ? '100%' : '150xp'"
              :class="{
                'disabled-action': !isDueDateValid || !isStartDateValid
              }"
              :loading="saveLoading"
              @click="updateMilestone"
            >
              {{ $t('Save') }}
            </v-btn>
            <v-skeleton-loader
              v-else
              class="rounded-lg primary"
              width="75"
              height="40"
              type="button"
            />
          </v-flex>
        </v-col>
      </v-row>
    </v-card>

    <ProjectDiscardDialog
      v-model="showConfirmBackDialog"
      :title="$t('milestone.edit_milestone.close_dialog.title')"
      :content="$t('milestone.edit_milestone.close_dialog.title')"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmClick"
    />
    <RemoveTestRunDialog
      v-model="showConfirmRemoveTestRunDialog"
      :title="$t('milestone.close_remove_dialog.title')"
      :content="$t('milestone.close_remove_dialog.title')"
      :content_part2="$t('milestone.close_remove_dialog.content_part1')"
      :btn_label="$t('milestone.close_remove_dialog.remove_button')"
      @close="handleCloseClick"
      @handleConfirmClick="handleRemoveConfirmClick"
    />
  </div>
</template>


<script>
import { mapGetters, mapActions as projectMapAcions } from 'vuex';
import ProjectDiscardDialog from '@/components/Project/ProjectDiscardDialog.vue';
import MilestoneTestRunTable from '@/components/Milestone/MilestoneTestRunTable.vue';
import MileatoneTestPlanTable from '@/components/Milestone/MilestoneTestPlanTable.vue';
import RemoveTestRunDialog from '@/components/Milestone/MilestoneEditRemoveDialog.vue';
import makeMilestonesService from '@/services/api/milestone'
import makeConfigurationService from '@/services/api/configuration'
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import RunFilter from '@/components/TestRuns/RunFilter.vue';
import PlanFilter from '@/components/TestPlans/PlanFilter'
import { showSuccessToast } from '@/utils/toast';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import handleLoading from '@/mixins/loader.js'
import makeRunService from '@/services/api/run'
import makePlanService from '@/services/api/plan'
import SearchIcon from '@/assets/svg/search-icon.svg';
import PencilIcon from '@/assets/svg/pencil.svg';
import makeTagService from '@/services/api/tag';
import { showErrorToast } from '@/utils/toast';
import dayjs from 'dayjs';
import colorPreferences from '@/mixins/colorPreferences'
import fileValidatorMixin from '@/mixins/fileValidator';
import { useRelations } from '@/composables/utils/relations';
import { useMilestoneIndex } from '@/composables/modules/milestone/index';

let makeMilestoneService;
export default {
  name: 'MilestoneEditView',
  components: {
    ProjectDiscardDialog,
    MilestoneTestRunTable,
    MileatoneTestPlanTable,
    RemoveTestRunDialog,
    SearchIcon,
    PencilIcon,
    SettingsMenu,
    RunFilter,
    PlanFilter
  },
  mixins: [handleLoading, colorPreferences,fileValidatorMixin],
  setup() {
    const { formatDate } = useDateFormatter();
    const { relationsLoading, relationLoadingStates, fetchPlanRelations, fetchRunRelations } = useRelations();
    const { clearCache } = useMilestoneIndex();
    
    return { 
      formatDate,
      relationsLoading,
      relationLoadingStates,
      fetchPlanRelations,
      fetchRunRelations,
      clearMilestoneCache: clearCache
    };
  },
  data() {
    return {
      form: {
        projectName: '',
        description: '',
        users: [],
        dueDate: false,
        startDate: false
      },
      selectedMilestone: {
        customFields: {
          test_runs: [],
        }
      },
      saveLoading: false,
      showConfirmRemoveTestRunDialog: false,
      showConfirmBackDialog: false,
      testRuns: [],
      testPlans: [],
      selectedRuns: [],
      selectedPlans: [],
      tableFilter: 'all',
      tableTestType: 'plans',
      searchFilter: '',
      tags: [],
      runHeaders: [],
      planHeaders: [],
      milestones: [],
      configurations: [],
      appliedRunFilters: null,
      appliedPlanFilters: null,
      runItems: [],
      planItems: [],
      startDate: null,
      dueDate: null,
      isEditing: false,
      statuses: [],
      // Pagination state for plans
      planTotalRows: 0,
      planCurrentPage: 1,
      planPerPage: 10,
      // Pagination state for runs
      runTotalRows: 0,
      runCurrentPage: 1,
      runPerPage: 10,
      // Sorting state for plans
      planSortBy: [],
      planSortDesc: [],
      // Sorting state for runs
      runSortBy: [],
      runSortDesc: [],
    };
  },
  computed: {
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders'
    }),
    getActivePlanItems() {
      return this.planItems?.filter((plan) => !plan.archivedAt) ?? [];
    },
    getActiveRunItems() {
      return this.runItems?.filter((run) => !run.archivedAt) ?? [];
    },
    getActivePlanItemsCount() {
      return this.getActivePlanItems.length;
    },
    getActiveRunItemsCount() {
      return this.getActiveRunItems.length;
    },
    selectedTestType() {
      const testType = {
        plans: this.$t('testPlans'),
        runs: this.$t('testRuns')
      }
      return testType[this.tableTestType];
    },
    selectedTableMenu(){
      return this.tableTestType  == 'runs' ? 'milestoneEditRuns' : 'milestoneEditPlans';
    },
    selectedRunLength() {
      return this.selectedRuns?.length || 0;
    },
    selectedPlanLength() {
      return this.selectedPlans?.length || 0;
    },
    isTableTypePlans() {
      return this.tableTestType === 'plans';
    },
    filteredRunHeaders() {
      return this.runHeaders?.filter((header) => header.checked);
    },
    filteredPlanHeaders() {
      return this.planHeaders?.filter((header) => header.checked);
    },
    filteredMenuHeaders() {
      return this.runHeaders?.filter((header) => header.text != 'Actions');
    },
    filteredTestRuns() {
      return this.filteredItems(this.testRuns);
    },

    filteredTestPlans() {
      return this.filteredItems(this.testPlans);
    },

    filteredItems() {
      return (items) => {
        // Return empty array if items is null or undefined
        if (!items) {
          return [];
        }

        if (this.searchFilter && !this.appliedRunFilters && !this.appliedPlanFilters) {
          return items.filter(item => item && this.matchesFilter(item));
        } else if (this.tableTestType === 'runs' && this.appliedRunFilters && !this.searchFilter) {
          return items.filter(item => item && this.matchRunApplyFilter(item));
        } else if (this.tableTestType === 'runs' && this.appliedRunFilters && this.searchFilter) {
          const mainFiltered = items.filter(item => item && this.matchRunApplyFilter(item));
          return mainFiltered.filter(item => item && this.matchesFilter(item));
        }
        else if(this.tableTestType === 'plans' && this.appliedPlanFilters && !this.searchFilter){
          return items.filter(item => item && this.matchPlanApplyFilter(item));
        }
        else if(this.tableTestType === 'plans' && this.appliedPlanFilters && this.searchFilter){
          const mainFiltered = items.filter(item => item && this.matchPlanApplyFilter(item));
          return mainFiltered.filter(item => item && this.matchesFilter(item));
        }
        return items;
      };
    },
    isStartDateValid() {
      // If either date is null/empty, validation passes
      if (!this.startDate || !this.dueDate) return true;
      return new Date(this.startDate) < new Date(this.dueDate);
    },
    isDueDateValid() {
      // If either date is null/empty, validation passes
      if (!this.startDate || !this.dueDate) return true;
      return new Date(this.dueDate) > new Date(this.startDate);
    },
    activeMilestones() {
      return this.milestones.filter((milestone) => !milestone?.archivedAt && !milestone?.deletedAt);
    },
  },
  watch: {
    currentOrg: 'refreshData',
    selectedMilestone(newValue) {
      if (newValue) {
        this.startDate = newValue?.customFields?.startDate ? this.formatDate(newValue.customFields.startDate, 'YYYY-MM-DD') : null;
        this.dueDate = newValue?.customFields?.dueAt ? this.formatDate(newValue.customFields.dueAt, 'YYYY-MM-DD') : null;
      }
    },
  },
  async created() {
    if(!this.dynamicHeaders.milestoneEditRuns) {
      this.initializeHeaders({ type: 'milestoneEditRuns' });
    }
    this.runHeaders = this.dynamicHeaders.milestoneEditRuns;

    if(!this.dynamicHeaders.milestoneEditPlans) {
      this.initializeHeaders({ type: 'milestoneEditPlans' });
    }
    this.planHeaders = this.dynamicHeaders.milestoneEditPlans;
    makeMilestoneService = makeMilestonesService(this.$api);
    this.statuses = this.getStatuses('milestone').filter(element => !element.archived)
  },
  async mounted(){
    const handle = this.$route.params.handle;
    await this.init([
      this.findMilestone(handle),
      this.getTags(),
      this.getAllRuns({ handle, projectKey: this.$route.params.key, perPage: 10, currentPage: 1, limit: 10, offset: 0 }),
      this.getAllPlans({ handle, projectKey: this.$route.params.key, perPage: 10, currentPage: 1 }),
      this.getRunsByMilestoneUid({ handle, projectKey: this.$route.params.key, perPage: 10, currentPage: 1 }),
      this.getPlansByMilestoneUid({ handle, projectKey: this.$route.params.key, perPage: 10, currentPage: 1 }),
    ]);

    // Update testRuns and testPlans when data changes
    this.updateTestData();

    this.getMilestones();
    this.getConfigurations();

      },
    methods: {
      ...projectMapAcions("headers", ['initializeHeaders']),
      updateTestData() {
        this.testRuns = this.getActiveRunItems;
        this.testPlans = this.getActivePlanItems;
      },
    handleRemoveConfirmClick(){

    },
    ruleStartDate() {
      return this.isStartDateValid || this.$t('milestone.create_milestone.startDateError')
    },
    ruleDueDate() {
      return this.isDueDateValid || this.$t('milestone.create_milestone.dueDateError')
    },
    async getAllRuns({handle, projectKey, limit, offset}) {
      const runService = makeRunService(this.$api);
      const queryParams = {
        limit: limit || 10,
        offset: offset || 0,
      };
        const response = await runService.getRuns(handle, projectKey, queryParams);
        this.runItems = response.data.items;
        this.runTotalRows = response.data?.count || response.data?.total || 0;
        
        // Update testRuns for filtering
        this.updateTestData();
        
        // Fetch relations for the runs in background
        if (this.runItems.length > 0) {
          this.fetchRunRelations(runService, handle, projectKey, this.runItems).catch(error => {
            console.warn('Failed to load run relations:', error);
          });
        }
    },
    async getAllPlans({ handle, projectKey, perPage, currentPage })
    {
          const queryParams = {
        limit: perPage || 10,
        offset: ((currentPage || 1) - 1) * (perPage || 10),
      };
    
      const planService = makePlanService(this.$api);
      const response = await planService.getPlans(handle, projectKey, queryParams);
      this.planItems = response.data.items;
      this.planTotalRows = response.data?.count || response.data?.total || 0;
      
      // Update testPlans for filtering
      this.updateTestData();
      
      // Fetch relations for the plans in background
      if (this.planItems.length > 0) {
        this.fetchPlanRelations(planService, handle, projectKey, this.planItems).catch(error => {
          console.warn('Failed to load plan relations:', error);
        });
      }
    },
    async getRunsByMilestoneUid({handle, projectKey, limit, offset}) {
      const runService = makeRunService(this.$api);
      const queryParams = {
        limit: limit || 10,
        offset: offset || 0,
        milestoneUids: this.$route.params.id
      };
        const response = await runService.getRuns(handle, projectKey, queryParams);
        this.selectedRuns = response.data.items;
        
        // Fetch relations for the runs in background
        if (this.selectedRuns.length > 0) {
          this.fetchRunRelations(runService, handle, projectKey, this.selectedRuns).catch(error => {
            console.warn('Failed to load run relations:', error);
          });
        }
    },
    async getPlansByMilestoneUid({ handle, projectKey, perPage, currentPage })
    {

          const queryParams = {
        limit: perPage || 10,
        offset: ((currentPage || 1) - 1) * (perPage || 10),
        milestoneUids: this.$route.params.id
      };
    
      const planService = makePlanService(this.$api);
      const response = await planService.getPlans(handle, projectKey, queryParams);
      this.selectedPlans = response.data.items;

      // Fetch relations for the plans in background
      if (this.selectedPlans.length > 0) {
        this.fetchPlanRelations(planService, handle, projectKey, this.selectedPlans).catch(error => {
          console.warn('Failed to load plan relations:', error);
        });
      }
    },
    editHeader() {
      this.isEditing = true;
    },
    saveHeader() {
      this.isEditing = false;
    },
    applyPlanFilters(filters){
      // Accept null (clear filters) or the emitted payload from PlanFilter (which is { ui, api })
      this.appliedPlanFilters = filters ?? null;
    },
    matchPlanApplyFilter(item){
      // Return false if item is null or undefined
      if (!item) {
        return false;
      }

      // Normalize filter object (supports both direct keys and nested under ui)
      const pf = this.appliedPlanFilters?.ui ?? this.appliedPlanFilters;
      if (!pf) return true;

      // Priority filter (compare against item.customFields?.priority by id/uid/name)
      const priorityValues = pf.panel_priority?.value ?? pf.panel_priority ?? [];
      if (Array.isArray(priorityValues) && priorityValues.length > 0) {
        const allowedPriorityIds = new Set(priorityValues.map(v => v?.id).filter(Boolean));
        const allowedPriorityUids = new Set(priorityValues.map(v => v?.uid).filter(Boolean));
        const allowedPriorityNames = new Set(priorityValues.map(v => v?.name).filter(Boolean));
        const itemPriority = item?.customFields?.priority ?? item?.priority;
        const itemPriorityName = undefined; // not available here reliably
        if (!(
          (itemPriority != null && (allowedPriorityIds.has(itemPriority) || allowedPriorityUids.has(itemPriority))) ||
          (itemPriorityName && allowedPriorityNames.has(itemPriorityName))
        )) {
          return false;
        }
      }

      // Status filter (compare against item.customFields?.status by id/uid/name)
      const statusValues = pf.panel_status?.value ?? pf.panel_status ?? [];
      if (Array.isArray(statusValues) && statusValues.length > 0) {
        const allowedStatusIds = new Set(statusValues.map(v => v?.id).filter(Boolean));
        const allowedStatusUids = new Set(statusValues.map(v => v?.uid).filter(Boolean));
        const allowedStatusNames = new Set(statusValues.map(v => v?.name).filter(Boolean));
        const itemStatus = item?.customFields?.status ?? item?.status;
        const itemStatusName = undefined;
        if (!(
          (itemStatus != null && (allowedStatusIds.has(itemStatus) || allowedStatusUids.has(itemStatus))) ||
          (itemStatusName && allowedStatusNames.has(itemStatusName))
        )) {
          return false;
        }
      }

      // Tags filter (item.tags is array of tag objects)
      const tagValues = pf.panel_tag?.value ?? pf.panel_tag ?? [];
      if (Array.isArray(tagValues) && tagValues.length > 0) {
        if (!Array.isArray(item?.tags)) return false;
        const allowedTagIds = new Set(tagValues.map(v => v?.id).filter(Boolean));
        const allowedTagUids = new Set(tagValues.map(v => v?.uid).filter(Boolean));
        const allowedTagNames = new Set(tagValues.map(v => v?.name).filter(Boolean));
        const hasMatch = item.tags.some(t =>
          allowedTagIds.has(t?.id) || allowedTagUids.has(t?.uid) || allowedTagNames.has(t?.name)
        );
        if (!hasMatch) return false;
      }

      // Date range filter
      const dateRange = pf.dateRange?.value ?? pf.dateRange;
      if (dateRange?.start && dateRange?.end) {
        if (!item.createdAt) return false;
        const itemDate = dayjs(item.createdAt);
        const startDate = dayjs(dateRange.start);
        const endDate = dayjs(dateRange.end);
        if (itemDate.isBefore(startDate) || itemDate.isAfter(endDate)) return false;
      }

      return true;
    },
    applyRunFilters(filters){
      // Accept null (clear filters) or the emitted payload from RunFilter (which is { ui, api })
      this.appliedRunFilters = filters ?? null;
    },
    matchRunApplyFilter(item) {
      // Return false if item is null or undefined
      if (!item) {
        return false;
      }

      // Normalize filter object (supports both direct keys and nested under ui)
      const rf = this.appliedRunFilters?.ui ?? this.appliedRunFilters;
      if (!rf) return true;

      // Priority filter (RunFilter uses panelPriority with ids)
      const runPriorityValues = rf.panelPriority?.value ?? rf.panelPriority ?? rf.panel_priority ?? [];
      if (Array.isArray(runPriorityValues) && runPriorityValues.length > 0) {
        const allowedPriorityIds = new Set(runPriorityValues.map(v => (v?.id ?? v)).filter(Boolean));
        const allowedPriorityUids = new Set(runPriorityValues.map(v => v?.uid).filter(Boolean));
        const itemPriority = item?.customFields?.priority ?? item?.priority;
        if (!(itemPriority != null && (allowedPriorityIds.has(itemPriority) || allowedPriorityUids.has(itemPriority)))) {
          return false;
        }
      }

      // Status filter (RunFilter uses panelStatus with ids)
      const runStatusValues = rf.panelStatus?.value ?? rf.panelStatus ?? rf.panel_status ?? [];
      if (Array.isArray(runStatusValues) && runStatusValues.length > 0) {
        const allowedStatusIds = new Set(runStatusValues.map(v => (v?.id ?? v)).filter(Boolean));
        const allowedStatusUids = new Set(runStatusValues.map(v => v?.uid).filter(Boolean));
        const itemStatus = item?.customFields?.status ?? item?.status;
        if (!(itemStatus != null && (allowedStatusIds.has(itemStatus) || allowedStatusUids.has(itemStatus)))) {
          return false;
        }
      }

      // Milestone filter (RunFilter uses panelMilestone with uids). Prefer relations property testMilestones
      const runMilestoneValues = rf.panelMilestone?.value ?? rf.panelMilestone ?? rf.panel_milestone ?? [];
      if (Array.isArray(runMilestoneValues) && runMilestoneValues.length > 0) {
        const allowedMilestoneUids = new Set(runMilestoneValues.map(v => (v?.uid ?? v)).filter(Boolean));
        let hasMilestone = false;
        if (Array.isArray(item?.testMilestones) && item.testMilestones.length > 0) {
          hasMilestone = item.testMilestones.some(m => allowedMilestoneUids.has(m?.uid));
        } else if (item?.customFields?.milestone) {
          hasMilestone = allowedMilestoneUids.has(item.customFields.milestone);
        }
        if (!hasMilestone) return false;
      }

      // Tag filter (RunFilter uses panelTag with uids)
      const runTagValues = rf.panelTag?.value ?? rf.panelTag ?? rf.panel_tag ?? [];
      if (Array.isArray(runTagValues) && runTagValues.length > 0) {
        const allowedTagIds = new Set(runTagValues.map(v => v?.id).filter(Boolean));
        const allowedTagUids = new Set(runTagValues.map(v => (v?.uid ?? v)).filter(Boolean));
        const allowedTagNames = new Set(runTagValues.map(v => v?.name).filter(Boolean));
        if (!Array.isArray(item?.tags)) return false;
        const hasTag = item.tags.some(t => allowedTagIds.has(t?.id) || allowedTagUids.has(t?.uid) || allowedTagNames.has(t?.name));
        if (!hasTag) return false;
      }

      // Date range filter
      const runDateRange = rf.dateRange?.value ?? rf.dateRange;
      if (runDateRange?.start && runDateRange?.end) {
        if (!item.createdAt) return false;
        const itemDate = dayjs(item.createdAt);
        const startDate = dayjs(runDateRange.start);
        const endDate = dayjs(runDateRange.end);
        if (itemDate.isBefore(startDate) || itemDate.isAfter(endDate)) return false;
      }

      return true;
    },
    async updateMilestone() {
      if (!this.isDueDateValid || !this.isStartDateValid) return;
      
      const payload = this.removeFalsyValuesfromObject({
        ...this.selectedMilestone.customFields,
        name: this.selectedMilestone.name,
        description: this.selectedMilestone.description,
        runIds: this.selectedRuns.map((e) => e.uid),
        planIds: this.selectedPlans.map((e) => e.uid),
        tagUids: this.selectedMilestone.tags,
        status: this.selectedMilestone.status,
      });

      // Only add dates if they are provided
      if (this.dueDate) {
        payload.dueAt = dayjs(this.dueDate).startOf('day').utc().toISOString();
      }
      if (this.startDate) {
        payload.startDate = dayjs(this.startDate).startOf('day').utc().toISOString();
      }


      try {
        this.saveLoading = true;
        await makeMilestoneService.updateMilestone(this.$route.params.handle, this.$route.params.key, this.selectedMilestone.uid, payload);
        showSuccessToast(this.$swal, this.$t('milestone.update_milestone_success'));
        // Clear milestone cache so updated milestone appears immediately
        this.clearMilestoneCache();
        this.$router.push({
          name: 'Milestones',
          params: {
            handle: this.$route.params.handle,
            key: this.$route.params.key,
          },
        });
      } catch (err) {
        showErrorToast(this.$swal, this.$t('toast.updateError', { item: 'milestone' }), {}, err?.response?.data);
      } finally {
        this.saveLoading = false;
      }

    },
    async getTags() {
      const handle = this.$route.params.handle;
      const tagService = makeTagService(this.$api);
        try {
          const response = await tagService.getTags(handle, 'milestones');
          this.tags = response.data
        } catch (err) {
          showErrorToast(this.$swal, 'fetchError', { item: 'tags' }, err?.response?.data);
          return [];
        }
    },
    async getMilestones() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const milestoneService = makeMilestonesService(this.$api);
      try {
        const response = await milestoneService.getMilestones(handle, projectKey);
        this.milestones = response.data?.items;
        return response.data?.items;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'milestones' }, err?.response?.data);
        return [];
      }
    },
    async getConfigurations() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const configurationService = makeConfigurationService(this.$api);
      try {
        const response = await configurationService.getConfigurations(handle, projectKey, 10, 0);
        this.configurations = response.data?.configurations;
        return response.data?.configurations;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
        return [];
      }
    },
    handleBackClick() {
      this.showConfirmBackDialog = true;
    },
    handleCloseClick() {
      this.showConfirmBackDialog = false;
    },
    handleConfirmClick() {
      this.showConfirmBackDialog = false;
      // Clear cache to ensure fresh data when returning to milestone index
      this.clearMilestoneCache();
      this.$router.replace({
        name: 'Milestones',
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key,
        },
      });
    },
    changeFilter(filter) {
      this.tableFilter = filter;
      // Reset pagination when switching filters
      this.planCurrentPage = 1;
      this.runCurrentPage = 1;
      
      // Load appropriate data based on filter
      if (filter === 'all') {
        // Load all plans/runs with pagination
        if (this.tableTestType === 'plans') {
          this.getAllPlans({
            handle: this.$route.params.handle,
            projectKey: this.$route.params.key,
            perPage: this.planPerPage,
            currentPage: this.planCurrentPage
          });
        } else {
          this.getAllRuns({
            handle: this.$route.params.handle,
            projectKey: this.$route.params.key,
            limit: this.runPerPage,
            offset: (this.runCurrentPage - 1) * this.runPerPage
          });
        }
      }
      // For 'selected' filter, we use the existing selectedPlans/selectedRuns data
    },
    changeTestType(type) {
      this.tableTestType = type;
    },
    matchesFilter(item) {
      // Return false if item is null or undefined
      if (!item || !item.name) {
        return false;
      }

      const lowerCaseFilter = this.searchFilter?.toLowerCase() || '';
      return item.name.toLowerCase().includes(lowerCaseFilter);
    },
    async findMilestone(handle) {
      try {
        const response = await makeMilestoneService.findMilestone(handle, this.$route.params.key, this.$route.params.id);
        this.selectedMilestone = response.data;
        this.selectedMilestone.tags = response.data?.tags.map((tag) => tag.uid);
    


      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'milestone' }, err?.response?.data);
      }
    },
    onRemoveSelectedTags(uid) {
      this.selectedMilestone.tags = this.selectedMilestone.tags.filter((tag) => tag !== uid);
    },
    tagsSelection(uid) {
        return this.selectedMilestone.tags.some((tag) => tag === uid);
    },
    onUpdatePlanPagination(options) {
      const newPage = options.page || this.planCurrentPage;
      const newItemsPerPage = options.itemsPerPage || this.planPerPage;
      const newSortBy = options.sortBy || [];
      const newSortDesc = options.sortDesc || [];
      
      // Check if anything actually changed
      const pageChanged = newPage !== this.planCurrentPage;
      const itemsPerPageChanged = newItemsPerPage !== this.planPerPage;
      const sortByChanged = JSON.stringify(newSortBy) !== JSON.stringify(this.planSortBy);
      const sortDescChanged = JSON.stringify(newSortDesc) !== JSON.stringify(this.planSortDesc);
      
      // Update state
      this.planCurrentPage = newPage;
      this.planPerPage = newItemsPerPage;
      this.planSortBy = newSortBy;
      this.planSortDesc = newSortDesc;
      
      // Only trigger API call if something actually changed and in "all" mode
      if ((pageChanged || itemsPerPageChanged || sortByChanged || sortDescChanged) && this.tableFilter === 'all') {
        this.getAllPlans({ 
          handle: this.$route.params.handle, 
          projectKey: this.$route.params.key, 
          perPage: this.planPerPage, 
          currentPage: this.planCurrentPage 
        });
      }
    },
    onUpdateRunPagination(options) {
      const newPage = options.page || this.runCurrentPage;
      const newItemsPerPage = options.itemsPerPage || this.runPerPage;
      const newSortBy = options.sortBy || [];
      const newSortDesc = options.sortDesc || [];
      
      // Check if anything actually changed
      const pageChanged = newPage !== this.runCurrentPage;
      const itemsPerPageChanged = newItemsPerPage !== this.runPerPage;
      const sortByChanged = JSON.stringify(newSortBy) !== JSON.stringify(this.runSortBy);
      const sortDescChanged = JSON.stringify(newSortDesc) !== JSON.stringify(this.runSortDesc);
      
      // Update state
      this.runCurrentPage = newPage;
      this.runPerPage = newItemsPerPage;
      this.runSortBy = newSortBy;
      this.runSortDesc = newSortDesc;
      
      // Only trigger API call if something actually changed and in "all" mode
      if ((pageChanged || itemsPerPageChanged || sortByChanged || sortDescChanged) && this.tableFilter === 'all') {
        this.getAllRuns({ 
          handle: this.$route.params.handle, 
          projectKey: this.$route.params.key, 
          limit: this.runPerPage, 
          offset: (this.runCurrentPage - 1) * this.runPerPage 
        });
      }
    },
  },
};
</script>

<style lang="scss" >
.options-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-items: center;
}
.back-to-projects {
  display: flex;
  cursor: pointer;
  width: max-content;
}

.search_input {
  width: 100%;

  @media screen and (min-width: 600px) {
    width: 300px;
  }
}

.project-logo {
  border-radius: 50%;
  border: 2px dashed grey;
  width: 150px;
  height: 150px;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
  cursor: pointer;
  transition: border-color 0.3s;
  background-size: cover;
  background-position: center;
  position: relative;
}

.project-logo:hover {
  border-color: #2196f3;
}

.hovering .edit-icon,
.hovering .delete-icon {
  display: block;
}

.edit-icon,
.delete-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateX(-30px);
  display: none;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 10px;
  cursor: pointer;
}

.delete-icon {
  margin-left: 60px;
}

.project-logo:hover .edit-icon,
.project-logo:hover .delete-icon {
  display: block;
}



.subdescription-edit-field {
  font-size: 17px;
  width: 600px;
}

.date-picker-field {
  width: 100px
}

.select-custom-milestone {
  height: 40px;
  width: 130px;
  border-radius: 6px;
  color: #0C111D;
  padding-top: 5px;
  background-color: #F9FAFB;
}

.custom-chip-theme {
  background: #F9FAFB;
  border-radius: 6px;
  padding: 2px 8px;
  height: 24px;
  font-size: 14px;
  line-height: 20px;
  color: #667085;
}

.label {
  max-width: 150px;
}

</style>
