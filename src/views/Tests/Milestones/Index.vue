<template>
  <v-container
    id="case-index-container"
    class="container h-full d-flex flex-column justify-start font-inter pb-0 pr-0"
    fluid
  >
    <MilestoneHeader
      :title="$t('milestone.title')"
      :action-text="$t('milestone.create')"
      :filter="filter"
      :ongoing-item-count="activeCount"
      :archived-item-count="archivedCount"
      :write-entity="writeEntity"
      @update-filter="updateFilter"
    />
    <template v-if="filteredMilestones.length == 0 && filter != 'closed' && hasInitiallyLoaded && !apiFilters">
      <div class="mb-0 white rounded-lg mx-0 project-placeholder-height d-flex justify-center align-center">
        <ActiveEmptyState
          :image-src="require('@/assets/png/milestone-empty-state.png')"
          :title="$t('milestone.empty_state.title')"
          :button-text="$t('milestone.createMilestone')"
          :button-route="{ name: 'MilestoneCreate' }"
          button-color="primary"
          :is-project-archived="isProjectArchived"
          :write-entity="writeEntity"
        >
          <template #description>
            <p class="mb-0 mt-3">
              {{ $t('milestone.empty_state.description_part1') }}
            </p>
            <p class="ma-0">
              {{ $t('milestone.empty_state.description_part2') }}
            </p>
            <p class="ma-0">
              {{ $t('milestone.empty_state.description_part3') }}
            </p>
          </template>
        </ActiveEmptyState>
      </div>
    </template>

    <template v-else-if="filteredMilestones.length == 0 && filter != 'open' && !apiFilters && hasInitiallyLoaded">
      <div class="mb-0 white rounded-lg mx-0 project-placeholder-height d-flex justify-center align-center">
        <ArchivedEmptyState
          :image-src="require('@/assets/png/milestone-empty-state.png')"
          :title="$t('closed_empty_state.title', { name: $t('milestones') })"
        >
          <template #description>
            <p class="mb-0 mt-3">
              {{ $t('closed_empty_state.description.part1', { name: $t('milestones') }) }}
            </p>
            <p class="mb-0">
              {{ $t('projects.archived_empty_state.description.part2') }}
            </p>
          </template>
        </ArchivedEmptyState>
      </div>
    </template>

    <template v-else>
      <template>
        <v-container
          class="pa-6 white rounded-lg h-full"
          fluid
        >
          <template>
            <v-row class="align-center">
              <v-col
                cols="6"
                sm="6"
              >
                <div class="d-flex flex-row justify-start align-center">
                  <v-responsive
                    class="ma-0"
                    max-width="344"
                  >
                    <v-text-field
                      v-model="searchFilter"
                      class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
                      background-color="#F9F9FB"
                      :placeholder="$t('search_by_name')"
                      clear-icon="mdi-close-circle"
                      clearable
                      height="38px"
                      hide-details
                    >
                      <template #prepend-inner>
                        <SearchIcon />
                      </template>
                    </v-text-field>
                  </v-responsive>
            
                  <v-col
                    flex
                    class="d-flex justify-space-between align-center"
                  >
                    <MilestoneFilter
                      v-if="filter == 'open'"
                      :current-filters="mainFilters"
                      :tags="tags"
                      @applyFilters="applyFilters"
                    />
                    <MilestoneClosedFilter
                      v-if="filter == 'closed'"
                      :current-filters="closedFilters"
                      @applyFilters="applyClosedFilters"
                    />
                  </v-col>
                </div>
              </v-col>
              <v-col
                cols="6"
                sm="6"
                class="d-flex justify-end"
              >
                <SettingsMenu table-type="milestone" />
              </v-col>
            </v-row>

            <v-row v-if="(mainFilter || closeFilter) && hasInitiallyLoaded">
              <v-col cols="12">
                <MilestoneFilterChips
                  :filters="filter === 'open' ? mainFilters : closedFilters"
                  :results-count="filteredMilestones.length" 
                  :statuses="statuses" 
                  :tags="tags" 
                  @update-filters="updateFilters"
                  @clear-filters="clearFilters"
                />
              </v-col>
            </v-row>

            <v-row>
              <v-col
                cols="12"
                class="pt-1"
              >
                <MilestoneTable
                  :filtered-headers="filteredHeaders"
                  :filtered-items="filteredMilestones"
                  :item-key="itemKey"
                  :write-entity="writeEntity"
                  :delete-entity="deleteEntity"
                  :total-items="isSearchMode ? filteredMilestones.length : totalRows"
                  :current-page="isSearchMode ? 1 : currentPage"
                  :items-per-page="isSearchMode ? filteredMilestones.length : perPage"
                  :relations-loading="relationsLoading"
                  :relation-loading-states="relationLoadingStates"
                  :sort-by="sortBy"
                  :sort-desc="sortDesc"
                  :is-relation-cached="isRelationCached"
                  @select-item="setSelected"
                  @close-item="confirmCloseMilestone"
                  @reopen-item="confirmUnArchiveMilestone"
                  @edit-item="editMilestone"
                  @delete-item="showDeleteDialog"
                  @update-pagination="onUpdatePagination"
                />
              </v-col>
            </v-row>
          </template>
        </v-container>
      </template>
    </template>
    <MilestoneArchiveDialog
      v-model="showConfirmOpenDialog"
      :title="isMilestoneArchived ? $t('milestone.close_dialog.title') : $t('milestone.reopen_dialog.title')"
      :content="isMilestoneArchived ? $t('milestone.close_dialog.content_part1') : $t('milestone.reopen_dialog.content')"
      :content_part2="isMilestoneArchived ? $t('milestone.close_dialog.content_part2') : ''"
      :btn_label="isMilestoneArchived ? $t('milestone.close_dialog.btn_label') : $t('milestone.reopen_dialog.btn_label')"
      :milestone_name="isMilestoneArchived ? selectedMilestone.name : ''"
      color="primary"
      @close="handleCloseClick"
      @handleConfirmClick="isMilestoneArchived ? handleConfirmOpenClick('close') : unArchiveMilestone()"
    />
    <MilestoneDeleteDialog
      v-model="showConfirmDeleteDialog"
      :title="$t('milestone.delete_dialog.title')"
      :content="$t('milestone.delete_dialog.content')"
      :btn_label="$t('milestone.close_dialog.btn_label')"
      :milestone_name="selectedMilestone.name"
      color="primary"
      @close="handleDeleteCloseClick"
      @handleConfirmClick="handleConfirmDeleteClick('close')"
    />
  </v-container>
</template>

<script>
  import MilestoneDeleteDialog from '@/components/Milestone/MilestoneDeleteDialog.vue';
  import MilestoneArchiveDialog from '@/components/Milestone/MilestoneArchiveDialog.vue';
  import MilestoneFilterChips from '../../../components/Milestone/MilestoneFilterChips.vue';
  import MilestoneTable from '@/components/Milestone/MilestoneTable.vue';
  import MilestoneHeader from '@/components/Milestone/MilestoneHeader.vue';
  import MilestoneFilter from '@/components/Milestone/MilestoneFilter.vue';
  import MilestoneClosedFilter from '@/components/Milestone/MilestoneClosedFilter.vue';
  import SettingsMenu from '@/components/Project/SettingsMenu.vue';
  import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue';
  import ArchivedEmptyState from '@/components/base/ArchivedEmptyState.vue';
  import SearchIcon from '@/assets/svg/search-icon.svg';
  import projectStatus from '@/mixins/projectStatus';
  import { useMilestoneIndex } from '@/composables/modules/milestone/index';
  import { useRelations } from '@/composables/utils/relations';

  export default {
    components: {
      MilestoneHeader,
      MilestoneTable,
      MilestoneArchiveDialog,
      MilestoneFilter,
      MilestoneFilterChips,
      MilestoneClosedFilter,
      MilestoneDeleteDialog,
      SettingsMenu,
      ActiveEmptyState,
      ArchivedEmptyState,
      SearchIcon,
    },
    mixins: [projectStatus],
    setup() {
      const composable = useMilestoneIndex();
      const { isRelationCached } = useRelations();
      return {
        ...composable,
        isRelationCached
      };
    },
  };
</script>

<style scoped>
  .h-full {
    height: 100%;
  }

  .custom-runtable {
    color: #344054 !important;
  }

  .custom-color-0c111d {
    color: #0c111d !important;
  }

  .custom-color-d0d5dd {
    color: #d0d5dd !important;
  }

  .custom-menu-item {
    min-height: 36px;
    height: 36px;
    max-height: 36px;
    align-items: center;
  }

  .custom-text-12 {
    font-size: 12px;
  }

  .custom-text-14 {
    font-size: 14px;
  }

  .custom-font-size {
    font-size: 18px;
  }

  .custom-chip-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .custom-chip-font-size {
    font-size: 16px;
  }

  .custom-result-title {
    font-size: 20px;
  }
</style>
