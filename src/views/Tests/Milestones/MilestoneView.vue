<template>
  <MilestoneTestView
    v-model="selectedMilestone"
    hide-action-btn
    :write-entity="writeEntity"
    @back="handleBack"
  />
</template>
<script>
import MilestoneTestView from '@/views/Tests/Milestones/TestActivities'
import { useMilestoneView } from '@/composables/modules/milestone/view';

export default{
  components:{
    MilestoneTestView,
  },
  setup() {
    const composable = useMilestoneView();

    // Initialize on mount
    composable.init();

    return {
      ...composable
    };
  },
}
</script>
