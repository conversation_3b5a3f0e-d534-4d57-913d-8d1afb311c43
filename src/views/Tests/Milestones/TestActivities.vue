<template>
  <div
    :class="{
      'pl-3': $route.name != 'MilestoneCreate',
    }"
  >
    <v-card
      class="py-4 px-6 my-3"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <div class="d-flex flex-column">
        <div class="d-flex flex-row align-center justify-space-between">
          <div class="d-flex flex-column">
            <div class="d-flex align-center">
              <button
                v-if="!skeletonLoaderState"
                class="d-flex flex-row align-center pointer mr-3"
                @click="$emit('back')"
              >
                <v-icon color="black">
                  mdi-arrow-left
                </v-icon>
              </button>
              <v-skeleton-loader
                v-else
                class="rounded-lg mr-3"
                height="24"
                width="24"
                type="button"
              />
              <v-tooltip
                v-if="!skeletonLoaderState"
                bottom
                max-width="430px"
                :disabled="value?.name?.length < 30"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <h2
                    class="custom__tooltip__title"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ value?.name }}
                  </h2>
                </template>
                <span>{{ value?.name }}</span>
              </v-tooltip>
              <v-skeleton-loader
                v-else
                class="rounded-lg mr-3"
                height="24"
                width="150"
                type="button"
              />
              <div
                v-if="!skeletonLoaderState"
                class="d-flex flex-row align-center justify-space-between ml-6"
              >
                <ProgressBar
                  :executions="generateExecutionsProgress(value?.customFields?.frequency)"
                  :percentage="value?.customFields?.progress"
                  :case-count="getObjectCount(value?.customFields?.frequency)"
                />
              </div>
              <v-skeleton-loader
                v-else
                class="rounded-lg"
                height="24"
                width="100"
                type="button"
              />
            </div>
            <div v-if="value.description">
              <v-tooltip
                v-if="!skeletonLoaderState"
                bottom
                max-width="430px"
                :disabled="value.description?.length < 61"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <p
                    class="edit-plan mb-0"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ value.description }}
                  </p>
                </template>
                <span>{{ value.description }}</span>
              </v-tooltip>
              <v-skeleton-loader
                v-else-if="skeletonLoaderState"
                class="mt-2"
                height="24"
                width="350"
                type="text"
              />
            </div>

            <!-- References Section in Header -->
            <div
              v-if="!skeletonLoaderState && value.references && value.references.length > 0"
              class="mt-3"
            >
              <h4 class="custom_field_heading mb-2">
                {{ $t('references') }}
              </h4>
              <div class="d-flex flex-wrap gap-2">
                <v-tooltip
                  v-for="(reference, index) in value.references"
                  :key="index"
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <div
                      v-bind="attrs"
                      class="reference-chip d-flex align-center justify-space-between w-full px-2 py-1 rounded-lg mr-2 mb-2"
                      style="background: #F2F4F7; border: 1px solid #E4E7EC; cursor: pointer; max-width: 200px;"
                      v-on="on"
                      @click="window.open(reference.externalLink, '_blank')"
                    >
                      <div
                        class="d-flex align-center"
                        style="min-width: 0; flex: 1;"
                      >
                        <span
                          class="fs-12px text-theme-label mr-1 text-truncate"
                          style="min-width: 0; flex: 1; font-weight: 500;"
                        >{{ reference.name }}</span>
                      </div>
                      <a
                        :href="reference.externalLink"
                        target="_blank"
                        class="reference-link"
                        style="text-decoration: none; color: inherit;"
                        @click.stop
                      >
                        <v-icon
                          size="12"
                          class="text-theme-secondary"
                        >
                          mdi-arrow-top-right
                        </v-icon>
                      </a>
                    </div>
                  </template>
                  <span>{{ reference.name }}</span>
                </v-tooltip>
              </div>
            </div>
          </div>
          <div>
            <template v-if="!skeletonLoaderState">
              <v-btn
                v-if="!isCreate"
                depressed
                color="primary"
                height="40px"
                class="text-capitalize btn-theme rounded-lg"
                @click="handleCloseClick"
              >
                {{ archiveButtonLabel }}
              </v-btn>
            </template>
            <v-skeleton-loader
              v-else
              class="rounded-lg primary"
              height="40"
              width="125"
              type="button"
            />
          </div>
        </div>
        <v-sheet
          class="white"
          color="#F2F4F7"
          rounded="lg"
          :outlined="true"
        >
          <div class="d-flex align-center justify-end">
            <template v-if="!skeletonLoaderState">
              <v-tooltip bottom>
                <template #activator="{ on, attrs }">
                  <v-btn
                    icon
                    depressed
                    :ripple="false"
                    plain
                    v-bind="attrs"
                    v-on="on"
                    @click="toggleView('list')"
                  >
                    <ViewListSelectedIcon v-if="!listView" />
                    <ViewListIcon v-else />
                  </v-btn>
                </template>
                <span>{{ $t('List View') }}</span>
              </v-tooltip>
            </template>
            <v-skeleton-loader
              v-else
              class="rounded-lg"
              height="35"
              width="35"
              type="button"
            />
            <template v-if="!skeletonLoaderState">
              <v-tooltip bottom>
                <template #activator="{ on, attrs }">
                  <v-btn
                    icon
                    depressed
                    :ripple="false"
                    plain
                    v-bind="attrs"
                    v-on="on"
                    @click="toggleView('dashboard')"
                  >
                    <ViewDashboardIcon v-if="!listView" />
                    <ViewDashboardSelectedIcon v-else />
                  </v-btn>
                </template>
                <span>{{ $t('Dashboard View') }}</span>
              </v-tooltip>
            </template>
            <v-skeleton-loader
              v-else
              class="rounded-lg"
              height="35"
              width="35"
              type="button"
            />
          </div>
        </v-sheet>
        <template v-if="isListView">
          <div
            v-if="!skeletonLoaderState"
            class="mt-5 d-flex"
          >
            <v-chip
              :class="{ 'blue--text': tableTestType === 'plans' }"
              width="200px"
              :color="tableTestType === 'plans' ? 'blue-light' : 'gray-light'"
              label
              @click="changeTestType('plans')"
            >
              <div
                class="px-2"
                :class="{ 'fw-semibold': tableTestType === 'plans', 'font-weight-medium': tableTestType !== 'plans' }"
              >
                {{ $t('testPlans') }} <span class="ml-1">{{ getActivePlanItemsCount }}</span>
              </div>
            </v-chip>
            <div class="ml-2">
              <v-chip
                :class="{ 'blue--text': tableTestType === 'runs' }"
                width="200px"
                :color="tableTestType === 'runs' ? 'blue-light' : 'gray-light'"
                label
                @click="changeTestType('runs')"
              >
                <div
                  class="px-2"
                  :class="{ 'fw-semibold': tableTestType === 'runs', 'font-weight-medium': tableTestType !== 'runs' }"
                >
                  {{ $t('testRuns') }} <span class="ml-1">{{ getActiveRunItemsCount }}</span>
                </div>
              </v-chip>
            </div>
          </div>
          <div
            v-else
            class="mt-5 d-flex"
          >
            <v-skeleton-loader
              class="rounded-sm d-flex gap-2 chip-primary"
              height="32"
              width="200"
              type="button@2"
            />
          </div>
        </template>
      </div>
    </v-card>

    <v-container
      v-if="isListView"
      class="d-flex flex-column align-self-stretch"
      fluid
    >
      <div class="row">
        <div
          v-if="!isTableTypePlans"
          class="pa-0"
          :style="{ width: isProjectMenuCollapsed ? '5%' : '15%' }"
        >
          <TestPlanMiniSection
            :is-collapsed.sync="isProjectMenuCollapsed"
            :selected-plan-id="selectedPlanId"
            @onPlanActiveId="onClickPlanActive"  
          />
        </div>
        <div
          class="pa-0"
          :style="{ width: !isTableTypePlans ? isProjectMenuCollapsed ? '95%' : '85%' : '100%' }"
        >
          <div class="container pa-6 white align-start card rounded-lg container--fluid app-height-global">
            <v-row class="align-center">
              <v-col
                cols="6"
                sm="6"
                class="py-0"
              >
                <div class="d-flex flex-row justify-start align-center">
                  <v-responsive
                    v-if="!skeletonLoaderState"
                    class="ma-0"
                    max-width="344"
                  >
                    <v-text-field
                      v-model="searchFilter"
                      class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
                      :placeholder="$t('searchByName')"
                      height="40"
                      clear-icon="mdi-close-circle"
                      clearable
                      background-color="#F9F9FB"
                      hide-details
                    >
                      <template #prepend-inner>
                        <SearchIcon />
                      </template>
                    </v-text-field>
                  </v-responsive>
                  <v-skeleton-loader
                    v-else
                    class="rounded-lg mr-3"
                    width="344"
                    height="40"
                    type="button"
                  />

                  <PlanFilter
                    v-if="singularTestType == 'plan'"
                    :configurations="configurations"
                    :view-type="isTableTypePlans"
                    :milestones="activeMilestones"
                    :tags="tags"
                    @applyFilters="applyPlanFilters"
                  />
                  <RunFilter
                    v-else
                    :configurations="configurations"
                    :milestones="activeMilestones"
                    :tags="tags"
                    @applyFilters="applyRunFilters"
                  />
                </div>
              </v-col>
              <v-col
                cols="6"
                sm="6"
                class="d-flex justify-end"
              >
                <SettingsMenu
                  :key="singularTestType"
                  :view-type="isTableTypePlans"
                  :table-type="singularTestType"
                />
              </v-col>
            </v-row>
            <template>
              <MilestoneTestPlanTable
                v-if="tableTestType === 'plans'"
                v-model="value.testPlans"
                :value="value.testPlans"
                :plans-data="filteredTestPlans"
                :table-filter="tableFilter"
                :filtered-headers="filteredPlanHeaders"
                :filtered-items="filteredTestPlans"
                :total-items="planTotalRows"
                :current-page="planCurrentPage"
                :items-per-page="planPerPage"
                :relations-loading="relationsLoading"
                :relation-loading-states="relationLoadingStates"
                @row-click="onTestPlanRowClick"
                @input="handleTestPlans"
                @update-pagination="onUpdatePlanPagination"
              />

              <MilestoneTestRunTable
                v-if="tableTestType === 'runs'"
                :value="value.testRuns"
                :runs-data="filteredTestRuns"
                :table-filter="tableFilter"
                :filtered-headers="filteredRunHeaders"
                :filtered-items="filteredTestRuns"
                :total-items="runTotalRows"
                :current-page="runCurrentPage"
                :items-per-page="runPerPage"
                :relations-loading="relationsLoading"
                :relation-loading-states="relationLoadingStates"
                @row-click="onTestRunRowClick"
                @input="handleTestRuns"
                @update-pagination="onUpdateRunPagination"
              />
            </template>
            <v-row
              v-if="!hideActionBtn && !skeletonLoaderState"
              justify="end"
              class="mt-4"
            >
              <v-col cols="12">
                <v-flex class="mt-6 d-sm-flex justify-end">
                  <v-btn
                    :disabled="!writeEntity"
                    background-color="#F2F4F7"
                    height="40px"
                    depressed
                    :class="{ 'text-capitalize fw-semibold rounded-lg black--text mr-4 mt-2': true, 'disabled-action': isProjectArchived }"
                    :width="$vuetify.breakpoint.smAndDown ? '100%' : '150xp'"
                    @click="openCreateTest()"
                  >
                    <v-icon
                      class="mr-1"
                      size="16"
                    >
                      mdi-plus
                    </v-icon> {{ isTableTypePlans ? $t('Create new test plan') : $t('Create new test run') }}
                  </v-btn>
                  <slot name="actionButton" />
                </v-flex>
              </v-col>
            </v-row>
          </div>
        </div>
      </div>

      <MilestoneArchiveDialog
        v-model="showConfirmOpenDialog"
        :title="$t('milestone.close_dialog.title')"
        :content="$t('milestone.close_dialog.content_part1')"
        :content_part2="$t('milestone.close_dialog.content_part2')"
        :btn_label="activeMilestoneFilter == 'open' ? $t('milestone.close_dialog.btn_label') : $t('milestone.reopen_dialog.btn_label')"
        :milestone_name="value.name"
        color="primary"
        @close="handleCloseClick"
        @handleConfirmClick="handleMilestoneConfirmation()"
      />
    </v-container>
    <Dashboard
      v-if="isDashboardView"
      :style="{
        'margin-top': '10px',
      }"
      :is-ready="true"
      :show-archived="false"
    />
  </div>
</template>

<script>
import MilestoneTestRunTable from '@/components/Milestone/MilestoneTestRunTable.vue';
import MilestoneTestPlanTable from '@/components/Milestone/MilestoneTestPlanTable.vue';
import MilestoneArchiveDialog from '@/components/Milestone/MilestoneArchiveDialog.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import RunFilter from '@/components/TestRuns/RunFilter.vue';
import PlanFilter from '@/components/TestPlans/PlanFilter'
import SearchIcon from '@/assets/svg/search-icon.svg';
import ProgressBar from '@/components/base/ProgressBar.vue';
import TestPlanMiniSection from '@/components/base/TestPlanMiniSection.vue';
import ViewListSelectedIcon from '@/assets/svg/view-list-selected.svg';
import ViewListIcon from '@/assets/svg/view-list.svg';
import ViewDashboardIcon from '@/assets/svg/left-menu/dashboard.svg';
import ViewDashboardSelectedIcon from '@/assets/svg/view-dashboard-selected.svg';
import Dashboard from "@/views/Dashboard";
import { useMilestoneTestActivities } from '@/composables/modules/milestone/testActivities';
import projectStatus from '@/mixins/projectStatus';
import colorPreferencesMixin from '@/mixins/colorPreferences';

export default {
  name: 'MilestoneTestActivitiesView',
  components: {
    MilestoneTestRunTable,
    MilestoneTestPlanTable,
    SearchIcon,
    SettingsMenu,
    RunFilter,
    PlanFilter,
    ProgressBar,
    TestPlanMiniSection,
    MilestoneArchiveDialog,
    ViewDashboardIcon,
    ViewDashboardSelectedIcon,
    ViewListSelectedIcon,
    ViewListIcon,
    Dashboard
  },
  mixins: [projectStatus, colorPreferencesMixin],
  props: {
    value: {
      type: Object,
    },
    hideActionBtn: {
      type: Boolean,
      default: false
    },
    writeEntity: {
      type: Boolean,
      default: false
    },
    isCreate: {
      type: Boolean,
      default: false
    }
  },
  emits: ['input', 'back'],
  setup(props) {
    const composable = useMilestoneTestActivities(props);

    // Initialize the composable
    composable.init();

    return {
      ...composable
    };
  }
};
</script>

<style lang="scss" scoped>
.back-to-projects {
  display: flex;
  cursor: pointer;
  width: max-content;
}

.search_input {
  width: 100%;

  @media screen and (min-width: 600px) {
    width: 300px;
  }
}

.project-logo {
  border-radius: 50%;
  border: 2px dashed grey;
  width: 150px;
  height: 150px;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
  cursor: pointer;
  transition: border-color 0.3s;
  background-size: cover;
  background-position: center;
  position: relative;
}

.project-logo:hover {
  border-color: #2196f3;
}

.hovering .edit-icon,
.hovering .delete-icon {
  display: block;
}

.edit-icon,
.delete-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateX(-30px);
  display: none;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 10px;
  cursor: pointer;
}

.delete-icon {
  margin-left: 60px;
}

.project-logo:hover .edit-icon,
.project-logo:hover .delete-icon {
  display: block;
}

.reference-chip {
  transition: all 0.2s ease;
  cursor: pointer;
}

.reference-chip:hover {
  background: #E4E7EC !important;
  transform: translateY(-1px);
}

.reference-link {
  text-decoration: none;
  color: inherit;
}

.reference-link:hover {
  opacity: 0.8;
}

.custom_field_heading {
  color: #667085;
  font-weight: 400;
  font-size: 13px;
  margin: 12px 0 4px 0px;
}
</style>
