<template>
  <div
    id="case-index-container"
    class="h-full d-flex flex-column justify-start font-inter align-self-stretch pt-3"
    fluid
  >
    <AddCases
      v-if="showCases"
      v-model="selectedCases"
      :back-title="$t('testruns.create_testrun.rerunTestRun')"
      @back="onBackShowCases"
      @close="onBackShowCases"
    >
      <template #action>
        <v-btn
          color="primary"
          depressed
          class="f-color-white ml-2 btn-theme text-capitalize"
          :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
          height="38px"
          :loading="isLoading"
          @click="createTestRuns"
        >
          {{ $t('save') }}
        </v-btn>
      </template>
    </AddCases>
    <template v-else>
      <RunHeader
        is-duplicate
        :title="$t('testRuns')"
        :action-text="$t('testruns.createTestRun')"
        :active_label="$t('testruns.active')"
        :archived_label="$t('testruns.archived')"
        :btn_show="!isFilterArchived"
        :filter="filter"
        :ongoing-item-count="activeItemCount"
        :archived-item-count="archivedItemCount"
        @update-filter="updateFilter"
      />
    
      <template>  
        <v-container
          class="d-flex flex-column align-self-stretch"
          fluid
        >
          <div class="row">
            <div
              class="pa-0"
              :style="{ width: isProjectMenuCollapsed ? '5%' : '15%' }"
            >
              <div
                class="white card d-flex flex-column justify-space-between pa-3 rounded-lg mr-3 sticky-on-scroll"
              >
                <v-list 
                  v-if="isProjectMenuCollapsed"
                  nav
                  class="pa-0"
                >
                  <v-list-item-group color="primary">
                    <v-list-item 
                      class="d-flex justify-center" 
                      :ripple="false"
                      @click="searchCollapsedMenu"
                    >
                      <v-list-item-icon class="justify-center mx-0">
                        <SearchIcon />
                      </v-list-item-icon>
                    </v-list-item>
                    <v-list-item
                      class="d-flex justify-center"
                      :ripple="false"
                      @click="unlinkedCollapsedMenu"
                    >
                      <v-list-item-icon class="justify-center mx-0">
                        <UnlinkedIcon24 />
                      </v-list-item-icon>
                    </v-list-item>
                    <v-list-item
                      class="d-flex justify-center"
                      :ripple="false"
                      @click="linkedCollapsedMenu"
                    >
                      <v-list-item-icon class="justify-center mx-0">
                        <LinkedIcon24 />
                      </v-list-item-icon>
                    </v-list-item>
                  </v-list-item-group>
                </v-list>  
                <div
                  v-if="!isProjectMenuCollapsed"
                  class="d-flex flex-column"
                >
                  <v-text-field
                    v-if="!skeletonLoaderState"
                    ref="searchField"
                    :placeholder="$t('search')"
                    background-color="#F9F9FB"
                    class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0 mb-3 flex-inherit"
                    height="38"
                    dense
                    hide-details
                  >
                    <template #prepend-inner>
                      <SearchIcon />
                    </template>
                  </v-text-field>
                  <v-skeleton-loader
                    v-else
                    class="rounded-lg mb-3"
                    height="38"
                    width="100%"
                    type="button"
                  />
                  <v-btn
                    v-if="!skeletonLoaderState"
                    text
                    :color="displayedRuns == 'all' ? '#0C2FF3' : '#667085'"
                    :class="displayedRuns == 'all' ? 'bg-theme-primary-light' : 'bg-gray-theme'"
                    class="text-capitalize rounded-lg justify-start mb-3"
                    width="100%"
                    @click="displayedRuns = 'all'"
                  >
                    {{ $t('testruns.all') }}
                  </v-btn>
                  <v-skeleton-loader
                    v-else
                    class="rounded-lg mb-3 primary"
                    height="38"
                    width="100%"
                    type="button"
                  />

                  <v-btn
                    v-if="!skeletonLoaderState"
                    text
                    :color="displayedRuns == 'unlinked' ? '#0C2FF3' : '#667085'"
                    :class="displayedRuns == 'unlinked' ? 'bg-theme-primary-light' : 'bg-gray-theme'"
                    class="text-capitalize rounded-lg bg-gray-theme justify-start mb-3"
                    width="100%"
                    @click="displayedRuns = 'unlinked'"
                  >
                    <div class="d-flex align-center">
                      <div class="mr-2">
                        <UnlinkedIcon />
                      </div>
                      <span>{{ $t('testruns.unlinked') }} 0</span>
                    </div>
                  </v-btn>
                  <v-btn
                    v-if="!skeletonLoaderState"
                    text
                    class="text-capitalize rounded-lg bg-gray-theme justify-start mb-2"
                    width="100%"
                    :color="Number.isInteger(displayedRuns) ? '#0C2FF3' : '#667085'"
                    :class="Number.isInteger(displayedRuns) ? 'bg-theme-primary-light' : 'bg-gray-theme'"
                    @click="onToPlanExpanded"
                  >
                    <div class="d-flex justify-space-between w-full">
                      <div class="d-flex align-center">
                        <div class="mt-1 mr-2">
                          <LinkedIcon />
                        </div>
                        <span>{{ $t('testruns.toPlans') }} {{ getActivePlansCount }}</span>
                      </div>
                      <div>
                        <v-icon>
                          {{ isToPlanExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }} 
                        </v-icon>
                      </div>
                    </div>
                  </v-btn>

                  <template v-if="!skeletonLoaderState">
                    <div
                      v-if="isToPlanExpanded"
                      class="plan-list-wrapper"
                    >
                      <div
                        v-for="(plan, index) in getActivePlans"
                        :key="index"
                      >
                        <v-tooltip
                          bottom
                          left
                          max-width="200px"
                          :disabled="!plan.name || plan.name.length < 15"
                          content-class="tooltip-theme"
                        >
                          <template #activator="{ on, attrs }">
                            <v-btn
                              text
                              :color="Number.isInteger(displayedRuns) && displayedRuns == plan.uid ? '#0C2FF3' : '#667085'"
                              :class="Number.isInteger(displayedRuns) && displayedRuns == plan.uid ? 'bg-theme-primary-light' : 'bg-gray-theme'"
                              class="text-capitalize btn-full font-weight-regular rounded-lg justify-start mb-2"
                              width="100%"
                              v-bind="attrs"
                              @click="displayedRuns = plan.uid"
                              v-on="on"
                            >
                              <div class="d-flex justify-between align-center w-full">
                                <div class="text-truncate">
                                  {{ plan.name || '' }}
                                </div>
                                <div>({{ (plan.runs && plan.runs.length) || 0 }})</div>
                              </div>
                            </v-btn>
                          </template>
                          <span>{{ plan.name || '' }}</span>
                        </v-tooltip>
                      </div>
                    </div>
                    <div
                      v-else
                      class="plan-list-wrapper"
                    />
                  </template>
                  <template v-else>
                    <div class="plan-list-wrapper">
                      <v-skeleton-loader
                        v-for="n in 5"
                        :key="n"
                        class="rounded-lg mb-3"
                        height="38"
                        width="100%"
                        type="button"
                      />
                    </div>
                  </template>
                </div>
                <div
                  v-if="!isProjectMenuCollapsed"
                  class="sticky-scroll"
                >
                  <div class="text-left">
                    <v-label
                      v-if="!skeletonLoaderState"
                      class="text-left fs-14px text-theme-label font-weight-medium"
                    >
                      {{ $t("createQuickPlan") }} 
                    </v-label>
                    <v-skeleton-loader
                      v-else
                      class="rounded-lg mb-1 w-75"
                      height="16"
                      type="text"
                    />
                    <v-text-field
                      v-if="!skeletonLoaderState"
                      v-model="planName"
                      :placeholder="$t('enterName')"
                      height="38"
                      background-color="#F9F9FB"
                      :class="{
                        'field-theme mt-0 pt-1': true,
                        'disabled-action': isProjectArchived
                      }"
                    />
                    <v-skeleton-loader
                      v-else
                      class="rounded-lg mb-3"
                      height="38"
                      width="100%"
                      type="button"
                    />
                  </div>
                  <v-btn
                    v-if="!skeletonLoaderState"
                    type="submit"
                    block
                    color="primary"
                    :depressed="true"
                    :class="{
                      'btn-theme': true,
                      'disabled-action': isProjectArchived || createButtonLoading
                    }"
                    width="100%"
                    height="40"
                    :loading="createButtonLoading"
                    @click="createTestPlan"
                  >
                    {{ $t("create") }}
                  </v-btn>
                  <v-skeleton-loader
                    v-else
                    class="rounded-lg primary"
                    height="40"
                    width="100%"
                    type="button"
                  />
                </div>

                <div
                  class="collapse-btn btn-runs-sticky"
                  @click="toggleProjectMenu"
                >
                  <v-icon
                    class="collapse-icon"
                    color="#0C2FF3"
                  >
                    {{ isProjectMenuCollapsed ? 'mdi-arrow-right-bottom' : 'mdi-arrow-left-bottom'
                    }}
                  </v-icon>
                  <span
                    v-if="!isProjectMenuCollapsed"
                    class="collapse-text"
                  >
                    {{ $t('collapse') }}
                  </span>
                </div>
              </div>
            </div>
            <div
              class="pa-0"
              :style="{ width: isProjectMenuCollapsed ? '95%' : '85%' }"
            >
              <template v-if="activeItemCount == 0 && !appliedFilters && filter != 'archived' && !skeletonLoaderState">
                <div class="mb-0 white rounded-lg mx-0 project-placeholder-height d-flex justify-center align-center">
                  <ActiveEmptyState
                    :image-src="require('@/assets/png/auth-banner.png')"
                    :title="$t('testruns.empty_state.title')"
                    :button-text="$t('testruns.createTestRun')"
                    :button-route="{ name: 'TestRunCreate' }"
                    button-color="primary"
                    :is-project-archived="isProjectArchived"
                  >
                    <template #description>
                      <p class="mb-0 mt-3">
                        {{ $t('testruns.empty_state.description_part1') }}
                      </p>
                      <p class="ma-0">
                        {{ $t('testruns.empty_state.description_part2') }}
                      </p>
                      <p class="ma-0">
                        {{ $t('testruns.empty_state.description_part3') }}
                      </p>
                    </template>
                  </ActiveEmptyState>
                </div>
              </template>

              <template v-else-if="archivedItemCount == 0 && !appliedFilters && filter != 'ongoing' && !skeletonLoaderState">
                <div class=" mb-0 white rounded-lg mx-0 project-placeholder-height d-flex justify-center align-center">
                  <ArchivedEmptyState
                    :image-src="require('@/assets/png/auth-banner.png')"
                    :title="$t('archived_empty_state.title', { name: $t('plans.testRuns.testRuns') })"
                  >
                    <template #description>
                      <p class="mb-0 mt-3">
                        {{ $t('archived_empty_state.description.part1', { name: $t('plans.testRuns.testRuns') }) }}
                      </p>
                      <p class="mb-0">
                        {{ $t('projects.archived_empty_state.description.part2') }}
                      </p>
                    </template>
                  </ArchivedEmptyState>
                </div>
              </template>
              <div 
                v-else
                class="container pa-6 white align-start card rounded-lg container--fluid app-height-global"
              >
                <v-row class="align-center">
                  <v-col
                    cols="6"
                    sm="6"
                  >
                    <div class="d-flex flex-row justify-start align-center">
                      <v-responsive
                        v-if="!skeletonLoaderState"
                        class="ma-0"
                        max-width="344"
                      >
                        <v-text-field
                          v-model="searchFilter"
                          :loading="loading"
                          class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
                          :placeholder="$t('searchByName')"
                          height="40"
                          clear-icon="mdi-close-circle"
                          clearable
                          background-color="#F9F9FB"
                          hide-details
                        >
                          <template #prepend-inner>
                            <SearchIcon />
                          </template>
                        </v-text-field> 
                      </v-responsive>
                      <v-skeleton-loader
                        v-else
                        class="rounded-lg mr-3"
                        width="344"
                        height="40"
                        type="button"
                      />
                      <RunFilter 
                        :configurations="configurations" 
                        :milestones="activeMilestones"  
                        :tags="tags"
                        @applyFilters="applyFilters"
                      />
                    </div>
                  </v-col>
                  <v-col
                    cols="6"
                    sm="6"
                    class="d-flex justify-end"
                  >
                    <SettingsMenu 
                      table-type="run" 
                    />
                  </v-col>
                </v-row>
                <v-row>
                  <v-col
                    cols="12"
                    class="fill-height pt-1"
                  >
                    <run-table
                      :filtered-headers="filteredHeaders"
                      :filtered-items="filteredRuns"
                      :item-key="itemKey"
                      :row-class="rowClass"
                      :clear-selection="clearSelection"
                      @edit-item="editItem"
                      @select-row="handleRowClick"
                      @select-item="setselected"
                      @archive-item="confirmArchiveRun"
                      @unarchive-item="confirmUnArchiveRun"
                      @delete-item="confirmDeleteRun"
                      @open-milestone-dialog="handleAddToMilestoneDialog"
                      @open-plan-dialog="handleAddToTestPlansDialog"
                      @open-duplicate-dialog="handleDuplicateAndApplyConfigDialog"
                    />
                  </v-col>
                  <v-row
                    v-if="isSelected"
                    class="fill-height action-btn-wrapper"
                  >
                    <v-col cols="12">
                      <v-flex class="d-sm-flex justify-end">
                        <v-btn
                          color="primary"
                          depressed
                          class="rounded-lg btn-theme text-capitalize"
                          width="100%"
                          max-width="141px"
                          :disabled="!hasOneSelectedItem"
                          height="40px"
                          @click="onClickDuplicate"
                        >
                          {{ $t('testruns.rerun') }}
                        </v-btn>
                      </v-flex>
                    </v-col>
                  </v-row>
                </v-row>
              </div>
            </div>  
          </div>
        </v-container>
      </template>
    </template>
    <AddToMilestoneDialog
      v-if="addToMilestoneDialog"
      :value="addToMilestoneDialog"
      :milestones="activeMilestones"
      @close="onCloseAddToMilestoneDialog"
      @handleAddMilestone="handleAddMilestone"
    />
    <AddToTestPlansDialog
      v-if="addToTestPlansDialog"
      :value="addToTestPlansDialog"
      :plans="getActivePlans"
      @close="onCloseAddToTestPlansDialog" 
      @handleAddTestPlan="handleConfirmTestPlanClick"
    />
    <DuplicateAndApplyConfigDialog
      v-if="duplicateAndApplyConfigDialog"
      :value="duplicateAndApplyConfigDialog"
      :configurations="configurations"
      @close="onCloseDuplicateAndApplyConfigDialog"
      @handleDuplicateAndApplyConfig="handleDuplicateAndApplyConfig"
    />
    <RunDiscardDialog
      v-model="showConfirmDialog"
      :title="confirmDialogTitle"
      :content="confirmDialogContent"
      :content_part2="confirmDialogContentPartTwo"
      :run_name="confirmDialogRunName"
      :items="confirmDialogItems"
      :type="confirmType"
      :btn_label="confirmDialogBtnLabel"
      :color="confirmDialogBtnColor"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmBtnClick"
    />
  </div>
</template>

<script>
import RunDiscardDialog from '@/components/TestRuns/RunDiscardDialog.vue';
import RunFilter from '@/components/TestRuns/RunFilter.vue';
import RunHeader from '@/components/TestRuns/RunHeader.vue';
import AddToMilestoneDialog from '@/components/TestRuns/AddToMilestoneDialog.vue';
import AddToTestPlansDialog from '@/components/TestRuns/AddToTestPlansDialog.vue';
import DuplicateAndApplyConfigDialog from '@/components/TestRuns/DuplicateAndApplyConfigDialog.vue';
import RunTable from '@/components/TestRuns/RunTable.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import SearchIcon from '@/assets/svg/search-icon.svg';
import UnlinkedIcon from '@/assets/svg/unlinked.svg';
import UnlinkedIcon24 from '@/assets/svg/unlinked24px.svg';
import LinkedIcon from '@/assets/svg/linked.svg';
import LinkedIcon24 from '@/assets/svg/linked24px.svg';
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue';
import ArchivedEmptyState from '@/components/base/ArchivedEmptyState.vue';
import AddCases from '@/views/Tests/Runs/Create/AddCases';
import { useRunDuplicate } from '@/composables/modules/testRun/duplicate';
import { handleNetworkStatusError } from '@/mixins/redirect';
import projectStatus from '@/mixins/projectStatus';

export default {
  components: {
    RunTable,
    RunHeader,
    RunFilter,
    RunDiscardDialog,
    AddToMilestoneDialog,
    AddToTestPlansDialog,
    DuplicateAndApplyConfigDialog,
    SearchIcon,
    SettingsMenu,
    AddCases,
    UnlinkedIcon,
    LinkedIcon,
    ActiveEmptyState,
    ArchivedEmptyState,
    LinkedIcon24,
    UnlinkedIcon24,
  },
  mixins: [handleNetworkStatusError, projectStatus],
  props: {
    runViewType: {
      type: String,
      default: ''
    }
  },
  setup() {
    const composable = useRunDuplicate();

    // Initialize the composable
    composable.init();

    return {
      ...composable
    };
  }
};
</script>
<style scoped>
.custom-runtable {
  color: #344054 !important;
}

.custom-color-0c111d {
  color: #0C111D !important;
}

.custom-color-d0d5dd {
  color: #D0D5DD !important;
}

.custom-color-061AAE {
  color: #061AAE !important;
}
.custom-menu-item {
  min-height: 36px;
  height: 36px;
  max-height: 36px;
  align-items: center;
}

.custom-text-12 {
  font-size: 12px;
}

.custom-text-14 {
  font-size: 14px;
}

.h-full {
  height: 100%;
  min-height: 100%;
}
.custom-swal-popup {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.custom-swal-title {
  text-align: left;
  width: 100%;
}
.custom-swal-content {
  text-align: left;
  width: 100%;
}
.custom-swal-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
.custom-swal-confirm-button,
.custom-swal-cancel-button {
  margin-left: 10px;
}
.action-btn-wrapper {
  position: sticky;
    bottom: 0;
    background-color: white;
    align-items: flex-end;
    display: flex;
    justify-content: flex-end;
    z-index: 8;
}
.sticky-on-scroll {
  position: -webkit-sticky;
  position: sticky;
  top: 12px;
  height: calc(100vh - 6px);
}
.sticky-scroll {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  padding-bottom: 5rem;
  background: #fff;
    z-index: 1;
    padding-top: 1rem;
}
.plan-list-wrapper {
  height: calc(100vh - 28rem);
  overflow: auto;
  scrollbar-width: thin;
}
.btn-runs-sticky {
  position: -webkit-sticky;
  position: sticky;
  background: #fff;
  justify-content: flex-start;
  z-index: 9;
}
</style>
