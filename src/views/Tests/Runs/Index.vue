<template>
  <div
    id="case-index-container"
    class="h-full d-flex flex-column justify-start font-inter align-self-stretch pl-3 pt-3"
    fluid
  >
    <RunHeader
      :is-duplicate="runViewType == 'Duplicate'"
      :title="$t('testRuns')"
      :action-text="$t('testruns.createTestRun')"
      :active_label="$t('testruns.active')"
      :archived_label="$t('testruns.archived')"
      :btn_show="!isFilterArchived"
      :filter="filter"
      :write-entity="writeEntity"
      :ongoing-item-count="activeCount"
      :archived-item-count="archivedCount"
      @update-filter="updateFilter"
    />
    <div
      class="d-flex flex-column align-self-stretch"
      :style="{
        height: 'calc(100vh)',
      }"
    >
      <div class="d-flex flex-row align-items-start runs-layout-container">
        <div
          class="pa-0 runs-sidebar-section"
          :style="{ width: isProjectMenuCollapsed ? '5%' : '15%' }"
        >
          <TestPlanMiniSection
            ref="testPlanMiniSection"
            :is-collapsed.sync="isProjectMenuCollapsed"
            :is-project-archived="isFilterArchived || isProjectArchived"
            :selected-plan-id="displayedRuns"
            @onPlanActiveId="onClickPlanActive"
          />
        </div>
        <div
          class="pa-0 runs-main-section"
          :style="{ width: isProjectMenuCollapsed ? '95%' : '85%' }"
        >
          <template
            v-if="loadingState"
          >
            <div
              class="mb-0 white rounded-lg mx-0 project-placeholder-height d-flex justify-center align-center"
            >
              <ActiveEmptyState
                :write-entity="writeEntity"
                :image-src="require('@/assets/png/auth-banner.png')"
                :title="$t('testruns.empty_state.title')"
                :button-text="$t('testruns.createTestRun')"
                :button-route="{ name: 'TestRunCreate' }"
                button-color="primary"
                :is-project-archived="isFilterArchived"
              >
                <template #description>
                  <p class="mb-0 mt-3">
                    {{ $t("testruns.empty_state.description_part1") }}
                  </p>
                  <p class="ma-0">
                    {{ $t("testruns.empty_state.description_part2") }}
                  </p>
                  <p class="ma-0">
                    {{ $t("testruns.empty_state.description_part3") }}
                  </p>
                </template>
              </ActiveEmptyState>
            </div>
          </template>

          <template
            v-else-if="
              filteredRuns.length == 0 &&
                !Object.keys(appliedFilters).length &&
                filter != 'ongoing' &&
                !skeletonLoaderState
            "
          >
            <div
              class="mb-0 white rounded-lg mx-0 project-placeholder-height d-flex justify-center align-center"
            >
              <ArchivedEmptyState
                :image-src="require('@/assets/png/auth-banner.png')"
                :title="
                  $t('archived_empty_state.title', {
                    name: $t('plans.testRuns.testRuns'),
                  })
                "
              >
                <template #description>
                  <p class="mb-0 mt-3">
                    {{
                      $t("archived_empty_state.description.part1", {
                        name: $t("plans.testRuns.testRuns"),
                      })
                    }}
                  </p>
                  <p class="mb-0">
                    {{ $t("projects.archived_empty_state.description.part2") }}
                  </p>
                </template>
              </ArchivedEmptyState>
            </div>
          </template>
          <div
            v-else
            class="container pa-6 pb-0 white align-start card rounded-lg container--fluid"
          >
            <v-row class="align-center">
              <v-col
                cols="6"
                sm="6"
              >
                <div class="d-flex flex-row justify-start align-center">
                  <v-responsive
                    class="ma-0"
                    max-width="344"
                  >
                    <v-text-field
                      v-model="searchFilter"
                      :loading="loading"
                      class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
                      :placeholder="$t('searchByName')"
                      height="40"
                      clear-icon="mdi-close-circle"
                      clearable
                      background-color="#F9F9FB"
                      hide-details
                    >
                      <template #prepend-inner>
                        <SearchIcon />
                      </template>
                    </v-text-field>
                  </v-responsive>
                  <RunFilter
                    :configurations="configurations"
                    :milestones="activeMilestones"
                    :current-filters="appliedFilters || {}"
                    :tags="tags"
                    @applyFilters="applyFilters"
                  />
                </div>
              </v-col>
              <v-col
                cols="6"
                sm="6"
                class="d-flex justify-end"
              >
                <SettingsMenu table-type="run" />
              </v-col>
              <v-col
                cols="12"
                md="auto"
                class="d-flex align-center"
              >
                <FilterChips
                  :filters="appliedFilters || {}"
                  :results-count="filteredRuns.length"
                  @update-filters="handleFilters"
                  @clear-filters="clearFilters"
                />
              </v-col>
            </v-row>
            <v-row>
              <v-col
                cols="12"
                class="pt-1 pr-0"
              >
                <RunTable
                  :filtered-headers="filteredHeaders"
                  :filtered-items="filteredRuns"
                  :item-key="itemKey"
                  :row-class="rowClass"
                  :clear-selection="clearSelection"
                  :selected-runs="selecteditems"
                  :write-entity="writeEntity"
                  :delete-entity="deleteEntity"
                  :is-run-project-archived="isFilterArchived"
                  :total-items="totalRows"
                  :current-page="currentPage"
                  :items-per-page="perPage"
                  :relations-loading="relationsLoading"
                  :relation-loading-states="relationLoadingStates"
                  :table-loading="tableLoading"
                  :sort-by="sortBy"
                  :sort-desc="sortDesc"
                  :has-initially-loaded="hasInitiallyLoaded"
                  @edit-item="editItem"
                  @select-row="handleRowClick"
                  @select-item="setselected"
                  @archive-item="confirmArchiveRun"
                  @unarchive-item="confirmUnArchiveRun"
                  @delete-item="confirmDeleteRun"
                  @open-milestone-dialog="handleAddToMilestoneDialog"
                  @open-plan-dialog="handleAddToTestPlansDialog"
                  @open-duplicate-dialog="handleDuplicateAndApplyConfigDialog"
                  @update-pagination="onUpdatePagination"
                />
              </v-col>
              <v-row
                v-if="isSelected"
                class="fill-height action-btn-wrapper"
              >
                <v-col cols="12">
                  <v-flex class="d-sm-flex justify-end">
                    <v-menu
                      v-if="!isDuplicateRunViewType"
                      v-model="menuOpen"
                      offset-y
                      top
                      right
                      :close-on-content-click="false"
                    >
                      <template #activator="{ on }">
                        <v-btn
                          color="primary"
                          :depressed="true"
                          class="btn-theme text-capitalize"
                          height="40"
                          :width="$vuetify.breakpoint.smAndDown ? '100%' : '141px'"
                          v-on="on"
                        >
                          {{ $t("testruns.create_testrun.actions")
                          }}<v-icon>
                            {{ menuOpen ? "mdi-chevron-up" : "mdi-chevron-down" }}
                          </v-icon>
                        </v-btn>
                      </template>
                      <v-list class="actions-list font-inter text-left">
                        <v-tooltip
                          bottom
                          :disabled="writeEntity"
                        >
                          <template #activator="{ on, attrs }">
                            <div
                              v-bind="attrs"
                              v-on="on"
                            >
                              <v-menu
                                v-if="filter == 'ongoing'"
                                v-model="menuDueDateParent"
                                offset-x
                                left
                                :close-on-content-click="false"
                                max-width="300px !important"
                                class="rounded-lg"
                              >
                                <template #activator="{ on: menuOn, attrs: menuAttrs }">
                                  <v-list-item
                                    :key="3"
                                    :class="{
                                      'action-btn actions-item': true,
                                      'disabled-action': isFilterArchived,
                                    }"
                                    v-bind="{ ...menuAttrs }"
                                    :close-on-content-click="false"
                                    :disabled="!writeEntity"
                                    dense
                                    v-on="{ ...menuOn }"
                                  >
                                    <div class="d-flex align-center">
                                      <Calendar24pxIcon class="minw-32px" />
                                      <v-list-item-title class="ml-1">
                                        {{ $t("testruns.changeDueDate") }}
                                      </v-list-item-title>
                                    </div>
                                  </v-list-item>
                                </template>

                                <v-list>
                                  <v-menu
                                    v-model="menuDueDate"
                                    max-width="290"
                                    left
                                    offset-x
                                  >
                                    <template #activator="{ on }">
                                      <div class="px-4 py-3">
                                        <v-label
                                          class="text-left fs-14px text-theme-label font-weight-medium mb-1"
                                        >
                                          {{ $t("milestone.create_milestone.dueDate") }}
                                        </v-label>
                                        <div class="calendar-textbox-container">
                                          <v-text-field
                                            v-model="getDueDate"
                                            dense
                                            background-color="#F9F9FB"
                                            height="38px"
                                            color="blue"
                                            class="field-theme rounded-lg"
                                            placeholder="MM/DD/YY"
                                            v-on="on"
                                          />
                                          <CalendarBlueIcon
                                            class="calendar-icon"
                                            v-on="on"
                                          />
                                        </div>
                                        <div class="d-flex justify-center w-100 px-2">
                                          <v-btn
                                            class="text-capitalize btn-theme mr-2"
                                            depressed
                                            height="38px"
                                            width="50%"
                                            @click="menuDueDateParent = false"
                                          >
                                            {{ $t("cancel") }}
                                          </v-btn>
                                          <v-btn
                                            class="text-capitalize btn-theme ml-2"
                                            depressed
                                            color="primary"
                                            height="38px"
                                            width="50%"
                                            @click="handleDueDateClick"
                                          >
                                            {{ $t("save") }}
                                          </v-btn>
                                        </div>
                                      </div>
                                    </template>
                                    <v-date-picker
                                      v-model="getDueDate"
                                      @change="onDateChange"
                                    />
                                  </v-menu>
                                </v-list>
                              </v-menu>
                            </div>
                          </template>
                          <span>
                            {{
                              $t("testruns.noPermissionToDo", {
                                action: $t("testruns.changeDueDate").toLowerCase(),
                              })
                            }}
                          </span>
                        </v-tooltip>

                        <v-tooltip
                          bottom
                          :disabled="writeEntity"
                        >
                          <template #activator="{ on, attrs }">
                            <div
                              v-bind="attrs"
                              v-on="on"
                            >
                              <v-list-item
                                v-if="filter == 'ongoing'"
                                :disabled="!writeEntity"
                                :class="{ 'disabled-action': isFilterArchived }"
                                @click="
                                  !isFilterArchived &&
                                    handleDuplicateAndApplyConfigDialog({
                                      items: selecteditems,
                                      type: 'generalDuplicate',
                                    })
                                "
                              >
                                <div class="d-flex align-center">
                                  <ApplyConfigIcon />
                                </div>
                                <v-list-item-title class="pl-3">
                                  {{ $t("testruns.duplicateApplyConfig") }}
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                          </template>
                          <span>
                            {{
                              $t("testruns.noPermissionToDo", {
                                action: $t("testruns.duplicateApplyConfig").toLowerCase(),
                              })
                            }}
                          </span>
                        </v-tooltip>
                        <v-tooltip
                          v-if="filter == 'ongoing'"
                          bottom
                          :disabled="writeEntity"
                        >
                          <template #activator="{ on, attrs }">
                            <div
                              v-bind="attrs"
                              v-on="on"
                            >
                              <v-list-item
                                :disabled="!writeEntity"
                                :class="{ 'disabled-action': isFilterArchived }"
                                @click="
                                  !isFilterArchived &&
                                    handleConfirmDialog('multi_archive')
                                "
                              >
                                <div class="d-flex align-center">
                                  <ArchivedIcon />
                                </div>
                                <v-list-item-title class="pl-3">
                                  {{ $t("testruns.archive") }}
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                          </template>
                          <span>
                            {{
                              $t("testruns.noPermissionToDo", {
                                action: $t("testruns.archive").toLowerCase(),
                              })
                            }}
                          </span>
                        </v-tooltip>
                        <v-tooltip
                          v-else-if="filter == 'archived'"
                          bottom
                          :disabled="writeEntity"
                        >
                          <template #activator="{ on, attrs }">
                            <div
                              v-bind="attrs"
                              v-on="on"
                            >
                              <v-list-item
                                :disabled="!writeEntity"
                                @click="handleConfirmDialog('multi_unarchive')"
                              >
                                <div class="d-flex align-center">
                                  <UnarchivedIcon />
                                </div>
                                <v-list-item-title class="pl-3">
                                  {{ $t("testruns.unarchive") }}
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                          </template>
                          <span>
                            {{
                              $t("testruns.noPermissionToDo", {
                                action: $t("testruns.unarchive").toLowerCase(),
                              })
                            }}
                          </span>
                        </v-tooltip>
                        <v-tooltip
                          bottom
                          :disabled="writeEntity"
                        >
                          <template #activator="{ on, attrs }">
                            <div
                              v-bind="attrs"
                              v-on="on"
                            >
                              <v-list-item
                                v-if="filter == 'ongoing'"
                                :disabled="!writeEntity"
                                :class="{ 'disabled-action': isFilterArchived }"
                                @click="
                                  !isFilterArchived &&
                                    handleAddToMilestoneDialog(selecteditems)
                                "
                              >
                                <div class="d-flex align-center">
                                  <MilestoneIcon />
                                </div>
                                <v-list-item-title class="pl-3">
                                  {{ $t("testruns.linkToMilestone") }}
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                          </template>
                          <span>
                            {{
                              $t("testruns.noPermissionToDo", {
                                action: $t("testruns.linkToMilestone").toLowerCase(),
                              })
                            }}
                          </span>
                        </v-tooltip>
                        <v-tooltip
                          bottom
                          :disabled="writeEntity"
                        >
                          <template #activator="{ on, attrs }">
                            <div
                              v-bind="attrs"
                              v-on="on"
                            >
                              <v-list-item
                                v-if="filter == 'ongoing'"
                                :class="{ 'disabled-action': isFilterArchived }"
                                :disabled="!writeEntity"
                                @click="
                                  !isFilterArchived &&
                                    handleAddToTestPlansDialog(selecteditems)
                                "
                              >
                                <div class="d-flex align-center">
                                  <CheckedIcon />
                                </div>
                                <v-list-item-title class="pl-3">
                                  {{ $t("testruns.linkToTestPlans") }}
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                          </template>
                          <span>
                            {{
                              $t("testruns.noPermissionToDo", {
                                action: $t("testruns.linkToTestPlans").toLowerCase(),
                              })
                            }}
                          </span>
                        </v-tooltip>
                        <v-tooltip
                          bottom
                          :disabled="deleteEntity"
                        >
                          <template #activator="{ on, attrs }">
                            <div
                              v-bind="attrs"
                              v-on="on"
                            >
                              <v-list-item
                                :class="{ 'red--text': true }"
                                :disabled="!deleteEntity"
                                @click="handleConfirmDialog('multi_delete')"
                              >
                                <div class="d-flex align-center">
                                  <RemoveIcon />
                                </div>
                                <v-list-item-title class="pl-3">
                                  {{ $t("testruns.delete") }}
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                          </template>
                          <span>
                            {{
                              $t("testruns.noPermissionToDo", {
                                action: $t("testruns.delete").toLowerCase(),
                              })
                            }}
                          </span>
                        </v-tooltip>
                      </v-list>
                    </v-menu>
                  </v-flex>
                </v-col>
              </v-row>
            </v-row>
            <v-row>
              <v-col>
                <Pagination
                  v-if="hasInitiallyLoaded && totalRows > 0"
                  :page="currentPage"
                  :items-per-page="perPage"
                  :total-pages="totalPages"
                  :total-items="totalRows"
                  @update:pagination="onUpdatePagination"
                />
              </v-col>
            </v-row>
          </div>
        </div>
      </div>
    </div>
    <AddToMilestoneDialog
      v-if="addToMilestoneDialog"
      :value="addToMilestoneDialog"
      :milestones="activeMilestones"
      :selected-runs="selecteditems"
      @close="onCloseAddToMilestoneDialog"
      @handleAddMilestone="handleAddMilestone"
    />
    <AddToTestPlansDialog
      v-if="addToTestPlansDialog"
      :value="addToTestPlansDialog"
      :plans="getActivePlans"
      :selected-runs="selecteditems"
      @close="onCloseAddToTestPlansDialog"
      @handleAddTestPlan="handleConfirmTestPlanClick"
      @planCreated="planCreated"
    />
    <DuplicateAndApplyConfigDialog
      :value="duplicateAndApplyConfigDialog"
      :configurations="configurationSorted"
      :button-loading="buttonLoading"
      :button-loading-items="buttonLoadingItems"
      :loading-configurations="loadingConfigurations"
      :is-menu-visible="buttonLoading"
      :selected-run="selectedRun"
      :global-configuration="globalConfiguration"
      :last-page="lastPage"
      @handleDuplicateAndApplyConfig="handleDuplicateAndApplyConfig"
      @close="onCloseDuplicateAndApplyConfigDialog"
      @addConfiguration="addConfiguration"
      @addConfigurationItems="addConfigurationItems"
      @deleteConfigurationGroup="deleteConfigurationGroup"
      @deleteConfigurationItem="deleteConfigurationItem"
      @editConfigurationGroup="editConfigurationGroup"
      @loadMoreConfigurations="getConfigurations"
    />
    <RunDiscardDialog
      v-model="showConfirmDialog"
      :title="ConfirmDialog_Title"
      :content="ConfirmDialog_Content"
      :content_part2="ConfirmDialog_Contentpart_two"
      :run_name="ConfirmDialog_RunName"
      :items="ConfirmDialog_Items"
      :type="ConfirmType"
      :btn_label="ConfirmDialog_btn_label"
      :color="ConfirmDialog_btn_color"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmBtnClick"
    />
  </div>
</template>

<script>
import RunDiscardDialog from "@/components/TestRuns/RunDiscardDialog.vue";
import RunFilter from "@/components/TestRuns/RunFilter.vue";
import RunHeader from "@/components/TestRuns/RunHeader.vue";
import AddToMilestoneDialog from "@/components/TestRuns/AddToMilestoneDialog.vue";
import AddToTestPlansDialog from "@/components/TestRuns/AddToTestPlansDialog.vue";
import DuplicateAndApplyConfigDialog from "@/components/TestRuns/DuplicateAndApplyConfigDialog.vue";
import RunTable from "@/components/TestRuns/RunTable.vue";
import SettingsMenu from "@/components/Project/SettingsMenu.vue";
import SearchIcon from "@/assets/svg/search-icon.svg";
import ApplyConfigIcon from "@/assets/svg/duplicate-apply-config.svg";
import CheckedIcon from "@/assets/svg/checked.svg";
import MilestoneIcon from "@/assets/svg/milestone.svg";
import RemoveIcon from "@/assets/svg/remove.svg";
import ArchivedIcon from "@/assets/svg/archived.svg";
import UnarchivedIcon from "@/assets/svg/unarchive24px.svg";
import ActiveEmptyState from "@/components/base/ActiveEmptyState.vue";
import ArchivedEmptyState from "@/components/base/ArchivedEmptyState.vue";
import Calendar24pxIcon from "@/assets/svg/calendar24px.svg";
import CalendarBlueIcon from "@/assets/svg/calendar-blue.svg";
import FilterChips from "@/components/base/FilterChips.vue";
import TestPlanMiniSection from "@/components/base/TestPlanMiniSection.vue";

import ProjectStatus from "@/mixins/projectStatus";
import colorPreferencesMixin from "@/mixins/colorPreferences";
import handleLoading from "@/mixins/loader.js";
import { handleNetworkStatusError } from "@/mixins/redirect";

import { useRunsIndex } from '@/composables/modules/testRun/index';

export default {
  components: {
    RunTable,
    RunHeader,
    RunFilter,
    RunDiscardDialog,
    AddToMilestoneDialog,
    AddToTestPlansDialog,
    DuplicateAndApplyConfigDialog,
    SearchIcon,
    SettingsMenu,
    ApplyConfigIcon,
    CheckedIcon,
    MilestoneIcon,
    RemoveIcon,
    ArchivedIcon,
    UnarchivedIcon,
    ActiveEmptyState,
    ArchivedEmptyState,
    Calendar24pxIcon,
    CalendarBlueIcon,
    FilterChips,
    TestPlanMiniSection,
  },
  mixins: [handleNetworkStatusError, colorPreferencesMixin, ProjectStatus, handleLoading],
  props: {
    runViewType: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    return useRunsIndex(props.runViewType);
  },
  computed:{
    loadingState() {
      return !(this.searchFilter || this.filteredRuns.length || Object.keys(this.appliedFilters).length || this.filter == 'archived' || this.tableLoading)
    }
  }
};
</script>
<style scoped>
.calendar-icon {
  position: absolute;
  right: 12px;
  top: 9px;
}
.box-shadow-none {
  box-shadow: none;
}

.calendar-textbox-container {
  position: relative;
}
.custom-runtable {
  color: #344054 !important;
}

.custom-color-0c111d {
  color: #0c111d !important;
}

.custom-color-d0d5dd {
  color: #d0d5dd !important;
}

.custom-color-061AAE {
  color: #061aae !important;
}
.custom-menu-item {
  min-height: 36px;
  height: 36px;
  max-height: 36px;
  align-items: center;
}

.custom-text-12 {
  font-size: 12px;
}

.custom-text-14 {
  font-size: 14px;
}

.h-full {
  height: 100%;
  min-height: 100%;
}
.custom-swal-popup {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.custom-swal-title {
  text-align: left;
  width: 100%;
}
.custom-swal-content {
  text-align: left;
  width: 100%;
}
.custom-swal-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
.custom-swal-confirm-button,
.custom-swal-cancel-button {
  margin-left: 10px;
}
.action-btn-wrapper {
  position: sticky;
  bottom: 0;
  background-color: white;
  align-items: flex-end;
  display: flex;
  justify-content: flex-end;
  z-index: 8;
}
.sticky-on-scroll {
  position: -webkit-sticky;
  position: sticky;
  height: calc(100vh);
}
.sticky-scroll {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  padding-bottom: 5rem;
  background: #fff;
  z-index: 1;
  padding-top: 1rem;
}
.plan-list-wrapper {
  height: calc(100vh - 28rem);
  overflow: auto;
  scrollbar-width: thin;
}
.btn-runs-sticky {
  position: -webkit-sticky;
  position: sticky;
  background: #fff;
  justify-content: flex-start;
  z-index: 9;
}

/* Layout improvements for sidebar and main content alignment */
.runs-layout-container {
  height: 100%;
  align-items: stretch !important;
}

.runs-sidebar-section {
  height: 100%;
  overflow-y: hidden;
  scrollbar-width: thin;
}
.runs-main-section .container {
  flex: 1;
  height: 100%;
}

/* Ensure the main content area fills available height */
.runs-main-section .project-placeholder-height {
  height: calc(100% - 40px) !important;
  min-height: 400px;
}
</style>