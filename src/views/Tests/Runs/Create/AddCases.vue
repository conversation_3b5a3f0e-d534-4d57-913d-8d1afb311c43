<template>
  <div
    id="case-index-container"
    class="d-flex flex-column align-stretch font-inter mt-2"
  >
    <v-row>
      <v-col class="pb-1">
        <v-card
          class="py-6 px-6"
          rounded="lg"
          elevation="0"
          width="100%"
        >
          <v-row v-if="caseType != 'EditCase'">
            <v-col flex>
              <div
                class="back-to-projects"
                @click="handleBackClick"
              >
                <v-icon color="blue">
                  mdi-chevron-left
                </v-icon>
                <p class="d-flex-inline justify-center align-center ma-0 blue--text font-weight-bold">
                  {{ backTitle }} 
                </p>
              </div>
            </v-col>
            <v-col
              cols="auto"
              class="pointer"
              @click="handleCloseClick"
            >
              <v-icon>mdi-close</v-icon>
            </v-col>
          </v-row>
          <v-row class="pt-0 justify-between">
            <v-col
              flex
              class="pt-0"
            >
              <template v-if="!skeletonLoaderState">
                <p
                  v-if="caseType == 'EditCase'"
                  class="text-start font-weight-bold mb-2 fs-24px"
                >
                  {{ $t('testruns.testCases') }}
                </p>
                <p
                  v-else
                  class="text-start font-weight-bold mb-2 fs-24px"
                >
                  {{ $t('testruns.addtestcases') }}
                </p>
              </template>
              <v-skeleton-loader
                v-else
                height="36"
                width="140"
                type="heading"
              />
              <div
                v-if="!skeletonLoaderState"
                class="mt-6 d-flex"
              >
                <v-chip
                  :class="{ 'blue--text': !tableFilter }"
                  width="200px"
                  :color="!tableFilter ? 'blue-light' : 'gray-light'"
                  label
                  @click="changeFilter(false)"
                >
                  <div class="font-weight-bold px-2">
                    {{ $t('testruns.unlinked') }} <span class="ml-2">{{ casesCount }}</span>
                  </div>
                </v-chip>
                <div class="ml-2">
                  <v-chip
                    :class="{ 'blue--text': tableFilter }"
                    width="200px"
                    :color="tableFilter ? 'blue-light' : 'gray-light'"
                    label
                    @click="changeFilter(true)"
                  >
                    <div class="font-weight-bold px-2">
                      {{ $t('testruns.linked') }} <span class="ml-2">{{ selectedRunCasesCount }}</span>
                    </div>
                  </v-chip>
                </div>
              </div>
              <div
                v-else
                class="mt-6 d-flex"
              >
                <v-skeleton-loader
                  class="rounded-sm d-flex gap-2 chip-primary"
                  height="32"
                  width="200"
                  type="button@2"
                />
              </div>
            </v-col>
            <v-col
              cols="auto"
              style="align-self: flex-end; align-items: end;"
            >
              <v-switch
                v-model="showTestFolder"
                inset
                color="blue"
                :ripple="false"
              >
                <template #prepend>
                  <div>
                    <div
                      class="text-no-wrap black--text"
                      style="font-size: larger; font-weight: 500; padding-top: 4px; padding-right: 10px;"
                    >
                      Show Test Folders
                    </div>
                  </div>
                </template>
              </v-switch>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <CaseManagement
      :show-collapse="false"
      :show-create="false"
      :select-option="tableFilter"
      :quick-create="false"
      :allow-action="false"
      :show-test-folder="showTestFolder"
      :cases="cases"
      :write-entity="writeEntity"
      :total-items="composableTotalCases"
      :delete-entity="deleteEntity"
      :folders="folders"
      :run-cases="selectedCases"
      :from-run="true"
      :set-selected-folder-uid.sync="folderUid"
      :pagination="paginationData"
      :current-page="composableCurrentPage"
      :items-per-page="composableItemsPerPage"
      :update-pagination="updatePagination"
      :relations-loading="composableRelationsLoading"
      :cases-loading="composableCasesLoading"
      :show-pagination="!tableFilter"
      @refresh-folders="getFolders"
      @selectedCases="handleCases"
      @updateCaseCount="UpdateCases"
      @folder-select="getCasesAfterMounted"
      @update-cases="updateCasesData"
      @applySearch="composableApplySearch($event, !showTestFolder)"
      @applyFilters="composableApplyFilters($event, !showTestFolder)"
      @clearFilters="composableClearFilter"
    >
      <template slot="control-area">
        <div
          v-if="!skeletonLoaderState"
          class="action-btn-wrapper pa-4 pl-6"
        >
          <v-row>
            <v-btn
              v-if="!isEditView"
              depressed
              height="40"
              class="fw-semibold black--text btn-theme mr-3"
              color="#F2F4F7"
              @click="handleBackClick"
            >
              <v-icon 
                color="blue" 
                size="16" 
                class="mr-1"
              >
                mdi-chevron-left
              </v-icon>
              {{ $t('back') }} 
            </v-btn>
            <v-col class="d-flex justify-end">
              <v-btn
                v-if="!isEditView"
                depressed
                height="40"
                class="fw-semibold black--text btn-theme mr-3"
                color="#F2F4F7"
                @click="onAddTestCase"
              >
                <v-icon
                  class="mr-1"
                  size="16"
                >
                  mdi-plus
                </v-icon> {{ $t('createNewTestCase') }} 
              </v-btn>
              <slot name="action" />
            </v-col>
          </v-row>
        </div>
      </template>
    </CaseManagement>
    
    <RunDiscardDialog
      v-model="showAddDialog"
      :title="confirmDialogTitle"
      :content="confirmDialogContent"
      :content_part2="confirmDialogContentPartTwo"
      :run_name="confirmDialogRunName"
      :btn_label="confirmDialogBtnLabel"
      :color="confirmDialogBtnColor"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmBtnClick(confirmType)"
    />
  </div>
</template>

<script>
import RunDiscardDialog from '@/components/TestRuns/RunDiscardDialog.vue';
import CaseManagement from '@/components/Cases/CaseManagement'
import { useRunAddCase } from '@/composables/modules/testRun/addCase';
import projectStatus from '@/mixins/projectStatus';
import permissions from '@/mixins/permissions';

export default {
  name: 'TestRunCreate',
  components: {
    CaseManagement,
    RunDiscardDialog,
  },
  mixins: [projectStatus, permissions],
  props: {
    value: {
      type: Array,
    },
    customCases: {
      type: Array,
    },
    caseType: {
      type: String,
    },
    backTitle: {
      type: String,
    },
    showPagination: {
      type: Boolean,
      default: true
    },
  },
  setup(props) {
    const composable = useRunAddCase(props);

    // Initialize the composable
    composable.init();

    return {
      ...composable
    };
  }

};
</script>

<style lang="scss" scoped>
.back-to-projects {
  display: flex;
  cursor: pointer;
  width: max-content;
}

.search_input {
  width: 100%;

  @media screen and (min-width: 600px) {
    width: 300px;
  }
}

.project-logo {
  border-radius: 50%;
  border: 2px dashed grey;
  width: 150px;
  height: 150px;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
  cursor: pointer;
  transition: border-color 0.3s;
  background-size: cover;
  background-position: center;
  position: relative;
}

.project-logo:hover {
  border-color: #2196f3;
}

.hovering .edit-icon,
.hovering .delete-icon {
  display: block;
}

.edit-icon,
.delete-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateX(-30px);
  display: none;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 10px;
  cursor: pointer;
}

.delete-icon {
  margin-left: 60px;
}

.project-logo:hover .edit-icon,
.project-logo:hover .delete-icon {
  display: block;
}
.action-btn-wrapper {
  position: sticky;
    bottom: 10px;
    background-color: white;
    align-items: flex-end;
    display: flex;
    justify-content: flex-end;
    z-index: 8;
}
</style>
