<template>
  <div
    id="case-index-container"
    fluid
    class="pl-3 pt-3"
  >
    <v-card
      class="py-6 px-6 d-flex flex-column"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-breadcrumbs
        v-if="!skeletonLoaderState"
        :items="breadCrumbs"
        class="grey lighten-4 rounded-lg"
        :disabled="true"
      >
        <template #divider>
          <v-icon size="24">
            mdi-chevron-right
          </v-icon>
        </template>
        <template #item="{ item }">
          <div class="d-flex flex-column">
            <div class="text-capitalize font-weight-bold">
              {{ item.text }}
            </div>
            <small>{{ item.sub }}</small>
          </div>
        </template>
      </v-breadcrumbs>
      <v-skeleton-loader
        v-else
        class="rounded-lg mb-5"
        height="50"
        width="full"
        type="button"
      />
      <div class="d-flex flex-row justify-space-between">
        <div class="d-flex flex-column">
          <div class="d-flex flex-row align-center">
            <v-icon
              v-if="!skeletonLoaderState"
              color="black"
              class="pointer"
              @click="handleBackClick"
            >
              mdi-arrow-left
            </v-icon>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-3"
              height="24"
              width="24"
              type="button"
            />
            <h2
              v-if="!skeletonLoaderState"
              class="d-flex-inline justify-center align-center ml-2 ma-0 font-weight-bold"
            >
              Failed Syncs
            </h2>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-3"
              height="24"
              width="150"
              type="button"
            />
          </div>
        </div>
        <div class="buttons d-flex flex-row">
          <template v-if="!skeletonLoaderState">
            <v-btn
              v-if="writeEntity"
              depressed
              color="primary"
              height="40px"
              class="text-capitalize btn-theme rounded-lg"
              :width="$vuetify.breakpoint.smAndDown ? '100%' : '118px'"
              @click="statusDialog = true"
            >
              {{ $t('retryAll') }}
            </v-btn>
          </template>
          <v-skeleton-loader
            v-else
            class="rounded-lg primary"
            height="40"
            width="125"
            type="button"
          />
        </div>
      </div>
    </v-card>
    <div class="mt-4">
      <FailedSyncsTable 
        :failed-syncs="failedSyncs"
        :headers="headers"
        :skeleton-loader-state="skeletonLoaderState"
      />
    </div>
  </div>
</template>

<script>
import { useRunsFailedSyncs } from '@/composables/modules/testRun/failedSyncs';
import FailedSyncsTable from '@/components/TestRuns/FailedSyncsTable.vue';
export default {
  components: {
    FailedSyncsTable
  },
  setup() {
    return useRunsFailedSyncs();
  },
};
</script>
<style lang="scss" scoped>
.custom-switch {
  ::v-deep .v-input--switch__thumb {
    width: 24px;
    height: 24px;
    top: 0;
    right: 2px;
  }
  ::v-deep .primary--text {
    background-color: #ffffff !important; /* Custom track color when switch is on */
  }
  ::v-deep .primary--text.v-input--switch__track {
    background-color: #0000ff !important; /* Custom thumb color */
    opacity: 1;
  }
}
.toggle-folder {
  gap: 10px;
  align-items: center;
}
.v-breadcrumbs {
  padding: 8px 12px;
  margin-bottom: 16px;
  display: flex;
  align-items: start;
}
</style>
<style scoped>
.custom-progressbar {
  width: 200px;
}
.__attachment_progress {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 8px;
  flex-direction: column !important;
  z-index: 999;
}
</style>