<template>
  <v-container
    class="d-flex flex-column justify-start"
    fluid
  >
    <v-row>
      <v-col class="pr-0 pl-3">
        <RunEditor
          v-if="selectedItem"
          :milestones="activeMilestones"
          :item="selectedItem"
          @update-filter="updateItem"
          @update-tags="updateTags"
          @update-milestones="updateMilestones"
          @update-due-date="updateDueDate"
          @tags-list="getTags"
        />
        <AddCases
          ref="AddCases"
          v-model="runCases"
          :case-type="'EditCase'"
          :back-title="$t('testruns.create_testrun.back_to_testrun')"
          :show-pagination="false"
          @input="updateCases"
        >
          <template #action>
            <v-btn
              v-if="!skeletonLoaderState"
              color="gray-100"
              depressed
              class="ml-2 btn-theme text-capitalize"
              :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
              height="38px"
              @click="$router.go(-1)"
            >
              {{ $t('cancel') }}
            </v-btn>
            <v-btn
              v-if="!skeletonLoaderState"
              color="primary"
              depressed
              class="f-color-white ml-2 btn-theme text-capitalize"
              :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
              height="38px"
              :loading="loading"
              @click="saveTestRuns"
            >
              {{ $t('save') }}
            </v-btn>
          </template>
        </AddCases>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import RunEditor from '@/components/TestRuns/RunEditor.vue';
import AddCases from '@/views/Tests/Runs/Create/AddCases';
import { useRunEdit } from '@/composables/modules/testRun/edit';
import { useRunsIndex } from '@/composables/modules/testRun/index';

export default {
  name: 'TestRunEdit',
  components: {
    RunEditor,
    AddCases,
  },
  props: {
    runViewType: {
      type: String,
      default: 'default',
    },
    customItem: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const editComposable = useRunEdit(props);
    const indexComposable = useRunsIndex(props.runViewType);

    // Initialize the edit composable
    editComposable.init();

    return {
      ...editComposable,
      // Only include specific methods from index composable to avoid conflicts
      clearCache: indexComposable.clearCache,
      refreshData: indexComposable.refreshData
    };
  }
};
</script>