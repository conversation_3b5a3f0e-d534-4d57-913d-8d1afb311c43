<template>
  <div class="pl-3 pt-3">
    <PlanHeader
      :is-duplicate="PlanViewType === 'Duplicate'"
      :title="$t('plans.title')"
      :action-text="$t('plans.createTestPlan')"
      :active_label="$t('plans.active')"
      :archived_label="$t('plans.archived')"
      :btn_show="writeEntity"
      :filter="filter"
      :active-item-count="activeCount"
      :archived-item-count="archivedCount"
      @update-filter="updateFilter"
    />
    <template>
      <template
        v-if="
          filteredPlans.length == 0 &&
            !isFilter &&
            filter != 'archived' &&
            !skeletonLoaderState
        "
      >
        <div
          class="mt-3 mb-0 white rounded-lg mx-0 plans-placeholder-height d-flex justify-center align-center"
        >
          <ActiveEmptyState
            :write-entity="writeEntity"
            :image-src="require('@/assets/png/test-plans-placeholder.png')"
            :title="$t('plans.placeholder.title')"
            :button-text="$t('plans.createTestPlan')"
            :button-route="{ name: 'TestPlanCreate' }"
            button-color="primary"
            :is-project-archived="isProjectArchived"
          >
            <template #description>
              <p class="mb-0 mt-3 plan-description">
                {{ $t("plans.placeholder.description") }}
              </p>
            </template>
          </ActiveEmptyState>
        </div>
      </template>
      <template
        v-else-if="
          filteredPlans.length == 0 &&
            !isFilter &&
            filter != 'ongoing' &&
            !skeletonLoaderState
        "
      >
        <div
          class="mt-3 mb-0 white rounded-lg mx-0 plans-placeholder-height d-flex justify-center align-center"
        >
          <ArchivedEmptyState
            :image-src="require('@/assets/png/test-plans-placeholder.png')"
            :title="$t('plans.archived_empty_state.title')"
          >
            <template #description>
              <p class="mb-0 mt-3">
                {{ $t("plans.archived_empty_state.description.part1") }}
              </p>
              <p class="mb-0">
                {{ $t("plans.archived_empty_state.description.part2") }}
              </p>
            </template>
          </ArchivedEmptyState>
        </div>
      </template>
      <template v-else>
        <div>
          <template>
            <v-card
              class="py-6 px-6"
              rounded="lg"
              elevation="0"
              width="100%"
            >
              <div class="d-flex flex-row justify-space-between align-center mb-4">
                <div class="d-flex flex-row justify-start align-center">
                  <v-responsive
                    v-if="!skeletonLoaderState"
                    class="ma-0"
                    max-width="344"
                  >
                    <v-text-field
                      v-model="searchFilter"
                      :loading="loading"
                      :placeholder="$t('searchByName')"
                      background-color="#F9F9FB"
                      class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
                      height="40"
                      clear-icon="mdi-close-circle"
                      clearable
                      hide-details
                    >
                      <template #prepend-inner>
                        <SearchIcon />
                      </template>
                    </v-text-field>
                  </v-responsive>
                  <v-skeleton-loader
                    v-else
                    class="rounded-lg mr-3"
                    width="235"
                    height="40"
                    type="button"
                  />
                  <PlanFilter
                    :milestones="milestones"
                    :current-filters="appliedFilters"
                    :tags="tags"
                    @applyFilters="applyFilters"
                  />
                </div>

                <SettingsMenu table-type="plan" />
              </div>
              <v-row
                v-if="isFilter && !skeletonLoaderState"
                justify="space-between"
                class="align-center"
              >
                <v-col
                  cols="12"
                  md="auto"
                  class="d-flex align-center"
                >
                  <FilterChips
                    :filters="appliedFilters"
                    :results-count="filteredPlans.length"
                    @update-filters="handleFilters"
                    @clear-filters="clearFilters"
                  />
                </v-col>
              </v-row>

              <test-plans-table
                :filtered-headers="filteredHeaders"
                :filtered-items="filteredPlans"
                :item-key="itemKey"
                :row-class="rowClass"
                :clear-selection="clearSelection"
                :write-entity="writeEntity"
                :delete-entity="deleteEntity"
                :selected-plans="selecteditems"
                :relations-loading="relationsLoading"
                :relation-loading-states="relationLoadingStates"
                :total-items="totalRows"
                :sort-by="sortBy"
                :sort-desc="sortDesc"
                :current-page="currentPage"
                @edit-item="editItem"
                @select-row="handleRowClick"
                @select-item="setselected"
                @archive-item="confirmArchiveTestPlan"
                @unarchive-item="confirmUnArchiveTestPlan"
                @delete-item="confirmDeletePlan"
                @add-to-milestone="handleAddToMilestione"
                @duplicate-plan="handleDuplicatePlan"
                @update-pagination="onUpdatePagination"
              />
              <Pagination
                :page="currentPage"
                :items-per-page="perPage"
                :total-pages="totalPages"
                :total-items="totalRows"
                @update:pagination="onUpdatePagination"
              />
            </v-card>
            <div
              v-if="selecteditems.length > 0"
              class="action-btn-wrapper pa-3"
            >
              <div class="d-flex flex-row justify-end">
                <v-menu
                  v-model="menuOpen"
                  :close-on-content-click="false"
                  offset-y
                  top
                  right
                >
                  <template #activator="{ on, attrs }">
                    <v-btn
                      v-bind="attrs"
                      height="40px"
                      depressed
                      color="primary"
                      class="font-inter text-capitalize rounded-lg fw-semibold"
                      :width="$vuetify.breakpoint.smAndDown ? '100%' : '150px'"
                      :disabled="!hasSelectedItems"
                      v-on="on"
                    >
                      {{ $t("actions") }}
                      <v-icon size="20">
                        {{ menuOpen ? "mdi-chevron-up" : "mdi-chevron-down" }}
                      </v-icon>
                    </v-btn>
                  </template>
                  <v-list class="actions-list font-inter text-left">
                    <v-tooltip
                      bottom
                      :disabled="writeEntity"
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-list-item
                            v-if="filter === 'ongoing' && selecteditems.length == 1"
                            :class="{
                              'actions-item': true,
                              'disabled-action': isProjectArchived,
                            }"
                            :disabled="!writeEntity"
                            @click="handleDuplicatePlan(selecteditems[0])"
                          >
                            <v-list-item-title class="d-flex align-center">
                              <DuplicateIcon class="mr-1" />
                              {{ $t("duplicate") }}
                            </v-list-item-title>
                          </v-list-item>
                        </div>
                      </template>
                      <span>
                        {{
                          $t("testruns.noPermissionToDo", {
                            action: $t("duplicate").toLowerCase(),
                          })
                        }}
                      </span>
                    </v-tooltip>
                    <v-tooltip
                      bottom
                      :disabled="writeEntity"
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-list-item
                            v-if="filter === 'ongoing' && selecteditems.length > 1"
                            :class="{
                              'actions-item': true,
                              'disabled-action': isProjectArchived,
                            }"
                            :disabled="!writeEntity"
                            @click="handleDuplicatePlans(selecteditems)"
                          >
                            <v-list-item-title class="d-flex align-center">
                              <DuplicateIcon class="mr-1" />
                              {{ $t("duplicate") }}
                            </v-list-item-title>
                          </v-list-item>
                        </div>
                      </template>
                      <span>
                        {{
                          $t("testruns.noPermissionToDo", {
                            action: $t("duplicate").toLowerCase(),
                          })
                        }}
                      </span>
                    </v-tooltip>
                    <v-tooltip
                      bottom
                      :disabled="writeEntity"
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-list-item
                            v-if="filter === 'ongoing'"
                            :class="{
                              'actions-item': true,
                              'disabled-action': isProjectArchived,
                            }"
                            :disabled="!writeEntity"
                            @click="openAddToMilestoneDialog()"
                          >
                            <v-list-item-title class="d-flex align-center">
                              <AddToMilesonteIcon class="mr-1" />
                              {{ $t("testruns.linkToMilestone") }}
                            </v-list-item-title>
                          </v-list-item>
                        </div>
                      </template>
                      <span>
                        {{
                          $t("plans.noPermissionToDo", {
                            action: $t("linkToMilestone").toLowerCase(),
                          })
                        }}
                      </span>
                    </v-tooltip>
                    <v-tooltip
                      v-if="filter === 'ongoing'"
                      bottom
                      :disabled="writeEntity"
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-list-item
                            :class="{
                              'actions-item': true,
                              'disabled-action': isProjectArchived,
                            }"
                            :disabled="!writeEntity"
                            @click="handleConfirmDialog('multi_archive')"
                          >
                            <v-list-item-title class="d-flex align-center">
                              <ArchiveIcon class="mr-1" />
                              {{ $t("plans.archive") }}
                            </v-list-item-title>
                          </v-list-item>
                        </div>
                      </template>
                      <span>
                        {{
                          $t("plans.noPermissionToDo", {
                            action: $t("archive").toLowerCase(),
                          })
                        }}
                      </span>
                    </v-tooltip>
                    <v-tooltip
                      v-else-if="filter !== 'ongoing'"
                      bottom
                      :disabled="writeEntity"
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-list-item
                            :class="{
                              'actions-item': true,
                              'disabled-action': isProjectArchived,
                            }"
                            :disabled="!writeEntity"
                            @click="handleConfirmDialog('multi_unarchive')"
                          >
                            <v-list-item-title class="d-flex align-center">
                              <UnarchiveIcon class="mr-1" />
                              {{ $t("plans.unarchive") }}
                            </v-list-item-title>
                          </v-list-item>
                        </div>
                      </template>
                      <span>
                        {{
                          $t("plans.noPermissionToDo", {
                            action: $t("unarchive").toLowerCase(),
                          })
                        }}
                      </span>
                    </v-tooltip>
                    <v-tooltip
                      bottom
                      :disabled="deleteEntity"
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-list-item
                            :class="{
                              'actions-item': true,
                              'disabled-action': isProjectArchived,
                            }"
                            :disabled="!deleteEntity"
                            @click="handleConfirmDialog('multi_delete')"
                          >
                            <v-list-item-title class="error--text d-flex align-center">
                              <DeleteIcon class="mr-1" />
                              {{ $t("delete") }}
                            </v-list-item-title>
                          </v-list-item>
                        </div>
                      </template>
                      <span>
                        {{
                          $t("plans.noPermissionToDo", {
                            action: $t("delete").toLowerCase(),
                          })
                        }}
                      </span>
                    </v-tooltip>
                  </v-list>
                </v-menu>
              </div>
            </div>
          </template>
        </div>
        <plans-list-filter
          :open="isOpenFilter"
          @filters="applyFilters"
          @close="isOpenFilter = false"
        />
        <PlanAddToMilestone
          v-if="showAddToMilestoneDialog"
          :test-plan="selectedPlan"
          :selected-plans="selecteditems"
          :show-dialog="showAddToMilestoneDialog"
          @change-milestone-drawer-state="toggleMilestoneDrawer"
          @refresh-test-plans="refreshTestPlan"
        />
      </template>
    </template>
    <PlanDiscardDialog
      v-model="showConfirmDialog"
      :title="dialog.confirmDialogTitle"
      :content="dialog.confirmDialogContent"
      :content_part2="dialog.confirmDialogContentPartTwo"
      :run_name="dialog.confirmDialogName"
      :btn_label="dialog.confirmDialogBtnLabel"
      :color="dialog.confirmDialogBtnColor"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmBtnClick(dialog.confirmType)"
    />
    <BaseDialog
      v-model="confirmDuplicateDialog"
      max-width="500px"
    >
      <v-card class="bg-white">
        <v-card-text class="py-8 px-sm-10">
          <v-flex class="d-flex align-start">
            <p class="ma-0 font-weight-bold text-h6 text-sm-h5 text-start black--text">
              {{ $t('testPlansBulkDuplicateTitle') }}
            </p>
            <v-icon
              class="mt-1 ml-4 pointer"
              @click="$emit('close')"
            >
              mdi-close
            </v-icon>
          </v-flex>
          <slot name="content">
            <v-flex class="mt-4">
              <p class="text-start">
                {{ $t('testPlansBulkDuplicate') }}
              </p>
            </v-flex>
          </slot>
          <slot name="footer">
            <v-row>
              <v-col cols="6">
                <v-btn
                  height="40"
                  width="100%"
                  class="text-capitalize fw-semibold black--text btn-theme"
                  color="#F2F4F7"
                  depressed
                  @click="closeConfirmDuplicateDialog"
                >
                  {{ $t('projects.create_project.close_dialog.cancel_button') }}
                </v-btn>
              </v-col>
              <v-col cols="6">
                <v-btn
                  depressed
                  height="40"
                  width="100%"
                  class="text-capitalize rounded-lg fw-semibold white--text btn-theme"
                  color="primary"
                  :loading="bulkDuplicateLoading"
                  @click="handleConfirmBulkDuplicate"
                >
                  {{ $t('projects.create_project.close_dialog.confirm_button') }}
                </v-btn>
              </v-col>
            </v-row>
          </slot>
        </v-card-text>
      </v-card>
    </BaseDialog>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import DeleteIcon from "@/assets/svg/delete.svg";
import ArchiveIcon from "@/assets/svg/archived.svg";
import UnarchiveIcon from "@/assets/svg/unarchive-16x16.svg";
import AddToMilesonteIcon from "@/assets/svg/milestone.svg";
import PlansListFilter from "@/views/Tests/Plans/Components/List/PlansListFilter";
import DuplicateIcon from "@/assets/svg/duplicate.svg";

import TestPlansTable from "@/components/TestPlans/TestPlansTable.vue";
import PlanDiscardDialog from "@/components/TestPlans/PlanDiscardDialog.vue";
import PlanFilter from "@/components/TestPlans/PlanFilter.vue";
import PlanHeader from "@/components/TestPlans/PlanHeader.vue";
import PlanAddToMilestone from "@/components/TestPlans/PlanAddToMilestone.vue";
import SearchIcon from "@/assets/svg/search-icon.svg";
import SettingsMenu from "@/components/Project/SettingsMenu.vue";
import projectStatus from "@/mixins/projectStatus";
import ActiveEmptyState from "@/components/base/ActiveEmptyState.vue";
import ArchivedEmptyState from "@/components/base/ArchivedEmptyState.vue";
import FilterChips from "@/components/base/FilterChips.vue";
import BaseDialog from "@/components/base/BaseDialog.vue";
import Pagination from "@/components/base/Pagination.vue";

import { usePlanIndex } from "@/composables/modules/testplan/index";

export default {
  
  components: {
    PlanHeader,
    PlanFilter,
    PlanAddToMilestone,
    PlanDiscardDialog,
    DuplicateIcon,
    TestPlansTable,
    PlansListFilter,
    DeleteIcon,
    ArchiveIcon,
    UnarchiveIcon,
    AddToMilesonteIcon,
    SettingsMenu,
    SearchIcon,
    ActiveEmptyState,
    ArchivedEmptyState,
    FilterChips,
    BaseDialog,
    Pagination
  },
  mixins: [projectStatus],
  props: ["PlanViewType", "NewRunForm"],
  setup() {
    const planIndex = usePlanIndex();
    return planIndex;
  },

  mounted() {
    this.initializeServices();
    this.initializeDebouncedSearch();
    if (!this.dynamicHeaders.plan) {
      this.initializeHeaders({ type: "plan" });
    }
    this.headers = this.dynamicHeaders.plan;
    this.init();
  },

  beforeDestroy() {
    if (this.debouncedSearch) {
      this.debouncedSearch.cancel();
    }
  },

  methods: {
    ...mapActions("headers", ["initializeHeaders"]),
  },
};
</script>

<style scoped>
.action-btn-wrapper {
  position: sticky;
  bottom: 0;
  background-color: white;
  align-items: flex-end;
  display: flex;
  justify-content: flex-end;
  z-index: 8;
}

.plans-placeholder-height {
  min-height: calc(100vh - 24px - 6.5rem);
  height: 100%;
}

.plan-description {
  max-width: 600px !important;
  text-align: center !important;
}
</style>