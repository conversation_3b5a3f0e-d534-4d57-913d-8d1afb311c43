<template>
  <div
    id="case-index-container"
    fluid
    class="pl-3 pt-3"
  >
    <StatusesDialog
      v-model="statusDialog"
      @completed="handleCompleteClick"
    />
    <v-card
      class="py-6 px-6"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <div class="d-flex flex-row align-center justify-space-between">
        <div class="d-flex flex-row align-start justify-start">
          <button
            v-if="!skeletonLoaderState"
            class="d-flex flex-row align-center pointer mr-3 mt-1"
            @click="handleBackClick"
          >
            <v-icon color="black">
              mdi-arrow-left
            </v-icon>
          </button>
          <v-skeleton-loader
            v-else
            class="rounded-lg mr-3"
            height="24"
            width="24"
            type="button"
          />
          <div
            class="d-flex flex-column"
            :class="{
              'gap-2': testPlan.description
            }"
          >
            <div class="d-flex flex-row">
              <v-tooltip
                v-if="!skeletonLoaderState"
                bottom
                max-width="430px"
                :disabled="testPlanName.length < 61"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <h2 
                    class="custom__tooltip__title"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ testPlanName }}
                  </h2>
                </template>
                <span>{{ testPlanName }}</span>
              </v-tooltip>
              <v-skeleton-loader
                v-else
                class="rounded-lg mr-3"
                height="24"
                width="150"
                type="button"
              />
              <div
                v-if="!skeletonLoaderState"
                class="d-flex flex-row align-center justify-space-between ml-6"
              >
                <ProgressBar
                  is-long-progress-bar
                  :executions="generateExecutionsProgress(testPlan?.customFields?.frequency)"
                  :percentage="testPlan?.customFields?.progress"
                  :case-count="getObjectCount(testPlan?.customFields?.frequency)"
                />
              </div>
              <v-skeleton-loader
                v-else
                class="rounded-lg"
                height="24"
                width="100"
                type="button"
              />
            </div>
            <div v-if="testPlan.description">
              <v-tooltip
                v-if="!skeletonLoaderState"
                bottom
                max-width="430px"
                :disabled="testPlan.description?.length < 61"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <p
                    class="edit-plan mb-0"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ testPlan.description }}
                  </p>
                </template>
                <span>{{ testPlan.description }}</span>
              </v-tooltip>
              <v-skeleton-loader
                v-else-if="skeletonLoaderState"
                height="24"
                width="350"
                type="text"
              />
            </div>

            <!-- References Section in Header -->
            <div
              v-if="!skeletonLoaderState && testPlan.references && testPlan.references.length > 0"
              class="mt-3"
            >
              <h4 class="custom_field_heading mb-2">
                {{ $t('references') }}
              </h4>
              <div class="d-flex flex-wrap gap-2">
                <v-tooltip
                  v-for="(reference, index) in testPlan.references"
                  :key="index"
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <div
                      v-bind="attrs"
                      class="reference-chip d-flex align-center justify-space-between w-full px-2 py-1 rounded-lg mr-2 mb-2"
                      style="background: #F2F4F7; border: 1px solid #E4E7EC; cursor: pointer; max-width: 200px;"
                      v-on="on"
                      @click="window.open(reference.externalLink, '_blank')"
                    >
                      <div
                        class="d-flex align-center"
                        style="min-width: 0; flex: 1;"
                      >
                        <span
                          class="fs-12px text-theme-label mr-1 text-truncate"
                          style="min-width: 0; flex: 1; font-weight: 500;"
                        >{{ reference.name }}</span>
                      </div>
                      <a
                        :href="reference.externalLink"
                        target="_blank"
                        class="reference-link"
                        style="text-decoration: none; color: inherit;"
                        @click.stop
                      >
                        <v-icon
                          size="12"
                          class="text-theme-secondary"
                        >
                          mdi-arrow-top-right
                        </v-icon>
                      </a>
                    </div>
                  </template>
                  <span>{{ reference.name }}</span>
                </v-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="_writeEntity"
          class="d-flex flex-row justify-end"
        >
          <template v-if="!skeletonLoaderState">
            <v-btn
              color="#F2F4F7"
              :class="{'text-capitalize btn-theme rounded-lg mr-3': true, 'disabled-action': isProjectArchived }"
              height="40px"
              depressed
              :loading="btnRerunLoading"
              @click="handleRerunClick"
            >
              {{ $t('testruns.rerun') }}
            </v-btn>
          </template>
          <v-skeleton-loader
            v-else
            class="rounded-lg mr-3"
            height="40"
            width="125"
            type="button"
          />
          <template v-if="!skeletonLoaderState">
            <v-btn
              depressed
              color="primary"  
              height="40px"
              :class="{'text-capitalize btn-theme rounded-lg mr-3': true, 'disabled-action': isProjectArchived }"
              :loading="btnCompleteLoading"
              @click="statusDialog = true"
            >
              {{ $t('testruns.complete') }}
            </v-btn>
          </template>
          <v-skeleton-loader
            v-else
            class="rounded-lg primary"
            height="40"
            width="125"
            type="button"
          />
        </div>
      </div>
      <v-sheet
        class="white"
        color="#F2F4F7"
        rounded="lg"
        :outlined="true"
      >
        <div class="d-flex align-center justify-end">
          <template v-if="!skeletonLoaderState">
            <v-tooltip bottom>
              <template #activator="{ on, attrs }">
                <v-btn
                  icon
                  depressed
                  :ripple="false"
                  plain
                  v-bind="attrs"
                  v-on="on"
                  @click="toggleView('list')"
                >
                  <ViewListSelectedIcon v-if="!listView" />
                  <ViewListIcon v-else />
                </v-btn>
              </template>
              <span>{{ $t('List View') }}</span>
            </v-tooltip>
          </template>
          <v-skeleton-loader
            v-else
            class="rounded-lg"
            height="35"
            width="35"
            type="button"
          />
          <template v-if="!skeletonLoaderState">
            <v-tooltip bottom>
              <template #activator="{ on, attrs }">
                <v-btn
                  icon
                  depressed
                  :ripple="false"
                  plain
                  v-bind="attrs"
                  v-on="on"
                  @click="toggleView('dashboard')"
                >
                  <ViewDashboardIcon v-if="!listView" />
                  <ViewDashboardSelectedIcon v-else />
                </v-btn>
              </template>
              <span>{{ $t('Dashboard View') }}</span>
            </v-tooltip>
          </template>
          <v-skeleton-loader
            v-else
            class="rounded-lg"
            height="35"
            width="35"
            type="button"
          />
        </div>
      </v-sheet>
    </v-card>
    <div>
      <v-card
        v-if="isListView"
        class="py-6 px-6 mt-3 app-height-global"
        rounded="lg"
        elevation="0"
        width="100%"
      >
        <div class="d-flex justify-space-between">
          <div class="d-flex justify-start">
            <search-field
              v-model="searchFilter"
              class="search-input mr-2"
              :placeholder="$t('searchByName')"
            />
            <PlanFilter 
              :configurations="configurations" 
              :milestones="activeMilestones"  
              :tags="tags"
              @applyFilters="applyFilters" 
            />
          </div>

          <SettingsMenu 
            table-type="testPlanRerun" 
          />
        </div>
        
        <PlansList
          :write-entity="_writeEntity"
          :delete-entity="_deleteEntity"
          :filtered-headers="filteredHeaders"
          :filtered-items="filteredRuns"
          :item-key="itemKey"
          :row-class="rowClass"
          :clear-selection="clearSelection"
          :total-items="totalRows"
          :current-page="currentPage"
          :items-per-page="perPage"
          :relations-loading="relationsLoading"
          @edit-item="editItem"
          @select-row="handleRowClick"
          @select-item="setselected"
          @delete-item="confirmDeleteRun"
          @update-pagination="onUpdatePagination"
        />

        <select-test-run-status-dialog
          v-model="showRerunDialog"
          @close="handleCloseRerunDialog"
          @rerun="handleRerunPlan"
        />
      </v-card>
      <Dashboard
        v-if="isDashboardView"
        :style="{
          'margin-top': '10px',
        }"
        :is-ready="true"
        :show-archived="false"
      />
    </div>

    <PlansListFilter
      :open="isOpenFilter"
      @filters="applyFilters"
      @close="isOpenFilter = false"
    />

    <PlanDiscardDialog
      v-model="showConfirmDeleteDialog"
      :title="$t('plans.deleteDialog.title')"
      :content="$t('plans.deleteDialog.content')"
      :run_name="selectedRunName"
      :btn_label="$t('delete')"
      color="danger"
      @close="handleCloseClick"
      @handleConfirmClick="handleDeleteRun"
    />
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import StatusesDialog from '@/components/TestPlans/StatusesDialog'
import SearchField from '@/components/Form/SearchField';
import PlansList from '@/views/Tests/Plans/Components/List/PlansList';
import SelectTestRunStatusDialog from '@/views/Tests/Plans/Components/SelectTestRunStatusDialog';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import PlanFilter from '@/components/TestPlans/PlanFilter.vue';
import PlansListFilter from '@/views/Tests/Plans/Components/List/PlansListFilter';
import PlanDiscardDialog from '@/components/TestPlans/PlanDiscardDialog.vue';

import makeMilestonesService from '@/services/api/milestone'
import makePlanService from '@/services/api/plan'
import makeConfigurationService from '@/services/api/configuration' 
import makeTagService from '@/services/api/tag';
import makeRunService from '@/services/api/run';
import handleLoading from '@/mixins/loader.js'
import { showErrorToast, showSuccessToast } from '@/utils/toast';
import projectStatus from '@/mixins/projectStatus';
import ProgressBar from '@/components/base/ProgressBar.vue';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import ViewListSelectedIcon from '@/assets/svg/view-list-selected.svg';
import ViewDashboardIcon from '@/assets/svg/left-menu/dashboard.svg';
import Dashboard from "@/views/Dashboard";

let planService;
let runsService;

export default {
  components: {
    SelectTestRunStatusDialog,
    SearchField,
    SettingsMenu,
    PlanFilter,
    PlansListFilter,
    PlansList,
    PlanDiscardDialog,
    ProgressBar,
    StatusesDialog,
    ViewDashboardIcon,
    ViewListSelectedIcon,
    Dashboard
  },
  mixins: [projectStatus, colorPreferencesMixin, handleLoading],
  data() {
    return {
      showRerunDialog: false,
      menuOpen: false,
      headers: [],
      isOpenFilter: false,
      appliedFilters: null,
      itemKey: 'uid',
      listView: false,
      clearSelection: false,
      rowClass: () => 'project-item',
      milestones: [],
      configurations: [],
      tags: [],
      isFilter: false,
      searchFilter: '',
      selecteditems: [],
      selectedRun: {},
      btnCompleteLoading: false,
      btnRerunLoading: false,
      showConfirmDeleteDialog: false,
      statusDialog: false,
      testPlan: {
        customFields: {}
      },
      totalRows: 0,
      currentPage: 1,
      perPage: 10,
      runs: [],
      relationsLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      currentOrg: 'user/currentAccount',
      dynamicHeaders:'headers/dynamicHeaders'
    }),
    _writeEntity(){
      return this.authorityTo('write_entity')
    },
    _deleteEntity(){
      return this.authorityTo('delete_entity')
    },
    activeMilestones() {
      return this.milestones.filter((milestone) => !milestone?.archivedAt && !milestone?.deletedAt);
    },
    filteredHeaders() {
      const filtered = this.headers.filter((header) => header.checked);
      return filtered
    },
    isRunsHasData() {
      return this.runs.length > 0;
    },
    selectedRunName() {
      return this.selectedRun?.name || '';
    },
    testPlanName() {
      return this.testPlan?.name || '';
    },
    isListView(){
      return this.$route.query.view === 'list'
    },
    isDashboardView(){
      return this.$route.query.view === 'dashboard';
    },
    filteredRuns() {
      let filtered = this.runs;
      if (this.searchFilter) {
        filtered = filtered.filter((item) => this.matchesFilter(item));
      }
      if (this.isFilter && this.appliedFilters) {
        filtered = filtered.filter((item) => this.matchApplyFilter(item));
      }
      return filtered;
    },
    listHeaders() {
      const filtered = this.headers.filter((header) => header.checked);
      if (filtered.length < this.headers.length)
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.isColumnFilter = true
      else
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.isColumnFilter = false
      return filtered;
    },
    totalPages() {
      return Math.ceil(this.totalRows / this.perPage);
    },

  },
  async created() {
    if(!this.dynamicHeaders.testPlanRerun) {
      this.initializeHeaders({ type: 'testPlanRerun' });
    }
    this.headers = this.dynamicHeaders.testPlanRerun;
    planService = makePlanService(this.$api);
    runsService = makeRunService(this.$api);
    
    const validViews = ['list', 'dashboard'];
    const view = validViews.includes(this.$route.query.view) ? this.$route.query.view : 'list';
    await this.toggleView(view)
  },
  mounted() {
    this.init();  
  },
  methods: {
    ...mapActions("headers", ['initializeHeaders']),
    handleTab(value) {
      this.tab = value;
    },
    handleCloseClick() {
      this.showConfirmDeleteDialog = false;
      this.selectedRun = {};
      this.clearSelection = true;
    },
    handleRerunClick() {
      this.showRerunDialog = true;
    },
    handleCloseRerunDialog() {
      this.showRerunDialog = false;
    },
    handleEditClick() {
      this.isEditPlan = true;
    },
    handleBackClick() {
      this.$router.replace({ name: 'TestPlans' });
    },
    async init() {
      try {
        this.showSkeletonLoader();
        await Promise.all([
          this.findTestPlan(),
          this.getMilestones(),
          this.getConfigurations(),
          this.getTags()
        ]);
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'plans' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
    async duplicateTestPlan(){
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const planId = Number(this.$route.params.planId);
      let selectedItemsUids  = this.selecteditems.map(item => item.uid)
      let data = {
        planUid: planId,
        runUids: selectedItemsUids
      }
      try {
        const response = await planService.duplicateTestPlan(handle, projectKey, data);
        console.log(response);
        return response;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'milestones' }, err?.response?.data);
        return [];
      }
    },
    async toggleView(view = 'list'){
      if (this.$route.query.view === view) {
        return;
      }

      await this.$router.push({
        name: 'TestPlanRerun',
        params: {
          ...this.$route.params,
        },
        query: {
          view: view
        },
      })
    },
    editItem(item) {
      this.selectedRun = item;
      this.$router.push({
        name: 'TestRunEdit',
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key,
          id: item.uid,
        },
        query: {
          isPlanRerunEdit: true,
          planId: this.$route.params.planId,
        }
      });
    },
    async refreshData() {
      await this.fetchPlanRuns();
    },
    async getMilestones() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const milestoneService = makeMilestonesService(this.$api);
      try {
        const response = await milestoneService.getMilestones(handle, projectKey);
        this.milestones = response.data?.items;
        return response.data?.items;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'milestones' }, err?.response?.data);
        return [];
      }
    },
    async findTestPlan(){
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const planId = this.$route.params.planId;
      
      try {
        const response = await planService.findTestPlan(handle, projectKey, planId);
        this.testPlan = response.data;
        
        await this.fetchPlanRuns();
        
        return response.data;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'plans' }, err?.response?.data);
        this.totalRows = 0;
        return [];
      }
    },
    async fetchPlanRuns() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const planId = this.$route.params.planId;
      
      const queryParams = {
        limit: this.perPage,
        offset: (this.currentPage - 1) * this.perPage,
        planUid: planId
      };

      try {
        const response = await runsService.getRuns(handle, projectKey, queryParams);
        this.runs = response.data?.items || [];
        this.totalRows = response.data?.count || 0;
        
        if (this.runs.length > 0) {
          await this.fetchRunRelations(handle, projectKey, this.runs);
        }
        
        return response.data;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'runs' }, err?.response?.data);
        this.runs = [];
        this.totalRows = 0;
        return [];
      }
    },
    async fetchRunRelations(handle, projectKey, runs) {
      const runUids = runs.map(run => run.uid);
      
      try {
        this.relationsLoading = true;
        
        const [tagsRes, milestonesRes, configurationsRes] = await Promise.allSettled([
          runsService.getRunRelations(handle, projectKey, 'tag', runUids),
          runsService.getRunRelations(handle, projectKey, 'milestone', runUids),
          runsService.getRunRelations(handle, projectKey, 'config', runUids)
        ]);

        if (tagsRes.status === 'fulfilled' && tagsRes.value.status === 200) {
          const tagRelations = tagsRes.value.data;
          this.runs = this.runs.map(run => ({
            ...run,
            tags: tagRelations[run.uid] || []
          }));
        }

        if (milestonesRes.status === 'fulfilled' && milestonesRes.value.status === 200) {
          const milestoneRelations = milestonesRes.value.data;
          this.runs = this.runs.map(run => ({
            ...run,
            testMilestones: milestoneRelations[run.uid] || []
          }));
        }

        if (configurationsRes.status === 'fulfilled' && configurationsRes.value.status === 200) {
          const configurationRelations = configurationsRes.value.data;
          this.runs = this.runs.map(run => ({
            ...run,
            configs: configurationRelations[run.uid] || []
          }));
        }

      } catch (error) {
        console.warn('Failed to fetch run relations:', error);
      } finally {
        this.relationsLoading = false;
      }
    },
    async getConfigurations() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const configurationService = makeConfigurationService(this.$api);
      try {
        const response = await configurationService.getConfigurations(handle, projectKey, 10, 0);
        this.configurations = response.data?.configurations;
        return response.data?.configurations;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
        return [];
      }
    },
    async getTags() {
      const handle = this.$route.params.handle;
      const tagService = makeTagService(this.$api);
      try {
          const response = await tagService.getTags(handle, 'plans');
          this.tags = response.data
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'tags' }, err?.response?.data);
        return [];
      }
    },
    async handleRowClick(item) {
      this.$router.push({
          name: 'TestRunCaseEdit',
          params: {
            handle: this.$route.params.handle,
            key: this.$route.params.key,
            id: item.uid,
          }
      });
    },
    confirmDeleteRun(item) {
      this.selectedRun = item;
      this.showConfirmDeleteDialog = true;
    },
    async handleDeleteRun(){
      try {
        this.showSkeletonLoader();
        await runsService.deleteTestRuns(this.$route.params.handle, this.$route.params.key, {
          runUids: [this.selectedRun.uid],
        });
        await this.refreshData()
        showSuccessToast(this.$swal, this.$t('toast.deleteSuccess', { item: this.$t('plans.title_single') }));
      } catch (error) {
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, 'deleteError', { item: 'Test run' }, error?.response?.data);
      } finally {
        this.handleCloseClick();
        this.hideSkeletonLoader();
      }

    },
    async handleRerunPlan(){
      try {
        this.btnRerunLoading = true;
        this.showRerunDialog = false;

        if (!this.isRunsHasData) {
          showErrorToast(
            this.$swal,
            this.$t('toast.planRunsRerunError', { item: this.$t('plans.title_single') })
          );
          return; 
        }

        const response = await this.duplicateTestPlan();
        if(response.status === 200){
          showSuccessToast(this.$swal, `${this.$t('rerunned')} "${ this.testPlanName }" ${this.$t('testPlan') }`);
          // this.$router.replace({ name: 'TestPlans' });
        }
      } catch (error) {
        showErrorToast(this.$swal, this.$t('toast.fetchError', { item: 'plans' }), {}, error?.response?.data);
      } finally {
        this.btnRerunLoading = false;
      }
    },
    matchApplyFilter(item) {
      if (this.appliedFilters?.panel_priority.length > 0 && 
          !this.appliedFilters.panel_priority.includes(item.priority)) {
        return false;
      }
      if (this.appliedFilters?.panel_status.length > 0 && 
          !this.appliedFilters.panel_status.includes(item.status)) {
        return false;
      }
      if (this.appliedFilters?.panel_milestone.length > 0 && 
          !this.appliedFilters.panel_milestone.includes(item.customFields.milestone) && 
          !this.appliedFilters.panel_milestone.includes('None')) {
        return false;
      }

      if (this.appliedFilters?.panel_tag.length > 0 && 
          !this.appliedFilters.panel_tag.includes(item.customFields.tags) && 
          !this.appliedFilters.panel_tag.includes('none')) {
        return false;
      }
      if (this.appliedFilters?.testcases_range[0] > item.customFields.caseCount || 
          this.appliedFilters?.testcases_range[1] < item.customFields.caseCount) {
        return false;
      }
      if (this.appliedFilters?.progress_range[0] > item.customFields.progress || 
          this.appliedFilters?.progress_range[1] < item.customFields.progress) {
        return false;
      }
      if (this.appliedFilters?.dateRange.start && this.appliedFilters?.dateRange.start > item.createdAt || 
          this.appliedFilters?.dateRange.end && this.appliedFilters?.dateRange.end < item.createdAt) {
        return false;
      }
      return true;
    },
    matchesFilter(item) {
      const lowerCaseFilter = this.searchFilter.toLowerCase();
      return item.name.toLowerCase().includes(lowerCaseFilter)
    },
    applyFilters(filters) {
      if (filters) {
        this.appliedFilters = filters;
        this.isFilter = true;
      } else {
        this.isFilter = false;
      }
      this.currentPage = 1;
      this.refreshData()
    },
    onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      if (newPage !== this.currentPage || newItemsPerPage !== this.perPage) {
        this.currentPage = newPage;
        this.perPage = newItemsPerPage;
        this.refreshData();
      }
    },
    setselected(selecteditems) {
      this.clearSelection = false
      this.selecteditems = selecteditems
    },
    async handleCompleteClick(status){
      const payload = {
        finalStatus: status
      }
      try {
        this.btnCompleteLoading = true;
        this.statusDialog = false;
        const response = await planService.updateTestPlan(this.currentAccount.handle, this.$route.params.key, this.$route.params.planId, payload);
        if(response.status === 200){
          showSuccessToast(this.$swal, this.$t('toast.updateSuccess', { item: this.$t('plans.title_single') }));
          this.refreshData();
          this.$router.replace({ name: 'TestPlans' });
        }
      } catch (error) {
        showErrorToast(this.$swal, this.$t('toast.fetchError', { item: this.$t('plans.title_single') }), {}, error?.response?.data);
      } finally {
        this.btnCompleteLoading = false;
      }

    },
    
  },
};
</script>

<style lang="scss" scoped>
.edit-plan {
  max-width: 476px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #667085; 
  font-size: 14px; 
  line-height: 14px
}
.test-plans-detail {
  height: 100%;
  max-height: calc(100vh - 90px);
  background: #f2f4f7;

  display: flex;
  flex-direction: column;
}

.test-plans-detail-wrapper {
  height: 100%;
  max-height: calc(100% - 116px - 8px);
  background: #ffffff;
  border-radius: 8px;
}

.edit-inputs{
  max-width: 476px;
}

.v-input {
  .v-input__control {
    .v-input__slot {
      textarea{
        line-height: 14px;
      }
    }
  }
}

.none{
  display: none;
}

.btn-show{
  display: block !important;
}

.action-button{
  width: 140px;
}

.reference-chip {
  transition: all 0.2s ease;
  cursor: pointer;
}

.reference-chip:hover {
  background: #E4E7EC !important;
  transform: translateY(-1px);
}

.reference-link {
  text-decoration: none;
  color: inherit;
}

.reference-link:hover {
  opacity: 0.8;
}

.custom_field_heading {
  color: #667085;
  font-weight: 400;
  font-size: 13px;
  margin: 12px 0 4px 0px;
}
</style>