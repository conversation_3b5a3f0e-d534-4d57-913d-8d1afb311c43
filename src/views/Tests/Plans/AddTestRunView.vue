<template>
  <div
    fluid
    class="pa-0"
  >
    <v-card
      class="pt-6 px-6 mt-3 plans-height"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <div class="">
        <div class="d-flex align-center mb-3">
          <button
            plain
            class="btn-nav-back font-inter"
            @click="handleBackClick($event)"
          >
            <v-icon color="blue">
              mdi-chevron-left
            </v-icon>
            <span class="d-flex-inline justify-center align-center ma-0 blue--text">
              {{ backTitle }}
            </span>
          </button>
          <button
            class="btn-close ml-auto"
            @click="handleBackClick($event)"
          >
            <v-icon>mdi-close</v-icon>
          </button>
        </div>

        <h2
          v-if="!skeletonLoaderState"
          class="font-inter text-start"
        >
          {{ $t('plans.duplicate.rerun.title') }}
        </h2>
        <v-skeleton-loader
          v-else
          height="36"
          width="140"
          type="heading"
        />
        <div
          v-if="!skeletonLoaderState"
          class="mt-4 d-flex"
        >
          <v-chip
            class="font-inter font-weight-bold px-4"
            width="200px"
            label
            :class="{ 'blue--text': tableFilter === 'all' }"
            :color="tableFilter === 'all' ? 'blue-light' : 'gray-light'"
            @click="changeFilter('all')"
          >
            {{ $t('testruns.unlinked') }} <span class="ml-2">{{ runTotalRows }}</span>
          </v-chip>
          <v-chip
            class="font-inter font-weight-bold px-4 ml-2"
            label
            width="200px"
            :class="{ 'blue--text': tableFilter === 'selected' }"
            :color="tableFilter === 'selected' ? 'blue-light' : 'gray-light'"
            @click="changeFilter('selected')"
          >
            {{ $t('testruns.linked') }} <span class="ml-2">{{ selectedTestRunsLength }}</span>
          </v-chip>
        </div>
        <div
          v-else
          class="mt-4 d-flex"
        >
          <v-skeleton-loader
            class="rounded-sm d-flex gap-2 chip-primary"
            height="32"
            width="200"
            type="button@2"
          />
        </div>
      </div>

      <div class="d-flex justify-space-between">
        <!-- <div class="left-width">
          <div
            class="sticky-on-scroll mt-5 pa-4 bg-gray-theme rounded-lg
          d-flex flex-column justify-space-between
          "
          >
            <div
              class="d-flex flex-column"
            >
              <v-text-field
                :placeholder="$t('search')"
                background-color="#FFFFFF"
                class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0 mb-3 flex-inherit"
                height="38"
                dense
                hide-details
              >
                <template v-slot:prepend-inner> 
                  <SearchIcon />
                </template>
              </v-text-field>

              <v-btn
                text
                color="#061AAE"
                class="text-capitalize rounded-lg bg-gray-theme text-theme-secondary justify-start mb-3"
                width="100%"
              >
                {{ $t('testruns.all') }}
              </v-btn>

              <v-btn
                text
                color="#061AAE"
                class="text-capitalize rounded-lg bg-gray-theme text-theme-secondary justify-start mb-3"
                width="100%"
              >
                <div class="d-flex align-center">
                  <div class="mr-2">
                    <UnlinkedIcon />
                  </div>
                  <span>{{ $t('testruns.unlinked') }} 0</span>
                </div>
              </v-btn>

              <v-btn
                text
                color="#061AAE"
                class="text-capitalize rounded-lg bg-theme-primary-light text-theme-secondary justify-start mb-2"
                width="100%"
                @click="onToPlanExpanded"
              >
                <div class="d-flex justify-space-between w-full">
                  <div class="d-flex align-center">
                    <div class="mt-1 mr-2">
                      <LinkedIcon />
                    </div>
                    <span>{{ $t('testruns.toPlans') }} {{ getActivePlansCount }}</span>
                  </div>
                  <div>
                    <v-icon>
                      {{ isToPlanExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }} 
                    </v-icon>
                  </div>
                </div>
              </v-btn>

              <div
                v-if="isToPlanExpanded"
                class="plan-list-wrapper"
              >
                <div
                  v-for="(plan, index) in getActivePlans"
                  :key="index"
                >
                  <v-tooltip
                    top
                    left
                    max-width="200px"
                    :disabled="plan.name.length < 15"
                    content-class="tooltip-theme"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        text
                        color="#667085"
                        class="text-capitalize btn-full font-weight-regular rounded-lg justify-start mb-2"
                        width="100%"
                        v-bind="attrs"
                        v-on="on"
                      >
                        <div class="d-flex justify-between align-center w-full">
                          <div class="text-truncate">
                            {{ plan.name }}
                          </div>
                          <div>(0)</div>
                        </div>
                      </v-btn>
                    </template>
                    <span>{{ plan.name }}</span>
                  </v-tooltip>
                </div>
              </div>
              <div
                v-else
                class="plan-list-wrapper"
              />
            </div>
            <div
              class="sticky-scroll"
            >
              <div class="text-left">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("createQuickPlan") }} 
                </v-label>
                <v-text-field
                  :placeholder="$t('enterName')"
                  height="38"
                  background-color="#FFFFFF"
                  class="field-theme mt-0 pt-1"
                />
              </div>
              <v-btn
                type="submit"
                block
                color="primary"
                :depressed="true"
                class="btn-theme"
                width="100%"
                height="40"
              >
                {{ $t("create") }}
              </v-btn>
            </div>
          </div>
        </div> -->


        <div class="right-width">
          <div class="pl-0">
            <div  
              v-if="runs.length == 0 && !skeletonLoaderState"
              class="d-flex flex-column align-center"
            >
              <ActiveEmptyState
                :image-src="require('@/assets/png/auth-banner.png')"
                :title="$t('plans.beginTestNewTitle')"
                :button-text="$t('testruns.create_testrun.title')"
                button-color="primary"
                @button-click="openCreateTestRun"
              >
                <template slot="description">
                  <div class="text-center">
                    <div class="mb-0 mt-3">
                      {{ $t('plans.addRuns.description.part1') }}
                    </div>
                    <div class="mb-0">
                      {{ $t('plans.addRuns.description.part2') }}
                    </div>
                  </div>
                </template>
              </ActiveEmptyState>
            </div>
            <div
              v-else
              class="mt-5"
            >
              <div class="d-flex flex-row justify-space-between align-center">
                <div class="d-flex flex-row justify-start align-center">
                  <search-field
                    v-model="searchFilter"
                    class="search-input mr-2"
                    :placeholder="$t('searchByName')"
                  />
                  <RunFilter
                    :configurations="[]" 
                    :tags="tags"
                    :milestones="activeMilestones"
                    @applyFilters="applyFilters"
                  />
                </div>
                <SettingsMenu 
                  table-type="addTestRun" 
                />
              </div>
              <test-run-list
                class="test-runs-list"
                :data="runs"
                :tab="tableFilter"
                :total-items="runTotalRows"
                :items-per-page="runPerPage"
                :current-page="runCurrentPage"
                :skeleton-loader-state="skeletonLoaderState"
                :headers="isTableFilterAll ? filteredHeadersAll : filteredHeadersSelected"
                :selected-runs="selectedRunsLocal"
                :global-configuration="globalConfiguration"
                :configurations="configurations"
                @selectTestRun="selectTestRun"
                @addConfiguration="handleAddConfiguration"
                @update-pagination="onUpdateRunPagination"
              />
              
              <div
                class="action-btn-wrapper pa-3 d-flex flex-column"
              >
                <Pagination
                  v-if="!skeletonLoaderState && runTotalRows > 0"
                  :page="runCurrentPage"
                  :items-per-page="runPerPage"
                  :total-pages="totalPages"
                  :total-items="runTotalRows"
                  @update:pagination="onUpdateRunPagination"
                />
                <slot
                  v-if="!skeletonLoaderState"
                  name="action"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </v-card>
    <DuplicateAndApplyConfigDialog
      is-add-config
      :value="duplicateAndApplyConfigDialog"
      :configurations="configurationSorted"
      :button-loading="buttonLoading"
      :button-loading-items="buttonLoadingItems"
      :is-menu-visible="buttonLoading"
      :selected-run="selectedRun"
      :global-configuration="globalConfiguration"
      @handleDuplicateAndApplyConfig="handleDuplicateAndApplyConfig"
      @close="onCloseDuplicateAndApplyConfigDialog"
      @addConfiguration="addConfiguration" 
      @addConfigurationItems="addConfigurationItems"
      @deleteConfigurationGroup="deleteConfigurationGroup"
      @deleteConfigurationItem="deleteConfigurationItem"
      @editConfigurationGroup="editConfigurationGroup"
    />
  </div>
</template>
<script>
import TestRunList from '@/views/Tests/Plans/Components/TestRunList';
import DuplicateAndApplyConfigDialog from '@/components/TestRuns/DuplicateAndApplyConfigDialog.vue';
import RunFilter from '@/components/TestRuns/RunFilter.vue';
import { mapGetters, mapState, mapActions } from 'vuex';
import makePlanService from '@/services/api/plan';
import makeConfigurationService from '@/services/api/configuration' 
import makeRunService from '@/services/api/run';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import { formatDate } from '@/utils/util';
import makeTagsService from '@/services/api/tag';
import makeMilestonesService from '@/services/api/milestone'
import handleLoading from '@/mixins/loader.js'
import SearchField from '@/components/Form/SearchField'
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue';
import Pagination from '@/components/base/Pagination.vue';


let runsService;

export default {
  components: {
    TestRunList,
    RunFilter,
    DuplicateAndApplyConfigDialog,
    SettingsMenu,
    SearchField,
    ActiveEmptyState,
    Pagination

  },
  mixins:[handleLoading],
  props: {
    globalConfiguration: {
      type: Object,
    },
    value: {
      type: Array,
    },
    backTitle: {
      type: String,
    },
    openAddConfigurationDialog: {
      type: Boolean,
    },
  },
  emits: ['update:value'],
  data() {
    return {
      filter: 'ongoing',
      selectedRun: null,
      isColumnFilter: false,
      selectedRunsLocal: this.value,
      tableFilter: 'all',
      searchFilter: '',
      appliedFilters: null,
      headers: [],
      tags: [],
      milestones: [],
      menuOpen: false,
      isOpenAddConfiguration: false,
      isToPlanExpanded: false,
      duplicateAndApplyConfigDialog: false,
      buttonLoading: false,
      buttonLoadingItems: false,
      runCurrentPage: 1,
      runPerPage: 10,
      runTotalRows: 0,
      configurations: [],
      headersTableAll: [],
      headersTableSelected: [],
      items: [],
    };
  },
  computed: {
    ...mapState('user', ['currentAccount']),
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders'
    }),
    // Configurations will be sorted on backend, so just return them directly
    configurationSorted() {
      return this.configurations || [];
    },
    totalPages() {
      return Math.ceil(this.runTotalRows / this.runPerPage);
    },
    runs() {
      const finalRuns = this.items?.map((item) => {
        if(item.archivedAt)
          return false;
          
        const createdFormat = new Date(item.createdAt);
        const createdAt = formatDate(createdFormat, 'yyyy-MM-dd');
        const runTags = item.testMilestones?.map(item => item.uid);
        if(this.searchFilter.length && !this.matchesFilter(item))
          return false;
        if(this.appliedFilters){
          if (this.appliedFilters.testCasesRange && Array.isArray(this.appliedFilters.testCasesRange) && this.appliedFilters.testCasesRange.length >= 2) {
           if ((item?.testcases < this.appliedFilters.testCasesRange[0]) || 
            (item?.testcases > this.appliedFilters.testCasesRange[1])) {
            return false;
          }
        }
        if (this.appliedFilters.progressRange && Array.isArray(this.appliedFilters.progressRange) && this.appliedFilters.progressRange.length >= 2) {
          if ((item?.percentage < this.appliedFilters.progressRange[0]) || 
            (item?.percentage > this.appliedFilters.progressRange[1])) {
            return false;
         }
        }
        if (this.appliedFilters.panelPriority && Array.isArray(this.appliedFilters.panelPriority) && 
          this.appliedFilters.panelPriority.length > 0 && !this.appliedFilters.panelPriority.includes(item.priority)) {
           return false;
        }
        if (this.appliedFilters.panelStatus && Array.isArray(this.appliedFilters.panelStatus) && 
          this.appliedFilters.panelStatus.length > 0 && !this.appliedFilters.panelStatus.includes(item.status)) {
           return false;
        }
        if (this.appliedFilters.dateRange && item.createdAt && 
          ((item.createdAt < this.appliedFilters.dateRange.start) || 
           (item.createdAt > this.appliedFilters.dateRange.end))) {
           return false;
        }
        if (this.appliedFilters.panelMilestone && Array.isArray(this.appliedFilters.panelMilestone) && 
          this.appliedFilters.panelMilestone.length > 0) {
          let milestoneExists = false;
           for (const milestone of this.appliedFilters.panelMilestone) {
            if (runTags.includes(milestone)) {
              milestoneExists = true;
           }
          }
          if (!milestoneExists) {
            return false;
          }
        }
      
        if (this.appliedFilters.panelTag && Array.isArray(this.appliedFilters.panelTag) && 
          this.appliedFilters.panelTag.length > 0) {
          let tagExists = false;
          for (const tag of this.appliedFilters.panelTag) {
           if (item?.customFields?.tags?.includes(tag)) {
            tagExists = true;
           }
         }
          if (!tagExists) {
           return false;
          }
        }
      }

        return {
          ...item,
          createdAt: createdAt
        }
      }).filter(Boolean);
      return finalRuns
    },
    selectedTestRunsLength() {
      return this.selectedRunsLocal.length
    },
    filteredHeaders() {
      const filtered = this.headers.filter((header) => header.checked);
      if (filtered.length < this.headers.length)
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.isColumnFilter = true
      else
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.isColumnFilter = false
      return filtered;
    },
    filteredMenuHeaders() {
      const filtered = this.headers.filter((header) => header.text != 'Actions');
      return filtered;
    },
    getActivePlans() {
      return this.plans?.filter(plan => plan.archivedAt == null);
    },
    getActivePlansCount() {
      return this.plans?.filter(plan => plan.archivedAt == null).length;
    },
    activeMilestones() {
      return this.milestones.filter((milestone) => !milestone?.archivedAt && !milestone?.deletedAt);
    },
    isTableFilterAll() {
      return this.tableFilter == 'all';
    },
    filteredHeadersAll() {
      return this.headersTableAll?.filter((header) => header.checked);
    },
    filteredHeadersSelected() {
      return this.headersTableSelected?.filter((header) => header.checked);
    },
  },
  watch: {
    openAddConfigurationDialog(newVal) {
      if(newVal){ 
        this.duplicateAndApplyConfigDialog = true;
      }else{
        this.onCloseDuplicateAndApplyConfigDialog()
      }
    }
  },
  async created() {
   
    if(!this.dynamicHeaders.addTestRun) {
      this.initializeHeaders({ type: 'addTestRun' });
    }
    this.headersTableAll = this.dynamicHeaders.addTestRun;

    if(!this.dynamicHeaders.addTestRunSelected) {
      this.initializeHeaders({ type: 'addTestRunSelected' });
    }
    this.headersTableSelected = this.dynamicHeaders.addTestRunSelected;
    runsService = makeRunService(this.$api);
    
  },
  async mounted(){
    let handle = this.currentAccount.handle;
    let projectKey = this.$route.params.key;
    await this.init([this.getProjectRun({handle, projectKey, perPage: this.runPerPage, currentPage: this.runCurrentPage})]);
    this.getAllTags(handle, 'runs');
    this.getMilestones();
    this.getTestPlans();
    this.getConfigurations();
  },  
  methods: {
    ...mapActions("headers", ['initializeHeaders']),
    async handleDuplicateAndApplyConfig(formattedConfigs) {
      if (this.selectedRun?.uid) {
        this.selectedRunsLocal = this.selectedRunsLocal.map(run => {
          if (run.uid === this.selectedRun.uid) {
            return {
              ...run,
              configuration: formattedConfigs
            };
          }
          return run;
        });

        const payload = {
          configs : formattedConfigs?.sets.flat()
        };

        const handle = this.$route.params.handle;
        const projectKey = this.$route.params.key;

        try {
          await runsService.updateTestRun(
            handle,
            projectKey,
            this.selectedRun.uid,
            payload
          );
          await this.getProjectRun({handle, projectKey});
          if(formattedConfigs.addedConfigCount > 0 && formattedConfigs.removedConfigCount > 0) {
            showSuccessToast(this.$swal, 'addRemoveConfigSuccess', { added: formattedConfigs.addedConfigCount, removed: formattedConfigs.removedConfigCount });
          } 
          else if(formattedConfigs.addedConfigCount > 0) {
            showSuccessToast(this.$swal, 'addConfigSuccess', { item: formattedConfigs.addedConfigCount });
          }
          else if(formattedConfigs.removedConfigCount > 0) {
            showSuccessToast(this.$swal, 'removeConfigSuccess', { item: formattedConfigs.removedConfigCount });
          }
          
        } catch(err) {
          showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
        } finally {
          this.duplicateAndApplyConfigDialog = false;
          this.$emit('configuration-dialog-closed');
        }

      } else {
        this.$emit('updateGlobalConfiguration', formattedConfigs);
      }
      this.onCloseDuplicateAndApplyConfigDialog();
    },
    async getProjectRun({handle, projectKey, perPage, currentPage}) {
      try {
        const queryParams = {
        limit: perPage || 10,
        offset: ((currentPage || 1) - 1) * (perPage || 10),
        }
        const response = await runsService.getRuns(handle, projectKey, queryParams);
        this.items = response.data.items;
        if (this.items.length > 0) {
          await this.fetchRunRelations(handle, this.$route.params.key, this.items);
        }
        this.runTotalRows = response.data?.count || response.data?.total || 0;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'runs' }, err?.response?.data);
      }
    },
    async fetchRunRelations(handle, projectKey, runs) {
      const runUids = runs.map(run => run.uid);
      
      try {
        this.relationsLoading = true;

        const configurationsRes = await runsService.getRunRelations(handle, projectKey, 'config', runUids)
        if (configurationsRes.status === 200) {
          const configurationRelations = configurationsRes.data;
          this.items = this.items.map(run => ({
            ...run,
            configs: configurationRelations[run.uid] || []
          }));
        }
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'runs' }, err?.response?.data);
      } finally {
        this.relationsLoading = false;
      }
    },
    onUpdateRunPagination({page, itemsPerPage}) {
      if (page !== this.runCurrentPage || itemsPerPage !== this.runPerPage) {
        this.runCurrentPage = page;
        this.runPerPage = itemsPerPage;

        this.getProjectRun({ 
          handle: this.$route.params.handle, 
          projectKey: this.$route.params.key, 
          perPage: this.runPerPage, 
          currentPage: this.runCurrentPage 
        });
      }
    },
    handleAddConfiguration(item) {
      this.selectedRun = item;
      this.selectedRunsLocal = Array.isArray(item) ? item : [item];
      this.duplicateAndApplyConfigDialog = true;
    },
    onToPlanExpanded(){
      this.isToPlanExpanded = !this.isToPlanExpanded
    },
    async editConfigurationGroup(data) {
      await this.updateConfiguration(data.uid, data.name, data?.items);
      await this.getConfigurations();
      showSuccessToast(this.$swal, 'deleteSuccess', { item: 'configuration item' });
    },
    async deleteConfigurationItem(data) {
      await this.updateConfiguration(data.uid, data.name, data.items);
      await this.getConfigurations();
      showSuccessToast(this.$swal, 'deleteSuccess', { item: 'configuration item' });
    },
    async deleteConfigurationGroup(group) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const configurationService = makeConfigurationService(this.$api);
      await configurationService.deleteConfiguration(handle, projectKey, group.uid);
      await this.getConfigurations();
      showSuccessToast(this.$swal, 'deleteSuccess', { item: 'configuration group' });
    },
    async addConfigurationItems(item) {
      this.buttonLoadingItems = true;
      try {

        const formatOptions = ()=>{
          return item.items.map(item =>
          {
            if (item.uid) {
              return { name: item.name, uid: item.uid }
            } else {
              return { name: item }
            }
          })
        }
        await this.updateConfiguration(item.uid, item.name, formatOptions());
        await this.getConfigurations();
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      } finally {
        this.buttonLoadingItems = false;
      }
    },
    async addConfiguration(configuration) {
      this.buttonLoading = true;
      try {
        await this.createConfigurations(configuration.newConfigurationName, configuration.options);
        await this.getConfigurations();
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      } finally {
        this.buttonLoading = false;
      }
    },
    async getTestPlans() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const testPlanService = makePlanService(this.$api);
      try {
        const response = await testPlanService.getPlans(handle, projectKey, 1000, 0);
        this.plans = response.data?.items;
        return response.data?.items;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'test plans' }, err?.response?.data);
        return [];
      }
    },
    async getConfigurations() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const configurationService = makeConfigurationService(this.$api);
      try {
        const response = await configurationService.getConfigurations(handle, projectKey, 1000, 0, 'name', 'asc');
        this.configurations = response.data?.configurations;
        return response.data?.configurations;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
        return [];
      }
    },
    async updateConfiguration(uid, name, options) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const configurationService = makeConfigurationService(this.$api);
      try {
        
        await configurationService.updateConfiguration(handle, projectKey, uid, {
          name: name,
          options: options,
        });

      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
        return [];
      }
    },
    async createConfigurations(name, options) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const configurationService = makeConfigurationService(this.$api);
      try {
        const response = await configurationService.createConfigurations(handle, projectKey, {
          name: name,
          options: options,
        });
        return response.data;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
        return [];
      }
    },
    async deleteConfigurations(id) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      const configurationService = makeConfigurationService(this.$api);
      try {
        const response = await configurationService.deleteConfiguration(handle, projectKey, id);
        return response.data;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
        return [];
      }
    },
    onCloseDuplicateAndApplyConfigDialog() {
      this.duplicateAndApplyConfigDialog = false;
      this.selectedRun = null;
      this.$emit('configuration-dialog-closed');
    },
    handleAddTestRuns() {
      this.$router.push({
        name: 'TestRunCreate',
        params: {
        handle: this.$route.params.handle,
        key: this.$route.params.key
        },
        query: {
          activeAddTestPlan: 'true'
        },
      });
    },
    handleColumnReset() {
      this.headers = this.headers?.map((header) => {
        header.isSelected = true;
        return header;
      })
    },
    handleBackClick(event) {
      event.preventDefault();
      this.$emit('back');
    },
    changeFilter(filter) {
      this.tableFilter = filter;
      this.$emit('onFilterChange', filter);
    },
    selectTestRun(runs) {
      this.selectedRunsLocal = runs
      this.$emit('input', this.selectedRunsLocal);
    },
    applyFilters(filters) {
      this.appliedFilters = filters;      
    },
    async getAllTags(handle, entityType) {
      const tagService = makeTagsService(this.$api);

      await tagService.getTags(
        handle,
        entityType
      ).then(response => {
        this.tags = response.data;
      })
    },
    async getMilestones(){
      const milestoneService = makeMilestonesService(this.$api);
      await milestoneService.getMilestones(
        this.$route.params.handle,
        this.$route.params.key
      ).then(response => {
        this.milestones = response.data.items;
      })
    },

    matchesFilter(item) {
      const lowerCaseFilter = this.searchFilter.toLowerCase();

      const nameMatch = item.name?.toLowerCase().includes(lowerCaseFilter);
      const sourceMatch = item.source?.toLowerCase().includes(lowerCaseFilter);

      return (nameMatch || sourceMatch) ?? false;
    },
    openCreateTestRun() {
      this.$router.push({
        name: 'TestRunCreate',
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key
        },
        query: {
          page: 'TestPlanCreate'
        }
      });
    }
  },
};
</script>
  
<style scoped>

.left-width {
    width: 12vw;
    min-width: 212px;
}
.right-width {
    width: 100vw;
}

.btn-nav-back {
  width: max-content;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #0c2ff3 !important;
  text-transform: none;
  opacity: 1;
  display: flex;
  align-items: center;
}

.plan-list-wrapper {
  height: calc(100vh - 43rem);
  overflow: auto;
  scrollbar-width: thin;
}
.sticky-on-scroll {
  position: -webkit-sticky;
  position: sticky;
  top: 12px;
  min-height: calc(100vh - 24px - 20rem);
  margin-bottom: 24px;
}
.plans-height {
  min-height: calc(100vh - 24px - 6.5rem);
  height: 100%;
}
.action-btn-wrapper {
  position: sticky;
  bottom: 0;
  background-color: white;
  align-items: flex-end;
  display: flex;
  justify-content: flex-end;
  z-index: 8;
}
</style>
