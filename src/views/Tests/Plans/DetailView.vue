<template>
  <div class="pl-3 pt-1">
    <v-card
      class="test-plans-section-header white pt-4 px-6 pb-4 mt-2"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <div class="d-flex flex-column">
        <div class="d-flex flex-row align-center justify-space-between">
          <button
            class="btn-back d-flex"
            @click="handleBackClick"
          >
            <v-icon color="blue">
              mdi-chevron-left
            </v-icon>
            <span class="d-flex-inline justify-center align-center ma-0 blue--text font-weight-bold">
              {{ $t("plans.create.backToPlans") }}
            </span>
          </button>
          <div class="d-flex flex-row justify-end align-center">
            <h4
              v-if="!skeletonLoaderState"
              class="font-weight-light mr-2"
            >
              {{ $t("status") }}:
            </h4>
            <v-skeleton-loader
              v-else
              class="mr-3"
              type="text"
              width="100px"
              height="24px"
            />
            <div>
              <v-menu
                v-if="!skeletonLoaderState"
                v-model="menuOpen"
                bottom
                offset-y
                right
              >
                <template #activator="{ on }">
                  <v-btn
                    dark
                    color="gray-100"
                    class="text-capitalize rounded-lg font-weight-regular black--text"
                    depressed
                    height="40px"
                    v-on="on"
                  >
                    {{ getStatusName(testPlan.customFields.status, statuses) }}
                    <v-icon size="20">
                      {{ menuOpen ? "mdi-chevron-up" : "mdi-chevron-down" }}
                    </v-icon>
                  </v-btn>
                </template>
                <v-list
                  dense
                  class="text-left"
                >
                  <v-list-item
                    v-for="(status, index) in activeStatuses"
                    :key="index"
                    @click="testPlan.customFields.status = status.id"
                  >
                    <v-list-item-title>{{ status.name }}</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
              <v-skeleton-loader
                v-else
                type="text"
                width="100px"
                height="40px"
              />
            </div>
          </div>
        </div>
        <div class="d-flex flex-column justify-start">
          <template v-if="!skeletonLoaderState">
            <v-hover
              v-if="!isEditPlan"
              v-slot="{ hover }"
            >
              <div class="d-flex flex-row">
                <h2 class="edit-plan">
                  {{ testPlan.name }}
                </h2>
                <button
                  class="btn-hide ml-3 mt-1"
                  :class="{ 'btn-show': hover }"
                  @click="handleEditPlanDetail"
                >
                  <PencilIcon />
                </button>
              </div>
            </v-hover>
            <v-text-field
              v-else
              v-model="testPlan.name"
              class="plan-name edit-plan font-weight-bold"
              solo
              flat
              hide-details="true"
              background-color="#F9FAFB"
            />
          </template>
          <v-skeleton-loader
            v-else
            class="mb-3"
            height="36"
            width="500"
            type="text"
          />
          <template v-if="!skeletonLoaderState">
            <p
              v-if="!isEditPlan"
              class="edit-plan"
              style="color: #667085; font-size: 14px; line-height: 14px"
            >
              {{ testPlan.description }}
            </p>
            <v-textarea
              v-else
              v-model="testPlan.description"
              class="edit-plan pt-0 mt-1 plan-description"
              dense
              solo
              flat
              auto-grow
              hide-details="true"
              background-color="#F9FAFB"
              height="44"
            />
          </template>
          <v-skeleton-loader
            v-else
            height="24"
            width="350"
            type="text"
          />

          <!-- References Section in Header -->
          <div
            v-if="!skeletonLoaderState && testPlan.references && testPlan.references.length > 0"
            class="mt-3"
          >
            <h4 class="custom_field_heading mb-2">
              {{ $t('references') }}
            </h4>
            <div class="d-flex flex-wrap gap-2">
              <v-tooltip
                v-for="(reference, index) in testPlan.references"
                :key="index"
                bottom
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-bind="attrs"
                    class="reference-chip d-flex align-center justify-space-between w-full px-2 py-1 rounded-lg mr-2 mb-2"
                    style="background: #F2F4F7; border: 1px solid #E4E7EC; cursor: pointer; max-width: 200px;"
                    v-on="on"
                    @click="window.open(reference.externalLink, '_blank')"
                  >
                    <div
                      class="d-flex align-center"
                      style="min-width: 0; flex: 1;"
                    >
                      <span
                        class="fs-12px text-theme-label mr-1 text-truncate"
                        style="min-width: 0; flex: 1; font-weight: 500;"
                      >{{ reference.name }}</span>
                    </div>
                    <a
                      :href="reference.externalLink"
                      target="_blank"
                      class="reference-link"
                      style="text-decoration: none; color: inherit;"
                      @click.stop
                    >
                      <v-icon
                        size="12"
                        class="text-theme-secondary"
                      >
                        mdi-arrow-top-right
                      </v-icon>
                    </a>
                  </div>
                </template>
                <span>{{ reference.name }}</span>
              </v-tooltip>
            </div>
          </div>
        </div>
        <div class="d-flex justify-space-between">
          <div class="d-flex flex-column w-33">
            <div class="text-left">
              <v-label
                v-if="!skeletonLoaderState"
                class="fs-14px text-theme-label font-weight-medium"
              >
                {{ $t("plans.edit.milestone") }}
              </v-label>
              <v-skeleton-loader
                v-else
                class="my-2"
                height="16"
                width="125"
                type="text"
              />
            </div>

            <v-select
              v-if="!skeletonLoaderState"
              v-model="selectedMilestones"
              dense
              background-color="#F9F9FB"
              :items="milestones"
              item-key="uid"
              item-value="uid"
              multiple
              item-text="name"
              class="rounded-lg field-theme custom-prepend mh-38px"
              append-icon="mdi-chevron-down"
              :placeholder="$t('testruns.milestone_dialog.content')"
              :hide-details="true"
              :menu-props="{ offsetY: true }"
            >
              <template #selection="{ item }">
                <div class="d-flex align-center custom-chip-theme mr-1 mb-1">
                  <div class="text-theme-label label text-truncate mr-1">
                    {{ item.name }}
                  </div>
                  <v-icon
                    size="16px"
                    @click="onRemoveSelectedMilestone(item.uid)"
                  >
                    mdi-close
                  </v-icon>
                </div>
              </template>

              <template #item="{ item, on }">
                <v-list-item
                  :ripple="false"
                  v-on="on"
                >
                  <v-list-item-action>
                    <v-checkbox
                      hide-details
                      :input-value="milestoneSelection(item.uid)"
                      class="field-theme mt-0 pt-0"
                      :ripple="false"
                      off-icon="icon-checkbox-off"
                      on-icon="icon-checkbox-on"
                    >
                      <template #label>
                        <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                      </template>
                    </v-checkbox>
                  </v-list-item-action>
                </v-list-item>
              </template>
            </v-select>
            <v-skeleton-loader
              v-else
              height="36"
              width="100%"
              type="button"
            />
          </div>
          <div class="d-flex flex-column w-33 mx-4">
            <div class="text-left">
              <v-label
                v-if="!skeletonLoaderState"
                class="fs-14px text-theme-label font-weight-medium"
              >
                {{ $t("plans.edit.priority") }}
              </v-label>
              <v-skeleton-loader
                v-else
                class="my-2"
                height="16"
                width="125"
                type="text"
              />
            </div>
            <v-select
              v-if="!skeletonLoaderState"
              v-model="testPlan.customFields.priority"
              dense
              background-color="#F9F9FB"
              :items="activePriorities"
              item-key="name"
              item-value="id"
              item-text="name"
              append-icon="mdi-chevron-down"
              class="rounded-lg field-theme custom-prepend mh-38px"
              :hide-details="true"
              :menu-props="{ offsetY: true }"
              :placeholder="$t('testruns.selectPriority')"
            />
            <v-skeleton-loader
              v-else
              height="36"
              width="100%"
              type="button"
            />
          </div>
          <div class="d-flex flex-column w-33">
            <div class="text-left">
              <v-label
                v-if="!skeletonLoaderState"
                class="fs-14px text-theme-label font-weight-medium"
              >
                {{ $t("plans.edit.tags") }}
              </v-label>
              <v-skeleton-loader
                v-else
                class="my-2"
                height="16"
                width="125"
                type="text"
              />
            </div>
            <v-select
              v-if="!skeletonLoaderState"
              v-model="selectedTags"
              background-color="#F9F9FB"
              dense
              :items="tags"
              item-key="uid"
              item-value="uid"
              item-text="name"
              :placeholder="$t('testruns.selectTags')"
              class="rounded-lg field-theme custom-prepend mh-38px"
              append-icon="mdi-chevron-down"
              hide-details="true"
              :menu-props="{ offsetY: true }"
              multiple
            >
              <template #selection="{ item }">
                <div class="d-flex align-center custom-chip-theme mr-1 mb-1">
                  <div class="text-theme-label label text-truncate mr-1">
                    {{ item.name }}
                  </div>
                  <v-icon
                    size="16px"
                    @click="onRemoveSelectedTags(item.name)"
                  >
                    mdi-close
                  </v-icon>
                </div>
              </template>

              <template #item="{ item, on }">
                <v-list-item
                  :ripple="false"
                  v-on="on"
                >
                  <v-list-item-action>
                    <v-checkbox
                      hide-details
                      :input-value="tagsSelection(item.uid)"
                      class="field-theme mt-0 pt-0"
                      :ripple="false"
                      off-icon="icon-checkbox-off"
                      on-icon="icon-checkbox-on"
                    >
                      <template #label>
                        <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                      </template>
                    </v-checkbox>
                  </v-list-item-action>
                </v-list-item>
              </template>
            </v-select>
            <v-skeleton-loader
              v-else
              height="36"
              width="100%"
              type="button"
            />
          </div>
        </div>
      </div>
    </v-card>
    <DetailSectionHeader
      :tab="tableFilter"
      :data="runItems"
      :total-items="runTotalRows"
      :selected-runs-count="testPlan.runs?.length || 0"
      @tab="handleTab"
    />
    <div
      class="app-height-global white rounded-lg d-flex flex-column"
      :class="{
        'justify-center': runItems.length === 0 && !skeletonLoaderState,
      }"
    >
      <ActiveEmptyState
        v-if="runItems.length === 0 && !skeletonLoaderState"
        :image-src="require('@/assets/png/auth-banner.png')"
        :title="$t('testruns.empty_state.title')"
        :button-text="$t('testruns.createTestRun')"
        :button-route="{ name: 'TestRunCreate' }"
        button-color="primary"
      >
        <template #description>
          <p class="mb-0 mt-3">
            {{ $t('testruns.empty_state.description_part1') }}
          </p>
          <p class="ma-0">
            {{ $t('testruns.empty_state.description_part2') }}
          </p>
          <p class="ma-0">
            {{ $t('testruns.empty_state.description_part3') }}
          </p>
        </template>
      </ActiveEmptyState>
      <v-card
        v-else
        class="py-6 px-6"
        rounded="lg"
        elevation="0"
        width="100%"
      >
        <div class="d-flex mb-4">
          <search-field
            v-model="searchFilter"
            class="search-input mr-2"
            :placeholder="$t('searchByName')"
          />

          <div class="ml-auto">
            <SettingsMenu table-type="planDetailsView" />
          </div>
        </div>
        <RunTable
          ref="runTable"
          :filtered-items="filteredRunItems"
          :filtered-headers="filteredHeaders"
          :table-filter="tableFilter"
          item-key="uid"
          :row-class="() => 'project-item'"
          :has-initially-loaded="hasInitiallyLoaded"
          :clear-selection="false"
          :write-entity="_writeEntity"
          :delete-entity="_deleteEntity"
          :tab="tableFilter"
          :selected-runs="selectedRuns"
          :configurations="configurationSorted"
          :show-configurations="true"
          :total-items="runTotalRows"
          :items-per-page="runPerPage"
          :current-page="runCurrentPage"
          @select-item="selectTestRun"
          @edit-item="editItem"
          @delete-item="confirmDeleteRun"
          @archive-item="confirmArchiveRun"
          @open-milestone-dialog="handleAddToMilestoneDialog"
          @open-plan-dialog="handleAddToTestPlansDialog"
          @open-duplicate-dialog="handleDuplicateAndApplyConfigDialog"
          @editConfigItem="editRunConfigurationItem"
          @deleteConfigItem="deleteRunConfigurationItem"
          @handleDuplicateAndApplyConfig="handleApplyConfig"
          @update-pagination="onUpdateRunPagination"
        />
      </v-card>

      <div
        v-if="!skeletonLoaderState && runItems.length > 0"
        class="d-flex flex-row justify-end py-4 px-6 action-btn-wrapper rounded-lg"
      >
        <v-btn
          class="px-6 text-capitalize mr-3 action-btn rounded-lg"
          depressed
          @click="handleCancel"
        >
          {{ $t("cancel") }}
        </v-btn>
        <v-btn
          class="px-6 text-capitalize primary action-btn rounded-lg"
          depressed
          @click="handleUpdate"
        >
          {{ $t("save") }}
        </v-btn>
      </div>
      <PlanConfirmDialog
        v-model="showConfirmBackDialog"
        :title="$t('plans.edit.discussConfirmation.title')"
        :description="$t('plans.edit.discussConfirmation.description')"
        :actions="[
          { label: $t('cancel'), color: 'secondary', action: 'close' },
          { label: $t('confirm'), color: 'primary', action: 'confirm' },
        ]"
        @close="showConfirmBackDialog = false"
        @confirm="handleConfirmClick"
      />
    </div>
    <RunDiscardDialog
      v-model="showConfirmDialog"
      :title="ConfirmDialog_Title"
      :content="ConfirmDialog_Content"
      :content_part2="ConfirmDialog_Contentpart_two"
      :run_name="ConfirmDialog_RunName"
      :items="ConfirmDialog_Items"
      :type="ConfirmType"
      :btn_label="ConfirmDialog_btn_label"
      :color="ConfirmDialog_btn_color"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmBtnClick"
    />
    <AddToMilestoneDialog
      v-if="addToMilestoneDialog"
      :value="addToMilestoneDialog"
      :milestones="activeMilestones"
      @close="onCloseAddToMilestoneDialog"
      @handleAddMilestone="handleAddMilestone"
    />
    <AddToTestPlansDialog
      v-if="addToTestPlansDialog"
      :value="addToTestPlansDialog"
      :plans="getActivePlans"
      @close="onCloseAddToTestPlansDialog"
      @handleAddTestPlan="handleConfirmTestPlanClick"
      @planCreated="planCreated"
    />
    <DuplicateAndApplyConfigDialog
      is-add-config
      :value="duplicateAndApplyConfigDialog"
      :configurations="configurationSorted"
      :button-loading="buttonLoading"
      :button-loading-items="buttonLoadingItems"
      :is-menu-visible="buttonLoading"
      :selected-run="selectedRun"
      :global-configuration="globalConfiguration"
      @handleDuplicateAndApplyConfig="handleDuplicateAndApplyConfig"
      @close="onCloseDuplicateAndApplyConfigDialog"
      @addConfiguration="addConfiguration"
      @addConfigurationItems="addConfigurationItems"
      @deleteConfigurationGroup="deleteConfigurationGroup"
      @deleteConfigurationItem="deleteConfigurationItem"
      @editConfigurationGroup="editConfigurationGroup"
    />
  </div>
</template>

<script>
import RunDiscardDialog from "@/components/TestRuns/RunDiscardDialog.vue";
import { mapGetters, mapActions as projectMapActions } from "vuex";
import DetailSectionHeader from "@/views/Tests/Plans/Components/DetailSectionHeader";
import PlanConfirmDialog from "@/views/Tests/Plans/Components/ConfirmDialog";
import MilestoneService from "@/services/api/milestone";
import SearchField from "@/components/Form/SearchField";
import makeTagsService from "@/services/api/tag";
import SettingsMenu from "@/components/Project/SettingsMenu.vue";
import { showSuccessToast, showErrorToast } from "@/utils/toast";
import makePlanService from "@/services/api/plan";
import makeRunService from "@/services/api/run";
import makeConfigurationService from "@/services/api/configuration";
import PencilIcon from "@/assets/svg/pencil.svg";
import colorPreferencesMixin from "@/mixins/colorPreferences";
import handleLoading from "@/mixins/loader.js";
import RunTable from "@/components/TestRuns/RunTable.vue";
import AddToMilestoneDialog from "@/components/TestRuns/AddToMilestoneDialog.vue";
import AddToTestPlansDialog from "@/components/TestRuns/AddToTestPlansDialog.vue";
import DuplicateAndApplyConfigDialog from "@/components/TestRuns/DuplicateAndApplyConfigDialog.vue";
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue';
import { usePlanIndex } from "@/composables/modules/testplan/index";

let planService;
let tagService;
let makeMilestoneService;
let runsService;
let configurationService;

export default {
  components: {
    SearchField,
    DetailSectionHeader,
    PlanConfirmDialog,
    RunTable,
    SettingsMenu,
    PencilIcon,
    RunDiscardDialog,
    AddToMilestoneDialog,
    AddToTestPlansDialog,
    DuplicateAndApplyConfigDialog,
    ActiveEmptyState
  },
  mixins: [colorPreferencesMixin, handleLoading],
  data()
  {
    return {
      menuOpen: false,
      isEditPlan: false,
      testPlanData: {},
      isColumnFilter: false,
      headers: [],
      searchFilter: "",
      tableFilter: "all",
      testPlan: {
        customFields: {},
      },
      runItems: [],
      milestones: [],
      planDescription: "",
      planName: "",
      showConfirmBackDialog: false,
      value: {
        id: 1,
        name: "",
        priority: "",
        status: "",
        configurations: [],
        creationdate: "",
        tag: [],
        progress: [],
      },
      tags: [],
      selectedTags: [],
      selectedMilestones: [],
      statuses: [],
      priorities: [],
      showConfirmDialog: false,
      ConfirmDialog_Title: "",
      ConfirmDialog_Content: "",
      ConfirmDialog_Contentpart_two: "",
      ConfirmDialog_Items: [],
      ConfirmDialog_btn_label: "",
      ConfirmDialog_btn_color: "primary",
      ConfirmDialog_RunName: "",
      ConfirmType: "",
      addToMilestoneDialog: false,
      addToTestPlansDialog: false,
      duplicateAndApplyConfigDialog: false,
      selecteditems: [],
      selectedRun: {},
      buttonLoading: false,
      buttonLoadingItems: false,
      configurations: [],
      globalConfiguration: {},
      runCurrentPage: 1,
      runPerPage: 10,
      runTotalRows: 0,
      hasInitiallyLoaded: false,
    };
  },
  computed: {
    _writeEntity()
    {
      return this.authorityTo("write_entity");
    },
    _deleteEntity()
    {
      return this.authorityTo("delete_entity");
    },
    _readEntity()
    {
      return this.authorityTo("read_entity");
    },
    ...mapGetters({
      currentOrg: "user/currentAccount",
      dynamicHeaders: "headers/dynamicHeaders",
    }),
    filteredHeaders()
    {
      const filtered = this.headers.filter((header) => header.checked);
      if (filtered.length < this.headers.length)
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.isColumnFilter = true;
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      else this.isColumnFilter = false;
      return filtered;
    },
    selectedRuns()
    {
      return this.testPlan.runs || []
    },
    activeStatuses()
    {
      return this.statuses.filter((element) => !element.archived);
    },
    activePriorities()
    {
      return this.priorities.filter((element) => !element.archived);
    },
    activeMilestones() {
      return this.milestones.filter((milestone) => !milestone?.archivedAt && !milestone?.deletedAt);
    },
    getActivePlans() {
      return this.plans?.filter(plan => plan.archivedAt == null) || [];
    },
    configurationSorted() {
      return this.configurations || [];
    },
    filteredRunItems() {
      if (!this.searchFilter) {
        return this.runItems;
      }
      const lowerCaseFilter = this.searchFilter.toLowerCase();
      return this.runItems.filter(item =>
        item.name.toLowerCase().includes(lowerCaseFilter)
      );
    }
  },
  watch: {
    testPlan: {
      handler(newVal)
      {
        // Update selectedTags when testPlan changes
        if (newVal && newVal.customFields && newVal.customFields.tags) {
          this.selectedTags = newVal.tags?.map((tag) => tag.uid);
        }
        if (newVal && newVal.milestoneUids) {
          this.selectedMilestones = newVal.milestones.map((milestone) => milestone.uid);
        }
      },
      immediate: true,
    },
  },
  setup() {
    const { clearCache, clearEntityTypeCache } = usePlanIndex();

    return {
      clearCache,
      clearEntityTypeCache
    }
  },
  created()
  {
    if (!this.dynamicHeaders.planDetailsView) {
      this.initializeHeaders({ type: "planDetailsView" });
    }
    this.headers = this.dynamicHeaders.planDetailsView;
    makeMilestoneService = MilestoneService(this.$api);
    planService = makePlanService(this.$api);
    tagService = makeTagsService(this.$api);
    runsService = makeRunService(this.$api);
    configurationService = makeConfigurationService(this.$api);
    this.priorities = this.getPriorities("testPlan");
    this.statuses = this.getStatuses("testPlan");
  },
  async mounted()
  {
    try {
      this.showSkeletonLoader();
      await this.findTestPlan();
      await this.getAllTestRuns({
        handle: this.$route.params.handle,
        projectKey: this.$route.params.key,
        perPage: this.runPerPage,
        currentPage: this.runCurrentPage
      });
      this.hasInitiallyLoaded = true;
      await this.getConfigurations();
      // Filter out any selected runs that have been deleted
      if (this.testPlan.runs && this.testPlan.runs.length > 0) {
        const availableRunUids = this.runItems.map(run => run.uid);
        this.testPlan.runs = this.testPlan.runs.filter(run =>
          availableRunUids.includes(run.uid)
        );
      }

      this.selectedMilestones = this.testPlan.milestones.map(
        (milestone) => milestone.uid
      );
      this.getAllTags();
      this.getMilestones();
    } catch (error) {
      showErrorToast(this.$swal, "fetchError", { item: "test plan" }, error?.response?.data);
    } finally {
      this.hideSkeletonLoader();
    }
  },
  methods: {
    ...projectMapActions("headers", ["initializeHeaders"]),
    handleCancel()
    {
      this.showConfirmBackDialog = true;
    },
    async findTestPlan()
    {
      try {
        const response = await planService.findTestPlan(
          this.$route.params.handle,
          this.$route.params.key,
          this.$route.params.planId
        );
        this.testPlan = response.data;
      } catch (error) {
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, "fetchError", { item: "test plan" }, error?.response?.data);
      }
    },
    async getAllTestRuns({handle, projectKey, perPage, currentPage})
    {
      try {
        const queryParams = {
          limit: perPage || 10,
          offset: ((currentPage || 1) - 1) * (perPage || 10),
          archived: false,
        }
        const response = await runsService.getRuns(handle, projectKey, queryParams);
        this.runItems = response.data.items;
        this.runTotalRows = response.data?.count || response.data?.total || 0;
        if (this.runItems.length > 0) {
          await this.fetchRunRelations(handle, this.$route.params.key, this.runItems);
        }
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'runs' }, err?.response?.data);
      }
    },
    async fetchRunRelations(handle, projectKey, runs) {
      const runUids = runs.map(run => run.uid);
      
      try {
        this.relationsLoading = true;

        const configurationsRes = await runsService.getRunRelations(handle, projectKey, 'config', runUids)
        if (configurationsRes.status === 200) {
          const configurationRelations = configurationsRes.data;
          this.runItems = this.runItems.map(run => ({
            ...run,
            configs: configurationRelations[run.uid] || []
          }));
        }
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'runs' }, err?.response?.data);
      } finally {
        this.relationsLoading = false;
      }
    },
    onUpdateRunPagination({page, itemsPerPage}) {
      if (page !== this.runCurrentPage || itemsPerPage !== this.runPerPage) {
        this.runCurrentPage = page;
        this.runPerPage = itemsPerPage;

        this.getAllTestRuns({ 
          handle: this.$route.params.handle, 
          projectKey: this.$route.params.key, 
          perPage: this.runPerPage, 
          currentPage: this.runCurrentPage 
        });
      }
    },
    milestoneSelection(uid)
    {
      return this.selectedMilestones
        ? this.selectedMilestones.some((id) => id === uid)
        : false;
    },
    async getMilestones()
    {
      try {
        const searchParams = new URLSearchParams();
        searchParams.set("includeCount", true);
        const response = await makeMilestoneService.getMilestones(
          this.$route.params.handle,
          this.$route.params.key,
          searchParams.toString()
        );
        this.milestones = response.data.items || [];
      } catch (error) {
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, "fetchError", { item: "milestones" }, error?.response?.data);
        this.milestones = [];
      }
    },
    tagsSelection(tagName)
    {
      if (typeof this.selectedTags === "string") {
        this.selectedTags = [this.selectedTags];
      } else if (!Array.isArray(this.selectedTags)) {
        this.selectedTags = [];
      }

      return this.selectedTags
        ? this.selectedTags.some((name) => name === tagName)
        : false;
    },
    onRemoveSelectedMilestone(uid)
    {
      const index = this.selectedMilestones.indexOf(uid);
      if (index !== -1) {
        this.selectedMilestones.splice(index, 1);
      }
    },
    onRemoveSelectedTags(uid)
    {
      const index = this.selectedTags.indexOf(uid);
      if (index !== -1) {
        this.selectedTags.splice(index, 1);
      }
    },
    async getAllTags()
    {
      try {
        const response = await tagService.getTags(this.$route.params.handle, "plans");
        if (response.status === 200) {
          this.tags = response.data;
        }
      } catch (err) {
        showErrorToast(this.$swal, "fetchError", { item: "Tags" }, err?.response?.data);
      } finally {
        this.selectedTags = this.testPlan?.tags?.map((tag) => tag.uid);
      }
    },
    async handleUpdate()
    {
      const runsList = this.testPlan.runs.map(
        ({ uid, link, priority, status, testPlanUid, customFields: { tags } }) => ({
          uid,
          name,
          link,
          priority,
          status,
          testPlanUid,
          customFields: { tags },
        })
      );
      const payload = {
        name: this.testPlan.name,
        status: this.testPlan.customFields.status,
        priority: this.testPlan.customFields.priority,
        tagUids: this.selectedTags,
      };
      if (this.selectedMilestones.length > 0) {
        payload.milestoneUids = this.selectedMilestones;
      }
      if (runsList.length > 0) {
        payload.runUids = runsList.map((run) => run.uid);
      }
      if (this.testPlan.description) {
        payload.description = this.testPlan.description;
      }
      try {
        const response = await planService.updateTestPlan(
          this.$route.params.handle,
          this.$route.params.key,
          this.testPlan.uid,
          payload
        );
        if (response.status === 200) {
          showSuccessToast(this.$swal, this.$t("success.testPlanUpdated"));
          this.clearCache();
          this.clearEntityTypeCache('plan');
          this.$router.push({
            name: "TestPlans",
            params: {
              handle: this.$route.params.handle,
              key: this.$route.params.key,
            },
          });
        }
      } catch (err) {
        showErrorToast(this.$swal, "updateError", { item: "Test Plan" }, err?.response?.data);
      }
    },
    selectTestRun(selectedRuns)
    {
      this.testPlan.runs = selectedRuns;
    },
    handleTab(value)
    {
      this.tableFilter = value;
    },
    handleEditPlanDetail()
    {
      this.isEditPlan = true;
    },
    handleBackClick()
    {
      this.$router.replace({ name: "TestPlans" });
    },
    handleConfirmClick()
    {
      this.showConfirmBackDialog = false;
      this.$router.replace({ name: "TestPlans" });
    },
    editItem(item)
    {
      this.selectedRun = item;
      this.$router.push({
        name: "TestRunEdit",
        params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key,
          id: item.uid,
          folderUid: 1,
          CustomItem: item,
        },
        query: {
          redirectTo: "TestPlanDetail",
          planId: this.$route.params.planId,
        },
      });
    },
    async editRunConfigurationItem(data) {
      const group = this.configurations.find((config) => config.uid == data.configUid);
      if (!group) return;

      const updatedOptions = group.options.map((option) => {

        if (option.uid == data.optionUid) {
          return { ...option, name: data.newItemName };
        }
        return option;
      });

      const sendData = {
        name: group.name,
        uid: Number(data.configUid),
        items: updatedOptions,
      };

      try {
        await this.updateConfiguration(sendData.uid, sendData.name, sendData?.items);
        await this.refreshData();
        showSuccessToast(this.$swal, 'updateSuccess', { item: 'configuration item' });
      } catch(err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      }
    },
    async deleteRunConfigurationItem(data) {
      const payload = {
        configs : data.newConfigs
      };

      try {
        await runsService.updateTestRun(
          this.$route.params.handle,
          this.$route.params.key,
          data.runUid,
          payload
        );
        await this.refreshData();
        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'configuration item' });
      } catch(err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      }
    },
    async handleApplyConfig(data){
      const payload = {
        configs : data.configs
      };

      try {
        await runsService.updateTestRun(
          this.$route.params.handle,
          this.$route.params.key,
          data.runUid,
          payload
        );
        await this.refreshData();
        if(data.addedConfigCount > 0 && data.removedConfigCount > 0) {
          showSuccessToast(this.$swal, 'addRemoveConfigSuccess', { added: data.addedConfigCount, removed: data.removedConfigCount });
        } 
        else if(data.addedConfigCount > 0) {
          showSuccessToast(this.$swal, 'addConfigSuccess', { item: data.addedConfigCount });
        }
        else if(data.removedConfigCount > 0) {
          showSuccessToast(this.$swal, 'removeConfigSuccess', { item: data.removedConfigCount });
        }
      } catch(err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      } finally {
        this.$refs.runTable.onCloseDuplicateAndApplyConfigDialog();
      }
    },
    async deleteConfigurationItem(data) {
      try {
        await this.updateConfiguration(data.uid, data.name, data.items);
        await this.getConfigurations();
        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'configuration item' });
      } catch(err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      }
    },
    handleAddToMilestoneDialog(item) {
      this.selecteditems = Array.isArray(item) ? item : [item];
      this.addToMilestoneDialog = true;
    },
    onCloseAddToMilestoneDialog() {
      this.addToMilestoneDialog = false;
    },
    handleAddToTestPlansDialog(item) {
      this.selecteditems = Array.isArray(item) ? item : [item];
      this.addToTestPlansDialog = true;
    },
    onCloseAddToTestPlansDialog() {
      this.addToTestPlansDialog = false;
    },
    handleDuplicateAndApplyConfigDialog(item) {
      this.selecteditems = Array.isArray(item) ? item : [item];
      this.selectedRun = this.selecteditems[0] || {};
      this.duplicateAndApplyConfigDialog = true;
    },
    onCloseDuplicateAndApplyConfigDialog() {
      this.duplicateAndApplyConfigDialog = false;
    },
    async getConfigurations() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      try {
        const response = await configurationService.getConfigurations(handle, projectKey, 10, 0);
        this.configurations = response.data?.configurations;
        return response.data?.configurations;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' });
        return [];
      }
    },
    async handleDuplicateAndApplyConfig(items) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      let runUids = this.selecteditems?.map(item => ({ uid: item.uid })) || [];
      const payload = { testRuns: runUids };

      if (items.sets.length > 0) {
        if (runUids.length > 1) {
          payload.configuration = items;
        } else {
          runUids.forEach(item => item.configuration = items);
        }
      }

      try {
        await runsService.duplicateTestRun(handle, projectKey, payload);
        showSuccessToast(this.$swal, 'addSuccess', { item: this.$t("duplicateRunCreated") });
        this.handleCloseClick();
      } catch (error) {
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, 'addError', { item: this.$t("errorCreateDuplicateRun") }, error?.response?.data);
      }
      this.refreshData();

    },
    async addConfiguration(configuration) {

      this.buttonLoading = true;
      try {
        await configurationService.createConfigurations(
          this.$route.params.handle,
          this.$route.params.key,
          {
            name: configuration.newConfigurationName,
            options: configuration.options,
          }
        );
        await this.getConfigurations();
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' });
      } finally {
        this.buttonLoading = false;
      }
    },
    async addConfigurationItems(item) {
      this.buttonLoadingItems = true;
      try {
        const formatOptions = () => {
          return item.items.map(opt => {
            if (opt.uid) {
              return { name: opt.name, uid: opt.uid };
            } else {
              return { name: opt };
            }
          });
        };
        await this.updateConfiguration(item.uid, item.name, formatOptions());
        await this.getConfigurations();
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' });
      } finally {
        this.buttonLoadingItems = false;
      }
    },
    async updateConfiguration(uid, name, options) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      try {
        await configurationService.updateConfiguration(handle, projectKey, uid, {
          name: name,
          options: options,
        });
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' });
      }
    },
    async deleteConfigurationGroup(group) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      try {
        await configurationService.deleteConfiguration(handle, projectKey, group.uid);
        await this.getConfigurations();
        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'configuration group' });
      } catch(err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' });
      }
    },
    async editConfigurationGroup(data) {
      try {
        await this.updateConfiguration(data.uid, data.name, data?.items);
        await this.getConfigurations();
        showSuccessToast(this.$swal, 'updateSuccess', { item: 'configuration group' });
      } catch(err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' });
      }
    },
    planCreated() {
      // This method will be called when a plan is created from the AddToTestPlansDialog
      // We can implement it if needed
    },
    async handleAddMilestone(items) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const milestoneService = MilestoneService(this.$api);
      // Ensure selecteditems is treated as an array
      const runIds = Array.isArray(this.selecteditems)
        ? this.selecteditems.map(item => item.uid)
        : this.selecteditems?.uid ? [this.selecteditems.uid] : [];

      const payload = {
        runIds
      };

      if(items.length) {
        for (const element of items) {
          try {
            await milestoneService.addRunsToMilestone(handle, projectKey, element, payload);
            showSuccessToast(this.$swal, 'addSuccess', { item: 'Test runs to milestone' });
            this.handleCloseClick();
          } catch (error) {
            this.redirectOnError(error.response.status);
            showErrorToast(this.$swal, 'addError', { item: 'Test runs to milestone' });
          }
        }
        await this.refreshData();
      }
    },
    async handleConfirmTestPlanClick(items) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      // Ensure selecteditems is treated as an array
      const runIds = Array.isArray(this.selecteditems)
        ? this.selecteditems.map(item => item.uid)
        : this.selecteditems?.uid ? [this.selecteditems.uid] : [];

      const payload = {
        runs: runIds
      };

      if(items.length) {
        for (const element of items) {
          try {
            await planService.addRunsToTestPlan(handle, projectKey, element, payload);
            showSuccessToast(this.$swal, 'addSuccess', { item: 'Test runs to test plan' });
            this.handleCloseClick();
          } catch (error) {
            this.redirectOnError(error.response.status);
            showErrorToast(this.$swal, 'addError', { item: 'Test runs to test plan' });
          }
        }
        await this.refreshData();
      }
    },
    confirmArchiveRun(item)
    {
      this.selectedRun = item;
      this.handleConfirmDialog('archive')
    },
    confirmDeleteRun(item)
    {
      this.selectedRun = item;
      this.handleConfirmDialog("delete");
    },
    onfirmArchiveRun(item)
    {
      this.selectedRun = item;
      this.handleConfirmDialog('archive')
    },
    handleCloseClick()
    {
      this.showConfirmDialog = false;
      this.selectedRun = [];
      this.selecteditems = [];
      this.ConfirmDialog_Items = [];
      this.clearSelection = true;
      this.addToMilestoneDialog = false;
      this.addToTestPlansDialog = false;
      this.duplicateAndApplyConfigDialog = false;
    },

    async handleConfirmDialog(type)
    {
      this.showConfirmDialog = true;
      switch (type) {
        case "archive":
          this.ConfirmDialog_Title = this.$t("testruns.archive_dialog.title");
          this.ConfirmDialog_Content = this.$t("testruns.archive_dialog.content");
          this.ConfirmDialog_Contentpart_two = "";
          this.ConfirmDialog_btn_label = this.$t("testruns.archive_dialog.btn_label");
          this.ConfirmDialog_btn_color = "primary";
          this.ConfirmDialog_RunName = this.selectedRun.name;
          this.ConfirmType = "archive";
          break;
        case "delete":
          this.ConfirmDialog_Title = this.$t("testruns.delete_dialog.title");
          this.ConfirmDialog_Content = this.$t("testruns.delete_dialog.content");
          this.ConfirmDialog_Contentpart_two =
            this.filter == "ongoing"
              ? this.$t("testruns.delete_dialog.content_part2")
              : "";
          this.ConfirmDialog_btn_label = this.$t("testruns.delete_dialog.btn_label");
          this.ConfirmDialog_btn_color = "danger";
          this.ConfirmDialog_RunName = "";
          this.ConfirmType = "delete";
          break;
      }
    },
    async handleConfirmDeleteClick()
    {
      try {
        this.showSkeletonLoader();
        await runsService.deleteTestRuns(
          this.$route.params.handle,
          this.$route.params.key,
          {
            runUids: [this.selectedRun.uid],
          }
        );
        await this.refreshData();
        showSuccessToast(this.$swal, "deleteSuccess", { item: "Test run" });
      } catch (error) {
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, "deleteError", { item: "Test run" });
      } finally {
        this.handleCloseClick();
        this.hideSkeletonLoader();
      }
    },
    async handleConfirmOneArchiveClick(status)
    {
      const payload = {
        customFields: {
          ...this.selectedRun.customFields,
        },
        archived: status,
        name: this.selectedRun.name,
      };

      try {
        this.showSkeletonLoader();
        await runsService.updateTestRun(
          this.$route.params.handle,
          this.$route.params.key,
          this.selectedRun.uid,
          payload
        );
        await this.refreshData();
        showSuccessToast(this.$swal, "archiveSuccess", { item: "Test run" });
      } catch (error) {
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, "archiveError", { item: "Test run" });
      } finally {
        this.handleCloseClick();
        this.hideSkeletonLoader();
      }
    },

    handleConfirmBtnClick(type)
    {
      switch (type) {
        case "archive":
          this.handleConfirmOneArchiveClick(true);
          break;
        case "delete":
          this.handleConfirmDeleteClick();
          break;
      }
    },
    async refreshData()
    {

      await this.getAllTestRuns({
        handle: this.$route.params.handle,
        projectKey: this.$route.params.key,
        perPage: this.runPerPage,
        currentPage: this.runCurrentPage,
      });

      // Update selected runs in the test plan to remove any that were deleted or archived
      if (this.testPlan.runs && this.testPlan.runs.length > 0) {
        const availableRunUids = this.runItems.map(run => run.uid);
        this.testPlan.runs = this.testPlan.runs.filter(run =>
          availableRunUids.includes(run.uid)
        );
      }
    },
  },
};
</script>

<style lang="scss">
.btn-hide {
  display: none;
}

.btn-show {
  display: block !important;
}

.v-input.plan-name {
  .v-input__control {
    .v-input__slot {
      padding-left: 4px !important;

      input {
        font-size: 24px;
        line-height: 28px;
      }
    }
  }
}

.edit-plan {
  max-width: 476px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.plan-description {
  .v-input__control {
    .v-input__slot {
      padding-left: 4px !important;

      textarea {
        line-height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #667085;
        height: 44px !important;
      }
    }
  }
}

.action-btn {
  width: 140px;
}

.action-btn-wrapper {
  position: sticky;
  bottom: 0;
  background-color: white;
  align-items: flex-end;
  display: flex;
  justify-content: flex-end;
  z-index: 8;
}

.reference-chip {
  transition: all 0.2s ease;
  cursor: pointer;
}

.reference-chip:hover {
  background: #E4E7EC !important;
  transform: translateY(-1px);
}

.reference-link {
  text-decoration: none;
  color: inherit;
}

.reference-link:hover {
  opacity: 0.8;
}

.custom_field_heading {
  color: #667085;
  font-weight: 400;
  font-size: 13px;
  margin: 12px 0 4px 0px;
}
</style>
