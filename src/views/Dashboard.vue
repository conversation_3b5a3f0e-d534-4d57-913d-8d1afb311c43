<template>
  <v-main
    class="elevation-0"
    :class="[{['pl-3 mt-3']: !isOrgScope && isOrgProjectView}, {['mt-3']: isOrgScope && isOrgProjectView}]"
  >
    <customize-charts 
      v-if="showCustomize" 
      :charts="charts"
      :is-org-project-view="isOrgProjectView"
      :is-adding-chart="isAddingChart"
      @removeCounterChart="removeCounterChart" 
      @addChart="addChart($event)"
      @customize="closeCustomize"
    />
    <div
      v-else
    > 
      <div class="dashboard">
        <DashboardHeader
          v-if="periodKey"
          :key="dashboardHeaderKey"
          v-model="filters"
          :period-key="periodKey"
          :projects="projects"
          :milestones="milestones"
          :test-plans="testPlans"
          :dashboards="dashboards"
          :tags="tags"
          :current-dashboard="currentDashboard"
          :is-org-project-view="isOrgProjectView"
          :view="view"
          :show-plans="!isPlanDetailView"
          :show-milestones="!isMilestoneDetailView"
          :disable-inputs="chartLoad"
          @update:searchInput="handleSearchInput"
          @deleteDashboard="deleteDashboard"
          @dateUpdated="dateUpdated"
          @editDashboard="editDashboard"
          @createDashboard="createDashboard"
          @selectedDashboard="selectedDashboard"
          @customize="openCustomize"
          @projectsUpdated="setProjects"
          @milestonesUpdated="setMilestones"
          @tagsUpdated="setTags"
          @plansUpdated="setPlans"
        />
        <template v-if="!skeletonLoaderState">
          <template v-if="charts.length">
            <grid-layout
              v-if="layout.length && ((isReady && !isOrgProjectView) || isOrgProjectView)"
              :layout="layout"
              :col-num="12"
              :row-height="30"
              :is-draggable="true"
              :is-resizable="true"
              :responsive="true"
              :prevent-collision="false"
              :auto-size="true"
              :margin="[10,10]"
              :vertical-compact="false"
              :use-css-transforms="false"
              @layout-ready="layoutReadyEvent"
            >
              <grid-item
                v-for="(chart, index) in charts"
                :key="index"
                :x="layout[index]?.x ?? 0"
                :y="layout[index]?.y ?? 0"                            
                :w="layout[index]?.w ?? 0"
                :h="layout[index]?.h ?? 0"
                :i="layout[index]?.i ?? 0"
                @resized="setCharts"
                @moved="setCharts"
              >
                <div
                  v-if="layoutReady"
                  class="dashboard__charts-card"
                >
                  <Component
                    :is="chart.type"
                    :key="chart.groupBy + chart.entityType + updatedKey + chart.key"
                    :data="chart"
                    :view="view"
                    :editable="isEditable"
                    :chart-load="chartLoad"
                    @pullChart="pullChartType"
                    @updateMultiSeries="updateMultiSeries"
                    @updateEntity="updateEntity"
                    @updateGroupBy="updateGroupBy"
                    @updateTimeGrouping="updateTimeGrouping"
                    @deleteChart="deleteChart"
                    @expandChart="setCharts({isExpand: true},$event)"
                  />
                </div>
              </grid-item>
            </grid-layout>
          </template>
          <ActiveEmptyState
            v-else
            :image-src="require('@/assets/png/empty_charts.png')"
            :image-max-width="'342px'"
            :title="$t('dashboard.noChartsTitle')"
            :button-text="$t('customize')"
            button-color="primary"
            class="mt-16"
            @button-click="openCustomize"
          >
            <template #description>
              <p
                class="mb-0 mt-3 plan-description text-center"
                :style="{
                  maxWidth: '428px'
                }"
              >
                {{ $t("dashboard.noChartsDescription") }}
              </p>
            </template>
          </ActiveEmptyState>
        </template>
        <template v-else>
          <DashboardSkeleton />
        </template>
      </div>
    </div>
  </v-main>
</template>

<script>
import DashboardHeader from '@/components/Dashboard/DashboardHeader'
import DonutChart from '@/components/Dashboard/Charts/DonutChart'
import CounterChart from '@/components/Dashboard/Charts/CounterChart'
import LineChart from '@/components/Dashboard/Charts/LineChart'
import BarChart from '@/components/Dashboard/Charts/BarChart'
import CustomizeCharts from '@/components/Dashboard/CustomizeCharts';
import BoxplotChart from '@/components/Dashboard/Charts/BoxplotChart'
import makeDashboardService from '@/services/api/dashboards'
import { GridLayout, GridItem } from 'vue-grid-layout';
import * as _ from 'lodash'
import loader from '@/mixins/loader'
import { createNamespacedHelpers } from 'vuex'
import { showSuccessToast, showErrorToast } from '@/utils/toast';
const { mapActions: mapDashboardActions, mapGetters: mapDashboardGetters, mapMutations: mapDashboardMutations} = createNamespacedHelpers('user')
import HeatmapChart from '@/components/Dashboard/Charts/HeatmapChart'
import DashboardSkeleton from '@/components/Skeletons/Dashboard/DashboardSkeleton.vue'
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue'
import dayjs from 'dayjs'
import { debounce } from 'debounce'

export default {
  name: 'Dashboard',
  components:{
    DashboardHeader,
    DonutChart,
    BarChart,
    HeatmapChart,
    BoxplotChart,
    CustomizeCharts,
    CounterChart,
    GridLayout,
    GridItem,
    LineChart,
    DashboardSkeleton,
    ActiveEmptyState
  },
  mixins:[loader],
  props:{
    isReady: {
      type: Boolean,
      required: false
    },
    showArchived: Boolean
  },
  data()
  {
    return {
      layout: [],
      items: [],
      classic: '',
      showCustomize: false,
      chartData: {},
      startRange: null,
      endRange: null,
      updatedKey: 0,
      layoutReady: false,
      overviewData: [],
      dashboards: [],
      chartLoad: false,
      filters:{
        selectedMilestones: [],
        selectedPlans: [],
        selectedProjects: [],
        selectedTags: [],
        selectedRuns: []
      },
      periodKey: null,
      isAddingChart: false,
    };
  },
  watch: {
  showArchived: {
    handler() {
      (async () => {
        await this.getDashboard(true);
        this.updateLayout()
      })();
    },
  },
},
  computed: {
    ...mapDashboardGetters(['period', 'dashboardUid', 'getFilters', 'lastUpdatedAt', 'storedCharts']),
    isOrgProjectView(){
      return ['OrgDashboard', 'ProjectDashboard'].includes(this.$route.name)
    },
    isPlanView(){
      return this.$route.name == 'TestPlans';
    },
    isPlanDetailView(){
       return this.$route.name == 'TestPlanRerun'
    },
    isMilestoneDetailView(){
      return this.$route.name == 'MilestoneView'
    },
    isEditable() {
      return !this.view ? this.currentDashboard?.editable : true;
    },
    view(){
      const viewMapping = {
        'TestRunCase': 'testRun',
        'TestPlanRerun': 'testPlan',
        'MilestoneView': 'milestone'
      }
      
      if(Object.keys(viewMapping).includes(this.$route.name))
        return viewMapping[this.$route.name]

      return null
    },
    isDetailView(){ // Indicate if it's a detail view to avoid using run/plan/milestone IDs as the dashboard ID
     return !!['TestRunCase', 'TestPlanRerun'].includes(this.$route.name)
    },
    currentDashboard(){
      const id = this.$route.params.id;
      return this.dashboards.find(element => element.uid == id);
    },
    _deleteDashboard(){
      return this.authorityTo('delete_dashboard')
    },
    _writeDashboard(){
      return this.authorityTo('write_dashboard')
    },
    currentPeriod(){
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      return this.period({handle, projectKey, view: this.view})
    },
    isOrgScope(){
      return !this.$route.params.key;
    },
    orgs(){
      return this.accounts?.filter(a => a.type === "org")
    },
    milestones(){
      const associateMilestonesWithPlans = [
        ...this.filters.selectedPlans
      ].reduce((acc, curr) => {
        acc.push(...curr.milestoneUids.map(item => item.uid))
        return acc
      }, []);

      const projects = this.isOrgScope 
      ? this.filters.selectedProjects 
      : this.projects?.find(element => element.key == this.$route.params.key)
        ? [this.projects?.find(element => element.key == this.$route.params.key)]
        : [];

      return this.overviewData?.milestones?.filter(milestone => {
        const hasProjects = projects.length > 0;
        const hasAssociatedMilestones = this.filters.selectedPlans.length > 0;
        return (!hasProjects || projects.map(item => item.uid).includes(milestone.projectUid)) &&
         (!hasAssociatedMilestones || associateMilestonesWithPlans.includes(milestone.uid));
      })

    },
    projects(){
      const associateProjectsWithEntities = [
        ...this.filters.selectedMilestones,
        ...this.filters.selectedPlans
      ].map(entity => entity.projectUid);

      return associateProjectsWithEntities.length && !this.isOrgProjectView ? this.overviewData.projects.filter(project =>
        associateProjectsWithEntities.includes(project.uid)
      ) : this.overviewData?.projects; 
    },
    testPlans() {
      const projects = this.isOrgScope ? this.filters.selectedProjects : [this.projects?.find(element => element.key == this.$route.params.key)];
      const hasProjects = projects.length > 0;
      const hasMilestones = this.filters.selectedMilestones?.length > 0; 
      const selectedMilestones = this.filters.selectedMilestones.map(item => item.uid);
      return this.overviewData?.testPlans?.filter(testPlan => {
        return (!hasProjects || projects.map(item => item.uid).includes(testPlan.projectUid)) &&
          (!hasMilestones || testPlan.milestoneUids.some(milestone => milestone.uid == selectedMilestones));
     });
    },
    tags(){
      return this.overviewData?.tags || []
    },
    charts(){
      const projectKey = this.$route.params.key;
      const handle = this.$route.params.handle;
      // updatedKey is used here to force the re-rendering of charts
      return this.storedCharts({projectKey, handle, view: this.view, updatedKey: this.updatedKey}) ?? [];
    },
    dashboardHeaderKey() {
      const periodKeyString = Array.isArray(this.periodKey)
        ? this.periodKey.join('-')
        : this.periodKey || '';
      return `${this.dashboards.length}-${periodKeyString}`;
    },
    dashboardLastUpdatedAt(){
      const projectKey = this.$route.params.key;
      const handle = this.$route.params.handle;
      return this.lastUpdatedAt({projectKey, handle, view: this.view})
    },
    mappedProjectUids() {
      const key = this.$route.params.key;
      return  key ? this.overviewData.projects?.reduce((acc, curr) => {
        if(curr.key == key)
          acc.push(curr.uid)

        return acc;
      },[]) : this.filters.selectedProjects.map(item => item.uid);
    },
    mappedMilestoneUids() {
      return this.filters.selectedMilestones.map(item => item.uid);
    },
    mappedPlansUid() {
      return this.filters.selectedPlans.map(item => item.uid);
    },
    mappedTagsUid(){
      return this.filters.selectedTags.map(item => item.uid)
    }
  },
  methods: {
    ...mapDashboardActions(['setDashboard', 'pullChart', 'forceLoadChart', 'storeFilters', 'storeCharts']),
    ...mapDashboardMutations(['saveChart']),
    async getDashboardsList(){
      try {
        const dashboardService = makeDashboardService(this.$api);
        const handle = this.$route.params.handle;
        const projectKey = this.$route.params.key;
        await dashboardService.getDashboards(handle, projectKey).then(response => { this.dashboards = response.data });
      } catch (error) {
        showErrorToast(this.$swal, error.response?.data?.message || 'Internal server error')
      } 
    },
    _updateKey(){
      this.updatedKey +=1;
    },
    async selectedDashboard(uid){
      this.layoutReady = false;
      await this.init([
        await this.$router.replace({name: this.$route.name, params: {id: uid}}),
        this.getDashboard(true)
      ])
      this.updateLayout()
    },
    async deleteDashboard(uid){
      if(!this._deleteDashboard)
        return showErrorToast(this.$swal, this.$t('dashboard.deleteChart'));
        
      const selectedDashboard = this.dashboards.find(element => element.uid == uid)
      if(selectedDashboard?.systemDefault)
        return showErrorToast(this.$swal, this.$t('dashboard.cannotDeleteDefaultDashboard'));
    
      const dashboardService = makeDashboardService(this.$api);
      const handle = this.$route.params.handle;
      await dashboardService.deleteDashboard(handle, uid).then(() => {
        showSuccessToast(this.$swal, this.$t('success.deleteDashboard'))
        const defaultDashboard = this.dashboards.find(element => element.systemDefault == true);
        if(defaultDashboard)
          this.selectedDashboard(defaultDashboard.uid) 
      }).catch(error => {
        showErrorToast(this.$swal, error.response?.data?.message || 'Internal server error')
      })
    },
    async createDashboard({name, isDefault}){
      const dashboardService = makeDashboardService(this.$api);
      const payload = {
        name,
        isDefault
      }
      const handle = this.$route.params.handle;
      await dashboardService.createDashboard({handle, payload}).then(response => {
        const { uid } = response.data;
        if(uid){
          this.getDashboardsList();
          this.selectedDashboard(uid);
          showSuccessToast(this.$swal, this.$t('success.dashboardCreated'))
        }
      }).catch((error) => {
        showErrorToast(this.$swal, error.response?.data?.message || 'Internal server error')
      })
    },
    async setDefault(id){
      const dashboardService = makeDashboardService(this.$api);
      const payload = {
        isDefault: true,
      }
      const handle = this.$route.params.handle;
      await dashboardService.updateDashboard({handle, id, payload}).then(() => {
          showSuccessToast(this.$swal, this.$t('success.dashboardSetToDefault'))
          this.getDashboardsList();
      }).catch((error) => {
        showErrorToast(this.$swal, error.response?.data?.message || 'Internal server error')
      })
    },

    async editDashboard(id,payload){
      const dashboardService = makeDashboardService(this.$api);
      const handle = this.$route.params.handle;
      await dashboardService.updateDashboard({handle, id, payload}).then(() => {
        showSuccessToast(this.$swal, this.$t('success.dashboardUpdated'));
        this.getDashboardsList();
      }).catch((error) => {
        showErrorToast(this.$swal, error.response?.data?.message || 'Internal server error')
      })
    },
    updateMultiSeries(isMultiSeries, chartID){
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key
      this.saveChart({chartID, handle, projectKey, modifiedValues: { isMultiSeries }, view: this.view})
      this.updateCharts();
    },
    async getDashboard(forceFetch = false){

      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const uid = this.$route.params.id;
      const params = {
        projects: this.mappedProjectUids,
        ...(!this.isOrgProjectView ? {view : this.view } : {}),
        ...(this.startRange ? { startRange: this.startRange } : {}),
        ...(this.endRange ? { endRange: this.endRange } : {}),
        ...(this.filters.selectedMilestones.length ? { milestones: this.mappedMilestoneUids } : {}),
        ...(this.filters.selectedPlans.length ? { testPlans: this.mappedPlansUid } : {}),
        ...(this.filters.selectedTags.length ? { tags: this.mappedTagsUid } : {}),
        ...(this.filters.selectedRuns.length ? { testRuns: this.filters.selectedRuns } : {}),
        ...(this.view ? { archived: this.showArchived } : {})
      }
      
      if((this.dashboardLastUpdatedAt &&  this.dashboardLastUpdatedAt > Date.now() - (5 * 60 * 1000)) && !forceFetch && !this.isDetailView){

        this.periodKey = this.currentPeriod.value
        const filters = this.getFilters({handle, projectKey, view: this.view});
        if(Array.isArray(filters?.projects))
          this.filters.selectedProjects = filters.projects
        if(Array.isArray(filters?.testPlans))
          this.filters.selectedPlans = filters.testPlans
        if(Array.isArray(filters?.testMilestones))
          this.filters.selectedMilestones = filters.testMilestones
        if(Array.isArray(filters?.tags))
          this.filters.selectedTags = filters.tags

        if(this.isOrgProjectView){
          this.$router.push({name: this.$route.name, params: {id: this.dashboardUid({handle, projectKey})}});
        }
        return;
      }

      if(this.$route.name === 'TestPlanRerun')
        params.testPlans = [this.$route.params.planId];
      if(this.$route.name === 'TestRunCase')
        params.testRuns = [this.$route.params.id];
      if(this.$route.name === 'MilestoneView')
        params.milestones = [this.$route.params.id];
      const payload = {
        projectKey,
        handle,
        params,
        ...(uid && !this.isDetailView ? { uid } : {})
      }

      this.chartLoad = true;
      const dashboardService = makeDashboardService(this.$api);
      await dashboardService.getDashboard(payload).then(response => {
        const { charts, period, dashboardUid, isOverridden } = response.data;
        if(isOverridden)
          showErrorToast(this.$swal, this.$t('dashboard.overridden', {startDate: dayjs(period[0]).format('DD MMM') , endDate: dayjs(period[1]).format('DD MMM')}));

        const kCharts = charts.map(item => {
          return {
            ...item,
            key: 0,
            retryTimes: 0
          }
        });
        this.chartLoad = false;
        this.periodKey = period
        if(this.isOrgProjectView && this.$route.params.id != dashboardUid)
          this.$router.push({name: this.$route.name, params: {id: dashboardUid}});
        this.setDashboard({
          charts: kCharts,
          lastUpdatedAt: new Date(),
          handle,
          projectKey,
          dashboardUid,
          ...(!this.isOrgProjectView ? { view: this.view } : {})
        });
        this.updateKey();
      }).catch(error => {
        showErrorToast(this.$swal, error.response?.data?.message || 'Internal server error')
        this.$router.replace({name: this.$route.name, params: {id: undefined}});
        this.$store.commit('error404/SET_SHOW_404', true);
      })
    },
    async setProjects(projects){
      this.filters.selectedProjects = projects;
      await this.getDashboard(true);
      this.updateLayout();
      this.saveFilters();
    },
    async setPlans(plans){
      this.filters.selectedPlans = plans
      await this.getDashboard(true);
      this.updateLayout();
      this.saveFilters();
    },
    async setTags(tags){
      this.filters.selectedTags = tags
      await this.getDashboard(true);
      this.updateLayout();
      this.saveFilters();
    },
    async setMilestones(milestones){
      this.filters.selectedMilestones = milestones
      await this.getDashboard(true);
      this.updateLayout();
      this.saveFilters();
    },
    openCustomize(){
      this.showCustomize = true;
      this.layoutReady = false;
      this.$emit('updateCustomize', true)
    },
    closeCustomize(){
      this.showCustomize = false;
      this.$emit('updateCustomize', false)
    },
    async dateUpdated(period, periodKey){
      if(period && period.length == 2 &&  this.periodKey !== periodKey){
        this.startRange = period[0]
        this.endRange = period[1]

        const handle = this.$route.params.handle;
        const projectKey = this.$route.params.key
        
        const payload = {
          period: periodKey
        }
        const params = {
          returnResult: true,
          projects: this.mappedProjectUids,
          view: this.view,
          ...(this.filters.selectedMilestones.length ? { milestones: this.mappedMilestoneUids } : {}),
          ...(this.filters.selectedPlans.length ? { testPlans: this.mappedPlansUid } : {}),
          ...(this.filters.selectedTags.length ? { tags: this.mappedTagsUid } : {}),
          ...(this.view ? { archived: this.showArchived } : {})
        }
        
        const dashboardService = makeDashboardService(this.$api);
        const id = this.isOrgProjectView ? this.$route.params.id : this.dashboardUid({handle, projectKey, view: this.view});
        this.chartLoad = true;
        await dashboardService.updateCharts({handle, payload, params, id, projectKey}).then((response) => {
          const { charts, period, dashboardUid, isOverridden } = response.data;
          const kCharts = charts.map(item => {
            return {
              ...item,
              key: 0,
              retryTimes: 0,
            }
          });

          if(isOverridden)
            showErrorToast(this.$swal, this.$t('dashboard.overridden', {startDate: dayjs(period[0]).format('DD MMM') , endDate: dayjs(period[1]).format('DD MMM')}));

          this.periodKey = period
          this.setDashboard({
            charts: kCharts,
            projectKey,
            handle,
            dashboardUid,
            lastUpdatedAt: new Date(),
            view: this.view
          });
          this.updateLayout()
        })
        this.updateKey();
        this.chartLoad = false;
      }
    },
    handleSearchInput: debounce(async function (entityType, query){
      await this.getOverview({
        ...(entityType == 'tags' ? { tagFilter: query } : {}),
        ...(entityType == 'milestones' ? { milestoneFilter: query } : {}),
        ...(entityType == 'testPlans' ? { testPlanFilter: query } : {}),
      })
    }, 500),
    async pullChartType({chartID}){
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const queries = {
        projects: this.mappedProjectUids,
        ...(!this.isOrgProjectView ? {view : this.view } : {}),
        ...(this.filters.selectedMilestones.length ? { milestones: this.mappedMilestoneUids } : {}),
        ...(this.filters.selectedPlans.length ? { testPlans: this.mappedPlansUid } : {}),
        ...(this.filters.selectedTags.length ? { tags: this.mappedTagsUid } : {}),
      }

      if(this.$route.name === 'TestPlanRerun')
        queries.testPlans = [this.$route.params.planId];
      if(this.$route.name === 'TestRunCase')
        queries.testRuns = [this.$route.params.id];
      if(this.$route.name === 'MilestoneView')
        queries.milestones = [this.$route.params.id];

      const chartIndex = this.charts.findIndex(e => e.id === chartID);
      let retryTimes = this.charts[chartIndex].retryTimes || 0;
      const dashboardID = this.isOrgProjectView ? this.$route.params.id : this.dashboardUid({handle, projectKey, view: this.view});
      await this.saveChart({chartID, handle, projectKey, modifiedValues: { load: true, retryTimes: retryTimes+=1 }, view: this.view})
      this.$forceUpdate();
      await this.pullChart({handle, projectKey, queries, view: this.view, dashboardID, chartID})
      await this.saveChart({chartID, handle, projectKey, modifiedValues: { load: false }, view: this.view}) 
      await this.forceLoadChart({chartID, projectKey, handle, view: this.view})
      this.$forceUpdate();
    },
    updateGroupBy(modifiedValue, chartID){
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key
      this.saveChart({chartID, handle, projectKey, modifiedValues: { 'groupBy': modifiedValue}, view: this.view})
      this.$forceUpdate();
    },
    changeSelected(modifiedKey, modifiedValue, chartID){
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key
      this.saveChart({chartID, handle, projectKey, modifiedKey, modifiedValue, view: this.view})
      this.$forceUpdate();
    },
    updateTimeGrouping(modifiedValue, chartID){
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key
      this.saveChart({chartID, handle, projectKey, modifiedValues: { groupByTime: modifiedValue }, view: this.view})
      this.updateCharts();
    },
    deleteChart(chartID){
      if(!this._deleteDashboard)
        return showErrorToast(this.$swal, this.$t('dashboard.deleteChart'));
        
      const chartIndex = this.charts.findIndex(element => element.id == chartID);
      this.charts.splice(chartIndex, 1);
      this.updateCharts();
    },
    async updateEntity(entityType, chartID){
      if(!this.currentDashboard.editable){
        return showErrorToast(this.$swal, this.$t('dashboard.dashboardNotEditable'));
      }
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const defaultGroupBy = [
        ...(!['testCase', 'defect'].includes(entityType) ? [{ field: 'status' }] : []),
        ...(!['milestone', 'defect'].includes(entityType) ? [{ field: 'priority' }] : []),
        ...(entityType === 'defect' ? [{field: 'labels'}] : [])
      ];
      this.saveChart({chartID, handle, projectKey, modifiedValues: { entityType, groupBy: defaultGroupBy, retryTimes: 0 }, view: this.view})  
      await this.updateCharts(); 
      await this.pullChartType({chartID});
    },
    layoutReadyEvent(){
      this.layoutReady = true;
    },
    saveFilters(){
      const filters = {
        projects:  this.filters.selectedProjects?.length ? this.filters.selectedProjects : [],
        testPlans: this.filters.selectedPlans?.length ? this.filters.selectedPlans : [],
        testMilestones: this.filters.selectedMilestones?.length ? this.filters.selectedMilestones : [],
        tags: this.filters.selectedTags?.length ? this.filters.selectedTags : []
      }
      this.storeFilters({
        handle: this.$route.params.handle,
        projectKey: this.$route.params.key,
        filters
      })
    },
    clearFilters(){
      this.filters = {
        selectedProjects: [],
        selectedPlans: [],
        selectedMilestones: [],
        selectedTags: [],
        selectedRuns: []
      }
    },
    async _setCharts({isExpand},...args){
      if(this.isOrgProjectView && !this.currentDashboard.editable){
        this.updateLayout();
        return showErrorToast(this.$swal, this.$t('dashboard.dashboardNotEditable'));
      }
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      const charts = this.charts.map((element, index) => {
        return{
          ...element,
          x: this.layout[index].x, 
          y: this.layout[index].y,
          w: this.layout[index].w, 
          h: this.layout[index].h,
          ...(isExpand && args[0] == element.id ? { w: 12 } : null) 
        }
      });
      this.storeCharts({projectKey, handle, charts,view: this.view})
      this.updateCharts(false,isExpand ?? false,charts)
      
    },
    async updateCharts(returnResult = false, updateLayout = true , charts){
      const usedCharts = Array.isArray(charts) ? charts : this.charts;
      const chartsPaylod = usedCharts.map(item => {
        return {
          id: item.id,
          type: item.type,
          entityType: item.entityType,
          ...(item.groupBy ? {groupBy: item.groupBy} : {}),
          ...(item.groupByTime ? { groupByTime: item.groupByTime } : {}),
          ...(item.isMultiSeries ? { isMultiSeries: item.isMultiSeries } : {}),
          ...(item.relatedTo ? { relatedTo: item.relatedTo } : {}),
          x: item.x,
          y: item.y,
          h: item.h,
          w: item.w,
        }
      })
      const dashboardService = makeDashboardService(this.$api);
      const handle = this.$route.params.handle;
      const payload = {
        charts: chartsPaylod
      }
      const params = {
        returnResult,
        view: this.view,
        ...(returnResult ? {projects: this.filters.selectedProjects} : undefined),
      }
      const projectKey = this.$route.params.key;
      const id = this.isOrgProjectView ? this.$route.params.id : this.dashboardUid({handle, projectKey, view: this.view});

      await dashboardService.updateCharts({handle, payload, projectKey ,params, id}).then((response) =>  {
        if(returnResult)
          this.charts = response.data.charts;
        if(updateLayout)
          this.updateLayout();
      })
      
    },
    async getOverview({
      tagFilter,
      milestoneFilter,
      testPlanFilter,
    }){
      try { 
        const dashboardService = makeDashboardService(this.$api);
        const handle = this.$route.params.handle;
        const projectKey = this.$route.params.key;
        // const queries = {}
        const queries = {
          ...(tagFilter ? {tagFilter} : {}),
          ...(milestoneFilter ? {milestoneFilter} : {}),
          ...(testPlanFilter ? {testPlanFilter} : {}),
          ...(this.filters.selectedTags ? {selectedTags: this.filters.selectedTags.map(item => item.uid)} : {}),
          ...(this.filters.selectedMilestones ? {selectedMilestones: this.filters.selectedMilestones.map(item => item.uid)} : {}),
          ...(this.filters.selectedPlans ? {selectedTestPlans: this.filters.selectedPlans.map(item => item.uid)} : {}),
        }

        await dashboardService.getDashboardOveview(handle, projectKey, queries).then(response => { this.overviewData = response.data });
        if(this.isOrgScope && !this.overviewData?.projects?.length){
          // Navigate the user to the Projects view if no projects are available
          this.$router.push({
            name: 'ProjectsView'
          })
        }

        if(!this.isOrgScope)
          this.filters.selectedProjects = [ this.overviewData.projects.find(element => element.key == this.$route.params.key).uid ]
      } catch (error) {
        showErrorToast(this.$swal, error.response?.data?.message || 'Internal server error')
      } 
    },
    async addChart({chartType, entityType, w, h, ...args}){
      if(!this._writeDashboard)
        return showErrorToast(this.$swal, this.$t('dashboard.addChart'));
      /*
      * Add Chart Flow Feature
      * - User clicks on 'Add' from customizeComponent.
      * - Hide customizeComponent, and a virtual element appears showing where this chart should be placed in the grid (dragging).
      * - Once the user selects the place and right-clicks, call the method for adding the chart to the layout and updating the backend.
      ==> Now we're placing it this way:
      * - Get the maximum Y value, then add the item's height to it with x:0, so the item will be placed at the left bottom of all items.
      */

      this.isAddingChart = true;
      const Ids = this.charts.map(item => item.id).filter(Boolean);
      const uniqueId = Ids.length ? _.max(Ids) + 1 : 1;
    
      let maxY = 0;
      this.layout.forEach(chart => {
        if(chart.y + chart.h > maxY)
            maxY = chart.y + chart.h 
      })
     

      this.charts.push({
        id: uniqueId,
        type: chartType,
        entityType: entityType,
        ...(chartType !== 'counterChart' && !args.relatedTo ? {groupBy: entityType == 'testCase' ? 'priorities' : 'statuses'} : {}),
        x: 0,
        key: 0,
        retryTimes: 0,
        y: maxY,
        w: w,
        h: h,
        ...args
      })
      await this.updateCharts();

      this.showCustomize = false
      if(!this.isOrgProjectView)
        this.$emit('updateCustomize', false)

      this.isAddingChart = false;
    },
    updateLayout(){
      this.layout =  this.charts?.map(item => {
        return {
          x: item.x,
          y: item.y,
          w: item.w,
          h: item.h,
          i: item.id
        }
      })
    },
    async removeCounterChart(entityType){
      if(!this._deleteDashboard)
        return showErrorToast(this.$swal, this.$t('dashboard.deleteChart'));

      const chart = this.charts.find(element => element.type == 'counterChart' && element.entityType == entityType);
      await this.deleteChart(chart.id);
      this.showCustomize = false;
    }
  },
  async created(){
    this.setCharts = _.debounce(this._setCharts, 500)
    this.updateKey = _.debounce(this._updateKey, 500)
    const creationRequests = [
      ...(this.isOrgProjectView ? [this.getDashboardsList()] : []),
      this.getOverview({
        tagFilter: '',
        milestoneFilter: '',
        planFilter: ''
      }),
      this.getDashboard(),
    ]
    await this.init([
      ...creationRequests
    ])
    this.updateLayout()

  },
};
</script>
<style>
.dashboard .vue-resizable-handle {
  display: none;
}
.vue-grid-layout{
  margin: 0px -10px !important;
}
.chart-settings-menu{
  z-index: 15 !important;
}
.vue-grid-item{
  border-radius: 8px;
  /* overflow: hidden; */
}
.apex_chart-filters{
  gap: 8px !important;
}
.__main-btn{
  font-family: 'Inter' !important;
  font-size: 14px;
  border-radius: 8px !important;
  padding: 10px 24px !important;
  background-color: #0C2FF3 !important;
  color: #FFF !important;
}
.__secondary-btn{
  font-family: 'Inter' !important;
  font-size: 14px;
  border-radius: 8px !important;
  padding: 10px 24px !important;
  background-color: #F2F4F7 !important;
  color: #0C111D !important;
}
.dashboard__header{
  background-color: #FFF;
  border-radius: 8px;
  padding: 24px !important;
}
.dashboard__header-title h3{
  font-size: 24px;
  font-family: 'Inter';
  font-weight: 700 !important;
  color: #101828;
}
.dashboard__header-tags{
  width: 380px;
}
.dashboard__header-actions{
  display: flex;
  gap: 12px;
}
.dashboard__header-actions #select-dashboard-views{
  width: 278px !important;
}
.dashboard__header-actions button .v-btn__content{
  width: 80% !important;
}
.dashboard__header-actions button .v-btn__content span{
  text-overflow: ellipsis !important; 
  white-space: nowrap !important;
  overflow: hidden !important;
  width: 100% !important;
}
.dashboard__header-actions #select-dashboard-views .v-btn__content{
  justify-content: space-between;
}
.dashboard-menu-list .dahboard-actions .v-btn span svg{
  transform: scale(0.8) !important;
}
.dashboard-menu-list .v-list-item-group{
  max-height: 300px !important;
  overflow: hidden;
  overflow-y: scroll;
}
.dashboard-menu-list .v-list-item-group{
  max-height: 300px !important;
  overflow: hidden;
  overflow-y: scroll;
}
.dashboard-menu-list .v-list-item-group .v-list-item::after{
  display: none;
}
.dashboard-menu-list .v-list-item-group::-webkit-scrollbar {
  width: 4px;
}
.dashboard-menu-list .v-list-item-group::-webkit-scrollbar-track {
  background: #F9FAFB;
}
.dashboard-menu-list .v-list-item-group::-webkit-scrollbar-thumb {
  background: #D0D5DD;
  border-radius: 4px;
}
.dashboard-menu-list .v-btn__content{
  width: 85%;
}
.dashboard__header-filters{
  gap: 12px;
}
.dashboard__header-filters .v-autocomplete{
  width: min-content;
  font-size: 14px;
  min-width: 260px;
}
.dashboard__header-filters .v-autocomplete .v-select__selections input,
.dashboard__header-tags .v-autocomplete .v-select__selections input{
  min-height: 40px;
}
.dashboard__header-filters .v-select .v-select__selection{
  overflow: visible;
}
.dashboard__analytics-card{
  background-color: #FFF;
  width: 100%;
  padding: 0px 0px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}
.v-icon-wraper{
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: #F2F4F7;
  display: flex;
  justify-content: center;
  align-content: center;
}
.v-icon-wraper i{
  color: #0C2FF3;
}
.analytics-card-text {
  text-align: left;
}
.analytics-card-text h3{
  font-size: 24px;
}
.analytics-card-text p{
  margin: 0px !important;
  color: #667085;
}
.v-icon-wraper svg path,
.v-icon-wraper svg path {
  stroke: #0c2ff3 !important;
  stroke-width: 2px;
}
.apexcharts-legend {
  text-align: left !important;
  justify-content: flex-start !important;
  gap: 10px;
  top: 0px;
}
.apexcharts-legend .apexcharts-legend-marker{
  margin-right: 10px;
}
</style>
<style scoped>
.col-4{
  background-color: #FFF;
}
.row{
  margin: 0px !important;
}
.dashboard__charts{
  display: flex !important;
  gap: 8px;

}
.dashboard__charts-form{
  gap: 10px;
}
.dashboard__charts-card{
  padding: 24px;
  border-radius: 8px;
  height: 100%;
  background: #fff;
}
.dashboard__charts-card > div{
  height: 100%;
}
.dashboard-main{
  position: relative;
}
.dashboard-main .loading-dashboard{
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  z-index: 20;
  background-color: rgb(255, 255, 255, 0.8);
}
.dashboard-main .loading-dashboard .loader-div{
  position: absolute;
  top: 0px;
}
</style>
