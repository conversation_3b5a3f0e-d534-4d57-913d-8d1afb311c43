<template>
  <div class="pl-2 pt-3">
    <DefectHeader
      :open-defects="openDefects"
      :closed-defects="closedDefects"
      :active-state="activeState"
      :integrations="integrationsList"
      :selected-integration="selectedIntegration"
      @update-state="setActiveState"
      @integration-change="handleIntegrationChange"
      @create-defect="handleCreateDefect"
    />
    
    <!-- Main Layout with Table and Side Panel -->
    <v-row class="defects-layout">
      <!-- Left: Table Section -->
      <v-col 
        :class="showDetailDialog ? 'col-8' : 'col-12'"
        class="defects-table-section"
      >
        <v-card
          class="pa-6 app-height-global rounded-lg "
          elevation="0"
          width="100%"
        >
          <v-row
            justify="space-between"
            class="align-center"
          >
            <v-col
              cols="12"
              md="auto"
              class="d-flex align-center"
            >
              <SearchComponent
                v-if="!skeletonLoaderState"
                :search="filter.name"
                :placeholder="getSearchPlaceholder()"
                class="mr-2"
                @update:search="filter.name = $event"
              />
              <v-skeleton-loader
                v-else
                class="rounded-lg mr-2"
                height="40"
                width="325"
                type="button"
              />
              <DefectFilterDialog
                v-if="!skeletonLoaderState"
                :data="filter"
                :priorities="priorities"
                :statuses="statuses"
                :filter-count="filterCount"
                :tags="systemTags"
                :selected-integration="selectedIntegration"
                @update-filter-condition="updateFilterCondition"
              />
              <v-skeleton-loader
                v-else
                class="rounded-lg"
                height="40"
                width="40"
                type="button"
              />
            </v-col>

            <v-col
              cols="12"
              md="auto"
            >
              <v-row
                justify="end"
                class="align-center"
              >
                <DefectSettingsMenu
                  v-if="!skeletonLoaderState"
                  class="mr-3"
                  :table-type="getTableType()"
                  :selected-integration="selectedIntegration"
                  @table-type-change="handleTableTypeChange"
                />
                <v-skeleton-loader
                  v-else
                  class="rounded-lg mr-3"
                  height="40"
                  width="40"
                  type="button"
                />
              </v-row>
            </v-col>
          </v-row>
          
          <v-row
            justify="space-between"
            class="align-center"
          >
            <v-col
              cols="12"
              md="auto"
              class="d-flex align-center flex-wrap"
            >
              <div v-if="filterCount > 0">
                <span
                  class="fw-bold font-weight-medium no-bg pr-2"
                  width="300px"
                >
                  {{ $t('results') }} ({{ totalItems }})
                </span>
              </div>
              <div
                v-for="priorityId in filter.priorities"
                :key="priorityId"
              >
                <v-chip
                  class="chips-style ma-2"
                  close
                  @click:close="removePriority(priorityId)"
                >
                  {{ $t('Priority') }}:{{ getPriorityName(priorityId, priorities) }}
                </v-chip>
              </div>
              <div
                v-for="statusId in filter.statuses"
                :key="statusId"
              >
                <v-chip
                  class="chips-style ma-2"
                  close
                  @click:close="removeStatus(statusId)"
                >
                  {{ $t('status') }}:{{ getStatusName(statusId, statuses) }}
                </v-chip>
              </div>
              <div
                v-if="filter.startDate || filter.endDate"
                class="d-flex align-center ml-2"
              >
                <v-chip
                  class="chips-style ma-2 black--text theme--light"
                  close
                  @click:close="removeDate()"
                >
                  {{ $t('lastUpdate') }}: {{ filter.startDate }} - {{ filter.endDate }}
                </v-chip>
              </div>
              <div
                v-for="tagId in filter.tags"
                :key="tagId"
              >
                <v-chip
                  class="chips-style ma-2"
                  close
                  @click:close="removeTag(tagId)"
                >
                  {{ $t('tag') }}: {{ getTagName(tagId) }}
                </v-chip>
              </div>
              <v-btn
                v-if="hasActiveFilters"
                class="blue--text fw-semibold font-weight-medium"
                width="100px"
                text
                elevation="0"
                style="text-transform: none; font-weight: 600 !important"
                @click="clearFilter()"
              >
                <div class="p-2">
                  {{ $t('clearAll') }}
                </div>
              </v-btn>
            </v-col>
          </v-row>

          <!-- Table View -->
          <div
            v-if="!skeletonLoaderState && paginatedDefects.length > 0"
            :class="tableContainerClass"
          >
            <DefectTable
              :headers="filteredHeaders"
              :items="paginatedDefects"
              item-key="uid"
              :priorities="priorities"
              :statuses="statuses"
              :integration-type="selectedIntegration"
              :active-state="activeState"
              :write-defect="writeDefect"
              :table-class="tableClass"
              :should-have-overflow="shouldHaveOverflow"
              @view="onViewDefect"
              @edit="onEditDefect"
              @close="onCloseDefect"
              @reopen="onReopenDefect"
            />
          </div>
          
          <!-- Skeleton Loading -->
          <DefectTableSkeleton v-else-if="skeletonLoaderState" />
          
          <!-- Empty State for Open Defects -->
          <div
            v-else-if="!skeletonLoaderState && paginatedDefects.length === 0 && activeState === 'active'"
            class="empty-state-container d-flex justify-center align-center"
            style="min-height: 400px;"
          >
            <ArchivedEmptyState
              :image-src="require('@/assets/png/empty-defect.png')"
              image-max-width="400px"
              :title="$t('defect.empty_state.no_open_title')"
            >
              <template #description>
                <p class="mb-0 mt-3 text-center">
                  {{ $t('defect.empty_state.no_open_description_1') }}
                </p>
                <p class="ma-0 text-center">
                  {{ $t('defect.empty_state.no_open_description_2') }}
                </p>
              </template>
            </ArchivedEmptyState>
          </div>

          <!-- Empty State for Closed Defects -->
          <div
            v-else-if="!skeletonLoaderState && paginatedDefects.length === 0 && activeState === 'closed'"
            class="empty-state-container d-flex justify-center align-center"
            style="min-height: 400px;"
          >
            <ArchivedEmptyState
              :image-src="require('@/assets/png/empty-defect.png')"
              image-max-width="400px"
              :title="$t('defect.empty_state.no_closed_title')"
            >
              <template #description>
                <p class="mb-0 mt-3 text-center">
                  {{ $t('defect.empty_state.no_closed_description') }}
                </p>
              </template>
            </ArchivedEmptyState>
          </div>

          <Pagination
            v-if="totalItems > 0"
            :page="currentPage"
            :items-per-page="itemsPerPage"
            :total-pages="totalPages"
            :total-items="totalItems"
            @update:pagination="updatePagination"
          />
        </v-card>
      </v-col>

      <!-- Right: Side Panel -->
      <v-col 
        v-if="showDetailDialog"
        cols="4"
        class="defects-side-panel "
      >
        <ViewDefectDialog
          v-model="showDetailDialog"
          :data="selectedDefect"
          :priorities="priorities"
          :statuses="statuses"
          :selected-integration="selectedIntegration"
          :is-selected-defect-first-index="isSelectedDefectFirstIndex"
          :is-selected-defect-last-index="isSelectedDefectLastIndex"
          @add-comment="onAddNewComment"
          @close-dialog="closeDetailView"
          @view-previous-defect="viewPreviousDefect"
          @view-next-defect="viewNextDefect"
          @edit="onEditDefect"
        />
      </v-col>
    </v-row>

    <EditDialog
      v-model="showEditDialog"
      :data="selectedDefect"
      :priorities="priorities"
      :statuses="statuses"
      :status-scopes="statusScopes"
      :tags="systemTags"
      :selected-integration="selectedIntegration"
      :loading="editLoading"
      :is-loading-status-scopes="isLoadingStatusScopes"
      @edit="editDefect"
      @close-dialog="closeEditDialog"
    />

    <CreateDefect
      v-if="showCreateDefectDialog"
      :create-defect-dialog="showCreateDefectDialog"
      :service="selectedIntegration ? selectedIntegration.toLowerCase() : null"
      :execution="defaultExecution"
      :action-selected="'Create new defect'"
      @closeDialog="showCreateDefectDialog = false"
      @defectCreated="handleDefectCreated"
      @defectLinked="handleDefectLinked"
    />
  </div>
</template>

<script>
import { createNamespacedHelpers, mapGetters, mapActions } from 'vuex';
import * as _ from 'lodash';


import { useDefectsIndex } from '@/composables/modules/defect/index';
import DefectHeader from '@/components/Defect/Header';
import DefectFilterDialog from '@/components/Defect/FilterDialog.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import DefectSettingsMenu from '@/components/Defect/DefectSettingsMenu.vue';
import DefectTable from '@/components/Defect/Table.vue';
import ViewDefectDialog from '@/components/Defect/ViewDefectDialog.vue';
import EditDialog from '@/components/Defect/EditDialog.vue';
import CreateDefect from '@/components/Defect/CreateDefect.vue';
import ArchivedEmptyState from '@/components/base/ArchivedEmptyState.vue';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import Pagination from '@/components/base/Pagination.vue';
import DefectTableSkeleton from '@/components/Skeletons/Defect/DefectTableSkeleton.vue';
import handleLoading from '@/mixins/loader.js';

const { mapState } = createNamespacedHelpers('user');

export default {
  name: 'Defects',

  components: {
    DefectHeader,
    DefectFilterDialog,
    SearchComponent,
    DefectSettingsMenu,
    DefectTable,
    ViewDefectDialog,
    EditDialog,
    CreateDefect,
    ArchivedEmptyState,
    Pagination,
    DefectTableSkeleton,
  },

  mixins: [colorPreferencesMixin, handleLoading],
  
  setup() {
    // Initialize composables - now unified in single composable
    const defectsIndex = useDefectsIndex();
    
    return {
      ...defectsIndex,
      // Expose composable's init with different name to avoid mixin conflict
      composableInit: defectsIndex.init,
    };
  },

  data() {
    return {
      showCreateDefectDialog: false,
      defaultExecution: {
        uid: null,
        name: '',
        status: 'passed',
        projectUid: null,
      },
    };
  },

  computed: {
    ...mapState(['currentAccount']),
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders',
    }),
    currentHeaders() {
      return this.dynamicHeaders[this.getTableType()] || [];
    },
    filteredHeaders() {
      return this.currentHeaders.filter((header) => header.checked);
    },
    shouldHaveOverflow() {
      // Enable overflow (horizontal scroll) when sidebar is open or more than 4 columns
      return this.showDetailDialog || (this.filteredHeaders && this.filteredHeaders.length > 4);
    },
    tableContainerClass() {
      return this.shouldHaveOverflow ? 'table-scroll-container' : 'table-no-scroll-container';
    },
    tableClass() {
      const baseClasses = 'table-fixed data-table-style';
      return this.shouldHaveOverflow ? `${baseClasses} table-min-width` : baseClasses;
    },
  },

  watch: {
    'filter.name': {
      handler: _.debounce(async function () {
        await this.filterDefects();
      }, 500),
    },
    
    '$route.params.defectId': {
      handler(newDefectId, oldDefectId) {
        if (newDefectId && newDefectId !== oldDefectId) {
          this.handleDefectIdFromRoute();
        }
      },
      immediate: false
    },
    
    paginatedDefects: {
      handler(newDefects) {
        if (newDefects.length > 0 && this.$route.params.defectId && !this.showDetailDialog) {
          this.handleDefectIdFromRoute();
        }
      },
      immediate: false
    },
    
    selectedIntegration: {
      handler(newIntegration, oldIntegration) {
        if (!newIntegration || newIntegration === oldIntegration) return;
        if (oldIntegration === null || oldIntegration === undefined) {
          return;
        }
        
        this.saveProjectIntegrationPreference();
        
        const tableType = this.getTableType();
        if (!this.dynamicHeaders[tableType]) {
          this.initializeHeaders({ type: tableType });
        }
        
        this.$nextTick(() => {
          this.headers = [...this.dynamicHeaders[tableType]];
          this.updateTableHeaders();
        });

        if (!this.isInitializing) {
          // Show skeleton loader when switching integrations
          this.showSkeletonLoader();
          this.currentPage = 1;
          this.clearCache();
          this.countsLoaded = false;
          this.getDefects().finally(() => {
            this.hideSkeletonLoader();
          });
        }
      },
    },
    

    

    integrationService: {
      handler(newIntegration) {
        if (!newIntegration) return;
        
        const formattedIntegration = newIntegration.charAt(0).toUpperCase() + newIntegration.slice(1).toLowerCase();
        
        if (this.selectedIntegration !== formattedIntegration) {
          this.selectedIntegration = formattedIntegration;
        }
      },
    },
  },

  created() {
    // Show skeleton loader immediately to prevent "no defect found" flash
    this.showSkeletonLoader();
    this.initializeHeaders({ type: this.getTableType() });
    this.updateTableHeaders();
  },

  async mounted() {
    // Use the composable's init method which handles integration preferences properly
    await this.composableInit();
    
    // Handle defect ID from route
    if (this.$route.params.defectId && !this.showDetailDialog) {
      await this.handleDefectIdFromRoute();
    }
  },

  methods: {
    ...mapActions('headers', ['initializeHeaders']),
    
    integrationService() {
      return this.$route.params.integration;
    },

    async handleDefectIdFromRoute() {
      const defectId = this.$route.params.defectId;
      if (!defectId) return;
      await this.onViewDefect({ uid: defectId });
    },
    getTableType() {
      const headerTypeMap = {
        Jira: 'projectsJiraDefect',
        Github: 'projectsGithubDefect',
      };
      return headerTypeMap[this.selectedIntegration] || 'projectsJiraDefect';
    },

    handleTableTypeChange(newType) {
      if (!this.dynamicHeaders[newType]) {
        this.initializeHeaders({ type: newType });
      }
      this.$nextTick(() => {
        this.headers = [...this.dynamicHeaders[newType]];
      });
    },

    updateTableHeaders() {
      const tableType = this.getTableType();
      if (!this.dynamicHeaders[tableType]) {
        this.initializeHeaders({ type: tableType });
      }
      const updatedHeaders = this.dynamicHeaders[tableType].map((header) => {
        if (header.value === 'updatedAt' || header.value === 'closedAt') {
          return {
            ...header,
            text: this.activeState === 'closed' ? this.$t('closedAt') : this.$t('lastUpdate'),
          };
        }
        return header;
      });
      this.$store.dispatch('headers/updateHeaders', { type: tableType, headers: updatedHeaders });
      this.headers = [...updatedHeaders];
    },

    handleCreateDefect() {
      // Set project information for the default execution
      this.defaultExecution.projectUid = this.$route.params.key;
      this.showCreateDefectDialog = true;
    },

    handleDefectCreated() {
      this.showCreateDefectDialog = false;
      // Refresh the defects list after creating a new defect
      this.getDefects(true);
    },

    handleDefectLinked() {
      this.showCreateDefectDialog = false;
      // Refresh the defects list after linking a defect
      this.getDefects(true);
    },

    getSearchPlaceholder() {
      if (this.selectedIntegration === 'Github') {
        return this.$t('placeHolder.searchByTitle');
      } else {
        return this.$t('placeHolder.searchByName');
      }
    },
  },

  // Clear all state when component is deactivated (navigating away)
  deactivated() {
    this.clearAllState();
    this.resetModuleCache();
  },

  // Clear all state when component is destroyed
  beforeDestroy() {
    this.clearAllState();
    this.resetModuleCache();
  },
    

    

    

    

  };
</script>

<style scoped>
.chips-style {
  background-color: #f9fafb !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
}

/* Layout styles for table and side panel */
.defects-layout {
  margin: 0;
}

.defects-table-section {
  padding-right: 8px;
  padding-left: 0;
}

/* Override any default grid padding */
.defects-table-section .v-col {
  padding-left: 0 !important;
}

.defects-side-panel {
  padding-left: 0px;
}
</style>
