<template>
  <v-container
    fluid
    style="padding: 0"
  >
    <ProjectHeader
      :filter="filter"
      :active-item-count="totalActiveCount"
      :archived-item-count="totalArchivedCount"
      :write-project="writeProject"
      :is-creating-demo-project="isCreatingDemoProject"
      @update-filter="updateFilter"
      @create-demo-project="handleCreateDemoProject"
    />
    <template
      v-if="
        getActiveProjectCount == 0 &&
          !isFilterApplied &&
          filter != 'archived' &&
          !tableLoadingState &&
          !skeletonLoaderState && search === ''
      "
    >
      <div class="mt-3 mb-0 white rounded-lg mx-0 project-placeholder-height d-flex justify-center align-center">
        <ActiveEmptyState
          :image-src="require('@/assets/png/table-empty-state.png')"
          :title="$t('projects.empty_state.title')"
          :write-entity="writeProject"
          button-color="primary"
        >
          <template #description>
            <p class="mb-0 mt-3">
              {{ $t('projects.empty_state.lets_get_started') }}
            </p>
            <p class="ma-0">
              {{ $t('projects.empty_state.take_the_lead.part1') }}
              <span class="fw-semibold">"{{ $t('projects.empty_state.take_the_lead.part2') }}"</span>
              {{ $t('projects.empty_state.take_the_lead.part3') }}
            </p>
            <div class="actions-btn">
              <v-btn
                background-color="#F2F4F7"
                depressed
                height="40px"
                class="text-capitalize rounded-lg btn-theme black--text"
                :disabled="!writeProject || isCreatingDemoProject"
                @click="handleCreateDemoProject"
              >
                <v-progress-circular
                  v-if="isCreatingDemoProject"
                  indeterminate
                  color="black"
                  size="16"
                  width="2"
                  class="mr-2"
                />
                {{ $t('createDemoProject') }}
              </v-btn>
              <v-btn
                color="blue"
                depressed
                :disabled="!writeProject"
                height="40px"
                class="text-capitalize rounded-lg btn-theme white--text"
                :to="{ name: 'ProjectCreateView' }"
              >
                {{ $t('createProject') }} <v-icon
                  class="ml-1"
                  size="16px"
                >
                  mdi-plus
                </v-icon>
              </v-btn>
            </div>
          </template>
        </ActiveEmptyState>
      </div>
    </template>
    <template
      v-else-if="
        getArchivedProjectCount == 0 &&
          !isFilterApplied &&
          filter != 'active' &&
          !tableLoadingState &&
          !skeletonLoaderState && search === ''
      "
    >
      <div class="mt-3 mb-0 white rounded-lg mx-0 project-placeholder-height d-flex justify-center align-center">
        <ArchivedEmptyState
          :image-src="require('@/assets/png/table-empty-state.png')"
          :title="$t('projects.archived_empty_state.title')"
        >
          <template #description>
            <p class="mb-0 mt-3">
              {{ $t('projects.archived_empty_state.description.part1') }}
            </p>
            <p class="mb-0">
              {{ $t('projects.archived_empty_state.description.part2') }}
            </p>
          </template>
        </ArchivedEmptyState>
      </div>
    </template>
    <template v-else>
      <v-card
        class="py-6 px-6 mt-3 project-height"
        rounded="lg"
        elevation="0"
        width="100%"
      >
        <template>
          <div class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <ProjectSearchComponent
                :search="search"
                class="mr-3"
                @update:search="updateSearch"
                @clear:search="clearSearch"
              />
            </div>
            <div class="d-flex align-center justify-end">
              <ToggleView
                :table="table"
                class="mr-3"
                @toggle-table="toggleTable"
              />
              <SettingsMenu
                table-type="projectsView"
                :required-items="['Name', 'Test Runs', 'Users']"
              />
            </div>
          </div>
          <ProjectTable
            v-if="table && filteredItems.length > 0 && !skeletonLoaderState"
            :filtered-headers="filteredHeaders"
            :filtered-items="filteredItems"
            :item-key="itemKey"
            :row-class="rowClass"
            :write-project="writeProject"
            :delete-project="deleteProjectPermission"
            :total-items="totalRows"
            :current-page="currentPage"
            :items-per-page="perPage"
            @select-item="useProject"
            @edit-item="editItem"
            @archive-item="confirmArchiveProject"
            @unarchive-item="confirmUnarchiveProject"
            @delete-item="confirmDeleteProject"
            @toggle-star="handleToggleStar"
            @update-pagination="onUpdatePagination"
          />

          <ProjectTableSkeleton v-else-if="table && skeletonLoaderState" />

          <v-row
            v-else-if="!table && filteredItems.length > 0 && !skeletonLoaderState"
            class="mt-6"
          >
            <v-col
              v-for="(item, index) in filteredItems"
              :key="index"
              cols="4"
            >
              <ProjectCard
                :item="item"
                :filter-items="filteredMenuHeaders"
                @select-item="useProject"
                @edit-item="editItem"
                @archive-item="confirmArchiveProject"
                @unarchive-item="confirmUnarchiveProject"
                @delete-item="confirmDeleteProject"
                @toggle-star="handleToggleStar"
              />
            </v-col>
          </v-row>
          <v-row
            v-else-if="skeletonLoaderState"
            class="mt-3"
          >
            <v-col
              v-for="i in 6"
              :key="i"
              cols="4"
            >
              <ProjectCardSkeleton />
            </v-col>
          </v-row>
          <div
            v-else
            class="py-10"
          >
            {{ $t('projects.no_data_available') }}
          </div>
        </template>
      </v-card>
    </template>
    <ProjectDiscardDialog
      v-model="showConfirmDeleteDialog"
      :title="$t('projects.delete_project_dialog.title')"
      @close="closeDeleteDialog"
      @handleConfirmClick="confirmDeleteProject"
    >
      <template #content>
        <v-flex class="mt-4 flex-column">
          <p class="text-start">
            {{ $t('projects.delete_project_dialog.warning') }}
          </p>
          <p
            v-if="filter != 'archived'"
            class="text-start"
          >
            {{ $t('projects.delete_project_dialog.recommend') }}
          </p>
        </v-flex>
      </template>
      <template #footer>
        <v-row>
          <v-col cols="6">
            <v-btn
              v-if="filter != 'archived'"
              depressed
              height="40px"
              width="100%"
              class="text-capitalize btn-theme rounded-lg black--text mt-2"
              background-color="#F2F4F7"
              @click="archiveProject"
            >
              {{ $t('archive') }}
            </v-btn>
            <v-btn
              v-else
              depressed
              height="40px"
              width="100%"
              class="text-capitalize btn-theme rounded-lg black--text mt-2"
              background-color="#F2F4F7"
              @click="closeDeleteDialog"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn
              depressed
              height="40px"
              width="100%"
              class="text-capitalize btn-theme rounded-lg white--text mt-2"
              color="danger"
              @click="deleteProject"
            >
              {{ $t('projects.create_project.close_dialog.confirm_button') }}
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </ProjectDiscardDialog>
    <ProjectDiscardDialog
      v-model="showConfirmArchiveDialog"
      :title="$t('projects.archive_project_dialog.title', { action: $t('archive') })"
      @close="closeArchiveDialog"
      @handleConfirmClick="confirmArchiveProject"
    >
      <template #content>
        <v-flex class="mt-4">
          <p class="text-start">
            {{ $t('projects.archive_project_dialog.warning') }}
          </p>
        </v-flex>
      </template>
      <template #footer>
        <v-row>
          <v-col cols="6">
            <v-btn
              depressed
              height="40px"
              width="100%"
              class="text-capitalize btn-theme rounded-lg black--text mt-2"
              background-color="#F2F4F7"
              @click="closeArchiveDialog"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn
              depressed
              width="100%"
              class="text-capitalize rounded-lg btn-theme white--text mt-2"
              color="primary"
              height="40px"
              @click="archiveProject"
            >
              {{ $t('archive') }}
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </ProjectDiscardDialog>
    <ProjectDiscardDialog
      v-model="showConfirmUnarchiveDialog"
      :title="$t('projects.archive_project_dialog.title', { action: $t('unarchive') })"
      @close="closeUnarchiveDialog"
      @handleConfirmClick="confirmUnarchiveProject"
    >
      <template #content>
        <v-flex class="mt-4">
          <p class="text-start">
            {{ $t('projects.archive_project_dialog.unarchive_warning') }}
          </p>
        </v-flex>
      </template>
      <template #footer>
        <v-row>
          <v-col cols="6">
            <v-btn
              depressed
              height="40px"
              width="100%"
              class="text-capitalize btn-theme rounded-lg black--text mt-2"
              background-color="#F2F4F7"
              @click="closeUnarchiveDialog"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn
              depressed
              height="40px"
              width="100%"
              class="text-capitalize btn-theme rounded-lg white--text mt-2"
              color="blue"
              @click="unarchiveProject"
            >
              {{ $t('unarchive') }}
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </ProjectDiscardDialog>
    <ProjectCreationStatus
      v-model="showCreationStatus"
      :progress="creationProgress"
      :status="projectStatus"
    />
  </v-container>
</template>

<script>
import ProjectHeader from '@/components/Project/ProjectHeader';
import ProjectSearchComponent from '@/components/Project/ProjectSearchComponent.vue';
import ProjectCard from '@/components/Project/ProjectCard.vue';
import ToggleView from '@/components/Project/ToggleView.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import ProjectTable from '@/components/Project/ProjectTable.vue';
import ProjectDiscardDialog from '@/components/Project/ProjectDiscardDialog';
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue';
import ArchivedEmptyState from '@/components/base/ArchivedEmptyState.vue';
import ProjectCardSkeleton from '@/components/Skeletons/Projects/ProjectCardSkeleton.vue';
import ProjectTableSkeleton from '@/components/Skeletons/Projects/ProjectTableSkeleton.vue';
import ProjectCreationStatus from '@/components/Project/ProjectCreationStatus.vue';

import { defineComponent, onMounted, onUnmounted, ref } from 'vue';
import { useProjectsManagement } from '@/composables/modules/project/view';
import { showErrorToast, showSuccessToast } from '@/composables/utils/toast';
import Swal from 'sweetalert2';
import { t } from '@/i18n';

export default defineComponent({
  name: 'Project',
  components: {
    ProjectHeader,
    ProjectSearchComponent,
    ProjectCard,
    ProjectTable,
    ToggleView,
    SettingsMenu,
    ProjectDiscardDialog,
    ActiveEmptyState,
    ArchivedEmptyState,
    ProjectCardSkeleton,
    ProjectTableSkeleton,
    ProjectCreationStatus,
  },
  setup() {
    const {
      // Data properties
      handle,
      selectedProject,
      projects,
      totalArchived,
      totalActive,
      filter,
      isFilterApplied,
      search,
      table,
      options,
      membersState,
      headers,
      itemKey,
      rowClass,
      showConfirmDeleteDialog,
      showConfirmArchiveDialog,
      showConfirmUnarchiveDialog,
      tableLoadingState,
      skeletonLoaderState,

      // Pagination properties
      totalRows,
      currentPage,
      perPage,
      totalPages,

      // Computed properties
      writeProject,
      deleteProjectPermission,
      filteredHeaders,
      filteredMenuHeaders,
      filteredItems,
      totalActiveCount,
      totalArchivedCount,
      getActiveProjects,
      getActiveProjectCount,
      getArchivedProjects,
      getArchivedProjectCount,

      // Methods
      init,
      getProjects,
      onUpdatePagination,
      updateFilter,
      updateSearch,
      clearSearch,
      toggleTable,
      closeDeleteDialog,
      confirmDeleteProject,
      deleteProject,
      archiveProject,
      unarchiveProject,
      closeArchiveDialog,
      confirmArchiveProject,
      closeUnarchiveDialog,
      confirmUnarchiveProject,
      editItem,
      handleToggleStar,
      useProject,
      showSkeletonLoader,
      hideSkeletonLoader,
      setProject,
      getProject,
      createDemoProject,
    } = useProjectsManagement();

    const showCreationStatus = ref(false);
    const creationProgress = ref(0);
    const projectStatus = ref('pending');
    const pollingInterval = ref(null);
    const progressInterval = ref(null);
    const isCreatingDemoProject = ref(false);

    const cleanupIntervals = () => {
      if (pollingInterval.value) {
        clearInterval(pollingInterval.value);
        pollingInterval.value = null;
      }
      if (progressInterval.value) {
        clearInterval(progressInterval.value);
        progressInterval.value = null;
      }
    };

    const startPolling = (projectKey) => {
      // Clear any existing polling interval
      if (pollingInterval.value) {
        clearInterval(pollingInterval.value);
      }

      pollingInterval.value = setInterval(async () => {
        try {
          const response = await getProject({ handle: handle.value, projectKey });
          const project = response.data;
          if (project.status === 'active') {
            cleanupIntervals();
            projectStatus.value = 'active';
            showSuccessToast(Swal, t('success.projectCreated'));
            setTimeout(() => {
              showCreationStatus.value = false;
              isCreatingDemoProject.value = false;
            }, 2000);
            projects.value.push(project);
            totalActive.value += 1;
            creationProgress.value = 100;
          } else if (project.status === 'failed') {
            cleanupIntervals();
            projectStatus.value = 'failed';
            setTimeout(() => {
              showCreationStatus.value = false;
              isCreatingDemoProject.value = false;
            }, 2000);
            showErrorToast(Swal, t('projects.demo_project.creation.failed'));
          } else if (project.status === 'pending') {
            projectStatus.value = 'pending';
          }
        } catch (error) {
          cleanupIntervals();
          projectStatus.value = 'failed';
          setTimeout(() => {
            showCreationStatus.value = false;
            isCreatingDemoProject.value = false;
          }, 2000);
          showErrorToast(Swal, t('projects.demo_project.creation.check_status_failed'));
        }
      }, 2000);
    };

    const handleCreateDemoProject = async () => {
      // Prevent multiple simultaneous demo project creations
      if (isCreatingDemoProject.value) {
        return;
      }

      isCreatingDemoProject.value = true;
      showCreationStatus.value = true;
      creationProgress.value = 0;
      projectStatus.value = 'pending';

      // Clear any existing intervals
      cleanupIntervals();

      // Start progress simulation
      progressInterval.value = setInterval(() => {
        if (creationProgress.value < 90) {
          creationProgress.value += 1;
        }
      }, 500);

      try {
        const response = await createDemoProject({ handle: handle.value });
        const project = response.data;

        startPolling(project.key);
      } catch (error) {
        cleanupIntervals();
        projectStatus.value = 'failed';
        setTimeout(() => {
          showCreationStatus.value = false;
          isCreatingDemoProject.value = false;
        }, 2000);
        showErrorToast(Swal, t('projects.demo_project.creation.failed'));
      }
    };

    onUnmounted(() => {
      cleanupIntervals();
    });

    // Lifecycle hooks
    onMounted(async () => {
      await init([getProjects()]);
    });

    return {
      // Data properties
      handle,
      selectedProject,
      projects,
      totalArchived,
      totalActive,
      filter,
      isFilterApplied,
      search,
      table,
      options,
      membersState,
      headers,
      itemKey,
      rowClass,
      showConfirmDeleteDialog,
      showConfirmArchiveDialog,
      showConfirmUnarchiveDialog,
      tableLoadingState,
      skeletonLoaderState,
      showCreationStatus,
      creationProgress,
      projectStatus,
      isCreatingDemoProject,

      // Pagination properties
      totalRows,
      currentPage,
      perPage,
      totalPages,

      // Computed properties
      writeProject,
      deleteProjectPermission,
      filteredHeaders,
      filteredMenuHeaders,
      filteredItems,
      totalActiveCount,
      totalArchivedCount,
      getActiveProjects,
      getActiveProjectCount,
      getArchivedProjects,
      getArchivedProjectCount,

      // Methods
      getProjects,
      onUpdatePagination,
      updateFilter,
      updateSearch,
      clearSearch,
      toggleTable,
      closeDeleteDialog,
      confirmDeleteProject,
      deleteProject,
      archiveProject,
      unarchiveProject,
      closeArchiveDialog,
      confirmArchiveProject,
      closeUnarchiveDialog,
      confirmUnarchiveProject,
      editItem,
      handleToggleStar,
      useProject,
      showSkeletonLoader,
      hideSkeletonLoader,
      setProject,
      getProject,
      createDemoProject,
      handleCreateDemoProject,
    };
  },
});
</script>
<style scoped>
.projects-loader {
  min-height: 500px;
}
.placeholder-img {
  max-width: 438px;
  width: 100%;
}
.project-height {
  min-height: calc(100vh - 24px - 6.25rem);
  height: 100%;
}
.skeleton-bg {
  background-color: rgb(249, 250, 251);
  border-color: rgb(249, 250, 251);
}
.actions-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 24px;
}
</style>
