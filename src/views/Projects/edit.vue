<template>
  <v-container
    fluid
    style="padding: 0"
  >
    <v-card
      class="white py-6 px-6 mt-3 project-detail-height"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <div class="d-flex align-center">
        <v-icon
          color="blue"
          @click="back"
        >
          mdi-chevron-left
        </v-icon>
        <v-btn
          text
          color="blue"
          class="font-weight-bold text-capitalize"
          @click="back"
        >
          {{
            $t('projects.create_project.back_to_projects')
          }}
        </v-btn>
      </div>
      <div class="d-flex align-center justify-space-between text-start mt-7">
        <div class="d-flex align-center">
          <div style="position: relative; display: inline-block;">
            <Avatar
              v-if="!skeletonLoaderState"
              :avatar="{ user: imageSrc ?? selectedProject?.avatarUrl }"
              :avatar-src="defaultImage"
              :size="85"
            />
            <v-skeleton-loader
              v-else
              height="85"
              width="85"
              type="avatar"
            />
            <v-btn 
              v-if="!skeletonLoaderState"
              icon
            >
              <upload-avatar
                emit-file
                profile-image="project"
                media-type="attachment"
                @croppedFile="handleFileChange"
              />
            </v-btn> 
          </div>
          <template v-if="!skeletonLoaderState">
            <template v-if="!editTitle">
              <div
                class="ml-4"
                @mouseenter="visibleTitle = true"
                @mouseleave="visibleTitle = false"
              >
                <div class="font-weight-bold fs-24px text-theme-base d-flex align-center">
                  {{ selectedProject.name }}
                  <v-btn
                    icon
                    elevation="0"
                    class=""
                    @click="handleClickEditTag"
                  >
                    <PencilIcon
                      class=""
                    />
                  </v-btn>
                </div>
                <div class="fs-14px text-theme-secondary font-weight-regular">
                  {{ selectedProject?.customFields?.description }}
                </div>
              </div>
            </template>
            <template v-else>
              <div class="ml-4">
                <div class="font-weight-bold fs-24px text-theme-base d-flex align-center">
                  <v-text-field
                    v-model="projectTitle"
                    dense
                    class="font-weight-bold fs-24px text-theme-base edit-title-input"
                    hide-details
                  />
                </div>
                <div class="fs-14px text-theme-secondary font-weight-regular edit-subtitle-input">
                  <v-text-field
                    v-model="projectSubTitle"
                    dense
                    height="16px"
                    class="fs-14px text-theme-secondary font-weight-regular"
                    hide-details
                  />
                </div>
              </div>
            </template>
          </template>
          <template v-else>
            <div class="d-flex flex-column ml-3">
              <v-skeleton-loader
                width="200"
                height="36"
                class="mb-2"
                type="text"
              />
              <v-skeleton-loader
                width="200"
                height="24"
                type="text"
              />
            </div>
          </template>
        </div>
        <div
          v-if="!skeletonLoaderState"
          class="d-flex align-center"
        >
          <div class="fs-14px text-theme-secondary font-weight-regular mr-2">
            {{ $t('projectKey') + ':' }} 
          </div>
          <span class="fs-16px fw-semibold">
            {{ selectedProject.key }}
          </span>
        </div>
        <v-skeleton-loader
          v-else
          width="200"
          height="24"
          type="text"
        />
      </div>
      <v-card
        class="mt-3"
        rounded="lg"
        elevation="0"
        width="100%"
      >
        <div
          class="d-flex align-center justify-space-between"
        >
          <div
            class="d-flex align-center"
          >
            <SearchComponent
              class="mr-3"
              @update:search="setSearchFilter"
            />
            <ProjectUserFilter 
              :available-roles="roles"
              :available-tags="tags"
              :available-projects="projectsData"
              @filters="handleUserFilter"
            />
          </div>
          <div
            class="d-flex align-center justify-end"
          >
            <SettingsMenu 
              table-type="projectDetails" 
            />
          </div>
        </div>
        <ProjectViewUserTable
          :filtered-headers="filteredHeaders"
          :items="activeMembersHasData ? filteredItem : []"
          :item-key="itemKey"
          :tags="tags"
          :roles="roles"
          :projects="projectsData"
        />
      </v-card>
      <div
        v-if="!skeletonLoaderState"
        class="d-flex justify-end btn-table-action"
      >
        <v-btn
          color="gray-100"
          width="150px"
          class="mr-4 text-capitalize"
          elevation="0"
          @click="onProjectCancel"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          color="blue"
          dark
          width="150px"
          elevation="0"
          class="text-capitalize"
          @click="onProjectSave"
        >
          {{ $t('save') }}
        </v-btn>
      </div>
    </v-card>
    <v-dialog
      v-model="drawer"
      fullscreen
    >
      <v-card class="pa-4">
        <v-container>
          <v-row>
            <v-col
              cols="12"
              class="d-flex justify-space-between align-center"
            >
              <h5 class="font-weight-bold">
                {{ !title ? $t('tagCreationHeader') : $t('tagEditHeader') }}
              </h5>
              <v-btn
                icon
                @click="drawer = false"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="title"
                :label="$t('titleLabel')"
                filled
                :placeholder="$t('enterTitle')"
              />
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="description"
                :label="$t('descriptionLabel')"
                filled
                :placeholder="$t('enterDescription')"
              />
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="type"
                :label="$t('typeLabel')"
                filled
                :placeholder="$t('enterType')"
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col
              cols="12"
              md="6"
            >
              <v-btn
                color="gray-100"
                full-width
                class="w-full text-capitalize"
                elevation="0"
                @click="drawer = false"
              >
                {{ $t('cancel') }}
              </v-btn>
            </v-col>
            <v-col
              cols="12"
              md="6"
            >
              <v-btn
                color="primary"
                class="w-full text-capitalize"
                depressed
                @click="onTagSave"
              >
                {{ $t('save') }}
              </v-btn>
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { onMounted, onBeforeMount } from 'vue';
import ProjectViewUserTable from '@/components/Project/ProjectViewUserTable.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import ProjectUserFilter from '@/components/Project/ProjectUserFilter.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import PencilIcon from '@/assets/svg/pencil.svg';
import UploadAvatar from '@/components/Profile/UploadAvatar.vue';
import Avatar from "@/components/base/Avatar.vue";
import { useProjectEdit } from '@/composables/modules/project/edit';

export default {
  name: 'ProjectDetailView',
  components: {
    ProjectViewUserTable,
    SearchComponent,
    ProjectUserFilter,
    SettingsMenu,
    PencilIcon,
    UploadAvatar,
    Avatar
  },
  async beforeRouteUpdate(to, from, next) {
    const { handleRouteUpdate } = useProjectEdit();
    await handleRouteUpdate(to, from);
    next();
  },
  setup() {
    // Use the composable - only destructure what's actually used in template
    const {
      // Reactive state used in template
      selectedProject,
      projectTitle,
      projectSubTitle,
      editTitle,
      visibleTitle,
      drawer,
      title,
      description,
      type,
      imageSrc,
      tags,
      roles,
      projectsData,
      itemKey,
      skeletonLoaderState,

      // Computed properties used in template
      activeMembersHasData,
      filteredItem,
      filteredHeaders,

      // Constants used in template
      defaultImage,

      // Methods used in template
      handleFileChange,
      handleUserFilter,
      setSearchFilter,
      handleClickEditTag,
      back,
      onProjectCancel,
      onProjectSave,
      onTagSave,

      // Lifecycle methods
      initializeHeaders,
      init,
    } = useProjectEdit();

    // Lifecycle hooks
    onBeforeMount(() => {
      initializeHeaders();
    });

    onMounted(async () => {
      await init();
    });

    // Return all reactive properties and methods to make them available in template
    return {
      // Reactive state
      selectedProject,
      projectTitle,
      projectSubTitle,
      editTitle,
      visibleTitle,
      drawer,
      title,
      description,
      type,
      imageSrc,
      tags,
      roles,
      projectsData,
      itemKey,
      skeletonLoaderState,

      // Computed properties
      activeMembersHasData,
      filteredItem,
      filteredHeaders,

      // Constants
      defaultImage,

      // Methods
      handleFileChange,
      handleUserFilter,
      setSearchFilter,
      handleClickEditTag,
      back,
      onProjectCancel,
      onProjectSave,
      onTagSave,
    };
  },
};
</script>

<style scoped>
.project-detail-height {
    min-height: calc(100vh - 24px - 6.25rem);
    height: 100%;
}
.custom-checkbox .v-input--selection-controls .v-input--checkbox .v-input__control .v-icon {
  border: none !important;
}

.custom-checkbox input[type='checkbox'] {
  border-radius: 20px;
  width: 16px;
  height: 16px;
}

.no-padding {
  padding: 0 !important;
  margin: 0 !important;
}

.v-expansion-panel-content__wrap {
  padding: 0 !important;
}

.v-expansion-panel-head__wrap {
  padding: 0 !important;
}

.custom-div {
  height: 15px;
  width: 15px;
  background-color: #eaecf0;
  border-radius: 4px;
}
.btn-table-action {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 1rem 1.5rem;
}
</style>
