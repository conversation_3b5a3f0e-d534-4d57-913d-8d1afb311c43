<template>
  <v-container
    id="create-org-container"
    fluid
    class="d-flex align-center justify-center"
  >
    <v-row class="d-flex align-center justify-center">
      <v-col
        cols="10"
      >
        <v-card
          class="pt-2 px-6 mt-3"
          rounded="lg"
          elevation="0"
          width="100%"
        >
          <v-form
            id="create-org-form"
            ref="form"
            v-model="validForm"
            lazy-validation
          >
            <v-row>
              <v-col
                cols="12"
                class="text-center max-width mb-5"
              >
                <h1 class="create-org-title">
                  {{ $t('organization.createOrganizationTitle') }}
                </h1>
                <p class="create-org-description">
                  {{ $t('organization.createOrganizationDescription') }}
                </p>

                <section class="d-flex ga-8 justify-center">
                  <div class="card bg-primary">
                    <span class="card-sub-title">
                      {{ $t('organization.freeTrial') }}
                    </span>
                    <p class="card-title">
                      <span class="font-weight-bold fs-32px">14</span>
                      {{ $t('organization.days') }}
                    </p>
                  </div>
                  <div class="card bg-grey">
                    <span class="card-sub-title">
                      {{ $t('organization.afterTrial') }}
                    </span>
                    <p class="card-title">
                      <v-skeleton-loader
                        v-if="loadingSubscriptionPlans"
                        type="text"
                        width="80"
                      />
                      <template v-else>
                        <span class="font-weight-bold fs-32px">{{ pricePerMonth }}</span>
                        {{ $t('organization.perUserPerMonth') }}
                      </template>
                    </p>
                  </div>
                </section>
              </v-col>
              <v-col
                cols="12"
                class="pb-0 max-width"
              >
                <p class="font-weight-medium body-2 text-left mb-1">
                  {{ $t('orgName') }} <strong class="red--text text--lighten-1">*</strong>
                </p>
                <v-text-field
                  v-model="org.name"
                  type="text"
                  dense
                  filled
                  :placeholder="$t('enterOrgname')"
                  :rules="orgNameValidation"
                />
              </v-col>

              <v-col
                cols="12"
                class="pb-0 max-width"
              >
                <p class="font-weight-medium body-2 text-left mb-1">
                  {{ $t('orgAccountName') }}  <strong class="red--text text--lighten-1">*</strong>
                </p>

                <v-text-field
                  v-model="org.handle"
                  type="text"
                  dense
                  filled
                  :placeholder="$t('enterOrgHandle')"
                  :loading="handleRequestState.isLoading"
                  :rules="accountNameValidation"
                  :hint="orgHandleHint"
                  persistent-hint
                />
              </v-col>

              <v-col
                cols="12"
                class="d-flex justify-center mt-4 max-width"
              >
                <div class="d-flex ga-3">
                  <v-btn

                    depressed
                    background-color="#F2F4F7"
                    class="font-inter text-capitalize black--text mr-4 "
                    height="40"
                    width="211px"
                    @click="skipToWorkspace()"
                  >
                    {{ $t('organization.skipForNow') }}
                  </v-btn>

                  <v-btn
                    color="blue"
                    width="211px"
                    elevation="0"
                    class="white--text text-capitalize rounded"
                    :disabled="!validForm || isLoading"
                    :loading="isLoading"
                    @click="createOrganization()"
                  >
                    {{ $t('organization.startFreeTrial') }}
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-form>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { useCreateOrg } from '@/composables/modules/auth/createOrg';

export default {
  setup() {
    const {
      // Reactive state
      validForm,
      isLoading,
      loadingSubscriptionPlans,
      subscriptionPlans,
      file,
      org,
      handleError,
      handleAvailable,
      handleRequestState,
      orgImageTypes,

      // Form refs
      formRef,

      // Computed properties
      orgNameValidation,
      accountNameValidation,
      orgHandleHint,
      pricePerMonth,

      // Methods
      validate,
      reset,
      resetValidation,
      createOrganization,
      skipToWorkspace,
      getSubscriptionPlans,
      checkUsername,
      usernameInUse,
    } = useCreateOrg();

    return {
      // Reactive state
      validForm,
      isLoading,
      loadingSubscriptionPlans,
      subscriptionPlans,
      file,
      org,
      handleError,
      handleAvailable,
      handleRequestState,
      orgImageTypes,

      // Form refs - these need to be available for template refs
      form: formRef,

      // Computed properties
      orgNameValidation,
      accountNameValidation,
      orgHandleHint,
      pricePerMonth,

      // Methods
      validate,
      reset,
      resetValidation,
      createOrganization,
      skipToWorkspace,
      getSubscriptionPlans,
      checkUsername,
      usernameInUse,
    };
  },
};
</script>

<style lang="scss" scoped>
.fs-32px{
  font-size: 32px;
}
.card{
  width: 215px;
  height: 112px;
  border-radius: 8px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
}
.card-sub-title{
  font-weight: 500;
  font-size: 14px;
  line-height: 28px;
  letter-spacing: 0%;
}

.card-title{
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  margin-top: 12px;
}
.create-org-title {
  font-family: Inter;
  font-weight: 600;
  font-size: 24px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  margin-top: 48px;
}

.create-org-description {
  font-family: Inter;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  margin-top: 16px;
}
.rounded{
  border-radius: 6px !important;
}
.bg-primary{
  background-color: #0C2FF3;
  color: white;
}
.bg-grey{
  background-color: #F9F9FB;
  color: #667085;
}
.max-width{
  max-width: 512px !important;
  margin: 0 auto;
}
.ga-8 {
  gap: 8px;
}
</style>
