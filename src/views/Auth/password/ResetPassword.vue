<template>
  <v-container
    id="container"
    fluid
  >
    <v-row>
      <v-col 
        cols="12" 
        class="pb-0 pl-6 pt-80"
      >
        <div
          class="back-button d-flex align-center cursor-pointer"
          @click="$router.go(-1)"
        >
          <v-icon 
            color="blue" 
            class="mr-2"
          >
            mdi-chevron-left
          </v-icon>
          <span class="blue--text font-weight-medium">
            {{ $t('back') }}
          </span>
        </div>
      </v-col>
    </v-row>
    <div
      class="h-85vh d-flex align-center justify-center"
    >
      <v-row class="d-flex align-center">
        <v-col
          offset="1"
          offset-md="2"
          cols="10"
          md="8"
        >
          <ValidationObserver
            ref="observer"
            v-slot="{ handleSubmit }"
          >
            <v-form
              role="resetPasswordForm"
              @submit.prevent="handleSubmit(resetPassword)"
            >
              <v-row
                class="text-h4 font-weight-bold mb-12"
                justify="center"
              >
                {{ $t("changePassword") }}
              </v-row>
              <v-row class="d-flex flex-column gap-3 mb-12">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("newPassword") }}
                </v-label>
                <v-text-field
                  id="new_password"
                  v-model="password"
                  filled
                  :placeholder="$t('password')"
                  :type="visiblePassword ? 'text' : 'password'"
                  :append-icon="visiblePassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                  background-color="#F9F9FB"
                  class="field-theme"
                  @click:append="visiblePassword = !visiblePassword"
                  hide-details
                />
              </v-row>
              <v-row class="d-flex flex-column gap-3">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("confirmNewPassword") }}
                </v-label>
                <v-text-field
                  id="confirm-password"
                  v-model="confirmation"
                  filled
                  :placeholder="$t('password')"
                  :type="visiblePassword ? 'text' : 'password'"
                  :append-icon="visiblePassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                  :rules="[passwordConfirmationRule]"
                  background-color="#F9F9FB"
                  class="field-theme"
                  @click:append="visiblePassword = !visiblePassword"
                  hide-details
                />
              </v-row>
              <v-btn
                id="reset-password"
                block
                color="primary"
                type="submit"
                :disabled="!isFormValid"
                height="48"
                class="btn-theme mt-12"
              >
                {{ $t("createNewPassword") }}
              </v-btn>
            </v-form>
          </ValidationObserver>
      </v-col>
      </v-row>
    </div>
  </v-container>
</template>
<script>
import { useResetPassword } from '@/composables/modules/auth/resetPassword';

export default {
  setup() {
    const {
      // Reactive state
      confirmation,
      password,
      token,
      visiblePassword,

      // Computed properties
      passwordConfirmationRule,
      isFormValid,

      // Methods
      resetPassword,
    } = useResetPassword();

    return {
      // Reactive state
      confirmation,
      password,
      token,
      visiblePassword,

      // Computed properties
      passwordConfirmationRule,
      isFormValid,

      // Methods
      resetPassword,
    };
  },
};
</script>

<style scoped>
.field-theme {
  border-radius: 8px;
}
.btn-theme {
  border-radius: 8px;
}
</style>
