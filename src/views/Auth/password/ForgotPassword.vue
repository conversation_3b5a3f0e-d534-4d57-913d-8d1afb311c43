<template>
  <v-container
    id="container"
    fluid
  >
    <v-row
      class="text-subtitle-1 mt-8 mt-md-16 pt-md-4 ml-6"
      justify="start"
      align="center"
    >
      <v-icon color="blue">
        mdi-chevron-left
      </v-icon>
      <router-link
        id="sign-up-link"
        :to="{ name: 'Login' }"
        class="text-decoration-none font-weight-bold fs-14 ml-4 mb-0"
      >
        {{ $t("backToSignIn") }}
      </router-link>
    </v-row>
    <v-row class="d-flex align-center mt-16">
      <v-col
        offset="1"
        offset-md="2"
        cols="10"
        md="8"
      >
        <ValidationObserver
          ref="observer"
          v-slot="{ handleSubmit }"
        >
          <v-form
            role="loginForm"
            @submit.prevent="handleSubmit(sendLink)"
          >
            <v-row
              class="text-h4 font-weight-bold mb-8"
              justify="center"
            >
              {{ $t("forgotPasswordHeader") }}
            </v-row>
            <v-row class="d-flex flex-column">
              <v-label class="text-left text-subtitle-1 font-weight-bold mb-2">
                {{ $t("emailLabel") }}
              </v-label>
              <v-text-field
                id="email"
                v-model="email"
                type="email"
                :rules="emailValidation"
                filled
                :placeholder="$t('email')"
              />
            </v-row>

            <v-row class="my-8">
              <v-btn
                block
                type="submit"
                :disabled="isLoading"
                color="primary"
              >
                {{ $t("sendEmail") }}
              </v-btn>
            </v-row>
            <v-row
              class="text-subtitle-1"
              justify="center"
            >
              {{ $t("doNotHaveAccount") }}
              <router-link
                id="sign-up-link"
                :to="{ name: 'signup' }"
                class="text-decoration-none font-weight-bold fs-14 ml-6"
              >
                {{ $t("signUpTo") }}
              </router-link>
            </v-row>
          </v-form>
        </ValidationObserver>
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
import { useForgotPassword } from '@/composables/modules/auth/forgotPassword';

export default {
  setup() {
    const {
      // Reactive state
      isLoading,
      email,

      // Computed properties
      emailValidation,

      // Methods
      sendLink,
    } = useForgotPassword();

    return {
      // Reactive state
      isLoading,
      email,

      // Computed properties
      emailValidation,

      // Methods
      sendLink,
    };
  },
};
</script>
