<template>
  <div class="w-full h-full d-flex justify-center align-center">
    <RegistrationForm v-if="step === 1" />
    <OrganizationSetup v-if="step === 2" />
  </div>
</template>

<script>
import { useRegister } from '@/composables/modules/auth/register';
import RegistrationForm from '@/components/Auth/RegistrationForm.vue';
import OrganizationSetup from '@/components/Auth/OrganizationSetup.vue';


export default {
  components: {
    RegistrationForm,
    OrganizationSetup
  },
  setup(){
    const {step} = useRegister();
    return {
      step
    }
  }
}
</script>

<style lang="scss" scoped>

</style>