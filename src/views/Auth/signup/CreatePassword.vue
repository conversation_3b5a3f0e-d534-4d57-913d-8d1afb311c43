<template>
  <v-container
    id="container"
    fluid
    class="d-flex align-center justify-center"
  >
    <v-row class="d-flex align-center">
      <v-col
        offset="1"
        offset-md="2"
        cols="10"
        md="8"
      >
        <v-row
          class="text-h4 font-weight-bold mb-8"
          justify="center"
        >
          {{ $t("signUpHeader") }}
        </v-row>
        <v-row class="d-flex flex-column">
          <v-label class="text-left text-subtitle-1 font-weight-bold mb-2">
            {{ $t("passwordLabel") }}
          </v-label>
          <v-text-field
            filled
            :placeholder="$t('password')"
            :type="visiblePassword ? 'text' : 'password'"
            :append-icon="visiblePassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
            @click:append="visiblePassword = !visiblePassword"
          />
        </v-row>
        <v-row
          class="my-8 text-subtitle-1 font-weight-medium"
          align="center"
        >
          <v-checkbox class="font-weight-medium" />
          {{ $t("rememberMe") }}
        </v-row>
        <v-row class="my-8">
          <v-btn
            block
            color="primary"
            :to="{ name: 'twoFactor' }"
            class="text-capitalize"
          >
            {{ $t("createMyAccount") }}
          </v-btn>
        </v-row>
        <v-row
          class="text-subtitle-1"
          justify="center"
        >
          {{ $t("alreadyHaveAccount") }}
          <router-link
            id="sign-up-link"
            :to="{ name: 'Login' }"
            class="text-decoration-none font-weight-bold fs-14 ml-6"
          >
            {{ $t("logintoyouraccount") }}
          </router-link>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
import { useCreatePassword } from '@/composables/modules/auth/createPassword';

export default {
  setup() {
    const {
      // Reactive state
      user,
      visiblePassword,

      // Computed properties
      emailValidation,
      nameValidation,
      passwordValidation,
      usernameValidation,

      // Methods
      saveUser,
    } = useCreatePassword();

    return {
      // Reactive state
      user,
      visiblePassword,

      // Computed properties
      emailValidation,
      nameValidation,
      passwordValidation,
      usernameValidation,

      // Methods
      saveUser,
    };
  },
};
</script>
