<template>
  <v-container
    id="login-container"
    fluid
    class="d-flex align-center justify-center"
  >
    <v-row
      v-if="showSSOLogin"
      class="d-flex align-center"
    >
      <v-col
        offset="1"
        offset-sm="2"
        offset-md="2"
        offset-lg="3"
        cols="10"
        sm="8"
        md="8"
        lg="6"
      >
        <ValidationObserver
          id="sso-validation-observer"
          ref="ssoObserver"
          v-slot="{ handleSubmit }"
        >
          <v-form
            id="sso-login-form"
            ref="ssoForm"
            role="ssoLoginForm"
            @submit.prevent="handleSubmit(ssoLogin)"
          > 
            <v-row
              class="fs-24px fw-semibold mb-8"
              justify="center"
            >
              {{ $t("loginWithSSO") }}
            </v-row>
            <v-row class="d-flex flex-column">
              <div class="d-flex align-center">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium mb-0 pr-1">
                  {{ $t("testfiestaOrganization") }}
                </v-label>
                <v-tooltip
                  top
                  max-width="300"
                >
                  <template #activator="{ on, attrs }">
                    <v-icon
                      color="#0C2FF3"
                      small
                      class="ml-1 pointer"
                      v-bind="attrs"
                      style="vertical-align: middle;"
                      v-on="on"
                    >
                      mdi-help-circle-outline
                    </v-icon>
                  </template>
                  <span>{{ $t('testfiestaOrganizationTooltip') }}</span>
                </v-tooltip>
              </div>
              <v-text-field
                id="org-handle-field"
                v-model="ssoLoginInfo.orgHandle"
                :placeholder="$t('enterOrgHandle')"
                :rules="accountNameValidation"
                background-color="#F9F9FB"
                class="field-theme"
                :disabled="ssoSignInBtnLoading"
              />
            </v-row>
            <v-row class="my-8 gap-4">
              <v-btn
                id="sso-login-button"
                type="submit"
                block
                color="primary"
                :depressed="true"
                class="btn-theme"
                width="188px"
                height="40"
                :loading="ssoSignInBtnLoading"
                :class="{ 'btn-loading-opacity': ssoSignInBtnLoading }"
              >
                {{ $t("loginWithSSO") }}
              </v-btn>
            </v-row>
            <v-row
              justify="center"
            >
              <a
                id="login-in-link"
                role="button"
                class="text-decoration-none fw-semibold fs-14 ml-3"
                @click="showSSOLogin = false"
              >
                {{ $t("backToLogin") }}
              </a>
            </v-row>
          </v-form>
        </ValidationObserver>
      </v-col>
    </v-row>

    <v-row
      v-else
      class="d-flex align-center"
    >
      <v-col
        offset="1"
        offset-sm="2"
        offset-md="2"
        offset-lg="3"
        cols="10"
        sm="8"
        md="8"
        lg="6"
      >
        <ValidationObserver
          id="validation-observer"
          ref="observer"
          v-slot="{ handleSubmit }"
        >
          <v-form
            id="login-form"
            ref="form"
            role="loginForm"
            @submit.prevent="handleSubmit(login)"
          >
            <v-row
              class="fs-24px fw-semibold mb-8"
              justify="center"
            >
              {{ $t("signInHeader") }}
            </v-row>
            <v-row class="d-flex flex-column">
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t("emailOrUsernameLabel") }} <strong class="red--text text--lighten-1">*</strong>
              </v-label>
              <v-text-field
                id="username-field"
                v-model="loginInfo.email"
                :rules="emailOrUsernameValidation"
                :placeholder="$t('inputPlaceholder', { field: $t('emailOrUsernameLabel') })"
                height="38"
                background-color="#F9F9FB"
                class="field-theme"
                :disabled="signinBtnLoading"
              />
            </v-row>
            <v-row class="d-flex flex-column">
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t("passwordLabel") }} <strong class="red--text text--lighten-1">*</strong>
              </v-label>
              <v-text-field
                id="password-field"
                v-model="loginInfo.password"
                :placeholder="$t('inputPlaceholder', { field: $t('password') })"
                height="38"
                :rules="passwordValidation"
                background-color="#F9F9FB"
                class="field-theme"
                :type="visiblePassword ? 'text' : 'password'"
                :append-icon="visiblePassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
                :disabled="signinBtnLoading"
                @click:append="visiblePassword = !visiblePassword"
              />
            </v-row>
            <div class="row d-flex justify-space-between align-center justify-center">
              <div class="d-flex justify-center align-center">
                <v-checkbox
                  id="remember-me-checkbox"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                >
                  <template #label>
                    <span class="fs-14 text-theme-label">{{ $t("rememberMe") }}</span>
                  </template>
                </v-checkbox>
              </div>
              <router-link
                id="forgot-password-link"
                to="/forgotPassword"
                class="text-decoration-none fw-semibold fs-14"
              >
                {{ $t("forgotPassword") }}
              </router-link>
            </div>
            <v-row class="my-8 gap-4">
              <v-btn
                id="login-button"
                type="submit"
                block
                color="primary"
                :depressed="true"
                class="btn-theme"
                width="188px"
                height="40"
                :loading="signinBtnLoading"
                :class="{ 'btn-loading-opacity': signinBtnLoading }"
              >
                {{ $t("signIn") }}
              </v-btn>

              <ContinueWithGoogleButton
                :loading="signinBtnLoading"
              />

              <v-btn
                id="login-button"
                block
                class="bg-theme-base fw-semibold"
                :depressed="true"
                width="188px"
                height="40"
                :loading="ssoSignInBtnLoading"
                :class="{ 'btn-loading-opacity': ssoSignInBtnLoading }"
                @click="showSSOLogin = true"
              >
                {{ $t("continueWithSSO") }}
              </v-btn>
            </v-row>
            <v-row
              class="text-theme-secondary"
              justify="center"
            >
              {{ $t("doNotHaveAccount") }}
              <router-link
                id="sign-up-link"
                :to="{ name: 'signup' }"
                class="text-decoration-none fw-semibold fs-14 ml-3"
              >
                {{ $t("signUpTo") }}
              </router-link>
            </v-row>
          </v-form>
        </ValidationObserver>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { onMounted, getCurrentInstance } from 'vue';
import { useLogin } from '@/composables/modules/auth/login';
import ContinueWithGoogleButton from '@/components/Auth/ContinueWithGoogleButton';

export default {
  components: {
    ContinueWithGoogleButton
  },

  setup() {
    const instance = getCurrentInstance();
    const { proxy } = instance;

    const {
      // Reactive state
      showSSOLogin,
      status,
      loginForm,
      visiblePassword,
      loginInfo,
      ssoLoginInfo,
      signinBtnLoading,
      ssoSignInBtnLoading,
      handleError,

      // Form refs
      formRef,
      ssoFormRef,
      observerRef,
      ssoObserverRef,

      // Computed properties
      emailOrUsernameValidation,
      passwordValidation,
      accountNameValidation,
      currentAccount,

      // Methods
      validate,
      reset,
      resetValidation,
      ssoValidate,
      ssoReset,
      ssoResetValidation,
      login,
      ssoLogin,
      handleGoogleLogin,
    } = useLogin();

    // Handle Google login token from URL
    onMounted(async () => {
      const { loginToken } = proxy.$route.query;
      if (loginToken) {
        await handleGoogleLogin(loginToken);
      }
    });

    return {
      // Reactive state
      showSSOLogin,
      status,
      loginForm,
      visiblePassword,
      loginInfo,
      ssoLoginInfo,
      signinBtnLoading,
      ssoSignInBtnLoading,
      handleError,

      // Form refs - these need to be available for template refs
      form: formRef,
      ssoForm: ssoFormRef,
      observer: observerRef,
      ssoObserver: ssoObserverRef,

      // Computed properties
      emailOrUsernameValidation,
      passwordValidation,
      accountNameValidation,
      currentAccount,

      // Methods
      validate,
      reset,
      resetValidation,
      ssoValidate,
      ssoReset,
      ssoResetValidation,
      login,
      ssoLogin,
    };
  },
};
</script>
