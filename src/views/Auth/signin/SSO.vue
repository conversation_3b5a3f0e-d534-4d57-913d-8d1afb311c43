<template>
  <v-container
    id="continue-with-sso-container"
    fluid
    class="d-flex align-center justify-center bg-black min-h-screen"
  >
    <v-row class="d-flex align-center justify-center">
      <v-col
        offset="1"
        offset-sm="2"
        offset-md="2"
        offset-lg="3"
        cols="10"
        sm="8"
        md="8"
        lg="6"
      >
        <v-row
          class="text-h4 font-weight-bold mb-8"
          justify="center"
        >
          {{ $t("loginWithSSO") }}
        </v-row>

        <p class="text-subtitle-1 text-grey mb-8 text-center">
          {{ $t("portalFor") }} {{ orgHandle }}
        </p>
        
        <v-btn
          id="login-button"
          block
          color="primary"
          class="btn-theme"
          :depressed="true"
          width="188px"
          height="40"
          :disabled="loading"
          :loading="loading"
          :class="{ 'btn-loading-opacity': loading }"
          @click="login"
        >
          {{ $t("continueWithSSO") }}
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { useContinueWithSSO } from '@/composables/modules/auth/continueWithSSO';

export default {
  name: 'ContinueWithSSOPage',

  setup() {
    const {
      // Reactive state
      loading,

      // Computed properties
      currentAccount,
      orgHandle,

      // Methods
      handleSSOLogin,
      redirectToSSO,
      login,
    } = useContinueWithSSO();

    return {
      // Reactive state
      loading,

      // Computed properties
      currentAccount,
      orgHandle,

      // Methods
      login,
      handleSSOLogin,
      redirectToSSO,
    };
  },
};
</script>
