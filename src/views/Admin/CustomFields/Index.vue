<template>
  <v-container
    fluid
    class="pb-0 pr-0"
  >
    <CustomFieldHeader
      :write-custom-field="_writeCustomField"
      @create-custom-field="onCreateCustomField"
    />
    <v-card
      v-if="totalCustomFields == 0 && !skeletonLoaderState && !hasActiveFilters"
      class="mt-3 app-height-global d-flex align-center justify-center"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <ActiveEmptyState
        :write-entity="_writeCustomField"
        :image-src="require('@/assets/png/empty-step.png')"
        image-max-width="323px"
        :title="$t('customFieldPage.emptyTitle')"
        :button-text="$t('customFieldPage.createCustomField')"
        :is-project-archived="isProjectArchived"
        button-color="primary"
        @button-click="onCreateCustomField"
      />
    </v-card>
    <v-card
      v-else
      class="py-6 px-6 mt-3 app-height-global"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-row
        justify="space-between"
        class="align-center"
      >
        <v-col
          cols="12"
          md="auto"
          class="d-flex align-center"
        >
          <SearchComponent
            :search="filter.name"
            :placeholder="$t('placeHolder.searchByName')"
            class="mr-3"
            @update:search="filter.name = $event"
          />

          <CustomFieldFilterDialog
            :data="filter"
            :data-sources="mergedDataSources"
            @update-filter-condition="updateFilterCondition"
          />
        </v-col>

        <v-col
          cols="12"
          md="auto"
        >
          <v-row
            justify="end"
            class="align-center"
          >
            <SettingsMenu 
              table-type="customFields"
            />
          </v-row>
        </v-col>
      </v-row>

      <CustomFieldTable
        :headers="filteredHeaders"
        :items="paginatedCustomFields"
        :write-custom-field="_writeCustomField"
        :delete-custom-field="_deleteCustomField"
        item-key="uid"
        @edit="onEditCustomField"
        @delete="onDeleteCustomField"
      />

      <Pagination
        v-if="totalCustomFields > 0"
        :page="currentPage"
        :items-per-page="perPage"
        :total-pages="totalPages"
        :total-items="totalCustomFields"
        @update:pagination="onUpdatePagination"
      />
    </v-card>
    <CreateUpdateCustomFieldDialog
      v-model="showCreateUpdateCustomFieldDialog"
      :data="selectedCustomField"
      @create-custom-field="createCustomField"
      @update-custom-field="updateCustomField"
      @close-dialog="showCreateUpdateCustomFieldDialog = false"
    />

    <DeleteConfirmDialog
      v-model="showDeleteConfirmDialog"
      :custom-field="selectedCustomField"
      @delete="deleteCustomField"
    />
  </v-container>
</template>

<script>
import * as _ from 'lodash'
import { createNamespacedHelpers, mapActions  } from 'vuex';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import makeCustomFieldService from '@/services/api/customField';
import { handleNetworkStatusError } from '@/mixins/redirect';
import { mapGetters } from 'vuex';
import CustomFieldHeader from '@/components/Admin/CustomField/CustomFieldHeader';
import CustomFieldFilterDialog from '@/components/Admin/CustomField/FilterDialog.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import CustomFieldTable from '@/components/Admin/CustomField/CustomFieldTable.vue';
import CreateUpdateCustomFieldDialog from '@/components/Admin/CustomField/CreateUpdateCustomFieldDialog.vue';
import DeleteConfirmDialog from '@/components/Admin/CustomField/DeleteConfirmDialog.vue';
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue';
import projectStatus from '@/mixins/projectStatus';
import handleLoading from '@/mixins/loader.js'
import Pagination from '@/components/base/Pagination.vue';
import { entityTypes } from '@/constants/templates';

const { mapState } = createNamespacedHelpers('user');

export default {
  name: 'CustomFields',
  components: {
    CustomFieldHeader,
    CustomFieldFilterDialog,
    SearchComponent,
    SettingsMenu,
    CustomFieldTable,
    CreateUpdateCustomFieldDialog,
    DeleteConfirmDialog,
    ActiveEmptyState,
    Pagination
  },
  mixins: [handleNetworkStatusError, projectStatus, handleLoading],
  data() {
    return {
      filter: {
        name: '',
        types: [],
        sources: [],
        startDate: null,
        endDate: null,
      },
      headers: [],
      originalCustomFields: [],
      filteredCustomFields: [],
      showCreateUpdateCustomFieldDialog: false,
      showDeleteConfirmDialog: false,
      selectedCustomField: {
        uid: '',
        name: '',
        type: '',
        source: '',
        options: [],
        entityTypes: [],
        description: '',
      },
      customFieldService: null,
      isLoading: false,
      errorMessage: '',
      dataSources: {
        testCase: [],
        testResult: []
      },
      currentPage: 1,
      perPage: 10,
      entityTypes: entityTypes,
    }
  },

  computed: {
    _writeCustomField(){
      return this.authorityTo('write_custom_field')
    },
    _deleteCustomField(){
      return this.authorityTo('delete_custom_field')
    },
    ...mapState(['currentAccount']),
    ...mapGetters({
      dynamicHeaders:'headers/dynamicHeaders',
    }),
    filteredHeaders() {
      const filtered = this.headers.filter((header) => header.checked);
      return filtered;
    },
    mergedDataSources() {
      return [...new Set(this.originalCustomFields.map(field => field.source))];
    },
    totalCustomFields() {
      return this.filteredCustomFields.length
    },
    hasCustomFields() {
      return this.originalCustomFields.length > 0
    },
    totalPages() {
      return Math.ceil(this.totalCustomFields / this.perPage);
    },
    paginatedCustomFields() {
      const start = (this.currentPage - 1) * this.perPage;
      const end = start + this.perPage;
      return this.filteredCustomFields.slice(start, end);
    },
    hasActiveFilters() {
      return !!(
        this.filter.name || 
        this.filter.types.length > 0 || 
        this.filter.sources.length > 0 || 
        this.filter.startDate || 
        this.filter.endDate
      );
    },
  },

  watch: {
    'filter.name': {
      handler: _.debounce(function () {
        this.filterCustomFields()
      }, 500),
    },
  },

  created() {
    if(!this.dynamicHeaders.customFields) {
      this.initializeHeaders({ type: 'customFields' });
    }
    this.headers = this.dynamicHeaders.customFields;
  },

  mounted() {
    this.customFieldService = makeCustomFieldService(this.$api);
    this.init(this.$route.params.handle);
  },

  async beforeRouteUpdate(to, from, next) {
    const handle = to.params.handle;
    if (handle && handle !== from.params.handle) {
      try {
        await this.init(handle);
        next();
      } catch (error) {
        next();
      }
    } else {
      console.log("not run")
      next();
    }
  },

  methods: {
    ...mapActions("headers", ['initializeHeaders']),
    async init(handle) {
      try {
        this.showSkeletonLoader();
        await this.getCustomFields(handle);
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'custom fields' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
    async getCustomFields(handle) {
      this.isLoading = true
      try {
        const queryParams = new URLSearchParams();
        this.entityTypes.forEach(entityType => {
          queryParams.append('entityTypes', entityType);
        });
        const response = await this.customFieldService.getCustomFields(handle, this.$route.params.key, queryParams.toString());
        const result = response?.data || [];
        this.originalCustomFields = result;
      } catch (err) {
        this.redirectOnError(err.response.status)
        showErrorToast(this.$swal, 'fetchError', { item: 'Custom fields' }, err?.response?.data);
      } finally {
        this.isLoading = false
      }

      this.filterCustomFields();
    },

    onCreateCustomField() {
      this.selectedCustomField = {
        uid: '',
        name: '',
        type: '',
        source: '',
        options: [],
        entityTypes: [],
        description: '',
      }

      this.showCreateUpdateCustomFieldDialog = true
    },

    async createCustomField(customField) {
      this.showCreateUpdateCustomFieldDialog = false;
      if(!this._writeCustomField){
        this.unauthorizedToast;
        return ;
      }
      try {
        this.showSkeletonLoader();
        await this.customFieldService.createCustomField(this.currentAccount.handle, this.$route.params.key, {
          ...customField,
          source: 'Manual',
        });

        this.init(this.$route.params.handle);
        showSuccessToast(this.$swal, 'createSuccess', { item: 'Custom field' });
      } catch (err) {
        this.redirectOnError(err.response.status)
        showErrorToast(this.$swal, 'createError', { item: 'Custom field' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }

      this.filterCustomFields();
    },

    onEditCustomField(customField) {
      this.selectedCustomField = {
        uid: customField.uid || '',
        name: customField.name ||  '',
        type: customField.type || '',
        source: customField.source || '',
        options: customField.options || [],
        entityTypes: customField.entityTypes || [],
        description: customField.description || '',
      }

      this.showCreateUpdateCustomFieldDialog = true
    },

    async updateCustomField(customField) {
      this.showCreateUpdateCustomFieldDialog = false;

      try {
        this.showSkeletonLoader();
        await this.customFieldService.updateCustomField(this.currentAccount.handle, this.$route.params.key, customField);
        this.init(this.$route.params.handle);
        showSuccessToast(this.$swal, 'updateSuccess', { item: 'Custom field' });
      } catch (err) {
        this.redirectOnError(err.response.status)
        showErrorToast(this.$swal, 'updateError', { item: 'Custom field' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }

      this.filterCustomFields();
    },

    onDeleteCustomField(customField) {
      this.selectedCustomField = {
        uid: customField.uid || '',
        name: customField.name ||  '',
        type: customField.type || '',
        source: customField.source || '',
        options: customField.options || [],
      }

      this.showDeleteConfirmDialog = true
    },

    async deleteCustomField() {
      this.showDeleteConfirmDialog = false;
      if(!this._deleteCustomField){
        this.unauthorizedToast;
        return ;
      }
      try {
        this.showSkeletonLoader();
        await this.customFieldService.deleteCustomField(this.currentAccount.handle, this.$route.params.key, this.selectedCustomField.uid);

        this.init(this.$route.params.handle);
        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'Custom field' });
      } catch (err) {
        this.redirectOnError(err.response.status)
        showErrorToast(this.$swal, 'deleteError', { item: 'Custom field' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }

      this.filterCustomFields();
    },

    updateFilterCondition(data) {
      this.filter = {
        ...this.filter,
        types: data.types,
        sources: data.sources,
        startDate: data.startDate,
        endDate: data.endDate,
      }

      this.filterCustomFields()
    },

    filterCustomFields() {
      let filteredCustomFields = _.cloneDeep(this.originalCustomFields)

      if (this.filter.name) {
        const searchTerm = this.filter.name.toLowerCase();
        filteredCustomFields = filteredCustomFields.filter(item => item.name.toLowerCase().includes(searchTerm));
      }

      if (this.filter.types.length > 0) {
        filteredCustomFields = filteredCustomFields.filter(item => this.filter.types.includes(item.type))
      }

      if (this.filter.sources.length > 0) {
        filteredCustomFields = filteredCustomFields.filter(item => this.filter.sources.includes(item.source))
      }

      if (this.filter.startDate) {
        filteredCustomFields = filteredCustomFields.filter(item => item.createdAt >= this.filter.startDate)
      }

      if (this.filter.endDate) {
        filteredCustomFields = filteredCustomFields.filter(item => item.createdAt <= this.filter.endDate)
      }

      this.filteredCustomFields = filteredCustomFields
      // Reset to first page when filtering
      this.currentPage = 1
    },


    onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      if (newPage !== this.currentPage || newItemsPerPage !== this.perPage) {
        this.currentPage = newPage;
        this.perPage = newItemsPerPage;
      }
    }
  }
}
</script>
