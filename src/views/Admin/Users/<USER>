<template>
  <div
    class="pl-3"
  >
    <UserHeader
      :filter="filter"
      :roles="roles"
      :active-user-count="activeUserCount"
      :pending-user-count="pendingInvitesCount"
      :requested-user-count="requestedInvitesCount"
      :write-member="_writeMember"
      :tags="tags"
      :project-scope="projectScope"
      :projects="projects"
      :users="orgScopeInvitedMembers"
      @update-filter="updateFilter"
      @showError="showError"
      @completed="inviteCompleted"
      @update-tags="fetchTags"
    />
    
    <v-card
      class="py-6 px-6 mt-3 app-height-global"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-row
        justify="space-between"
        class="align-center"
      >
        <v-col
          cols="12"
          md="auto"
          class="d-flex align-center"
        >
          <SearchComponent 
            class="mr-3"
            @update:search="debouncedSetSearchFilter" 
          />
          <ProjectUserFilter
            :available-projects="projects"
            :available-roles="roles"
            :available-tags="tags"
            @filters="handleFilters"
          />
        </v-col>
        <v-col
          cols="12"
          md="auto"
        >
          <v-row
            justify="end"
            class="align-center"
          >
            <SettingsMenu 
              table-type="users" 
            />
          </v-row>
        </v-col>
      </v-row>
      <UserTable
        :filtered-headers="filteredHeaders"
        :items="paginatedItems"
        :item-key="itemKey"
        :roles="roles"
        :tags="tags"
        :write-member="_writeMember"
        :delete-member="_deleteMember"
        :filter="filter"
        :is-tag-loading="isTagLoading"
        @update-role="confirmRoleUpdate"
        @update-tag="updateTag"
        @delete-item="onDelete"
        @edit-item="onEdit"
        @resend-invite="onResendInvite"
        @refreshTags="fetchTags"
        @update-invite="updateUser"
      />
      <Pagination
        v-if="totalFilteredItems > 0"
        :page="currentPage"
        :total-pages="totalPages"
        :items-per-page="perPage"
        :total-items="totalFilteredItems"
        @update:pagination="onUpdatePagination"
      />
    </v-card>
    <DiscardDialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :filter="filter"
      @close="onDialogClose"
      @handleConfirmClick="onConfirm"
    >
      <template #content>
        <v-flex class="mt-4">
          <p class="text-start">
            {{ dialogDescription }}
          </p>
          <template v-if="filter === 'requested' && currentItem?.status == 'requested' && _writeMember">
            <v-label
              class="text-theme-label font-weight-medium fs-14px"
            >
              {{ $t('comment') }}
            </v-label>
            <v-textarea
              v-model="comment"
              class="rounded-lg field-theme"
              background-color="#F9F9FB"
              :placeholder="$t('leaveComment')"
              rows="3"
              hide-details="auto"
              auto-grow
            />
          </template>
        </v-flex>
      </template>
      <template #footer>
        <v-row>
          <v-col cols="6">
            <v-btn
              depressed
              width="100%"
              class="text-capitalize fw-semibold rounded-lg black--text mt-2"
              color="#F2F4F7"
              elevation="0"
              height="40"
              @click="closeDiscardDialog"
            >
              {{ $t('discard_cancel') }}
            </v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn
              depressed
              width="100%"
              class="text-capitalize fw-semibold rounded-lg white--text mt-2"
              color="danger"
              height="40"
              @click="onConfirm"
            >
              {{ $t('discard_confirm') }}
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </DiscardDialog>
    <UpdateDialog
      v-if="isOpenUpdateDialog"
      v-model="isOpenUpdateDialog"
      :selected-user="selectedUser"
      :filter="filter"
      :roles="roles"
      :projects="projects"
      :tags="tags"
      :write-member="_writeMember"
      @handleDelete="onDelete"
      @closeDialog="handleCloseUpdateDialog"
      @clickSave="confirmUserUpdate"
      @acceptRequest="acceptRequest"
      @cancelRequest="cancelRequest"
      @declineRequest="declineRequest"
    />
    <ReassignOwnerConfirmDialog 
      v-if="confirmOwnershipChange"
      v-model="confirmOwnershipChange"
      :is-new-owner-assignment="isNewOwnerAssignment"
      @updateRole="onConfirmReassign"
      @close="handleReassignOwnerDialogClose"
    />
  </div>
</template>

<script>
import { mapGetters, mapActions as projectMapActions } from 'vuex';
import makeRoleService from '@/services/api/role';
import makeOrgService from '@/services/api/org';
import makeProjectService from '@/services/api/project';
import makeTagService from '@/services/api/tag';
import makeInviteService from '@/services/api/invite'
import makeUserService from '@/services/api/user';
import UserHeader from '@/components/User/UserHeader';
import ProjectUserFilter from '@/components/Project/ProjectUserFilter.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import UserTable from '@/components/User/UserTable.vue';  
import UpdateDialog from '@/components/User/UpdateDialog.vue';  
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import DiscardDialog from '@/components/base/DiscardDialog.vue';
import { users } from '@/constants/data.js';
import { handleNetworkStatusError } from '@/mixins/redirect';
import handleLoading from '@/mixins/loader.js'
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import ReassignOwnerConfirmDialog from '@/components/Admin/Role/ReassignOwnerConfirmDialog.vue';
import Pagination from '@/components/base/Pagination.vue';
import * as _ from 'lodash'

export default {
  name: 'Users',
  components: {
    UserHeader,
    ProjectUserFilter,
    SearchComponent,
    UserTable,
    DiscardDialog,
    SettingsMenu,
    UpdateDialog,
    Pagination,
    ReassignOwnerConfirmDialog
  },
  mixins: [handleNetworkStatusError, handleLoading],
  async beforeRouteUpdate(to, from, next) {
    const handle = to.params.handle;
    if (handle && handle !== from.params.handle) {
      try {
        this.activeMembers = [];
        this.roles = [];
        await this.init(handle);
        next();
      } catch (error) {
        next();
      }
    } else {
      next();
    }
  },
  data() {
    return {
      isOpenUpdateDialog: false,
      selectedUser: null,
      dialogVisible: false,
      dialogTitle: '',
      dialogDescription: '',
      currentItem: null,
      membersState: {
        isLoading: false,
        hasError: false,
        errorMessage: '',
      },
      currentDate: new Date(),
      activeMembers: [],
      requestedUsers: [],
      activeUserCount: 0,
      users: users,
      pendingUserCount: 0,
      requestedUserCount: 0,
      items: [],
      roles: [],
      projects: [],
      tags: [],
      filter: 'active',
      itemKey: 'uid',
      searchFilter: '',
      headers: [],
      filteredItems: [], // New data property to store filtered items
      pendingInvites: [],
      requestedInvites: [],
      orgService: null,
      inviteService: null,
      currentPage: 1,
      perPage: 10,
      userService: null,
      isTagLoading: false,
      confirmOwnershipChange: false,
      selectedRole: null,
      selectedUserId: null,
      userData: null,
      requestedInvitesCount: 0,
      pendingInvitesCount: 0,
      debouncedSetSearchFilter: null,
      comment: null,
    };
  },
  computed: {
    ...mapGetters(['isMenuCollapsed']),
    ...mapGetters({
      currentAccount: 'user/currentAccount',
      dynamicHeaders: 'headers/dynamicHeaders',
      currentUser: 'user/user'
    }),
    _readRole(){
      return this.authorityTo('read_role')
    },
    _writeMember(){
      return this.authorityTo('write_member')
    },
    _deleteMember(){
      return this.authorityTo('delete_member')
    },
    ...mapGetters({
      holderAccount: 'user/user',
    }),
    pendingUsers() {
      return this.pendingUsers || [];
    },
    expiredUsers() {
      return this.expiredInvites || [];
    },
    filteredItem() {
      let items;
      if (this.filteredItems.length > 0) {
        items = this.filteredItems;
      } else {
        items = this.getFilteredMembers();
        if (this.searchFilter.length > 0) {
          items = this.applySearchFilter(items);
        }
      }
      return this.addProjects(items);
    },
    totalFilteredItems() {
      if(this.filter === 'pending')
        return this.pendingInvitesCount
      if(this.filter === 'active')
        return this.activeUserCount;
      if(this.filter === 'requested')
        return this.requestedInvitesCount
      return 0;
    },
    totalPages() {
      return Math.ceil(this.totalFilteredItems / this.perPage);
    },
    paginatedItems() {
      if(this.filter === 'active')
        return this.activeMembers || []
      if(this.filter === 'pending')
        return this.pendingInvites || []
      if(this.filter === 'requested')
        return this.requestedInvites || []
      return []
    },
    orgScopeInvitedMembers(){
      const orgRoles = this.roles.map(role => role.uid);
      return this.activeMembers.filter(member => {
        return member.role && orgRoles.includes(member.role.uid);
      });
    },
    filteredHeaders() {
      const filtered = this.filteredMenuHeaders.filter((header) => {
        if (this.filter !=='pending' && header.value ==='createdAt'){
          return false;
        }
        if((this.projectScope && header.value === 'project') || (this.filter !== 'active' && header.value === 'project')){
          return false;
        }
        if (this.filter === 'expired' && header.value === 'lastActivity') {
          return false;
        }
        if (!['pending', 'requested'].includes(this.filter) && header.value === 'status') {
          return false;
        }
        return header.checked;
      });
      return filtered;
    },
    filteredMenuHeaders() {
      const filtered = this.headers.filter((header) => header.text != 'Actions');
      return filtered;
    },
    getActiveUserCount() {
      return this.activeMembers.length;
    },
    projectScope(){
      return !!this.$route.params.key;
    }
  },
  mounted() {
    let handle = this.$route.params.handle;
    this.init(handle);
  },
  created() {
    this.orgService = makeOrgService(this.$api);
    this.inviteService = makeInviteService(this.$api);
    this.userService = makeUserService(this.$api);
    this.debouncedSetSearchFilter = _.debounce(this.setSearchFilter, 300)
    if(!this.dynamicHeaders.users) {
      this.initializeHeaders({ type: 'users' });
    }
    this.headers = this.dynamicHeaders.users;
  },
  methods: {
    ...projectMapActions("headers", ['initializeHeaders']),
    handleFilters(filters) {
      const handle = this.$route.params.handle
      // Reset to first page when applying filters so backend query uses correct offset
      this.currentPage = 1;
      const tagUids = filters.tags?.map(t => t.uid) || [];
      const roleUids = filters.roles?.map(r => r.uid) || [];
      if(this.filter === 'active'){
        return this.getMembers(handle,{
          offset: 0,
          limit: this.perPage,
          ...(this.searchFilter ? {q: this.searchFilter} : {}),
          ...(tagUids?.length ? { tagUids } : {}),
          ...(roleUids?.length ? { roleUids } : {})
        })
      }
      if(this.filter === 'requested' || this.filter === 'pending'){
        return this.getInvites(handle, {
          offset: 0,
          limit: this.perPage,
          status: this.filter,
          ...(this.searchFilter ? {q: this.searchFilter} : {}),
          ...(tagUids?.length ? { tagUids } : {}),
          ...(roleUids?.length ? { roleUids } : {})
        })
      }
    },

    applyFilters(filters) {
      this.filteredItems = this.getFilteredMembers().filter(user => {
        const projectMatch = filters.projects.length === 0 || 
          (user.projects && filters.projects.some(p => user.projects.includes(p.id)));
        const roleMatch = filters.roles.length === 0 || 
          (user.role && filters.roles.some(r => r.id === user.role));
        const tagMatch = filters.tags.length === 0 || 
          (user.tags && filters.tags.some(t => user.tags.includes(t.id)));
        return projectMatch && roleMatch && tagMatch;
      });
      // Reset to first page when applying filters
      this.currentPage = 1;
    },

    async updateFilter(newFilter) {
      this.showSkeletonLoader();
      const handle = this.$route.params.handle;
      this.filter = newFilter;
      this.filteredItems = []; // Reset filtered items when filter changes
      // Reset to first page when filter changes
      this.currentPage = 1;
      const offset = (this.currentPage - 1) * this.perPage;
      if(this.filter === 'active'){
        await this.getMembers(handle, {
          limit: this.perPage,
          offset
        })
      }
      else{
        await this.getInvites(handle, {
          status: newFilter,
          limit: this.perPage,
          offset
        })
      }
      this.hideSkeletonLoader();
    },
    setSearchFilter(searchText) {
      const handle = this.$route.params.handle;
      this.searchFilter = searchText;
      // Reset to first page when searching and use backend query param `q`
      this.currentPage = 1;
      if(this.filter === 'active'){
        return this.getMembers(handle, {
          offset: 0,
          limit: this.perPage,
          q: searchText
        })
      }
      return this.getInvites(handle, {
        offset: 0,
        limit: this.perPage,
        q: searchText,
        status: this.filter
      })
    },
    getFilteredMembers() {
      switch (this.filter) {
        case 'active':
          return this.activeMembers;
        case 'pending':
          return this.pendingUsers;
        case 'requested':
          return this.requestedUsers;
      }
    },
    applySearchFilter(members) {
      return members.filter((item) => this.matchesFilter(item));
    },
    addProjects(items){
      return items.map((item) => {
        return {...item, projects: this.projects.filter(proj => proj.members?.map(m => m.uid).includes(item.uid))}
      });
    },
    matchesFilter(item) {
      const lowerCaseFilter = this.searchFilter.toLowerCase();
      const name = `${item.firstName.trim()} ${item.lastName.trim()}`;

      const nameMatch = name.toLowerCase().includes(lowerCaseFilter);

      return nameMatch;
    },

    async showError(errorMessage) {
      showErrorToast(this.$swal, 'genericError', { message: errorMessage });
    },
    async inviteCompleted() {
      try {
        const handle = this.$route.params.handle;
        this.showSkeletonLoader();
        showSuccessToast(this.$swal, 'inviteSuccess');
        await this.init(handle)
      } catch (error) {
        showErrorToast(this.$swal, 'inviteError', { error: error.message }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
    onDelete(item) {
      this.isOpenUpdateDialog = false;
      this.currentItem = item;
      if (this.filter === 'active') {
        this.dialogTitle = this.$t('user_remove_title');
        this.dialogDescription = this.$t('user_remove_description', { projectName: 'TestFiesta' });
      } else if (this.filter === 'pending') {
        this.dialogTitle = this.$t('invite_discard_title');
        this.dialogDescription = this.$t('invite_discard_description');
      } else if (this.filter === 'requested') {
        this.dialogTitle = this.$t('request_delete_title');
      } else {
        this.dialogTitle = this.$t('request_decline_title');
      }
      this.dialogVisible = true;
    },
    onEdit(item) {
      this.selectedUser = item;
      this.isOpenUpdateDialog = true;
    },
    async onResendInvite(item) {
      try {
        this.showSkeletonLoader();
        const projectKey = this.$route.params.key;
        await this.inviteService.resendInvite({handle: this.$route.params.handle, emails: [item.email], projectKey});
        showSuccessToast(this.$swal, 'resendSuccess', { item: 'invite' });
      } catch (error) {
        showErrorToast(this.$swal, 'resendError', { item: 'invite' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
    onConfirmReassign(password){
      if (this.userData) {
        this.updateUser(this.userData, password);
      } else {
        this.updateRole(this.selectedRole, this.selectedUserId, password);
      }
    },
    closeDiscardDialog(){
      this.dialogVisible = false; 
      this.comment = null;
    },
    handleReassignOwnerDialogClose(){
      this.confirmOwnershipChange = false;  
      this.userData = null;
      this.selectedRole = null;
      this.selectedUserId = null;
      this.isNewOwnerAssignment = null;
    },
    async confirmUserUpdate(data){
      const isSelfReassignment = data?.currentRole?.toLowerCase() === 'owner' && this.selectedUser?.uid === this.currentUser?.uid;
      const isAssigningNewOwner = data?.assignedRole?.toLowerCase() === 'owner';

      if (isSelfReassignment || isAssigningNewOwner) {
        this.confirmOwnershipChange = true;
        this.userData = data;
        this.isNewOwnerAssignment = isAssigningNewOwner;
      } else {
        await this.updateUser(data);
      }
    },
    async confirmRoleUpdate(role, userId){
      const isOwnerRole = role.name?.toLowerCase() === 'owner';
      const isSelfReassignment = role.currentRoleName?.toLowerCase() === 'owner' && userId === this.currentUser.uid;

      if (isSelfReassignment) {
        this.isNewOwnerAssignment = false;
      } else if (isOwnerRole) {
        this.isNewOwnerAssignment = true;
      }
      
      if (isSelfReassignment || isOwnerRole) {
        this.confirmOwnershipChange = true;
        this.selectedRole = role;
        this.selectedUserId = userId;
      } else {
        await this.updateRole(role, userId);
      }
    },
    async updateRole(role, userId, password){
      const payload = {
        roleId: role.uid,
        members: [{
          userId
        }],
        ...( password ? {password}: {}) 
      }
      const roleMembersService = makeRoleService(this.$api);
      const projectKey = this.$route.params.key;
      await roleMembersService.reAssignRole({handle: this.currentAccount.handle, roleId: role.uid, payload, projectKey}).then(() => {
          const findIndex = this.activeMembers.findIndex(element => element.uid == userId)
          this.activeMembers.splice(findIndex,1, {...this.activeMembers[findIndex], role: {...role, projects: this.activeMembers[findIndex].role.projects} })
          showSuccessToast(this.$swal, this.$t('reassignToRole', {userCount: 1, newRole: role.name}))
      }).catch((err) => {
          showErrorToast(this.$swal, this.$t('unableToReassign'), {}, err?.response?.data)   
      })
      this.confirmOwnershipChange = false;
      this.selectedRole = null;
      this.selectedUserId = null;
    },
    async acceptRequest(data){
      const handle = this.currentAccount.handle;
      const projectKey = this.$route.params.key;
      const inviteUid = data.uid;
      const payload = {
        roleUid: data.selectedRole.uid ? data.selectedRole.uid : data.selectedRole,
        tagUids: data.tagsData.selectedTags.map(tag => tag?.uid),
        tagReplacements: {
          existingTagUids: data.tagsData.replaceTag.map(tag => tag?.uid),
          newTagUids: data.tagsData.withTag.map(tag => tag?.uid)
        },
        email: data?.email,
        overriddenRoles: data.assignedProjects?.map((p) => {
          return {
            projectUid: p.projectUid,
            roleUid: p.roleUid
          }
        }) ?? []
      };

      const inviteService = makeInviteService(this.$api);
      await inviteService.acceptRequest({handle, inviteUid, projectKey, payload}).then(async () => {
        this.handleCloseUpdateDialog()
        showSuccessToast(this.$swal, this.$t('inviteAccepted'))
        await Promise.all([
          this.getInvites(handle, {
            status: 'requested',
            returnCount: true
          }),
          this.updateFilter('pending'),
        ]) 
      }).catch(() => {
        showErrorToast(this.$swal, this.$t('failedToAcceptInvite'));
      })
    },
    async cancelRequest(data){
      const handle = this.currentAccount.handle;
      const projectKey = this.$route.params.key;
      const inviteUid = data.uid;
      const payload = {
        status: 'canceled'
      }
      const inviteService = makeInviteService(this.$api);
      await inviteService.cancelRequest({handle, inviteUid, projectKey, payload}).then(async () => {
        this.handleCloseUpdateDialog()
        showSuccessToast(this.$swal, this.$t('success.requestCanceled'))
        await this.init(this.$route.params.handle);
      }).catch(() => {
        showErrorToast(this.$swal, this.$t('failedToCancelRequest'));
      })
    },
    async declineRequest(data){
      const handle = this.currentAccount.handle;
      const projectKey = this.$route.params.key;
      const inviteUid = data.uid;
      const payload = {
        status: 'declined',
        comment: this.comment
      }
      if(!this.comment)
        return showErrorToast(this.$swal, this.$t('commentRequired'))
      const inviteService = makeInviteService(this.$api);
      await inviteService.declineRequest({handle, inviteUid, projectKey, payload}).then(async () => {
        this.handleCloseUpdateDialog()
        showSuccessToast(this.$swal, this.$t('success.requestCanceled'))
        await this.init(this.$route.params.handle);
      }).catch(() => {
        showErrorToast(this.$swal, this.$t('failedToCancelRequest'));
      })
      this.comment = null;
    },
    async updateUser(data, password) {
      this.isOpenUpdateDialog = false;
      const handle = this.currentAccount.handle;

      const payload = {
        ...(this.selectedUser?.uid ? {members: [{userId: this.selectedUser.uid}]} : {}),
        roleUid: data.selectedRole.uid ? data.selectedRole.uid : data.selectedRole,
        tagUids: data.tagsData.selectedTags.map(tag => tag?.uid),
        tagReplacements: {
          existingTagUids: data.tagsData.replaceTag.map(tag => tag?.uid),
          newTagUids: data.tagsData.withTag.map(tag => tag?.uid)
        }
        ,
        ...(this.selectedUser?.uid && this.filter === 'active' ? {userUids: [this.selectedUser?.uid]} : {}),
        ...(password ? { password } : {}),
        email: this.selectedUser?.email,
        overriddenRoles: data.assignedProjects?.map((p) => {
          return {
            projectUid: p.projectUid,
            roleUid: p.roleUid
          }
        }) ?? []
      };

      if (this.filter === 'pending') {
        const email = data.email ?? this.selectedUser?.email;
        payload.email = email
        const inviteService = makeInviteService(this.$api);
        await inviteService.updatePendingInvites({handle, updates: [payload], projectKey: this.$route.params.key}).then(() => {
          showSuccessToast(this.$swal, this.$t('inviteUpdated', { email }))
          this.getInvites(handle, {
            status: this.filter,
            limit: this.perPage,
            offset: (this.currentPage - 1) * this.perPage
          });
        }).catch((err) => {
          showErrorToast(this.$swal, this.$t('unableToUpdateInvite'), {}, err?.response?.data)   
        })
      } else if(this.filter === 'active'){
        try {
          const roleMembersService = makeRoleService(this.$api);
          await roleMembersService.reAssignRole({handle, roleId: payload.roleUid, payload}).then(() => {
              showSuccessToast(this.$swal, this.$t('userRolesUpdated'))
          }).catch((err) => {
              showErrorToast(this.$swal, this.$t('unableToReassign'), {}, err?.response?.data)   
          })
          this.init(this.$route.params.handle); // Refresh user list
        } catch (error) {
          showErrorToast(this.$swal, 'updateError', { item: 'user' }, error?.response?.data);
        } finally{
          this.confirmOwnershipChange = false;
          this.userData = null;
        }
      }
    },
    async updateTag(data) {
      try {
        this.isTagLoading = true;
        const projectKey = this.$route.params.key;
        await this.userService.updateUsers( this.currentAccount.handle, data, projectKey);
        showSuccessToast(this.$swal, this.$t('success.tagsUpdated'));
        await this.getMembers(this.$route.params.handle, {
          limit: this.perPage,
          offset: (this.currentPage - 1) * this.perPage
        })
      } catch (error) {
        showErrorToast(this.$swal, 'updateError', { item: 'user tags' }, error?.response?.data);
      } finally {
        this.isTagLoading = false;
      }
    },
    handleCloseUpdateDialog() {
      this.isOpenUpdateDialog = false;
      this.selectedUser = null;
    },
    onDialogClose() {
      this.dialogVisible = false;
    },
    async onConfirm() {
      this.showSkeletonLoader();
      this.dialogVisible = false;
      const handle = this.currentAccount.handle;
      const projectKey = this.$route.params.key;
      if (this.filter === 'active') {
        try {
          const userId = this.currentItem.uid;
          await this.deleteUserOrg(handle, userId, { userId });
          this.dialogVisible = false;
          await this.init(handle);
          showSuccessToast(this.$swal, 'deleteSuccess', { item: 'member' });
        } catch (error) {
          showErrorToast(this.$swal, 'deleteError', { item: 'member' }, error?.response?.data);
        } finally {
          this.hideSkeletonLoader();
        }
      } else if (this.filter === 'pending') {
        try {
          await this.deleteInvite({ handle, email: this.currentItem.email });
          this.dialogVisible = false;
          await this.init(handle);
          showSuccessToast(this.$swal, 'deleteSuccess', { item: 'invite' });
        } catch (error) {
          showErrorToast(this.$swal, 'deleteError', { item: 'invite' }, error?.response?.data);
        } finally {
          this.hideSkeletonLoader();
        }
      } else if(this.filter === 'requested'){
        try{
          const inviteUid = this.currentItem.uid;
          const status = this.currentItem.status;
          const requesterUid = this.currentItem.requesterUid;
          if(requesterUid === this.currentUser.uid && (status === 'declined' || !this._writeMember)){
            await this.inviteService.deleteRequest({handle, inviteUid, projectKey})
          }else if(this._writeMember){
            if(!this.comment)
              return showErrorToast(this.$swal, this.$t('commentRequired'))

            const payload = {
              comment: this.comment,
              status: 'declined'
            }
            await this.inviteService.declineRequest({handle, inviteUid, projectKey, payload});
          }
          await this.init(this.currentAccount.handle);
          showSuccessToast(this.$swal, 'deleteSuccess', { item: 'request' });
        }catch(error){
          showErrorToast(this.$swal, 'deleteError', { item: 'request' }, error?.response?.data);
        }finally{
          this.hideSkeletonLoader();
        }
      }
    },
    async getMembers(handle, params) {
      const scopeService = this.projectScope ? makeProjectService(this.$api) : makeOrgService(this.$api);
      this.membersState.isLoading = true;
      const projectKey = this.$route.params.key;
      try {
        const response = await scopeService.getUsers({
          handle,
          params: {
            ...params,
            ...(this.searchFilter ? { q: this.searchFilter } : {})
          },
          projectKey 
        });
        this.activeMembers = response.data.items;
        this.activeUserCount = response.data.count;
        this.membersState.isLoading = false;
      } catch (error) {
        this.membersState.isLoading = false;
        this.membersState.hasError = true;
        this.membersState.errorMessage = error.message;
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, 'fetchError', { item: 'members' }, error?.response?.data);
      }
    },
    async deleteUserOrg(handle, userId, payload) {
      const orgService = makeOrgService(this.$api);
      try {
        await orgService.removeUserOrg(handle, userId, payload);
      } catch (error) {
        this.redirectOnError(error.response.status);
        showErrorToast(this.$swal, 'deleteError', { item: 'member' }, error?.response?.data);
      }
    },
    async init(handle) {
      try {
        this.showSkeletonLoader();
        await Promise.all([
          ...(this.filter === 'active' ? [
            this.getMembers(handle, {
              limit: this.perPage,
              offset: (this.currentPage - 1 ) * this.perPage
            })
          ]: []),
          ...(this.filter === 'pending' ? [
            this.getInvites(handle, {
              status: 'pending',
              limit: this.perPage,
              offset: (this.currentPage - 1 ) * this.perPage
            })
          ] : []),
          ...(this.filter === 'requested' ? [
            this.getInvites(handle, {
              status: 'requested',
              limit: this.perPage,
              offset: (this.currentPage - 1 ) * this.perPage
            })
          ] : []),
          this.fetchProjects(),
          this.fetchRoles(),
          this.fetchTags(),
          ...(this._writeMember ? [this.getInvites(handle, {
            status: 'pending',
            returnCount: true
          })] : []),
          this.getInvites(handle, {
            status: 'requested',
            returnCount: true
          })
        ]);
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'test runs' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
    async fetchProjects() {
      try {
        const projectService = makeProjectService(this.$api);
        const response = await projectService.getProjects(this.$route.params.handle);
        this.projects = response.data.items;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'projects' }, error?.response?.data);
      }
    },
    async fetchRoles() {
      if(!this._readRole){
        this.unauthorizedToast;
        return ;
      }
      try {
        const roleService = makeRoleService(this.$api);
        const params = {
          includePermissions: true,
          ...(this.projectScope ? {includeOrgRoles: true} : {})
        }
        const projectKey = this.$route.params.key;
        const response = await roleService.getRoles(this.$route.params.handle, projectKey, params);
        this.roles = response.data.items;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'roles' }, error?.response?.data);
      }
    },
    async fetchTags() {
      try {
        const tagService = makeTagService(this.$api);
        const response = await tagService.getTags(this.$route.params.handle, 'users');
        this.tags = response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'tags' }, error?.response?.data);
      }
    },
    async getInvites(handle, params){
      const projectKey = this.$route.params.key;
      await this.inviteService.listInvites({
        handle,
        params,
        projectKey
      }).then(response => {
        if(params.returnCount){
          if(params.status === 'pending')
            this.pendingInvitesCount = Number(response.data?.count || 0);
          else if(params.status === 'requested')
            this.requestedInvitesCount = Number(response.data?.count || 0);
        }else{
          if(params.status === 'pending'){
            this.pendingInvites = response.data.items
            this.pendingInvitesCount = response.data.count
          }else if(params.status === 'requested'){
            this.requestedInvites = response.data.items
            this.requestedInvitesCount = response.data.count
          }
        }
      }).catch(error => {
        showErrorToast(this.$swal, 'fetchError', { item: `${this.filter} invites` }, error?.response?.data);
      })
    },

    async deleteInvite({ handle, email }) {
      try {
        this.showSkeletonLoader();
        const projectKey = this.$route.params.key;
        await this.inviteService.deleteInviteByEmail({handle, email, projectKey});
      } catch (error) {
        showErrorToast(this.$swal, 'deleteError', { item: 'invite' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },
    async onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      this.currentPage = newPage;
      if (newItemsPerPage !== this.perPage) {
        this.perPage = newItemsPerPage;
        this.currentPage = 1; 
        this.perPage = newItemsPerPage;
      }
      const offset = (newPage - 1) * newItemsPerPage;  
      this.showSkeletonLoader();
      try {
        if(this.filter === 'active'){
          await this.getMembers(this.currentAccount.handle, {
            limit: this.perPage,
            offset 
          });
        }else{
          await this.getInvites(this.currentAccount.handle, {
            limit: this.perPage,
            offset,
            status: this.filter
          })
        }
      } finally {
        this.hideSkeletonLoader();
      }
    },
  },
}
</script>