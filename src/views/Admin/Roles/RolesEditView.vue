<template>
  <div
    class="pl-3 pt-3"
  >
    <v-card
      class="py-6 px-6"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <button
        plain
        class="btn-nav-back font-inter mb-3"
        @click="backToRoles"
      >
        <v-icon color="blue">
          mdi-chevron-left
        </v-icon>
        <span class="d-flex-inline justify-center align-center ma-0 blue--text">
          {{ $t('backToRoles') }}
        </span>
      </button>

      <div class="mb-3">
        <div
          v-if="!editingName"
          class="d-flex align-center"
        >
          <div 
            class="role-name-display cursor-pointer d-flex align-center"
            @click="startEditName"
          >
            <h1 class="mb-0 mr-2">
              {{ roleName }}
            </h1>
          </div>
        </div>
        <div
          v-else
          class="edit-name-container"
        >
          <v-text-field
            ref="nameInput"
            v-model="tempRoleName"
            class="edit-name-field"
            :rules="nameRules"
            dense
            single-line
            filled
            background-color="#F9F9FB"
            @blur="saveNameEdit"
            @keyup.enter="saveNameEdit"
            @keyup.esc="cancelNameEdit"
          />
        </div>
      </div>

      <div class="mb-4">
        <div
          v-if="!editingDescription"
          class="d-flex align-center"
        >
          <div 
            class="description-display cursor-pointer d-flex align-center"
            @click="startEditDescription"
          >
            <p class="mb-0 mr-2 grey--text">
              {{ roleDescription || $t('rolePage.clickToAddDescription') }}
            </p>
          </div>
        </div>
        <div
          v-else
          class="edit-description-container"
        >
          <v-text-field
            ref="descriptionInput"
            v-model="tempRoleDescription"
            class="edit-description-field"
            dense
            single-line
            filled
            background-color="#F9F9FB"
            @blur="saveDescriptionEdit"
            @keyup.enter="saveDescriptionEdit"
            @keyup.esc="cancelDescriptionEdit"
          />
        </div>
      </div>

      <div class="d-flex align-center">
        <div class="d-flex align-center">
          <span class="label-text">{{ $t('roleLevel') }}:</span>
          <v-select
            v-model="currentRoleLevel"
            :items="roleLevelOptions"
            item-text="text"
            item-value="value"
            append-icon="mdi-chevron-down"
            dense
            filled
            disabled
            class="rounded-lg pt-0 field-theme custom-prepend mh-38px"
            background-color="#F9F9FB"
            hide-details
          />
        </div>
        
        <div class="d-flex align-center ml-2">
          <span class="label-text">{{ $t('tags') }}:</span>
          <TagSelector
            v-model="roleTags"
            :items="tags"
          />
        </div>
      </div>
    </v-card>

    <v-card
      class="py-6 px-6 mt-3 app-height-global"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-form
        ref="form"
        v-model="valid"
      >
        <div>
          <h4 class="medium mb-5 permission-text">
            {{ $t('rolePage.rolePermissions') }}
          </h4>
          <div
            v-for="(item, index) in items"
            :key="index"
          >
            <template v-if="item.type == 1">
              <CheckboxPanel 
                :key="permissionsKey"
                :items="item.actions" 
                :parent-label="item.name"
                :description="item.description"
                :initial="permissions" 
                @update-items="(updatedItems) => checkboxUpdate(updatedItems, index)"
                @update-required="setRequired"
              />
            </template>
            <template v-else-if="item.type == 2">
              <NestedCheckboxPanel
                :items="item.actions"
                :parent-label="item.name"
                :description="item.description"
                :initial="permissions" 
                @update-items="(updatedItems) => nestedCheckboxUpdate(updatedItems, index)"
              />
            </template>
            <template v-else>
              <v-expansion-panels
                mandatory
                flat
              >
                <v-expansion-panel>
                  <v-expansion-panel-header class="pa-0">
                    <div class="d-flex justify-start align-center">
                      <div class="cw-custom">
                        <p class="text-body-2 mb-0">
                          {{ item.name }}
                        </p>
                      </div>
                      <p class="text-body-2 mb-0 grey--text ml-custom">
                        {{ item.description }}
                      </p>
                    </div>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-radio-group
                      v-model="billing"
                      mandatory
                    >
                      <div
                        v-for="(_item) in item.actions"
                        :key="_item.id"
                        class="d-flex justify-start align-center"
                        style="height: 66px;"
                      >
                        <v-radio
                          :label="_item.name"
                          :value="_item.value"
                          class="mb-1 cw-custom"
                          @change="onRadioChange(_item.value, index)"
                        />
                        <p class="text-body-2 mb-0 grey--text ml-custom">
                          {{ _item.description }}
                        </p>
                      </div>
                    </v-radio-group>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
            </template>
          </div>
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="#F2F4F7"
            depressed
            class="mr-4 text-capitalize rounded-lg"
            :disabled="loading"
            @click="backToRoles"
          >
            {{ $t('cancel') }}
          </v-btn>
          <v-btn
            :disabled="!valid"
            color="primary"
            depressed
            class="mr-4 text-capitalize rounded-lg"
            :loading="loading"
            @click="validate"
          >
            {{ $t('save') }}
          </v-btn>
        </div>
      </v-form>
    </v-card>
  </div>
</template>

<script>
import { createNamespacedHelpers } from 'vuex';
import makeRoleService from '@/services/api/role';
import CheckboxPanel from '@/components/Project/CheckboxPanel.vue';
import NestedCheckboxPanel from '@/components/Project/NestedCheckboxPanel.vue';
import handleLoading from '@/mixins/loader.js'
import { showErrorToast } from '@/utils/toast'
import makeTagService from "@/services/api/tag";
import TagSelector from "@/components/base/TagSelector.vue";

const { mapState } = createNamespacedHelpers('user');

export default {
  name: 'EditRoleView',

  components: {
    CheckboxPanel,
    NestedCheckboxPanel,
    TagSelector,
  },
  mixins: [handleLoading],
  data() {
    return {
      valid: false,
      roleName: '',
      roleDescription: '',
      items: [],
      billing: null,
      selected: [],
      loading: false,
      permissions: [],
      nameRules: [
          v => !!v || 'Name is required'
      ],
      roleTags: [],
      tags: [],
      permissionsKey: 0,
      roleLevelOptions: [
        { text: this.$t('rolePage.organizationLevel'), value: 'organization' },
        { text: this.$t('rolePage.projectLevel'), value: 'project' }
      ],
      editingName: false,
      editingDescription: false,
      tempRoleName: '',
      tempRoleDescription: '',
    };
  },
  computed: {
    ...mapState(['currentAccount']),
    flattenedSelectedItems() {
      const data = this.selected.map(item => item.items);
      return data.flat(1);
    },
    billingPermissionText() {      
      if (this.permissions.includes('full_billing')) {
          return 'full_billing';
      } else if (this.permissions.includes('limited_billing')) {
          return 'limited_billing';
      } else if (this.permissions.includes('no_billing')) {
          return 'no_billing';
      } else {
          return undefined;
      }
    },
    isProjectRole() {
      return !!this.$route.params.key;
    },
    currentRoleLevel() {
      if (this.$route.query.level) {
        return this.$route.query.level === 'project' ? 'project' : 'organization';
      }
      return this.isProjectRole ? 'project' : 'organization';
    },
    displayRoleLevel() {
      return this.isProjectRole ? 'project' : 'organization';
    },
    roleTypeText() {
      return this.currentRoleLevel === 'project' ? 'Project level' : 'Organization level';
    }
  },

  async mounted() {
    this.fetchTags();
  },
  async created(){
    await this.init([this.getRoleDetail(this.currentAccount.handle),this.getPermissions()])
    
    if(this.roleName?.toLowerCase() == 'owner'){
      this.unauthorizedToast;
      this.$router.push({
        name: 'Roles'
      })
    }
  },
  methods: {
    backToRoles() {
      if (this.isProjectRole) {
        this.$router.push({ 
          name: 'ProjectRoles',
          params: this.$route.params
        });
      } else {
        this.$router.push({ 
          name: 'Roles',
          params: { handle: this.$route.params.handle },
          query: { level: this.currentRoleLevel === 'project' ? 'project' : 'organization' }
        });
      }
    },
    checkboxUpdate(updatedItems, index) {
      const existingItemIndex = this.selected.findIndex(item => item.id === index);
      if (existingItemIndex !== -1) {
        this.selected[existingItemIndex].items = updatedItems;
      } else {
        this.selected.push({
          id: index,
          items: updatedItems
        });
      }
    },
    setRequired(list = []){
      list.forEach(element => {
        let parentId = element < 1000 ? 
        parseInt(element.toString().substr(0,1))-1 : parseInt(element.toString().substr(0,2) - 1);
          this.items[parentId].actions = this.items[parentId].actions.map(item => {
          if(list.includes(item.id)){
            item = {
              ...item,
              selected: true
            }
          }
          return item;
        })
      })
    },
    nestedCheckboxUpdate(updatedItems, index) {
      const existingItemIndex = this.selected.findIndex(item => item.id === index);
      if (existingItemIndex !== -1) {
        this.selected[existingItemIndex].items = updatedItems;
      } else {
        this.selected.push({
            id: index,
            items: updatedItems
        });
      }
    },
    onRadioChange(value, index) {
      const existingItemIndex = this.selected.findIndex(item => item.id === index);
      if (existingItemIndex !== -1) {
        this.selected[existingItemIndex].items = value;
      } else {
        this.selected.push({
          id: index,
          items: value
        });
      }
    },
    async getPermissions(){
      const roleService = makeRoleService(this.$api);
      const handle = this.$route.params.handle;
      // Use currentRoleLevel to determine if we need project-specific permissions
      const projectKey = this.currentRoleLevel === 'project' ? this.$route.params.key : null;
      await roleService.getPermissions(handle, projectKey).then(response => {
          this.items = response.data;
          
          // Initialize billing permission after items are loaded
          this.initializeBillingPermission();
      }).catch((err) => {
        showErrorToast(this.$swal, 'fetchError', { item: 'Permissions' }, err?.response?.data);
      })
    },
    validate() {
      if (this.$refs.form.validate()) {
        const roleService = makeRoleService(this.$api);
        const id = this.$route.params.id;
        const projectKey = this.currentRoleLevel === 'project' ? this.$route.params.key : null;
        this.loading = true;
        roleService.updateRole(this.currentAccount.handle, {
          name: this.roleName,
          description: this.roleDescription,
          permissions: this.flattenedSelectedItems,
          tagUids: this.roleTags.map(tag => tag.uid)
        }, id, projectKey).then(() => {
          if (this.isProjectRole) {
            this.$router.push({ 
              name: 'ProjectRoles',
              params: this.$route.params
            });
          } else {
            this.$router.push({ 
              name: 'Roles',
              params: { handle: this.$route.params.handle },
              query: { level: this.currentRoleLevel === 'project' ? 'project' : 'organization' }
            });
          }
        }).finally(() => {
          this.loading = false;
        });
      }
    },
    async getRoleDetail(handle) {
      const id = this.$route.params.id;
      const roleService = makeRoleService(this.$api);
      try {
        const projectKey = this.$route.params.key;
        const response = await roleService.getRoleDetails(handle, id, projectKey);
        this.permissions = response.data?.permissions;
        this.roleName = response.data?.name;
        this.roleDescription = response.data?.description;
        this.roleTags = response.data?.tags?.map(tag => ({ uid: tag.uid, name: tag.name })) || [];
        
        // Initialize billing permission after permissions are loaded
        this.initializeBillingPermission();
      } catch (err) {
        this.errorMessage = err.message;
      }
      this.permissionsKey++;
    },
    
    initializeBillingPermission() {
      const hasBillingPermission = this.permissions.some(permission => 
        permission === 'full_billing' || 
        permission === 'limited_billing' || 
        permission === 'no_billing'
      );
      
      if (hasBillingPermission && this.permissions.length > 0 && this.items.length > 0) {      
        const billingIndex = this.items.findIndex(item => 
          item.type === 3 &&
          item.actions && 
          item.actions.some(action => 
            action.value === 'full_billing' || 
            action.value === 'limited_billing' || 
            action.value === 'no_billing'
          )
        );
        
        
        if (billingIndex !== -1) {
          const existingIndex = this.selected.findIndex(item => item.id === billingIndex);
          if (existingIndex !== -1) {
            this.selected[existingIndex].items = this.billingPermissionText;
          } else {
            this.selected.push({
              id: billingIndex,
              items: this.billingPermissionText
            });
          }
          this.billing = this.billingPermissionText;  
        } 
      } 
    },

    async fetchTags() {
      const tagService = makeTagService(this.$api);
      try {
        const response = await tagService.getTags(this.currentAccount.handle, 'roles');
        if (response.status === 200) {
            this.tags = response.data.map((tag) => ({ uid: tag.uid, name: tag.name }));
        }
      } catch (error) {
        console.error("Error fetching tags:", error);
      }
    },
    startEditName() {
      this.editingName = true;
      this.tempRoleName = this.roleName;
      this.$nextTick(() => {
        if (this.$refs.nameInput) {
          this.$refs.nameInput.focus();
        }
      });
    },
    saveNameEdit() {
      if (this.tempRoleName && this.tempRoleName.trim()) {
        this.roleName = this.tempRoleName;
        this.editingName = false;
      } else {
        this.$nextTick(() => {
          if (this.$refs.nameInput) {
            this.$refs.nameInput.focus();
          }
        });
      }
    },
    cancelNameEdit() {
      this.editingName = false;
    },
    startEditDescription() {
      this.editingDescription = true;
      this.tempRoleDescription = this.roleDescription;
      this.$nextTick(() => {
        if (this.$refs.descriptionInput) {
          this.$refs.descriptionInput.focus();
        }
      });
    },
    saveDescriptionEdit() {
      this.roleDescription = this.tempRoleDescription;
      this.editingDescription = false;
    },
    cancelDescriptionEdit() {
      this.editingDescription = false;
    },
  },
}
</script>

<style scoped>
.custom_input {
  background-color: #f9f9fb;
  height: 38px;
}

.ml-custom {
  margin-left: 20rem;
}

.cw-custom {
  width: 100%;
  max-width: 160px;
}

.btn-back {
  margin-left: -12px;
}

.role-name-display {
  cursor: pointer;
}

.role-name-display h1 {
  font-size: 1.5rem;
  font-weight: 700;
  font-style: bold;
}

.description-display {
  cursor: pointer;
}

.description-display p {
  font-size: 1rem;
  font-weight: 400;
}

.edit-icon {
  width: 16px;
  height: 16px;
}



.edit-name-container,
.edit-description-container {
  width: 100%;
  padding: 0.5rem;
  background-color: #f9f9fb;
  border-radius: 8px;
}

.edit-name-field,
.edit-description-field {
  width: 100%;
}

.label-text {
  font-weight: 500;
  margin-right: 8px;
  color: #374151;
}

.field-theme {
  margin-top: 0;
  margin-bottom: 0;
}
.permission-text {
  font-weight: 600;
  font-variant: 1rem;
  line-height: 1.5rem;
}
</style>
