<template>
  <div class="pl-3 pt-3">
    <!-- Step 1: Role Info -->
    <template v-if="currentStep === 1">
      <v-card
        class="role-info-step pt-6 pb-2 px-6 font-inter app-height-global"
        rounded="lg"
        elevation="0"
        width="100%"
      >
        <v-btn
          class="btn-back font-inter text-none btn-plain-theme"
          depressed
          small
          plain
          :ripple="false"
          @click="handleBackClick"
        >
          <v-icon color="blue">
            mdi-chevron-left
          </v-icon>
          <span> {{ $t('backToRoles') }} </span>
        </v-btn>
        
        <v-row
          justify="center"
          class="mt-8"
        >
          <v-col
            md="4"
            lg="3"
          >
            <h1 class="text-start mb-6">
              {{ $t('roleInfo') }}
            </h1>
            
            <v-form
              ref="form"
              v-model="valid"
            >
              <div class="mb-5">
                <p class="d-flex ma-0 mb-2 font-weight-medium body-2">
                  {{ $t('roleLevel') }}<strong class="red--text text--lighten-1">*</strong>
                </p>
                <v-select
                  v-model="roleLevel"
                  :items="roleLevelOptions"
                  item-text="text"
                  item-value="value"
                  item-disabled="disabled"
                  dense
                  single-line
                  filled
                  :placeholder="$t('selectRoleLevel')"
                  class="rounded-lg"
                  background-color="#F9F9FB"
                  :menu-props="{ offsetY: true }"
                  append-icon="mdi-chevron-down"
                  :rules="roleLevelRules"
                  :disabled="roleLevelDisabled"
                  required
                >
                  <template #item="{ item, attrs, on }">
                    <v-tooltip 
                      v-if="item.disabled"
                      bottom
                      :open-on-hover="true"
                      :close-on-content-click="false"
                    >
                      <template #activator="{ on: tooltipOn, attrs: tooltipAttrs }">
                        <v-list-item
                          v-bind="{ ...attrs, ...tooltipAttrs }"
                          :disabled="item.disabled"
                          class="disabled-option"
                          style="pointer-events: auto !important;"
                          v-on="{ ...on, ...tooltipOn }"
                        >
                          <v-list-item-content>
                            <v-list-item-title>{{ item.text }}</v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                      </template>
                      <span>{{ $t('roleWritePermissionDenied') }}</span>
                    </v-tooltip>
                    <v-list-item
                      v-else
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-list-item-content>
                        <v-list-item-title>{{ item.text }}</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                </v-select>
              </div>

              <!-- Project Dropdown for Admin Project Tab -->
              <div 
                v-if="showProjectDropdown"
                class="mb-5"
              >
                <p class="d-flex ma-0 mb-2 font-weight-medium body-2">
                  {{ $t('rolePage.roleProject') }}<strong class="red--text text--lighten-1">*</strong>
                </p>
                <v-select
                  v-model="selectedProject"
                  :items="projects"
                  item-text="name"
                  item-value="key"
                  return-object
                  dense
                  single-line
                  filled
                  :placeholder="$t('selectProject')"
                  class="rounded-lg"
                  background-color="#F9F9FB"
                  :menu-props="{ offsetY: true }"
                  append-icon="mdi-chevron-down"
                  :rules="projectRules"
                  required
                >
                  <template #item="{ item }">
                    <div>
                      <div class="font-weight-medium">
                        {{ item.name }}
                      </div>
                    </div>
                  </template>
                </v-select>
              </div>

              <div class="mb-5">
                <p class="d-flex ma-0 mb-2 font-weight-medium body-2">
                  {{ $t('rolePage.roleName') }}<strong class="red--text text--lighten-1">*</strong>
                </p>
                <v-text-field
                  v-model="roleName"
                  type="text"
                  dense
                  single-line
                  filled
                  :placeholder="$t('rolePage.enterRoleName')"
                  class="rounded-lg"
                  background-color="#F9F9FB"
                  :rules="nameRules"
                  required
                />
              </div>

              <div class="mb-5">
                <p class="d-flex ma-0 mb-2 font-weight-medium body-2">
                  {{ $t('description') }}
                </p>
                <v-textarea
                  v-model="roleDescription"
                  type="text"
                  dense
                  single-line
                  filled
                  :placeholder="$t('enterDescription')"
                  class="rounded-lg"
                  background-color="#F9F9FB"
                  rows="3"
                />
              </div>

              <div class="mb-6">
                <p class="d-flex ma-0 mb-2 font-weight-medium body-2">
                  {{ $t('tags') }}
                </p>
                <TagSelector
                  v-model="roleTags"
                  :items="tags"
                />
              </div>
            </v-form>
          </v-col>
        </v-row>
        
        <div class="actions-container">
          <v-btn
            dark
            depressed
            color="primary"
            class="mt-2 f-color-white btn-theme text-capitalize"
            width="180px"
            height="38px"
            :disabled="!valid"
            @click="goToStepTwo"
          >
            {{ $t('next') }}
          </v-btn>
        </div>
      </v-card>
    </template>

    <!-- Step 2: Permissions -->
    <template v-else-if="currentStep === 2">
      <v-card
        class="py-6 px-6 app-height-global"
        rounded="lg"
        elevation="0"
        width="100%"
      >  
        <button
          plain
          class="btn-nav-back font-inter mb-3"
          @click="backToStepOne"
        >
          <v-icon color="blue">
            mdi-chevron-left
          </v-icon>
          <span class="d-flex-inline justify-center align-center ma-0 blue--text">
            {{ $t('backToRoles') }}
          </span>
        </button>
        <v-row justify="center">
          <v-col
            cols="12"
            md="12"
            lg="8"
          >
            <h2 class="text-h5 mb-6 text-center font-weight-bold">
              {{ $t('settingsPage.permissions') }}
            </h2>
            
            <div
              v-for="(item, index) in filterItems"
              :key="index"
            >
              <template v-if="item.type == 1">
                <CheckboxPanel
                  :items="item.actions"
                  :parent-label="item.name"
                  :description="item.description"
                  @update-items="(updatedItems) => checkboxUpdate(updatedItems, index)"
                  @update-required="setRequired"
                />
              </template>
              <template v-else-if="item.type == 2">
                <NestedCheckboxPanel
                  :items="item.actions"
                  :parent-label="item.name"
                  :description="item.description"
                  @update-items="(updatedItems) => nestedCheckboxUpdate(updatedItems, index)"
                />
              </template>
              <template v-else>
                <v-expansion-panels
                  mandatory
                  flat
                >
                  <v-expansion-panel>
                    <v-expansion-panel-header class="pa-0">
                      <div class="d-flex justify-start align-center">
                        <div class="cw-custom">
                          <p class="text-body-2 mb-0">
                            {{ item.name }}
                          </p>
                        </div>
                        <p class="text-body-2 mb-0 grey--text ml-custom">
                          {{ item.description }}
                        </p>
                      </div>
                    </v-expansion-panel-header>
                    <v-expansion-panel-content>
                      <v-radio-group
                        v-model="billing"
                        mandatory
                      >
                        <div
                          v-for="(_item) in item.actions"
                          :key="_item.id"
                          class="d-flex justify-start align-center"
                          style="height: 66px;"
                        >
                          <v-radio
                            :label="_item.name"
                            :value="_item.value"
                            class="mb-1 cw-custom"
                            @change="onRadioChange(_item.value, index)"
                          />
                          <p class="text-body-2 mb-0 grey--text ml-custom-child">
                            {{ _item.description }}
                          </p>
                        </div>
                      </v-radio-group>
                    </v-expansion-panel-content>
                  </v-expansion-panel>
                </v-expansion-panels>
              </template>
            </div>
          </v-col>
        </v-row>
        
        <div class="d-flex justify-space-between align-center mt-6">
          <v-btn
            text
            class="text-capitalize"
            @click="backToStepOne"
          >
            <v-icon
              left
              color="primary"
            >
              mdi-chevron-left
            </v-icon>
            {{ $t('back') }}
          </v-btn>
          
          <v-btn
            color="primary"
            depressed
            class="text-capitalize rounded-lg"
            :loading="loading"
            @click="validate"
          >
            {{ $t('createRole') }}
          </v-btn>
        </div>
      </v-card>
    </template>
  </div>
</template>

<script>
import { createNamespacedHelpers } from 'vuex';
import makeRoleService from '@/services/api/role';
import makeProjectService from '@/services/api/project';
import CheckboxPanel from '@/components/Project/CheckboxPanel.vue';
import handleLoading from '@/mixins/loader.js'
import NestedCheckboxPanel from '@/components/Project/NestedCheckboxPanel.vue';
import { showErrorToast,showSuccessToast } from '@/utils/toast'
import TagSelector from "@/components/base/TagSelector.vue";
import makeTagService from "@/services/api/tag";

const { mapState } = createNamespacedHelpers('user');

export default {
    name: 'CreateRoleView',

    components: {
      CheckboxPanel,
      NestedCheckboxPanel,
      TagSelector,
    },
    mixins: [handleLoading],
    data() {
      return {
        currentStep: 1,
        roleLevel: '',
        roleLevelDisabled: false,
        valid: false,
        roleName: '',
        roleDescription: '',
        items: [],
        billing: null,
        selected: [],
        loading: false,
        nameRules: [
            v => !!v || this.$t('rolePage.roleNameRequired')
        ],
        roleLevelRules: [
            v => !!v || this.$t('rolePage.roleLevelRequired')
        ],
        roleTags: [],
        tags: [],
        selectedProject: null, 
        projects: [],
        projectRules: [
          v => !!v || this.$t('rolePage.roleProjectRequired')
        ],
        scopedAccess: {}
      };
    },
    computed: {
      ...mapState(['currentAccount']),
      flattenedSelectedItems() {
          const data = this.selected.map(item => item.items);
          return data.flat(1);
      },
      filterItems(){
        return this.items;
      },
      isProjectRole() {
        return this.roleLevel === 'project';
      },
      showProjectDropdown() {
        return !this.$route.params.key && this.roleLevel === 'project';
      },
      readRole(){
        return this.authorityTo('read_role')
      },
      roleLevelOptions() {
        return [
          { 
            text: this.$t('rolePage.organizationLevel'), 
            value: 'organization',
            disabled: !this.scopedAccess['write_role']?.org
          },
          { 
            text: this.$t('rolePage.projectLevel'), 
            value: 'project',
            disabled: !(this.scopedAccess['write_role']?.projects?.length)
          }
        ];
      }
    },
    
    watch: {
      roleLevel(newLevel) {
        if (newLevel !== 'project') {
          this.selectedProject = null;
        }
      },
      selectedProject(newProject) {
        if (this.currentStep === 2 && newProject) {
          this.getPermissions();
        }
      }
    },
    async created(){
      this.currentStep = 1;
      if(this.$route.params.key) {
        this.roleLevel = 'project';
        this.roleLevelDisabled = true;
      } else {
        this.roleLevelDisabled = false;
        
        if (!this.readRole) {
          this.roleLevel = 'project';
        } else if (this.$route.query.level) {
          this.roleLevel = this.$route.query.level;
        }    
      }
      const handle = this.$route.params.handle;
      await this.init(handle);
    },
    methods: {
      async init(handle) {
        try {
          this.showSkeletonLoader();
          await this.getPermissionScope(handle);
          await this.getPermissions();
          this.fetchTags();
          this.fetchProjects();
        } catch (error) {
          showErrorToast(this.$swal, 'fetchError', { item: 'roles' }, error?.response?.data);
        } finally {
          this.hideSkeletonLoader();
        }
      },
      handleBackClick() {
        if (this.$route.params.key) {
          this.$router.push({ 
            name: 'ProjectRoles'
          });
        } else {
          this.$router.push({ 
            name: 'Roles',
            query: { tab: this.roleLevel === 'project' ? 'project' : 'org' }
          });
        }
      },
      backToStepOne() {
        this.currentStep = 1;
        this.selected = [];
        this.billing = null;
      },
      backToRoles() {
        if (this.$route.params.key) {
          this.$router.push({ 
            name: 'ProjectRoles'
          });
        } else {
          // Admin route
          this.$router.push({ 
            name: 'Roles',
            query: { tab: this.roleLevel === 'project' ? 'project' : 'org' }
          });
        }
      },
      async goToStepTwo() {
        if (!this.$refs.form.validate()) {
          return;
        }
        
        if (this.showProjectDropdown && !this.selectedProject) {
          showErrorToast(this.$swal, 'Please select a project before proceeding.');
          return;
        }
        
        this.currentStep = 2;
      },
      checkboxUpdate(updatedItems, index) {
        const existingItemIndex = this.selected.findIndex(item => item.id === index);
          if (existingItemIndex !== -1) {
            this.selected[existingItemIndex].items = updatedItems;
          } else {
            this.selected.push({
              id: index,
              items: updatedItems
            });
          }
      },
      setRequired(list = []){
        list.forEach(element => {
          let parentId = element < 1000 ? 
          parseInt(element.toString().substr(0,1))-1 : parseInt(element.toString().substr(0,2) - 1);
            this.items[parentId].actions = this.items[parentId].actions.map(item => {
            if(list.includes(item.id)){
              item = {
                ...item,
                selected: true
              }
            }
            return item;
          })
        })
      },
      nestedCheckboxUpdate(updatedItems, index) {
        const existingItemIndex = this.selected.findIndex(item => item.id === index);
        if (existingItemIndex !== -1) {
          this.selected[existingItemIndex].items = updatedItems;
        } else {
          this.selected.push({
            id: index,
            items: updatedItems
          });
        }
      },
      onRadioChange(value, index) {
          const existingItemIndex = this.selected.findIndex(item => item.id === index);
          if (existingItemIndex !== -1) {
            this.selected[existingItemIndex].items = value;
          } else {
            this.selected.push({
              id: index,
              items: value
            });
          }
      },
      async getPermissionScope(handle){
        const roleService = makeRoleService(this.$api);
        await roleService.getPermissionScope(handle, 'write_role').then(response => {
          this.scopedAccess = {
            write_role: response.data
          }
        })
      },
      async getPermissions(){
        this.showSkeletonLoader();
        const roleService = makeRoleService(this.$api);
        const handle = this.$route.params.handle;
        
        let projectKey = null;
        if (this.$route.params.key) {
          projectKey = this.$route.params.key;
        } else if (this.selectedProject && this.roleLevel === 'project') {
          projectKey = this.selectedProject.key;
        }
        
        await roleService.getPermissions(handle, projectKey).then(response => {
          this.items = response.data;
        }).catch((err) => {
          showErrorToast(this.$swal, 'fetchError', { item: 'Permissions' }, err?.response?.data);
        })
        this.hideSkeletonLoader();
      },
      validate() {
        const roleMembersService = makeRoleService(this.$api);
        this.loading = true;
        
        let projectKey = null;
        if (this.$route.params.key) {
          projectKey = this.$route.params.key;
        } else if (this.selectedProject && this.roleLevel === 'project') {
          projectKey = this.selectedProject.key;
        }
        
        roleMembersService.createRole(this.currentAccount.handle, {
          name: this.roleName,
          description: this.roleDescription,
          permissions: this.flattenedSelectedItems,
          tagUids: this.roleTags.map(tag => tag.uid)
        }, projectKey).then(() => {
          showSuccessToast(this.$swal, this.$t('toast.createSuccess', { item: 'Role' }));
          
          if (this.$route.params.key) {
            this.$router.push({ 
              name: 'ProjectRoles'
            });
          } else {
            this.$router.push({ 
              name: 'Roles',
              query: { tab: this.roleLevel === 'project' ? 'project' : 'org' }
            });
          }
        }).catch((error) => {
          if (error?.response?.status === 403) {
            showErrorToast(this.$swal, "You don't have permission to create roles at this level");
          } else {
            showErrorToast(this.$swal, 'createError', { item: 'role' }, error?.response?.data);
          }
        }).finally(() => {
          this.loading = false;
        });
      },
      async fetchTags() {
        const tagService = makeTagService(this.$api);
        try {
          const response = await tagService.getTags(this.currentAccount.handle, 'roles');
          if (response.status === 200) {
            this.tags = response.data?.map((role) => ({ uid: role.uid, name: role.name }));
          }
        } catch (error) {
          console.error("Error fetching tags:", error);
        }
      },
      async fetchProjects() {
         const projectService = makeProjectService(this.$api);
         try {
           const params = {
             status: 'active',
           };
           const queryString = new URLSearchParams(params).toString();
           const response = await projectService.getProjects(this.currentAccount.handle, queryString);
           this.projects = response.data?.items.map(p => ({
            ...p,
            disabled: !(this.scopedAccess['write_role']?.projects.includes(p.uid))
           })) || [];
         } catch (error) {
           console.error("Error fetching projects:", error);
         }
       }
    },
}
</script>

<style scoped>
.gap-4{
  gap: 16px;
}
.custom_input {
    background-color: #f9f9fb;
    height: 38px;
}

.ml-custom {
    margin-left: 7rem;
}

.ml-custom-child {
    margin-left: 5.5rem;
}

.cw-custom {
    width: 100%;
    max-width: 160px;
}

.role-info-step {
  background: #ffffff;
  display: flex;
  flex-direction: column;
}

.role-info-step h1 {
  font-weight: 700;
  font-size: 24px;
  line-height: 28px;
  color: #18181a;
}

.btn-back {
  width: max-content;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #0c2ff3;
  display: flex;
  align-items: center;
}

.actions-container {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
  z-index: 0 !important;
}

.custom-chip-theme {
  background-color: #e6ecff;
  color: #0c2ff3;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
}

.role-option {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e0e0e0;
}

.role-option:hover {
  border-color: #0c2ff3;
  background-color: #f5f7ff;
}

.role-option-selected {
  border-color: #0c2ff3;
  background-color: #e6ecff;
}

.role-option ::v-deep .v-input--selection-controls__input {
  cursor: pointer;
}

.field-theme .v-input__control .v-input__slot {
  background-color: #f9f9fb !important;
}

.fs-14px {
  font-size: 14px;
}

.text-theme-label {
  color: #374151;
}

.label {
  font-size: 12px;
  font-weight: 500;
}

.disabled-option {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  pointer-events: auto !important;
}

.disabled-option:hover {
  background-color: inherit !important;
}

/* Ensure tooltip works on disabled items */
.disabled-option .v-list-item__content {
  pointer-events: auto !important;
}
</style>
