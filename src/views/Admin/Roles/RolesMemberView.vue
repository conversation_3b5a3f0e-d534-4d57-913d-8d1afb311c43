<template>
  <div class="pl-3 pt-3">
    <RoleMembersHeader
      :role="roleDetail"
      @back-to-roles="backToRoles"
    />
    <v-card
      class="py-6 px-6 mt-3 app-height-global"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <template v-if="!isLoading">
        <v-row
          justify="space-between"
          class="align-center"
        >
          <v-col
            cols="12"
            md="auto"
            class="d-flex align-center"
          >
            <SearchComponent
              :search="filter.name"
              :placeholder="$t('placeHolder.searchByName')"
              class="mr-3"
              @update:search="filter.name = $event"
            />

            <RoleMemberFilterDialog
              :tags="tags"
              :initial-filters="activeFilters"
              @apply="applyFilter"
            />
          </v-col>

          <v-col
            cols="12"
            md="auto"
          >
            <v-row
              justify="end"
              class="align-center"
            >
              <SettingsMenu 
                table-type="roleMember"
              />
            </v-row>
          </v-col>
        </v-row>

        <!-- Filter Chips -->
        <RoleMemberFilterChips
          :filters="activeFilters"
          :results-count="filtersMembers.length"
          :show-project-filter="false"
          @update-filters="updateFilters"
          @clear-filters="clearAllFilters"
        />

        <!-- Show empty state when no members -->
        <EmptyRoleMembers v-if="filtersMembers.length === 0" />

        <!-- Show table and actions when members exist -->
        <template v-else>
          <Table
            :headers="filteredHeaders"
            :items="filtersMembers"
            item-key="uid"
            :clear-selection="clearSelection"
            :role="roleDetail"
            @select-item="onCheckboxSelected"
          />

          <div class="d-flex align-end justify-end">
            <v-btn
              depressed
              color="red"
              class="font-inter text-capitalize rounded-lg white--text mt-2"
              :disabled="!selectedMembersHasData"
              @click="onDeleteMembers"
            >
              {{ $t('testruns.delete') }}
              {{ selectedMembersHasData ? `(${getSelectedMembersCount})` : '' }}
            </v-btn>

            <v-menu
              v-model="menuOpen"
              :close-on-content-click="false"
              offset-y
              top
              right
              class="menu-shadow-custom"
            >
              <template #activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  depressed
                  color="blue"
                  :disabled="!selectedMembersHasData"
                  class="font-inter text-capitalize rounded-lg ml-4 mt-2 white--text"
                  v-on="on"
                >
                  {{ $t('reassignTo') }}
                  {{ selectedMembersHasData ? `(${getSelectedMembersCount})` : '' }}
                  <v-icon>
                    {{ menuOpen ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
                  </v-icon>
                </v-btn>
              </template>

              <v-list class="actions-list font-inter">
                <v-list-item
                  v-for="(role, index) in getRolesName"
                  :key="index"
                  class="actions-item text-start"
                  @click="confirmRoleUpdate(role.roleId, role.roleName)"
                >
                  <v-list-item-title class="font-inter actions-item-title">
                    {{ role.roleName }}
                  </v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </template>
        <ReassignOwnerConfirmDialog 
          v-if="confirmOwnershipChange"
          v-model="confirmOwnershipChange"
          :is-new-owner-assignment="isNewOwnerAssignment"
          @updateRole="(password) => onSelectRole(selectedRole, selectedRoleName, password)"
          @close="handleReassignOwnerDialogClose"
        />
      </template>

      <RoleTableSkeleton v-else />
    </v-card>
  </div>
</template>

<script>
import * as _ from 'lodash'
import { createNamespacedHelpers } from 'vuex';

import makeRoleService from '@/services/api/role';
import makeProjectService from '@/services/api/project';
import makeTagService from '@/services/api/tag';

import RoleMembersHeader from '@/components/Admin/Role/Members/RoleMembersHeader';
import RoleMemberFilterDialog from '@/components/Admin/Role/Members/RoleMemberFilterDialog.vue';
import RoleMemberFilterChips from '@/components/Admin/Role/Members/RoleMemberFilterChips.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import Table from '@/components/Admin/Role/Members/Table.vue';
import EmptyRoleMembers from '@/components/Admin/Role/Members/EmptyRoleMembers.vue';
import RoleTableSkeleton from '@/components/Skeletons/Role/RoleTableSkeleton.vue';
import { mapGetters, mapActions } from 'vuex';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import ReassignOwnerConfirmDialog from '@/components/Admin/Role/ReassignOwnerConfirmDialog.vue';

const { mapState } = createNamespacedHelpers('user');

export default {
    name: 'Tags',

    components: {
        RoleMembersHeader,
        RoleMemberFilterDialog,
        RoleMemberFilterChips,
        SearchComponent,
        SettingsMenu,
        Table,
        EmptyRoleMembers,
        RoleTableSkeleton,
        ReassignOwnerConfirmDialog
    },

    data() {
        return {
            filter: {
                name: '',
                status: 'active',
                entityTypes: [],
            },
            originalMembers: [],
            roleDetail: null,
            roles: [],
            filtersMembers: [],
            projects: [],
            tags: [],
            headers: [],
            isLoading: false,
            menuOpen: false,
            errorMessage: '',
            showArchiveConfirmDialog: false,
            showDeleteConfirmDialog: false,
            selectedTag: {
                uid: '',
                name: '',
                description: '',
                entityTypes: [],
            },
            selectedMembers: [],
            clearSelection: false,
            activeFilters: {
                tags: []
            },
            confirmOwnershipChange: false,
            isNewOwnerAssignment: null,
            selectedRole: null,
            selectedRoleName: null

        }
    },

    computed: {
        ...mapGetters({
            dynamicHeaders:'headers/dynamicHeaders',
            currentUser: 'user/user'
        }),
        ...mapState(['currentAccount']),
        filteredMenuHeaders() {
            const filtered = this.headers.filter((header) => {
                if (header.value === 'role' && this.$route.params.key) {
                    return false;
                }
                if(header.value === 'project' && this.$route.params.key){
                    return false
                }
                return header.value !== 'actions';
            });
            return filtered;
        },
        filteredHeaders() {
            const filtered = this.filteredMenuHeaders.filter((header) => header.checked);
            return filtered;
        },
        getSelectedMembersCount() {
            return this.selectedMembers.length
        },
        selectedMembersHasData() {
            return this.selectedMembers.length > 0
        },
        getRolesName() {
            const currentRouteId = this.$route.params.id;
            return this.roles
                .filter(role => role.uid !== currentRouteId)
                .map(role => {
                    return {
                        roleName: role.name,
                        roleId: role.uid,
                    };
                });
        },
        selectedMembersObj() {
            return this.selectedMembers?.map(item => {
                return {
                    userId: item.uid,
                }
            })
        },
        selectedMembersUserIds() {
            return this.selectedMembers?.map(item => item.uid) || [];
        },
    },

    watch: {
        'filter.name': {
            handler: _.debounce(function () {
                this.getRoleDetail(this.currentAccount.handle)
            }, 500),
        },
        activeFilters: {
            handler() {
                this.getRoleDetail(this.currentAccount.handle);
            },
            deep: true,
        },
    },

    created() {
       if (!this.dynamicHeaders.roleMember) {
         this.initializeHeaders({ type: 'roleMember' });
        }
        this.headers = this.dynamicHeaders.roleMember;
    },

    async mounted() {
        await this.init(this.currentAccount.handle);
    },
    async beforeRouteUpdate(to, from, next) {
        const handle = to.params.handle;
        if (handle && handle !== from.params.handle) {
            try {
                await this.init(handle);
                next();
            } catch (error) {
                next();
            }
        } else {
            next();
        }
    },

    methods: {
        ...mapActions("headers", ['initializeHeaders']),
        async confirmRoleUpdate(roleId, roleName){
            const isSelfReassignment = this.roleDetail.slug?.toLowerCase() === 'owner' && this.selectedMembersObj.find(u => u.userId == this.currentUser.uid);
            const isAssigningNewOwner = roleName?.toLowerCase() == 'owner';

            if(isSelfReassignment){
                this.isNewOwnerAssignment = false;
            }else if(isAssigningNewOwner){
                this.isNewOwnerAssignment = true;
            }

            if(isSelfReassignment || isAssigningNewOwner){
                this.confirmOwnershipChange = true;
                this.selectedRole = roleId;
                this.selectedRoleName = roleName;
            }else{
                this.onSelectRole(roleId, roleName)
            }
        },
        handleReassignOwnerDialogClose(){
            this.confirmOwnershipChange = false;  
            this.selectedRole = null;
            this.selectedRoleName = null;
            this.isNewOwnerAssignment = null;
        },
        async onSelectRole(roleId, roleName, password) {
            try {
                let newRole = roleName;
                let userCount = this.getSelectedMembersCount;
                let members = this.selectedMembersObj?.map(item => {
                    return {
                        ...item,
                        roleId: this.$route.params.id,
                    };
                });
                await this.reAssignRole(roleId, { members, ...(password ? {password} : {}) });
                this.menuOpen = false;
                await this.getRoleDetail(this.currentAccount.handle);
                this.clearSelection = false;
                this.$nextTick(() => {
                    this.clearSelection = true;
                });
                const selectedUserIds = this.selectedMembersObj.map(member => member.userId);
                this.filtersMembers = this.filtersMembers.filter(member => !selectedUserIds.includes(member.uid));
                showSuccessToast(this.$swal, this.$t('reassignToRole',{userCount, newRole}));
            } catch (error) {
                showErrorToast(this.$swal, error.response?.data.message || this.$t('errorOccurred'));
            } finally {
                this.confirmOwnershipChange = false
            }
        },
    
        async onDeleteMembers() {
            try {
                const userIds = this.selectedMembersUserIds;
                const roleId = this.$route.params.id;

                await this.deleteMembersFromRole(roleId, { userIds });
                await this.init(this.currentAccount.handle);
                this.selectedMembers = [];
                showSuccessToast(this.$swal, this.$t('user successfully deleted'));
            } catch (error) {
                console.error('An error occurred:', error);
                showErrorToast(this.$swal, this.$t('errorOccurred'));
            } finally {
                this.selectedMembers = [];
            }
        },
        backToRoles() {
            const name = this.$route.params.key ? `ProjectRoles` : 'Roles';
            this.$router.push({ name });
        },
        async init(handle) {
            this.isLoading = true;
            try {
                await this.getRoles(handle);
                await this.getProjects(handle);
                await this.getTags(handle);
                await this.getRoleDetail(handle);
            } finally {
                this.isLoading = false;
            }
        },
        async reAssignRole(roleId, payload) {
            const roleMembersService = makeRoleService(this.$api);
            const projectKey = this.$route.params.key;
            await roleMembersService.reAssignRole({handle: this.currentAccount.handle, roleId, payload, projectKey});
        },
        async deleteMembersFromRole(roleId, payload) {
            const roleMembersService = makeRoleService(this.$api);
            try {
                 await roleMembersService.deleteMembersFromRole(this.currentAccount.handle, roleId, payload);
            } catch (err) {
                this.errorMessage = err.message;
            }
        },
        async getRoleDetail(handle) {
            const id = this.$route.params.id;
            const roleMembersService = makeRoleService(this.$api);
            const projectKey = this.$route.params.key;
            
            try {
                const queryParams = {};
                
                // Add name search as 'q' parameter
                if (this.filter.name) {
                    queryParams.q = this.filter.name;
                }
                
                // Add tag UIDs as 'tagUids' parameter
                if (this.activeFilters.tags && this.activeFilters.tags.length > 0) {
                    const tagUids = [];
                    this.activeFilters.tags.forEach(tag => {
                        if (tag.uid) {
                            tagUids.push(tag.uid);
                        }
                    });
                    if (tagUids.length > 0) {
                        queryParams.tagUids = tagUids;
                    }
                }
                
                const response = await roleMembersService.getRoleDetails(handle, id, projectKey, queryParams);
                this.roleDetail = response.data;
                
                let members = response.data?.members || [];
                
                this.originalMembers = members;
                this.filtersMembers = members; 
            } catch (err) {
                this.errorMessage = err.message;
            }
        },
        async getRoles(handle) {
            const roleMembersService = makeRoleService(this.$api);
            const projectKey = this.$route.params.key;
            try {
                const response = await roleMembersService.getRoles(handle, projectKey);
                this.roles = response.data?.items;
            } catch (err) {
                this.errorMessage = err.message;
            }
        },
        async getProjects(handle) {
            const projectService = makeProjectService(this.$api);
            try {
                const params = {
                    status: 'active',
                };
                const queryString = new URLSearchParams(params).toString();
                const response = await projectService.getProjects(handle, queryString);
                this.projects = response.data?.items || [];
            } catch (err) {
                this.errorMessage = err.message;
            }
        },
        async getTags(handle) {
            const tagService = makeTagService(this.$api);
            try {
                const response = await tagService.getTags(handle, 'users');
                this.tags = response.data || [];
            } catch (err) {
                this.errorMessage = err.message;
            }
        },

        updateFilterStatus(value) {
            this.filter.status = value;
            this.filtersMembersFunc();
        },

        updateFilterTypes(value) {
            this.filter.entityTypes = value;
            this.filtersMembersFunc();
        },

        filtersMembersFunc() {
            let filtersMembers = _.cloneDeep(this.originalMembers)

            // Only handle status filtering client-side, everything else is server-side
            if (this.filter.status) {
                filtersMembers = filtersMembers.filter(item => 
                    this.filter.status === 'active' && !item.archivedAt || 
                    this.filter.status === 'archived' && item.archivedAt
                )
            }

            if (this.filter.entityTypes.length > 0) {
                filtersMembers = filtersMembers.filter(item => 
                    this.filter.entityTypes.find(tagType => 
                        item.entityTypes && item.entityTypes.includes(tagType.name)
                    )
                )
            }

            this.filtersMembers = filtersMembers
        },
        onCheckboxSelected(selectedItems) {
            this.selectedMembers = selectedItems;
        },
        applyFilter(filterData) {
            this.activeFilters = {
                tags: filterData.tagSelected || []
            };
        },

        updateFilters(newFilters) {
            this.activeFilters = newFilters;
        },
        clearAllFilters() {
            this.activeFilters = {
                tags: []
            };
        },
    }
}
</script>
