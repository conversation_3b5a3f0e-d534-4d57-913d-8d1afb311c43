<template>
  <div class="pl-3 pt-3">
    <TagHeader
      :status="filter.status"
      :active-count="activeCount"
      :write-tag="_writeTag"
      :archived-count="archivedCount"
      @update-status="updateFilterStatus"
      @create-new-tag="onCreateTag"
    />

    <template v-if="activeCount == 0 && filter.status != 'archived' && !isFilter && !skeletonLoaderState">
      <div class="mt-3 mb-0 white rounded-lg mx-0 app-height-global d-flex justify-center align-center">
        <ActiveEmptyState
          :write-entity="_writeTag"
          :image-src="require('@/assets/png/empty-tags.png')"
          image-max-width="323px"
          :title="$t('tagsPlaceholder.title')"
          :button-text="$t('tagsPlaceholder.description.highlight')"
          button-color="primary"
          @button-click="onCreateTag"
        >
          <template #description>
            <p class="mb-0 mt-3">
              {{ $t('tagsPlaceholder.description.part1') }}
            </p>
            <p class="mb-0">
              <strong>{{ `"${$t('tagsPlaceholder.description.highlight')}"` }}</strong>{{ $t('tagsPlaceholder.description.part2') }}
            </p>
            <p class="mb-0">
              {{ $t('tagsPlaceholder.description.part3') }}
            </p>
          </template>
        </ActiveEmptyState>
      </div>
    </template>

    <template v-else-if="archivedCount == 0 && filter.status != 'active' && !isFilter && !skeletonLoaderState">
      <div class="mt-3 mb-0 white rounded-lg mx-0 app-height-global d-flex justify-center align-center">
        <ArchivedEmptyState
          :image-src="require('@/assets/png/empty-tags.png')"
          image-max-width="323px"
          :title="$t('archived_empty_state.title', { name: $t('tags') })"
        >
          <template #description>
            <p class="mb-0 mt-3">
              {{ $t('archived_empty_state.description.part1', { name: $t('tags') }) }}
            </p>
            <p class="mb-0">
              {{ $t('projects.archived_empty_state.description.part2') }}
            </p>
          </template>
        </ArchivedEmptyState>
      </div>
    </template>
    <v-card
      v-else
      class="py-6 px-6 mt-3 app-height-global"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <template>
        <v-row
          justify="space-between"
          class="align-center"
        >
          <v-col
            cols="12"
            md="auto"
            class="d-flex align-center"
          >
            <SearchComponent
              class="mr-3"
              :search="filter.name"
              :placeholder="$t('placeHolder.searchByName')"
              @update:search="filter.name = $event"
            />

            <TagFilterDialog @update-filter-types="updateFilterTypes" />
          </v-col>

          <v-col
            cols="12"
            md="auto"
          >
            <v-row
              justify="end"
              class="align-center"
            >
              <SettingsMenu 
                table-type="tags"
              />
            </v-row>
          </v-col>
        </v-row>

        <TagTable
          :headers="filteredHeaders"
          :items="paginatedTags"
          item-key="uid"
          :write-tag="_writeTag"
          :delete-tag="_deleteTag"
          @edit-tag="onEditTag"
          @archive-tag="onArchiveTag"
          @delete-tag="onDeleteTag"
        />

        <Pagination
          v-if="totalFilteredTags > 0"
          :page="currentPage"
          :total-pages="totalPages"
          :items-per-page="perPage"
          :total-items="totalFilteredTags"
          @update:pagination="onUpdatePagination"
        />
      </template>
    </v-card>
    <CreateUpdateTagDialog
      v-model="showCreateUpdateTagDialog"
      :data="selectedTag"
      @create-new-tag="createTag"
      @update-tag="updateTag"
      @close-dialog="showCreateUpdateTagDialog = false"
    />

    <TagArchiveConfirmDialog
      v-model="showArchiveConfirmDialog"
      :tag-name="selectedTag.name"
      :filter-status="filter.status"
      @archive-tag="archiveTag"
    />

    <TagDeleteConfirmDialog
      v-model="showDeleteConfirmDialog"
      :tag="selectedTag"
      @delete-tag="deleteTag"
    />
  </div>
</template>

<script>
import * as _ from "lodash";
import { createNamespacedHelpers, mapActions } from "vuex";
import makeTagService from "@/services/api/tag";
import TagHeader from "@/components/Admin/Tag/TagHeader";
import TagFilterDialog from "@/components/Admin/Tag/FilterDialog.vue";
import SearchComponent from "@/components/Project/SearchComponent.vue";
import SettingsMenu from "@/components/Project/SettingsMenu.vue";
import TagTable from "@/components/Admin/Tag/TagTable.vue";
import CreateUpdateTagDialog from "@/components/Admin/Tag/CreateUpdateTagDialog.vue";
import TagArchiveConfirmDialog from "@/components/Admin/Tag/ArchiveConfirmDialog.vue";
import TagDeleteConfirmDialog from "@/components/Admin/Tag/DeleteConfirmDialog.vue";
import { mapGetters } from 'vuex';
import { handleNetworkStatusError } from "@/mixins/redirect";
import { showSuccessToast, showErrorToast } from "@/utils/toast";
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue';
import ArchivedEmptyState from '@/components/base/ArchivedEmptyState.vue';
import handleLoading from '@/mixins/loader.js'
import Pagination from '@/components/base/Pagination.vue';


const { mapState } = createNamespacedHelpers("user");

export default {
  name: "Tags",

  components: {
    TagHeader,
    TagFilterDialog,
    SearchComponent,
    SettingsMenu,
    TagTable,
    CreateUpdateTagDialog,
    TagArchiveConfirmDialog,
    TagDeleteConfirmDialog,
    ActiveEmptyState,
    ArchivedEmptyState,
    Pagination,
  },
  mixins: [handleNetworkStatusError, handleLoading],

  data() {
    return {
      filter: {
        name: "",
        status: "active",
        entityTypes: [],
      },
      originalTags: [],
      filteredTags: [],
      headers: [],
      isLoading: false,
      showCreateUpdateTagDialog: false,
      showArchiveConfirmDialog: false,
      showDeleteConfirmDialog: false,
      selectedTag: {
        uid: "",
        name: "",
        description: "",
        entityTypes: [],
      },
      isFilter: false,
      tableLoadingState: false,
      currentPage: 1,
      perPage: 10
    };
  },

  computed: {
    ...mapGetters({
      dynamicHeaders:'headers/dynamicHeaders'
    }),
    _writeTag(){
      return this.authorityTo('write_tag');
    },
    _deleteTag(){
      return this.authorityTo('delete_tag');
    },
    ...mapState(["currentAccount"]),
    filteredMenuHeaders() {
      const filtered = this.headers.filter((header) => header.value != "actions");
      return filtered;
    },
    filteredHeaders() {
        const filtered = this.filteredMenuHeaders.filter((header) => header.checked);
        return filtered;
    },
    activeCount() {
      return this.originalTags.filter((item) => !item.archivedAt).length;
    },

    archivedCount() {
      return this.originalTags.filter((item) => !!item.archivedAt).length;
    },

    hasTags() {
      return this.originalTags.length > 0;
    },
    hasNoTags() {
      return this.originalTags.length === 0;
    },
    totalFilteredTags() {
      return this.filteredTags.length;
    },
    totalPages() {
      return Math.ceil(this.totalFilteredTags / this.perPage);
    },
    paginatedTags() {
      const start = (this.currentPage - 1) * this.perPage;
      const end = start + this.perPage;
      return this.filteredTags.slice(start, end);
    },
  },

  watch: {
    "filter.name": {
      handler: _.debounce(function () {
        this.filterTags();
      }, 500),
    },
  },

  created() {
    if(!this.dynamicHeaders.tags) {
      this.initializeHeaders({ type: 'tags' });
    }
    this.headers = this.dynamicHeaders.tags;
  },

  mounted() {
    let handle = this.$route.params.handle;
    this.init(handle);
  },
  
  async beforeRouteUpdate(to, from, next) {
    const handle = to.params.handle;
    if (handle && handle !== from.params.handle) {
      try {
        await this.init(handle);
        next();
      } catch (error) {
        next();
      }
    } else {
      next();
    }
  },

  methods: {
    ...mapActions("headers", ['initializeHeaders']),
    async init(handle) {
      try {
        this.showSkeletonLoader();
        await this.getTags(handle);
        await this.getTagRelationsData(handle, 'sumCounts');
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'test runs' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    async getTagRelationsData(handle, relationType = 'sumCounts') {
      if (this.originalTags.length === 0) return;

      const tagService = makeTagService(this.$api);
      
      try {
        this.loadingRelations = true;
        const tagUids = this.originalTags.map(tag => tag.uid);
        
        const params = {
          relation: relationType,
          tagUids: tagUids,
          // Optional: specify entity types if needed
          // entityTypes: ['cases', 'executions', 'runs', 'plans', 'milestones']
        };

        if (relationType === 'sumCounts') {
          params.entityTypes = ['cases', 'executions', 'runs', 'plans', 'milestones', 'defects'];
        }

        const response = await tagService.getTagRelations(handle, params);
        
        if (response.status === 200) {
          this.tagRelations = response.data;
          this.updateTagsWithRelations();
        }
      } catch (err) {
        console.error('Error fetching tag relations:', err);
        showErrorToast(this.$swal, "fetchError", { item: "tag relations" }, err?.response?.data);
      } finally {
        this.loadingRelations = false;
      }
    },

    updateTagsWithRelations() {
      this.originalTags = this.originalTags.map(tag => ({
        ...tag,
        relationCount: this.tagRelations[tag.uid] || 0
      }));
      this.filterTags();
    },

    async getSpecificTagRelation(handle, relationType, tagUids = null, entityTypes = null) {
      const tagService = makeTagService(this.$api);
      
      const uids = tagUids || this.originalTags.map(tag => tag.uid);
      
      const params = {
        relation: relationType,
        tagUids: uids
      };

      if (relationType === 'sumCounts') {
        params.entityTypes = entityTypes || ['cases', 'executions', 'runs', 'plans', 'milestones', 'defects'];
      }

      try {
        const response = await tagService.getTagRelations(handle, params);
        return response.data;
      } catch (err) {
        console.error(`Error fetching ${relationType} relations:`, err);
        return {};
      }
    },

    async getAllTagRelations(handle, tagUids = null) {
      const relationTypes = [
        'sumCounts',
        'runCount', 
        'planCount',
        'caseCount',
        'milestoneCount',
        'resultCount',
        'executionCount'
      ];

      const uids = tagUids || this.originalTags.map(tag => tag.uid);
      const relations = {};

      try {
        this.loadingRelations = true;
        
        // Fetch all relation types in parallel
        const promises = relationTypes.map(async (relationType) => {
          const data = await this.getSpecificTagRelation(handle, relationType, uids);
          relations[relationType] = data;
        });

        await Promise.all(promises);
        
        return relations;
      } catch (err) {
        console.error('Error fetching all tag relations:', err);
        return {};
      } finally {
        this.loadingRelations = false;
      }
    },

    async getTagRelationsByEntityTypes(handle, entityTypes, tagUids = null) {
      const tagService = makeTagService(this.$api);
      
      const uids = tagUids || this.originalTags.map(tag => tag.uid);
      
      const params = {
        relation: 'sumCounts',
        tagUids: uids,
        entityTypes: entityTypes
      };

      try {
        const response = await tagService.getTagRelations(handle, params);
        return response.data;
      } catch (err) {
        console.error('Error fetching tag relations by entity types:', err);
        return {};
      }
    },

    async getTags(handle) {
      const tagService = makeTagService(this.$api);

      try {
        this.isLoading = true;
        const response = await tagService.getTags(handle, '', {includeArchived: true, includeRelationCount: true});
        if (response.status == 200) {
          this.originalTags = response.data;
        }
      } catch (err) {
        this.redirectOnError(err.response.status);
        showErrorToast(this.$swal, "fetchError", { item: "tags" }, err?.response?.data);
      } finally {
        this.isLoading = false;
      }

      this.filterTags();
    },

    updateFilterStatus(value) {
      this.filter.status = value;

      this.filterTags();
    },

    updateFilterTypes(value) {
      this.filter.entityTypes = value;

      this.filterTags();
    },

    filterTags() {
      let filteredTags = _.cloneDeep(this.originalTags);

      if (this.filter.name) {
        const searchName = this.filter.name.toLowerCase();
        filteredTags = filteredTags.filter((item) =>
          item.name.toLowerCase().includes(searchName)
        );
      }

      if (this.filter.status) {
        filteredTags = filteredTags.filter(
          (item) =>
            (this.filter.status === "active" && !item.archivedAt) ||
            (this.filter.status === "archived" && item.archivedAt)
        );
      }

      // if any of the selected types are in the entityTypes array, filter the tags accordingly
      if (this.filter.entityTypes.length > 0) {
        filteredTags = filteredTags.filter((item) =>
          this.filter.entityTypes.some((selectedType) => 
            item.entityTypes && item.entityTypes.some(tagType => 
              tagType.toLowerCase() === selectedType.name.toLowerCase()
            )
          )
        );
      }

      this.filteredTags = filteredTags;
      // Reset to first page when filtering
      this.currentPage = 1;
    },

    onCreateTag() {
      this.selectedTag = {
        uid: "",
        name: "",
        description: "",
        entityTypes: [],
      };
      this.showCreateUpdateTagDialog = true;
    },

    async createTag(tag) {
      this.showCreateUpdateTagDialog = false;
      if(!this._writeTag) {
        this.unauthorizedToast;
        return ;
      }
      const tagService = makeTagService(this.$api);
      this.isLoading = true;
    
      try {
        this.showSkeletonLoader();
        const payload = {
          name: tag.name,
          description: tag.description,
          entityTypes: tag.entityTypes,
        }

        const response = await tagService.createTag(this.currentAccount.handle, payload);
        this.originalTags.push(response.data);
        showSuccessToast(this.$swal, "createSuccess", { item: "Tag" });
      } catch (err) {
        showErrorToast(this.$swal, "createError", { item: "Tag" }, err?.response?.data);
      } finally {
        this.isLoading = false;
        this.hideSkeletonLoader();
      }

      this.filterTags();
    },

    onEditTag(tag) {
      this.selectedTag = {
        uid: tag.uid,
        name: tag.name,
        description: tag.description,
        entityTypes: tag.entityTypes,
        archivedAt: tag.archivedAt,
      };

      this.showCreateUpdateTagDialog = true;
    },

    async updateTag(tag) {
      this.showCreateUpdateTagDialog = false;
      if(!this._writeTag){
        this.unauthorizedToast;
        return ;
      }
      const tagService = makeTagService(this.$api);
      this.isLoading = true;
      const sentData = {
        ...tag,
        entityTypes: tag.entityTypes,
      }
      try {
        this.showSkeletonLoader();
        const response = await tagService.updateTag(this.currentAccount.handle, sentData);

        this.originalTags = this.originalTags.map((item) => {
          if (item.uid !== tag.uid) {
            return item;
          }

          return response.data;
        });
        showSuccessToast(this.$swal, "updateSuccess", { item: "Tag" });
      } catch (err) {
        showErrorToast(this.$swal, "updateError", { item: "Tag" }, err?.response?.data);
        throw new Error(err);
      } finally {
        this.isLoading = false;
        this.hideSkeletonLoader();
      }

      this.filterTags();
    },

    onArchiveTag(tag) {
      this.selectedTag = {
        uid: tag.uid,
        name: tag.name,
        description: tag.description,
        entityTypes: tag.entityTypes,
        archived: this.filter.status === "active" ? true : new Date(),
      };

      this.showArchiveConfirmDialog = true;
    },

    async archiveTag() {
      this.showArchiveConfirmDialog = false;
      if(!this._writeTag){
        this.unauthorizedToast;
        return ;
      }
      try {
        this.showSkeletonLoader();
        await this.updateTag(this.selectedTag);

        if(this.filter.status === "active") {
          showSuccessToast(this.$swal, "archiveSuccess", { item: "Tag" });
        } else {
          showSuccessToast(this.$swal, "unarchiveSuccess", { item: "Tag" });
        }
        
      } catch (err) {
        showErrorToast(this.$swal, "archiveError", { item: "Tag" }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    onDeleteTag(tag) {
      this.selectedTag = {
        uid: tag.uid,
        name: tag.name,
        description: tag.description,
        entityTypes: tag.entityTypes,
        count: tag.count,
        archived: tag.archivedAt,
      };

      this.showDeleteConfirmDialog = true;
    },

    async deleteTag() {
      this.showDeleteConfirmDialog = false;
      if(!this._deleteTag){
        this.unauthorizedToast;
        return ;
      }
      const tagService = makeTagService(this.$api);

      try {
        this.showSkeletonLoader();
        await tagService.deleteTag(this.currentAccount.handle, this.selectedTag.uid);
        this.originalTags = this.originalTags.filter(
          (item) => item.uid !== this.selectedTag.uid
        );
        showSuccessToast(this.$swal, "deleteSuccess", { item: "Tag" });
      } catch (err) {
        showErrorToast(this.$swal, "deleteError", { item: "Tag" }, err?.response?.data);
      } finally {
        this.isLoading = false;
        this.hideSkeletonLoader();
      }

      this.filterTags();
    },

    onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      if (newPage !== this.currentPage || newItemsPerPage !== this.perPage) {
        this.currentPage = newPage;
        this.perPage = newItemsPerPage;
      }
    },
  },
};
</script>
