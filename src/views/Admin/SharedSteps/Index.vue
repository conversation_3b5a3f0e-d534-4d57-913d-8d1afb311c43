<template>
  <v-container
    fluid
    class="pr-0 pb-0"
  >
    <SharedStepHeader
      :status="tab"
      :active-count="activeCount"
      :archived-count="archivedCount"
      :write-step="_writeStep"
      :is-project-archived="isProjectArchived"
      @update-status="updateFilterStatus"
      @create-shared-step="onCreateSharedStep"
      @close="closeDetail"
    />

    <template v-if="activeCount == 0 && tab != 'archived' && !skeletonLoaderState">
      <div class="mt-3 mb-0 white rounded-lg mx-0 app-height-global d-flex justify-center align-center">
        <ActiveEmptyState
          :write-entity="_writeStep"
          :image-src="require('@/assets/png/empty-step.png')"
          image-max-width="323px"
          :title="$t('sharedStepPage.emptyTitle')"
          :button-text="$t('sharedStepPage.createSharedStep')"
          :is-project-archived="isProjectArchived"
          button-color="primary"
          @button-click="onCreateSharedStep"
        />
      </div>
    </template>

    <template v-else-if="archivedCount == 0 && tab != 'active' && !skeletonLoaderState">
      <div class="mt-3 mb-0 white rounded-lg mx-0 app-height-global d-flex justify-center align-center">
        <ArchivedEmptyState
          :image-src="require('@/assets/png/empty-step.png')"
          :title="$t('archived_empty_state.title', { name: $t('sharedStepPage.title') })"
          image-max-width="323px"
        >
          <template #description>
            <p class="mb-0 mt-3">
              {{ $t('archived_empty_state.description.part1', { name: $t('sharedStepPage.title') }) }}
            </p>
            <p class="mb-0">
              {{ $t('plans.archived_empty_state.description.part2') }}
            </p>
          </template>
        </ArchivedEmptyState>
      </div>
    </template>
    <template v-else>
      <v-row
        class="align-start"
        dense
      >
        <v-col
          :cols="isDetailMode ? 7 : 12"
        >
          <v-card
            class="pa-6 mt-3 app-height-global"
            rounded="lg"
            elevation="0"
            width="100%"
          >
            <v-row
              justify="space-between"
              class="align-center"
            >
              <v-col
                cols="12"
                md="auto"
                class="d-flex align-center"
              >
                <v-text-field
                  v-if="!skeletonLoaderState"
                  v-model="searchFilter"
                  :loading="loading"
                  prepend-inner-icon="mdi-magnify"
                  :placeholder="$t('search_by_name')"
                  class="text-field mt-0 pa-0 mr-3 rounded-lg field-theme custom-prepend pa-0"
                  height="40"
                  clear-icon="mdi-close-circle"
                  clearable
                  background-color="#F9F9FB"
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchIcon />
                  </template>
                </v-text-field>
                <v-skeleton-loader
                  v-else
                  class="rounded-lg mr-3"
                  width="235"
                  height="40"
                  type="button"
                />

                <SharedStepFilterDialog @update-filter="updateFilter" />
              </v-col>

              <v-col
                cols="12"
                md="auto"
                class="mr-3"
              >
                <v-row
                  justify="end"
                  class="align-center"
                >
                  <SettingsMenu
                    table-type="sharedSteps"
                  />
                </v-row>
              </v-col>
            </v-row>

            <SharedStepTable
              :headers="filteredHeaders"
              :items="filteredSteps"
              :tab="tab"
              item-key="uid"
              :write-step="_writeStep"
              :delete-step="_deleteStep"
              @edit="handleEditItem"
              @delete="handleDeleteItem"
              @archive="handleArchiveItem"
              @unarchive="handleUnArchiveItem"
              @input="handleInput"
              @row-click="handleClickRow"
            />

            <Pagination
              v-if="hasSteps && totalItems > 0"
              :page="currentPage"
              :items-per-page="itemsPerPage"
              :total-pages="totalPages"
              :total-items="totalItems"
              @update:pagination="onUpdatePagination"
            />

        

            <v-row
              v-if="selectedItems.length > 0"
              justify="end"
              class="action-btn-wrapper px-6 py-4"
            >
              <v-btn
                width="140"
                color="danger"
                :class="{ 'text-capitalize rounded-lg mr-3 white--text btn-theme': true, 'disabled-action': isProjectArchived }"
                depressed
                @click="deleteSharedSteps"
              >
                {{ $t('delete') }}
              </v-btn>
              <v-btn
                v-if="tab == 'active'"
                width="140"
                color="primary"
                :class="{ 'text-capitalize rounded-lg btn-theme': true, 'disabled-action': isProjectArchived }"
                depressed
                @click="!isProjectArchived && handleChangeActiveStatuses(true)"
              >
                {{ $t('archive') }}
              </v-btn>
              <v-btn
                v-else
                width="140"
                :class="{ 'text-capitalize rounded-lg btn-theme': true, 'disabled-action': isProjectArchived }"
                depressed
                color="primary"
                @click="!isProjectArchived && handleChangeActiveStatuses(false)"
              >
                {{ $t('unarchive') }}
              </v-btn>
            </v-row>
          </v-card>
        </v-col>
        <v-col
          v-if="isDetailMode"
          cols="5"
        >
          <SharedStepDetail
            :detail-item="currentItem"
            :has-previous="currentFilteredIndex > 0"
            :has-next="currentFilteredIndex < filteredSteps.length - 1"
            @previous="navigateToPrevious"
            @next="navigateToNext"
            @edit="handleEditItem"
            @close="closeDetail"
          />
        </v-col>
      </v-row>
    </template>
    <CreateUpdateSharedStepDialog
      v-model="showCreateUpdateSharedStepDialog"
      :data="selectedStep"
      @create-shared-step="createSharedStep"
      @update-shared-step="updateSharedStep"
      @close-dialog="showCreateUpdateSharedStepDialog = false"
    />
    <confirm-dialog-shared-steps
      v-model="showDeleteConfirmDialog"
      :title="$t('sharedStepPage.deleteConfirm', { name: selectedStep.name })"
      :description="$t('sharedStepPage.deleteDescription')"
      :actions="[
        { label: $t('cancel'), color: 'secondary', action: 'cancel' },
        { label: $t('delete'), color: 'danger', action: 'delete' },
      ]"
      @cancel="showDeleteConfirmDialog = false"
      @close="showDeleteConfirmDialog = false"
      @delete="deleteSharedStepItem"
    />

    <confirm-dialog-shared-steps
      v-model="showArchiveConfirmDialog"
      :title="$t('sharedStepPage.archiveConfirm', { action: isArchive ? $t('archive') : $t('unarchive'), name: selectedStep.name })"
      :description="isArchive ? $t('sharedStepPage.archiveDescription') : $t('sharedStepPage.unarchiveDescription')"
      :actions="[
        { label: $t('cancel'), color: 'secondary', action: 'cancel' },
        { label: isArchive ? $t('archive') : $t('unarchive'), color: 'primary', action: 'archive' },
      ]"
      @cancel="showArchiveConfirmDialog = false"
      @close="showArchiveConfirmDialog = false"
      @archive="changeActiveStatusSharedStepItem"
    />

    <confirm-dialog-shared-steps
      v-model="showBulkDeleteConfirmDialog"
      :title="$t('sharedStepPage.bulkDeleteConfirm', { count: selectedItems.length })"
      :description="$t('sharedStepPage.deleteDescription')"
      :actions="[
        { label: $t('cancel'), color: 'secondary', action: 'cancel' },
        { label: $t('delete'), color: 'danger', action: 'delete' },
      ]"
      @cancel="showBulkDeleteConfirmDialog = false"
      @close="showBulkDeleteConfirmDialog = false"
      @delete="bulkDeleteSharedSteps"
    />

    <StepsConfirmDialog
      v-model="showBulkArchiveUnarchiveDialog"
      :title="$t('sharedStepPage.bulkArchiveConfirm', { count: selectedItems.length, action: tab === 'active' ? $t('archive') : $t('unarchive') })"
      :description="isArchive ? $t('sharedStepPage.archiveDescription') : $t('sharedStepPage.unarchiveDescription')"
      :actions="[
        { label: $t('cancel'), color: 'secondary', action: 'cancel' },
        { label: tab === 'active' ? $t('archive') : $t('unarchive'), color: 'primary', action: 'archive' },
      ]"
      @cancel="showBulkArchiveUnarchiveDialog = false"
      @close="showBulkArchiveUnarchiveDialog = false"
      @archive="bulkChangeStatusSharedSteps"
    />
  </v-container>
</template>

<script>
import { createNamespacedHelpers } from 'vuex';
import makeSharedStepService from '@/services/api/sharedStep';
import SharedStepHeader from '@/components/Admin/SharedStep/SharedStepHeader';
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue';
import ArchivedEmptyState from '@/components/base/ArchivedEmptyState.vue';
import SharedStepTable from '@/components/Admin/SharedStep/SharedStepTable.vue';
import SharedStepFilterDialog from '@/components/Admin/SharedStep/FilterDialog.vue';
import CreateUpdateSharedStepDialog from '@/components/Admin/SharedStep/CreateUpdateSharedStepDialog.vue';
import { formatDate } from '@/utils/util';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import StepsConfirmDialog from '@/views/Admin/SharedSteps/ConfirmDialog.vue';
import SharedStepDetail from '@/components/Admin/SharedStep/SharedStepDetail.vue';
import { handleNetworkStatusError } from '@/mixins/redirect';
import projectStatus from '@/mixins/projectStatus';
import SearchIcon from '@/assets/svg/search-icon.svg';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import handleLoading from '@/mixins/loader.js'
import { mapGetters, mapActions } from 'vuex';
import ConfirmDialogSharedSteps from '@/views/Admin/SharedSteps/ConfirmDialog.vue';
import Pagination from '@/components/base/Pagination.vue';
import { useSharedStepCount } from '@/composables/modules/sharedStep/count';


const { mapState } = createNamespacedHelpers('user');
let sharedStepService;

export default {
  name: 'SharedStep',

  components: {
    SharedStepHeader,
    SharedStepTable,
    ActiveEmptyState,
    ArchivedEmptyState,
    SharedStepFilterDialog,
    CreateUpdateSharedStepDialog,
    StepsConfirmDialog,
    SharedStepDetail,
    SearchIcon,
    SettingsMenu,
    ConfirmDialogSharedSteps,
    Pagination
  },

  mixins: [handleNetworkStatusError, projectStatus, handleLoading],

  setup() {
    const { activeCount, archivedCount, getEntityCount } = useSharedStepCount();

    return {
      entityActiveCount: activeCount,
      entityArchivedCount: archivedCount,
      getEntityCount
    };
  },

  data()
  {
    return {
      projectKey: this.$route.params.key,
      loading: false,
      isDetailMode: false,
      headers: [],
      tableData: [],
      isColumnFilter: false,
      searchFilter: '',
      stepRangeFilter: [],
      referencedByRangeFilter: [],
      showCreateUpdateSharedStepDialog: false,
      tab: 'active',
      selectedStep: {
        uid: '',
        name: '',
        steps: [],
        expectedResultByStep: false,
      },
      showArchiveConfirmDialog: false,
      showDeleteConfirmDialog: false,
      showBulkDeleteConfirmDialog: false,
      showBulkArchiveUnarchiveDialog: false,
      isArchive: true,
      selectedItems: [],
      detailItem: '',
      originalTags: [],
      filteredTags: [],
      isLoading: false,
      errorMessage: '',
      currentIndex: 0,
      // Pagination properties
      currentPage: 1,
      itemsPerPage: 10,
      totalItems: 0,
      // Initialization flag to prevent watchers from firing during setup
      isInitialized: false,
    };
  },

  computed: {
    _writeStep(){
      return this.authorityTo('write_step')
    },
    _deleteStep(){
      return this.authorityTo('delete_step')
    },
    ...mapGetters({
      dynamicHeaders:'headers/dynamicHeaders',
    }),
    ...mapState(['currentAccount']),
    filteredSteps()
    {
      // For server-side pagination, we should return the tableData as-is
      // All filtering should be handled server-side to maintain proper pagination
      // Client-side filtering interferes with server-side pagination counts
      return this.tableData;
    },
    filteredHeaders() {
      const filtered = this.headers.filter((header) => header.checked);
      return filtered;
    },
    activeCount()
    {
      return this.entityActiveCount || 0;
    },

    archivedCount()
    {
      return this.entityArchivedCount || 0;
    },

    hasSteps()
    {
      return this.totalItems > 0;
    },

    totalPages()
    {
      return Math.ceil(this.totalItems / this.itemsPerPage);
    },

    currentItem()
    {
      return this.detailItem || (this.filteredSteps.length > 0 ? this.filteredSteps[0] : null);
    },

    currentFilteredIndex()
    {
      return this.filteredSteps.findIndex((step) => step.uid === this.currentItem.uid);
    },
  },

  watch: {
    tab() {
      // Reset to first page when switching tabs
      this.currentPage = 1;
      this.clearSelections();
      this.getSharedSteps();
      this.getEntityCount(this.currentAccount.handle, this.projectKey); // Refresh entity count when switching tabs
    },
    
    searchFilter() {
      if (!this.isInitialized) return;
      // Reset to first page when search changes
      this.currentPage = 1;
      // Clear selections when search changes
      this.clearSelections();
      // Implement server-side search filtering
      this.getSharedSteps();
    },
    
    stepRangeFilter() {
      if (!this.isInitialized) return;
      // Reset to first page when filter changes
      this.currentPage = 1;
      // Clear selections when filter changes
      this.clearSelections();
      // Implement server-side range filtering
      this.getSharedSteps();
    },
    
    referencedByRangeFilter() {
      if (!this.isInitialized) return;
      // Reset to first page when filter changes  
      this.currentPage = 1;
      // Clear selections when filter changes
      this.clearSelections();
      // Implement server-side range filtering
      this.getSharedSteps();
    },
  },

  mounted() {
    this.init();
  },

  created()
  {
    if(!this.dynamicHeaders.sharedSteps) {
      this.initializeHeaders({ type: 'sharedSteps' });
    }
    this.headers = this.dynamicHeaders.sharedSteps;
    sharedStepService = makeSharedStepService(this.$api);
  },

  methods: {
    ...mapActions("headers", ['initializeHeaders']),
    async init() {
      try {
        this.showSkeletonLoader();
        await Promise.all([
          this.getSharedSteps(),
          this.getEntityCount(this.currentAccount.handle, this.projectKey)
        ]);
      } catch (error) {
        console.log(error);
        showErrorToast(this.$swal, 'fetchError', { item: 'templates' }, error?.response?.data);
      } finally {
        this.hideSkeletonLoader();
        // Set initialization flag to true after initial data load
        this.isInitialized = true;
      }
    },



    validateCurrentPage() {
      // Ensure current page is valid after operations
      if (this.totalItems === 0) {
        this.currentPage = 1;
        return;
      }
      
      const maxPages = Math.ceil(this.totalItems / this.itemsPerPage);
      if (this.currentPage > maxPages) {
        this.currentPage = Math.max(1, maxPages);
      }
    },

    async refreshData() {
      // Store current detail state
      const currentDetailUid = this.detailItem?.uid || null;
      const wasInDetailMode = this.isDetailMode;
      
      // Refresh data and validate pagination after operations
      await Promise.all([
        this.getSharedSteps(),
        this.getEntityCount(this.currentAccount.handle, this.projectKey)
      ]);
      
      // If current page is now invalid, fetch again with corrected page
      if (this.currentPage > this.totalPages && this.totalPages > 0) {
        this.currentPage = this.totalPages;
        await this.getSharedSteps();
      }
      
      // Handle detail mode after refresh
      if (wasInDetailMode && currentDetailUid) {
        const foundItem = this.tableData.find(item => item.uid === currentDetailUid);
        if (foundItem) {
          this.detailItem = foundItem;
        } else {
          // Item no longer exists (likely archived/deleted), close detail mode
          this.isDetailMode = false;
          this.detailItem = '';
        }
      }
    },
    clearSelections(){
      this.selectedItems = [];
    },
    updateFilter(isDefault, stepRange, referenceRange)
    {
      if (isDefault == false) {
        this.stepRangeFilter = stepRange;
        this.referencedByRangeFilter = referenceRange;
      } else {
        this.stepRangeFilter = [];
        this.referencedByRangeFilter = [];
      }
    },
    formatCreatedAt(createdAt)
    {
      return formatDate(createdAt, 'MM/dd/yy');
    },

    handleInput(selectedItems)
    {
      this.selectedItems = selectedItems;
    },

    handleDeleteItem(item)
    {
      this.showDeleteConfirmDialog = true;
      this.selectedStep = item;
    },

    handleUnArchiveItem(item)
    {
      this.isArchive = false;
      this.showArchiveConfirmDialog = true;
      this.selectedStep = item;
    },

    handleArchiveItem(item)
    {
      this.isArchive = true;
      this.showArchiveConfirmDialog = true;
      this.selectedStep = item;
    },

    deleteSharedSteps()
    {
      if(!this.isProjectArchived) this.showBulkDeleteConfirmDialog = true;
    },

    async bulkDeleteSharedSteps()
    {
      this.showBulkDeleteConfirmDialog = false;
      const sharedStepIds = this.selectedItems.map(item => item.uid);
      const payload = { sharedStepIds };
      try {
        this.showSkeletonLoader();
        const response = await sharedStepService.deleteSharedSteps(
          this.currentAccount.handle,
          this.projectKey,
          payload
        );
        if (response.status == 200) {
          showSuccessToast(this.$swal, 'deleteSuccess', { item: 'Shared step' });
          this.clearSelections();
          await this.refreshData();
        }
      } catch (err) {
        showErrorToast(this.$swal, 'deleteError', { item: 'Shared step' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    handleChangeActiveStatuses(status)
    {
      this.isArchive = status;
      this.showBulkArchiveUnarchiveDialog = true;
    },

    async bulkChangeStatusSharedSteps()
    {
      this.showBulkArchiveUnarchiveDialog = false;
      const payload = this.selectedItems.map(item => ({
        id: item.uid,
        archived: this.tab != 'archived',
      }));
      try {
        this.showSkeletonLoader();
        const response = await sharedStepService.updateSharedSteps(
          this.currentAccount.handle,
          this.projectKey,
          { sharedSteps: payload }
        );
        if (response.status == 200) {
          showSuccessToast(this.$swal, this.tab != 'archived' ? 'archiveSuccess' : 'unarchiveSuccess', { item: 'Shared step' });
          this.clearSelections();
          await this.refreshData();
        }
      } catch (err) {
        showErrorToast(this.$swal, this.tab != 'archived' ? 'archiveError' : 'unarchiveError', { item: 'Shared step' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    async changeActiveStatusSharedStepItem()
    {
      this.showArchiveConfirmDialog = false;
      const payload = [{
        id: this.selectedStep.uid,
        archived: this.tab != 'archived',
      }];
      try {
        this.showSkeletonLoader();
        const response = await sharedStepService.updateSharedSteps(
          this.currentAccount.handle,
          this.projectKey,
          { sharedSteps: payload }
        );
        if (response.status == 200) {
          showSuccessToast(this.$swal, this.tab != 'archived' ? 'archiveSuccess' : 'unarchiveSuccess', { item: 'Shared step' });
          await this.refreshData();
        }
      } catch (err) {
        showErrorToast(this.$swal, this.tab != 'archived' ? 'archiveError' : 'unarchiveError', { item: 'Shared step' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    async updateSharedStep(sharedStep)
    {
      this.showCreateUpdateSharedStepDialog = false;
      const payload = {
        name: sharedStep.name,
        description: sharedStep.description,
        expectedResult: sharedStep.expectedResult,
        steps: sharedStep.steps,
        expectedResultByStep: sharedStep.expectedResultByStep,
      };
      try {
        this.showSkeletonLoader();
        const response = await sharedStepService.updateSharedStep(
          this.currentAccount.handle,
          this.projectKey,
          sharedStep.uid,
          payload
        );
        if (response.status == 200) {
          showSuccessToast(this.$swal, 'updateSuccess', { item: 'Shared step' });
          await this.refreshData();
        }
      } catch (err) {
        showErrorToast(this.$swal, 'updateError', { item: 'Shared step' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    async createSharedStep(sharedStep)
    {
      this.showCreateUpdateSharedStepDialog = false;
      if(!this._writeStep){
        this.unauthorizedToast;
        return ;
      }
      this.isLoading = true;
      const data = {
        name: sharedStep.name,
        steps: sharedStep.steps,
        expectedResultByStep: sharedStep.expectedResultByStep,
      };
      try {
        this.showSkeletonLoader();
        const response = await sharedStepService.createSharedStep(
          this.currentAccount.handle,
          this.projectKey,
          data
        );
        if (response.status == 200) {
          showSuccessToast(this.$swal, 'createSuccess', { item: 'Shared step' });
          await this.refreshData();
        } else {
          showErrorToast(this.$swal, 'createError', { item: 'Shared step' });
        }
      } catch (err) {
        showErrorToast(this.$swal, 'createError', { item: 'Shared step' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    async deleteSharedStepItem()
    {
      this.showDeleteConfirmDialog = false;
      if(!this._deleteStep){
        this.unauthorizedToast;
        return ;
      }
      try {
        this.showSkeletonLoader();
        const response = await sharedStepService.deleteSharedStep(
          this.currentAccount.handle,
          this.projectKey,
          this.selectedStep.uid
        );
        if (response.status == 200) {
          showSuccessToast(this.$swal, 'deleteSuccess', { item: 'Shared step' });
          await this.refreshData();
        }
      } catch (err) {
        showErrorToast(this.$swal, 'deleteError', { item: 'Shared step' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    handleClickRow(item)
    {
      this.isDetailMode = true;
      this.detailItem = item;
    },

    closeDetail()
    {
      this.isDetailMode = false;
    },

    onCreateSharedStep()
    {
      (this.selectedStep = {
        uid: '',
        name: '',
        steps: [],
        expectedResultByStep: false,
      }),
        (this.showCreateUpdateSharedStepDialog = true);
    },

    handleEditItem(item)
    {
      this.selectedStep = {
      uid: item.uid || '',
      name: item.name || '',
      steps: item.steps ? [...item.steps] : [],
      expectedResultByStep: item.expectedResultByStep || false,
      };
      this.showCreateUpdateSharedStepDialog = true;
    },

    async getSharedSteps() {
     this.isLoading = true;

     try {
      // Calculate offset from current page and items per page
      const offset = (this.currentPage - 1) * this.itemsPerPage;
      
      // Build filters object
      const filters = {};
      if (this.searchFilter) {
        filters.name = this.searchFilter;
      }
      if (this.stepRangeFilter && this.stepRangeFilter.length === 2) {
        filters.minSteps = this.stepRangeFilter[0];
        filters.maxSteps = this.stepRangeFilter[1];
      }
      if (this.referencedByRangeFilter && this.referencedByRangeFilter.length === 2) {
        filters.minReferencedBy = this.referencedByRangeFilter[0];
        filters.maxReferencedBy = this.referencedByRangeFilter[1];
      }

      const response = await sharedStepService.getSharedSteps(
        this.currentAccount.handle,
        this.projectKey,
        {
          limit: this.itemsPerPage,
          offset: offset,
          archived: this.tab === 'archived',
          active: this.tab !== 'archived',
          filters: filters
        }
      );
      
      if (response.status == 200) {
        this.tableData = response.data.items;
        this.totalItems = response.data.count;

        // Validate current page after getting data
        this.validateCurrentPage();
      }
     } catch (err) {
      console.log(err);
      showErrorToast(this.$swal, 'fetchError', { item: 'shared steps' }, err?.response?.data);
      this.isDetailMode = false;
    } finally {
      this.isLoading = false;
    }
  },
    updateFilterStatus(value)
    {
      this.tab = value;
    },

    navigateToPrevious()
    {
      if (this.currentFilteredIndex > 0) {
        this.detailItem = this.filteredSteps[this.currentFilteredIndex - 1];
      }
    },

    navigateToNext()
    {
      if (this.currentFilteredIndex < this.filteredSteps.length - 1) {
        this.detailItem = this.filteredSteps[this.currentFilteredIndex + 1];
      }
    },

    onUpdatePagination(options) {
      const { page, itemsPerPage } = options;
      
      // Debug logging
      console.log('Pagination update:', { 
        newPage: page, 
        newItemsPerPage: itemsPerPage,
        currentPage: this.currentPage,
        currentItemsPerPage: this.itemsPerPage
      });
      
      if (page !== this.currentPage || itemsPerPage !== this.itemsPerPage) {
        this.currentPage = page;
        this.itemsPerPage = itemsPerPage;
        
        // Clear selections when page size changes
        this.selectedItems = [];
        
        this.getSharedSteps();
      }
    },
  },

};
</script>

<style scoped>
p.mb-0 {
  font-size: 14px;
  color: #0c111d;
}

h5.used-in {
  font-size: 14px;
  color: #667085;
}
.action-btn-wrapper {
  position: sticky;
    bottom: 0;
    background-color: white;
    align-items: flex-end;
    display: flex;
    justify-content: flex-end;
    z-index: 8;
}
</style>
