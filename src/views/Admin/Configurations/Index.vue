<template>
  <div class="pl-3 pt-3">
    <BaseHeader
      :title="$t('configurations.title')"
      :btn-text="$t('configurations.add_configuration')"
      :is-project-archived="isProjectArchived"
      @create="openCreateDialog"
    />
    <v-card
      class="white py-6 px-6 mt-3 app-height-global"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <ActiveEmptyState
        v-if="configurations.length === 0 && !skeletonLoaderState"
        :image-src="require('@/assets/png/empty-config.png')"
        image-max-width="323px"
        :title="$t('configurations.emptyTitle')"
        :button-text="$t('configurations.add_configuration')"
        button-color="primary"
        class="mt-10"
        :is-project-archived="isProjectArchived"
        @button-click="openCreateDialog"
      >
        <template #description>
          <p class="desc">
            {{ $t('configurations.emptyDescription') }}
          </p>
        </template>
      </ActiveEmptyState>

      <template v-else>
        <v-row
          justify="space-between"
          class="align-center mb-4"
        >
          <v-col
            cols="12"
            md="auto"
            class="d-flex align-center"
          >
            <v-text-field
              v-if="!skeletonLoaderState"
              v-model="searchFilter"
              prepend-inner-icon="mdi-magnify"
              :placeholder="$t('search_by_name')"
              class="text-field mt-0 pa-0 mr-3 rounded-lg field-theme custom-prepend pa-0"
              height="40"
              clear-icon="mdi-close-circle"
              clearable
              background-color="#F9F9FB"
              hide-details
            >
              <template #prepend-inner>
                <SearchIcon />
              </template>
            </v-text-field>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-3"
              width="235"
              height="40"
              type="button"
            />
          </v-col>

          <v-col
            cols="12"
            md="auto"
            class="mr-3"
          >
            <v-row
              justify="end"
              class="align-center"
            >
              <SettingsMenu 
                table-type="configurations"
              />
            </v-row>
          </v-col>
        </v-row>
    
        <v-data-table
          v-if="!skeletonLoaderState"
          :headers="filteredHeaders"
          :items="filteredConfigurations"
          :search="searchFilter"
          class="custom-table data-table-style"
          v-resize-columns="{ type: 'configurations' }"
          :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
          hide-default-footer
          disable-pagination
        >
          <template #[`item.name`]="{ item }">
            <v-tooltip
              bottom
              left
              max-width="485px"
              :disabled="item.name.length < 61"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <div 
                  class="custom-attribute text-truncate font-weight-bold"
                  v-bind="attrs"
                  v-on="on"
                >
                  {{ item.name }}
                </div>
              </template>
              <span>{{ item.name }}</span>
            </v-tooltip>
          </template>

          <template #[`item.options`]="{ item }">
            <div class="text-theme-table-text">
              {{ item.options.length }} 
            </div>
          </template>

          <template #[`item.actions`]="{ item }">
            <div class="d-flex flex-row justify-end">
              <v-btn
                v-if="item.archivedAt == null"
                icon
                small
                class="mr-2"
                :disabled="isProjectArchived"
                @click="!isProjectArchived && editConfiguration(item)"
              >
                <EditIcon />
              </v-btn>
              <v-btn
                icon
                small
                :disabled="isProjectArchived"
                @click="!isProjectArchived && handleDeleteItem(item)"
              >
                <DeleteIcon />
              </v-btn>
            </div>
          </template>
        </v-data-table>
        <template v-else>
          <SharedStepSkeleton class="mt-6" />
        </template>

        <Pagination
          v-if="!skeletonLoaderState && totalRows > 0"
          :page="currentPage"
          :items-per-page="perPage"
          :total-pages="totalPages"
          :total-items="totalRows"
          @update:pagination="onUpdatePagination"
        />
      </template>
    </v-card>

    <ConfigurationsConfirmDialog
      v-model="showDeleteConfirmDialog"
      :title="$t('configurations.deleteConfirm', { name: selectedConfiguration.name })"
      :description="$t('configurations.deleteDescription', { count: selectedConfiguration.options.length })"
      @cancel="showDeleteConfirmDialog = false"
      @close="showDeleteConfirmDialog = false"
      @delete="deleteConfigurationItem"
    />

    <configuration-dialog
      v-model="showConfigurationDialog"
      :edit-mode="editMode"
      :configuration="selectedConfiguration"
      @close="showConfigurationDialog = false"
      @submit="handleConfigurationSubmit"
    />
  </div>
</template>

<script>
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue'
import BaseHeader from "@/components/base/Header.vue";
import handleLoading from '@/mixins/loader.js'
import makeConfigurationService from '@/services/api/configuration' 
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import EditIcon from '@/assets/svg/edit.svg';
import DeleteIcon from '@/assets/svg/delete.svg';
import projectStatus from '@/mixins/projectStatus';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import SearchIcon from '@/assets/svg/search-icon.svg';
import { mapGetters, mapActions } from 'vuex';
import ConfigurationsConfirmDialog from '@/views/Admin/Configurations/ConfirmDialog.vue';
import ConfigurationDialog from '@/views/Admin/Configurations/ConfigurationDialog.vue';
import SharedStepSkeleton from '@/components/Skeletons/Admin/SharedStep/SharedStepSkeleton.vue';
import Pagination from '@/components/base/Pagination.vue';

let configurationService;

export default {
  name: 'ConfigurationsView',
  
  components: {
    BaseHeader,  
    ActiveEmptyState,
    EditIcon,
    SearchIcon,
    DeleteIcon, 
    SettingsMenu,
    ConfigurationsConfirmDialog,
    SharedStepSkeleton,
    ConfigurationDialog,
    Pagination
  },

  mixins: [handleLoading, projectStatus],

  data() {
    return {
      configurations: [],
      searchFilter: '',
      headers: [],
      showDeleteConfirmDialog: false,
      showConfigurationDialog: false,
      editMode: false,
      selectedConfiguration: {
        uid: '',
        name: '',
        description: '',
        options: []
      },
      currentPage: 1,
      perPage: 10,
      totalRows: 0
    }
  },

  computed: {
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders',
    }),
    filteredHeaders() {
      const filtered = this.headers.filter((header) => header.checked);
      return filtered;
    },
    filteredConfigurations() {
      let filteredConfigurationsData = this.configurations;
      if (this.searchFilter) {
        filteredConfigurationsData = filteredConfigurationsData.filter((item) =>
          item.name.toLowerCase().includes(this.searchFilter.toLowerCase())
        );
      }
      return filteredConfigurationsData;
    },
    totalPages() {
      return Math.ceil(this.totalRows / this.perPage);
    }
  },

  created() {
    configurationService = makeConfigurationService(this.$api);
    if(!this.dynamicHeaders.configurations) {
      this.initializeHeaders({ type: 'configurations' });
    }
    this.headers = this.dynamicHeaders.configurations;
    this.init([this.getConfigurations()])
  },

  methods: {
    ...mapActions('headers', ['initializeHeaders']),
    
    openCreateDialog() {
      this.editMode = false;
      this.selectedConfiguration = {
        uid: '',
        name: '',
        description: '',
        options: []
      };
      this.showConfigurationDialog = true;
    },
  
    async getConfigurations() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      try {
        const response = await configurationService.getConfigurations(
          handle, 
          projectKey, 
          this.perPage, 
          this.currentPage 
        );
        this.configurations = response.data?.configurations || [];
        this.totalRows = response.data?.total || 0;
        return response.data?.configurations;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
        return [];
      }
    },
    
    handleDeleteItem(item) {
      this.showDeleteConfirmDialog = true;
      this.selectedConfiguration = item;
    },

    async deleteConfigurationItem() {
      this.showDeleteConfirmDialog = false;
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      
      try {
        this.showSkeletonLoader();
        await configurationService.deleteConfiguration(handle, projectKey, this.selectedConfiguration.uid);
        await this.getConfigurations();
        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'configuration group' });
      } catch (err) {
        showErrorToast(this.$swal, 'deleteError', { item: 'configuration group' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    editConfiguration(item) {
      this.editMode = true;
      this.selectedConfiguration = { ...item };
      this.showConfigurationDialog = true;
    },

    async handleConfigurationSubmit(payload) {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      
      try {
        this.showSkeletonLoader();
        if (this.editMode) {
          await configurationService.updateConfiguration(
            handle, 
            projectKey, 
            this.selectedConfiguration.uid,
            payload
          );
          showSuccessToast(this.$swal, 'updateSuccess', { item: `${this.selectedConfiguration.name} group` });
        } else {
          await configurationService.createConfigurations(handle, projectKey, payload);
          showSuccessToast(this.$swal, 'createSuccess', { item: `${payload.name} group` });
        }
        await this.getConfigurations();
        this.showConfigurationDialog = false;
      } catch (err) {
        console.log(err);
        const toastType = this.editMode ? 'updateError' : 'createError';
        showErrorToast(this.$swal, toastType, { item: 'configuration' }, err?.response?.data);
      } finally {
        this.hideSkeletonLoader();
      }
    },

    async onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;
      
      if (newPage !== this.currentPage || newItemsPerPage !== this.perPage) {
        this.currentPage = newPage;
        this.perPage = newItemsPerPage;
        
        this.showSkeletonLoader();
        try {
          await this.getConfigurations();
        } catch(err) {
          console.error(err)
        } finally {
          this.hideSkeletonLoader();
        }
      }
    },
  }
}
</script> 

<style scoped>
.mt-10{
  margin-top: 10rem !important;
}
.desc {
  max-width: 480px;
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  margin-top: 16px;
}

.custom-table {
  border-radius: 8px;
}

.data-table-style {
  width: 100%;
}

.data-table-style .v-data-table__wrapper {
  border-radius: 8px;
}

.data-table-style .v-data-table__wrapper table {
  border-collapse: collapse;
}

.data-table-style .v-data-table__wrapper th {
  background-color: #F9FAFB !important;
  color: #475467 !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  border-bottom: 1px solid #EAECF0 !important;
  text-transform: none !important;
}

.data-table-style .v-data-table__wrapper td {
  border-bottom: 1px solid #EAECF0 !important;
  color: #344054 !important;
  font-size: 14px !important;
}

.data-table-style .v-data-table__wrapper tr:hover {
  background-color: #F9FAFB !important;
}

.search-field {
  max-width: 320px;
}

.header_text {
  color: #475467;
  font-weight: 600;
  font-size: 14px;
}

.text-theme-table-text {
  color: #344054;
  font-size: 14px;
}

.custom-attribute {
  max-width: 400px;
}

.list-item {
  min-height: 35px !important;
}

.list-item:hover {
  background-color: #F9FAFB !important;
}

.v-list-item__icon {
  margin: 4px 0 !important;
}
</style>
