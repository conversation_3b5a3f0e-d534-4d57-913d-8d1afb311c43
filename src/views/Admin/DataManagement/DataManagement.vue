<template>
  <div class="pl-3 pt-3">
    <div>
      <DataManagementHeader />
      <DataConfiguration />
    </div>
  </div>
</template>

<script>
import DataManagementHeader from "@/components/Admin/DataManagement/DataManagementHeader.vue";
import DataConfiguration from "@/components/Admin/DataManagement/DataConfiguration.vue";

export default {
  name: 'DataManagementPage',
  components: { DataManagementHeader, DataConfiguration },
};
</script>
