<template>
  <v-container
    fluid
  >
    <section
      class="white fill-height v-align-start font-inter rounded-lg app-height-global pb-3 ma-3 mr-0 flex-grow-1 pt-6"
    >
      <div
        class="back-btn pl-6"
        @click="goBack"
      >
        <v-icon color="blue">
          mdi-chevron-left
        </v-icon>
        {{ $t('backToWorkspace') }}
      </div>
      <div class="body_section">
        <div class="d-flex flex-column align-center justify-center w-full">
          <v-col
            cols="12"
            class="block main-area mt-4"
            :style="{ 'max-width': '438px' }"
          >
            <h2 class="mb-1">
              {{ $t('contactUs') }}
            </h2>
            <p class="text-body-2 mb-8">
              {{ $t('gotQuestion') }}
            </p>
            <v-form ref="contactForm">
              <div class="text-left">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('subject') }}
                </v-label>
                <v-select
                  v-model="subject"
                  class="rounded-lg field-theme custom-prepend"
                  :items="topics"
                  style="padding: 0%;"
                  background-color="#F9F9FB"
                  item-text="label"
                  item-value="value"
                  height="38px"
                  :placeholder="$t('subject')"
                  append-icon="mdi-chevron-down"
                  :menu-props="{ offsetY: true }"
                  :rules="titleValidation"
                />
              </div>
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('email') }}
                </v-label>
                <v-text-field
                  id="email"
                  v-model="email"
                  :rules="emailValidation"
                  :placeholder="$t('inputPlaceholder', { field: $t('email') })"
                  height="38"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="loading"
                />
              </div>
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('addAttachment') }}
                </v-label>
                <FileInput
                  :files.sync="files"
                  :type="'.jpg,.jpeg,.png,.mp4'"
                  @deleteAttachment="deleteAttachment"
                />
              </div>
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('message') }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-textarea
                  v-model="message"
                  auto-grow
                  style="padding: 0%;"
                  background-color="#F9F9FB"
                  placeholder="Your message"
                  class="field-theme item-area"
                />
              </div>
            </v-form>
            
            <div
              v-if="isEmailCooldown"
              class="mt-4 pa-3 warning--text text-center rounded"
              style="background-color: #fff3cd; border: 1px solid #ffeaa7;"
            >
              <v-icon
                color="orange"
                class="mr-2"
              >
                mdi-clock-outline
              </v-icon>
              {{ $t('emailCooldown') }}
            </div>
          </v-col>
        </div>
      </div>
      <div class="d-flex justify-end pt-8 pr-10">
        <v-btn
          height="40px"
          color="primary"
          depressed
          :class="{ 'v-btn--disabled': loading || !isFormValid }"
          :disabled="loading || !isFormValid"
          :loading="loading"
          class="text-capitalize btn-theme rounded-lg mt-2"
          :style="{ width: $vuetify.breakpoint.smAndDown ? '100%' : '150px' }"
          @click="handleSubmit"
        >
          {{ $t('inviteUser.send') }}
        </v-btn>
      </div>
    </section>
  </v-container>
</template>
  
<script>
import FileInput from "@/components/base/FileInput.vue";
import { requiredAndMax255FieldValidationRules, emailValidationRules } from '@/utils/validation';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import makeContactService from '@/services/api/contact';
import { executeApiQuery } from '@/composables/utils/useApiQuery';

export default {
  name: 'ReportsView',
  components: {
    FileInput
  },
  data() {
    return {
      subject: '',
      email: '',
      files: [],
      message: '',
      loading: false,
      uploadingFiles: false,
      contactReport: null,
      recaptchaToken: null,
      isEmailCooldown: false,
      titleValidation: requiredAndMax255FieldValidationRules(),
      emailValidation: emailValidationRules(),
      topics: [
        { label: 'Bug Report', value: 'bug' },
        { label: 'Feature Request', value: 'feature' },
        { label: 'General Feedback', value: 'feedback' },
      ],
    };
  },
  computed: {
    isFormValid() {
      const basicEmailRegex = /\S+@\S+\.\S+/;     
      const valid = this.subject &&
        this.email &&
        this.message &&
        basicEmailRegex.test(this.email) &&
        !this.isEmailCooldown;
      return valid;
    },
    hasFilesToUpload() {
      return this.files && this.files.length > 0 && this.files.some(file => !file.uid);
    },  
  },
  created() {
    // this.loadRecaptchaScript();
    this.contactService = makeContactService(this.$api);
  },
  beforeDestroy() {
    // Clean up global callback
    if (window.onRecaptchaSubmit) {
      delete window.onRecaptchaSubmit;
    }
    // Clean up cooldown timeout
    if (this.cooldownTimeout) {
      clearTimeout(this.cooldownTimeout);
    }
    // Remove reCAPTCHA script && UI if it exists
    // if(document.getElementById('recaptcha-enterprise')) {
    //   this.removeRecaptchaScript();
    // }
  },
  methods: {
    goBack() {
      this.$router.push({
        name: 'Workspace',
        params: { handle: this.$route.params.handle },
      });
    },
    async deleteAttachment(attachmentUid) {
      try {
        if (attachmentUid && this.contactReport) {
          const params = {
            handle: this.$route.params.handle,
            uid: this.contactReport.uid
          };
          await this.contactService.cleanupFailedContactAttachment({ params });
          showSuccessToast(this.$swal, this.$t('success.deleteAttachment'));
        }
      } catch (err) {
        showErrorToast(this.$swal, this.$t('error.failedToDeleteAttachment'), {}, err?.response?.data);
      }
    },
    // loadRecaptchaScript() {
    //   const scriptId = 'recaptcha-enterprise';
    //   const script = document.createElement('script');
    //   script.id = scriptId;
    //   script.src = `https://www.google.com/recaptcha/enterprise.js?render=6LfTtWwrAAAAAGYkZV5xxD7LHBGgcLs5Mng2UY3z`;
    //   script.async = true;
    //   script.defer = true;

    //   document.head.appendChild(script);
    // },
    // removeRecaptchaScript() {
    //   const script = document.getElementById('recaptcha-enterprise');
    //   if (script) {
    //     document.head.removeChild(script);
    //   }
    //   const badge = document.querySelector('.grecaptcha-badge');
    //   if (badge && badge.parentNode) {
    //     badge.parentNode.removeChild(badge);
    //   }
    // },
    async uploadContactAttachment(file, reportUid) {
      const handle = this.$route.params.handle;

      try {
        const payload = {
          fileName: file.name,
          size: file.size,
          fileType: file.type,
        };
        const params = {
          handle,
          uid: reportUid
        };
        const response = await this.contactService.uploadContactAttachment({ params, payload });

        if (response.status === 200) {
          file.uid = response.data.uid;
          return { success: true, attachmentUid: response.data.uid };
        } 

      } catch (error) {
        console.error(error);
        showErrorToast(this.$swal, this.$t('error.failedToUploadAttachment'));
      }
    },

    async saveAttachments() {
      if (!this.contactReport) {
        return false;
      }

      if (!this.files || this.files.length === 0) {
        return true; 
      }

      this.uploadingFiles = true;

      try {
        const filesToUpload = this.files.filter(file => !file.uid);
        const uploadPromises = filesToUpload.map(async (file) => {
          try {
            const result = await this.uploadContactAttachment(file, this.contactReport.uid);
            return result;
          } catch (err) {
            showErrorToast(this.$swal, this.$t('error.failedToUploadAttachment'));
            return { success: false, error: err, fileName: file.name };
          }
        });
        const uploadResults = await Promise.all(uploadPromises);
        const successfulUploads = uploadResults.filter(result => result.success === true);
        const failedUploads = uploadResults.filter(result => result.success === false);

        if (failedUploads.length > 0 && successfulUploads.length === 0) {
          return false;
        }

        return true;

      } catch (error) {
        showErrorToast(this.$swal, this.$t('error.unableToUploadAttachment'), {}, error);
        return false;
      } finally {
        this.uploadingFiles = false;
      }
    },

    // async waitForRecaptcha(maxRetries = 10, delay = 500) {
    //   for (let i = 0; i < maxRetries; i++) {
    //     // Check if grecaptcha is available on window object
    //     if (typeof window.grecaptcha !== 'undefined' && 
    //         window.grecaptcha.enterprise && 
    //         window.grecaptcha.enterprise.execute) {
    //       return true;
    //     }
    //     // Wait 500ms and try again
    //     await new Promise(resolve => setTimeout(resolve, delay));
    //   }
    // },

    async handleSubmit() {
      if (!this.isFormValid) {
        showErrorToast(this.$swal, this.$t('error.requiredFieldsError'));
        return;
      }

      // const recaptchaSiteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY;
      // if (!recaptchaSiteKey) {
      //   console.error('reCAPTCHA site key not configured');
      //   showErrorToast(this.$swal, this.$t('error.sendEmailError'));
      //   return;
      // }

      try {
        this.loading = true;
        
        // await this.waitForRecaptcha();
        
        // const token = await window.grecaptcha.enterprise.execute(recaptchaSiteKey, {
        //   action: 'bugReport'
        // });
        
        // this.recaptchaToken = token;
        
        await this.submitReport();
      } catch (error) {
        console.error('reCAPTCHA execution failed:', error);
      } finally {
        this.loading = false;
      }
    },

    async submitReport() {
      // if (!this.isFormValid || !this.recaptchaToken) {
      //   if (!this.recaptchaToken) {
      //     showErrorToast(this.$swal, this.$t('error.sendEmailError'));
      //   } else {
      //     showErrorToast(this.$swal, this.$t('error.requiredFieldsError'));
      //   }
      //   return;
      // }
      if(!this.isFormValid){
        showErrorToast(this.$swal, this.$t('error.requiredFieldsError'));
      }

      const handle = this.$route.params.handle;

      try {
        const cacheKey = 'contactSubmission';
        await executeApiQuery(
          cacheKey,
          async () => {
            const payload = {
              subject: this.subject,
              email: this.email,
              message: this.message,
              recaptchaToken: this.recaptchaToken,
            };
            const response = await this.contactService.createContactUsReport(handle, payload);
            this.contactReport = response.data;

            await this.saveAttachments();

            const params = {
              handle,
              uid: this.contactReport.uid
            };

            await this.contactService.sendBugReportEmail({ params });
            
            return 'success';
          },
          { 
            debounce: true, 
            staleTime: 20000 
          }
        );
        
        // Email sent successfully - start cooldown immediately
        this.startEmailCooldown();
        
        showSuccessToast(this.$swal, this.$t('emailSent'));
        this.finishAndGoBack();

      } catch (error) {
        showErrorToast(this.$swal, this.$t('sendEmailError'));
      }
    },

    finishAndGoBack() {
      this.subject = '';
      this.email = '';
      this.files = [];
      this.message = '';
      this.contactReport = null;
      this.uploadingFiles = false;
      this.recaptchaToken = null;
      this.$nextTick(() => {
        if (this.$refs.contactForm) {
          this.$refs.contactForm.resetValidation();
        }
      });
    },

    startEmailCooldown() {
      // Clear any existing timeout
      if (this.cooldownTimeout) {
        clearTimeout(this.cooldownTimeout);
      }

      // Disable send button immediately
      this.isEmailCooldown = true;

      // Re-enable after 20 seconds
      this.cooldownTimeout = setTimeout(() => {
        this.isEmailCooldown = false;
        this.cooldownTimeout = null;
      }, 20000);
    },

    resetForm() {
        this.subject = '';
        this.email = '';
        this.files = [];
        this.message = '';
        this.contactReport = null;
        this.uploadingFiles = false;
        this.recaptchaToken = null;
        this.isEmailCooldown = false; 
        if (this.cooldownTimeout) {
          clearTimeout(this.cooldownTimeout);
          this.cooldownTimeout = null;
        }
        this.$nextTick(() => {
          if (this.$refs.contactForm) {
            this.$refs.contactForm.resetValidation();
          }
        });
    },
  },
};
</script>
  
<style scoped>
  .back-btn {
    text-decoration: none;
    color: #0c2ff3 !important;
    font-weight: 600;
    cursor: pointer;
  }
  
  h2 {
    font-size: 24px !important;
    font-weight: 700;
    color: #18181A;
  }
  
  .item-area {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
  }
  
  .body_section {
    margin-top: 0rem;
  }

  .v-btn--disabled {
    background-color: #e0e0e0 !important;
    color: #9e9e9e !important;
    cursor: not-allowed;
  }

  .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }
</style>
