<template>
  <v-container
    fluid
    style="padding: 0"
  >
    <IntegrationHeader
      :filter="filter"
      :added-item-count="totalAdded"
      :all-item-count="totalAll"
      @update-filter="updateFilter"
      @select-integration="handleIntegrationSelect"
    />

    <Loader
      v-if="loaderState"
      class="integration-loader"
    />
    <template v-else-if="totalAdded == 0 && filter != 'all'">
      <div class="mt-3 mb-0 py-10 white rounded-lg mx-0 empty-integration-container d-flex justify-center align-center">
        <ActiveEmptyState
          :write-entity="_writeIntegration"
          :image-src="emptyIntegrationImage"
          :title="$t('integrations.empty_state.no_added_apps')"
          :button-text="$t('integrations.button_custom_app.text')"
          :button-route="{ name: 'create-integration' }"
          button-color="primary"
        >
          <template #extra-buttons>
            <v-btn
              color="gray-100"
              depressed
              height="40px"
              class="mr-5 text-capitalize rounded-lg px-6"
              @click="updateFilter('all')"
            >
              {{ $t('integrations.button_library.text') }}
            </v-btn>
          </template>
        </ActiveEmptyState>
      </div>
    </template>

    <template v-else-if="totalAll == 0">
      <div class="mt-3 mb-0 white rounded-lg mx-0 available-integration-container d-flex justify-center align-center">
        <ArchivedEmptyState
          :image-src="emptyIntegrationImage"
          :title="$t('integrations.empty_state.no_available_apps')"
        />
      </div>
    </template>

    <template v-else>
      <v-card
        class="py-6 px-6 mt-3 integration-height"
        rounded="lg"
        elevation="0"
        width="100%"
      >
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <SearchComponent
              :placeholder="$t('search')"
              class="mr-3"
              @update:search="search = $event"
            />
            <FilterIntegration
              v-if="filter != 'all'"
              :services="services"
              :projects="projects"
              @applyFilters="applyFilters"
            />
          </div>
          <div class="d-flex align-center justify-end">
            <ToggleView
              :table="table"
              class="mr-3"
              @toggle-table="toggleTable"
            />
            <IntegrationSettingsMenu
              v-if="table && filter === 'added'"
              table-type="integration"
              :required-items="['Name', 'Projects', 'Actions']"
            />
          </div>
        </div>

        <!-- IntegrationTable component for table view -->
        <IntegrationTable
          v-if="table && filter != 'all' && filteredItems.length > 0"
          :filtered-headers="filteredHeaders"
          :filtered-items="filteredItems"
          :item-key="itemKey"
          :row-class="rowClass"
          :total-items="totalRows"
          :current-page="currentPage"
          :items-per-page="perPage"
          :skeleton-loader-state="loaderState"
          @delete-item="confirmDeleteIntegration"
          @refresh="fetchAddedIntegrations"
          @reauth="handleReauth"
          @show-error-details="showErrorDetails"
          @personal-token="handlePersonalToken"
          @update-pagination="onUpdatePagination"
        />

        <!-- Empty state -->
        <div
          v-else-if="table && filter != 'all'"
          class="d-flex align-center justify-center py-8"
        >
          <p
            class="text-subtitle-1"
            style="color: #667085"
          >
            {{ $t('integrations.no_integrations') }}
          </p>
        </div>

        <IntegrationCardAll
          v-else-if="!table && filter != 'added'"
          :tools="tools"
          @select-integration="handleIntegrationSelect"
        />

        <!-- Card View (if needed) -->
        <v-row
          v-else-if="!table && filter != 'all'"
          class="mt-6"
        >
          <v-col
            v-for="(item, index) in filteredItems"
            :key="index"
            cols="4"
          >
            <IntegrationCard
              :item="item"
              :filtered-headers="filteredHeaders"
              @delete-item="confirmDeleteIntegration"
              @refresh="fetchAddedIntegrations"
              @reauth="handleReauth"
              @show-error-details="showErrorDetails"
              @personal-token="handlePersonalToken"
            />
          </v-col>
        </v-row>

        <!-- No data message -->
        <div
          v-else
          class="py-10"
        >
          <IntegrationListAll
            :tools="tools"
            @select-integration="handleIntegrationSelect"
          />
        </div>
      </v-card>
    </template>

    <!-------------Delete Integration Dialog-------------------->

    <IntegrationDeleteDialog
      v-if="selectedIntegration"
      v-model="showConfirmDeleteDialog"
      :title="$t('integrations.delete_integration_dialog.title', { integrationName: selectedIntegration.name })"
      @close="closeDeleteDialog"
      @handleConfirmClick="confirmDeleteIntegration"
    >
      <template #content>
        <v-flex class="mt-4 mb-8 flex-column">
          <p class="text-start">
            {{ $t('integrations.delete_integration_dialog.info') }}
          </p>
        </v-flex>
      </template>
      <template #footer>
        <v-row>
          <v-col cols="6">
            <v-btn
              depressed
              height="40px"
              width="100%"
              class="text-capitalize btn-theme rounded-lg black--text mt-2"
              background-color="#F2F4F7"
              @click="closeDeleteDialog"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn
              height="40px"
              width="100%"
              class="text-capitalize btn-theme rounded-lg white--text mt-2"
              color="danger"
              dark
              @click="deleteIntegration"
            >
              {{ $t('integrations.delete_integration_dialog.confirm_button') }}
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </IntegrationDeleteDialog>
    <ReAuthenticateDialog
      v-model="showReAuthenticateDialog"
      @close="closeReauthDialog"
      @save="handleReauthToken"
    />
    <ErrorDetailsSidebar
      v-model="showErrorSidebar"
      :errors="selectedItemErrors"
      :integration-uid="selectedIntegration.uid"
      @all-errors-resolved="fetchAddedIntegrations"
    />
  </v-container>
</template>

<script>
import IntegrationHeader from '@/components/Integration/IntegrationHeader.vue';
import ActiveEmptyState from '@/components/base/ActiveEmptyState.vue';
import ArchivedEmptyState from '@/components/base/ArchivedEmptyState.vue';
import IntegrationTable from '@/components/Integration/IntegrationTable.vue';
import IntegrationCard from '@/components/Integration/IntegrationCard.vue';
import IntegrationListAll from '@/components/Integration/IntegrationListAll.vue';
import IntegrationCardAll from '@/components/Integration/IntegrationCardAll.vue';
import Loader from '@/components/base/Loader';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import ToggleView from '@/components/Project/ToggleView.vue';
import { showErrorToast, showSuccessToast } from '@/utils/toast';
import handleLoading from '@/mixins/loader.js';
import FilterIntegration from '@/components/Integration/FilterIntegration.vue';
import IntegrationDeleteDialog from '@/components/Integration/IntegrationDeleteDialog.vue';
import makeIntegrationsService from '@/services/api/integrations';
import { mapActions, mapGetters } from 'vuex/dist/vuex.common.js';
import IntegrationSettingsMenu from '@/components/Integration/IntegrationSettingsMenu.vue';
import ErrorDetailsSidebar from '@/components/Integration/ErrorDetailsSidebar.vue';
import ReAuthenticateDialog from '@/components/Integration/ReAuthenticateDialog.vue';
import dayjs from 'dayjs';
import jiraIcon from '@/assets/png/jira.png';
import githubIcon from '@/assets/png/github.png';
import testrailIcon from '@/assets/png/testrail.png';
import { encryptObject } from '@/utils/encryption';
import makeEncryptionService from '@/services/api/encryption';

export default {
  name: 'IntegrationIndex',
  components: {
    IntegrationDeleteDialog,
    IntegrationHeader,
    ActiveEmptyState,
    IntegrationTable,
    IntegrationCard,
    IntegrationListAll,
    IntegrationCardAll,
    Loader,
    SearchComponent,
    FilterIntegration,
    ToggleView,
    ArchivedEmptyState,
    IntegrationSettingsMenu,
    ErrorDetailsSidebar,
    ReAuthenticateDialog,
  },
  mixins: [handleLoading],
  data() {
    return {
      handle: this.$route.params.handle,
      title: 'Main Menu',
      filteredMenuHeaders: null,
      totalAdded: 0,
      totalAll: 0,
      filter: 'added',
      search: '',
      table: true,
      tools: [],
      headers: [],
      filteredItems: [],
      itemKey: 'name',
      rowClass: 'integration-row',
      showConfirmDeleteDialog: false,
      integrationImages: {
        'jira.png': jiraIcon,
        'github.png': githubIcon,
        'testrail.png': testrailIcon,
      },
      originalItems: [],
      services: [],
      projects: {},
      filteredTotal: 0,
      tableType: 'integration',
      hasErrors: false,
      showErrorSidebar: false,
      selectedItemErrors: [],
      showReAuthenticateDialog: false,
      reAuthUid: null,
      reAuthService: null,
      emptyIntegrationImage: new URL('@/assets/png/empty-integrations.png', import.meta.url).href,
      originalTools: [],
      selectedIntegration: {},
      totalRows: 0,
      currentPage: 1,
      perPage: 10,
    };
  },

  computed: {
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders',
      latestPublicKey: 'encryption/latestPublicKey',
    }),
    filteredHeaders() {
      if (!this.dynamicHeaders.integration) return [];
      return this.dynamicHeaders.integration.filter((header) => header.checked);
    },
    totalPages() {
      return Math.ceil(this.totalRows / this.perPage);
    },
    _deleteIntegration() {
      return this.authorityTo('delete_integration');
    },
    _writeIntegration() {
      return this.authorityTo('write_integration');
    },
  },

  watch: {
    search: {
      handler() {
        this.currentPage = 1; // Reset to first page when searching
        this.filterItems();
      },
      immediate: true,
    },
    filter: {
      handler() {
        // Reinitialize headers if they're not properly set
        if (
          !this.headers.length ||
          this.headers.some((h) => !h.checked && ['Name', 'Projects', 'Actions'].includes(h.text))
        ) {
          if (!this.dynamicHeaders.integration) {
            this.initializeHeaders({ type: 'integration' });
          }
          this.headers = this.dynamicHeaders.integration;
        }
      },
      immediate: true,
    },
    'dynamicHeaders.integration': {
      handler(newHeaders) {
        if (newHeaders) {
          this.headers = newHeaders;
        }
      },
      deep: true,
    },
    originalItems: {
      handler() {
        if (this.search === '') {
          this.filteredItems = [...this.originalItems];
        }
      },
      deep: true
    }
  },

  created() {
    if (!this.dynamicHeaders.integration) {
      this.initializeHeaders({ type: 'integration' });
    }
    this.fetchPublicKey({ service: 'integration' });
    this.headers = this.dynamicHeaders.integration;
    this.checkUrlStatus();
  },

  async mounted() {
    await this.init([this.fetchAvailableIntegrations(), this.fetchAddedIntegrations()]);
  },

  methods: {
    ...mapActions('headers', ['initializeHeaders', 'updateHeaders']),
    ...mapActions('integration', ['setIntegrationError']),
    ...mapActions('encryption', ['fetchPublicKey']),
    filterItems() {
      const searchTerm = this.search.toLowerCase();

      if (this.filter === 'all') {
        // Filter tools for all view
        this.tools = this.originalTools
          .map((category) => ({
            ...category,
            items: category.items.filter(
              (item) =>
                item.name.toLowerCase().includes(searchTerm) || item.description.toLowerCase().includes(searchTerm)
            ),
          }))
          .filter((category) => category.items.length > 0);
      } else {
        // Filter added integrations
        if (!searchTerm) {
          this.filteredItems = [...this.originalItems];
          this.totalRows = this.originalItems.length;
          return;
        }

        this.filteredItems = this.originalItems.filter((integration) => {
          return (
            integration.name.toLowerCase().includes(searchTerm) ||
            integration.service.toLowerCase().includes(searchTerm)
          );
        });
        this.totalRows = this.filteredItems.length;
      }
    },
    openReauthDialog() {
      this.showReAuthenticateDialog = true;
    },
    closeReauthDialog() {
      this.showReAuthenticateDialog = false;
    },
    async handleReauth(uid, service) {
      try {
        // Find the integration item from originalItems
        const integration = this.originalItems.find((item) => item.uid === uid);

        if (!integration) {
          throw new Error('Integration not found');
        }
        if (integration.type !== 'oauth') {
          this.reAuthUid = uid;
          this.reAuthService = service;
          this.openReauthDialog();
        } else {
          const integrationService = makeIntegrationsService(this.$api);
          const params = `integrationUid=${uid}`;
          const response = await integrationService.getRedirectUrl(this.handle, service, params);
          const redirectUrl = response.data?.url || response.data;
          window.location.href = redirectUrl;
        }
      } catch (error) {
        console.error('Redirect error:', error);
        showErrorToast(this.$swal, error.response?.data?.message || 'Failed to get authorization URL');
      }
    },
    async handleReauthToken({ token, username }) {
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const encryptionService = makeEncryptionService(this.$api);

        // Get the latest public key directly from the encryption service
        const publicKeyResponse = await encryptionService.getPublicKeyInString('integration');
        const latestPublicKey = publicKeyResponse.data;

        // Encrypt both token and username
        const { encryptedFields, symmetricKeyData } = await encryptObject(
          {
            accessToken: token,
          },
          latestPublicKey
        );

        const body = {
          token: encryptedFields.accessToken,
          username: username,
          symmetricKeyData,
        };

        await integrationsService.reAuthIntegration(this.handle, this.reAuthUid, body);

        showSuccessToast(this.$swal, this.$t('integrations.success.reauth_success'));
        this.closeReauthDialog();
        await this.fetchAddedIntegrations();
      } catch (error) {
        console.error('Reauth error:', error);
        showErrorToast(this.$swal, error.response?.data?.message || this.$t('integrations.error.reauth_failed'));
      }
    },
    closeDeleteDialog() {
      this.showConfirmDeleteDialog = false;
    },
    confirmDeleteIntegration(integration) {
      this.selectedIntegration = integration;
      this.showConfirmDeleteDialog = true;
    },
    async deleteIntegration() {
      if (!this._deleteIntegration)
        return showErrorToast(this.$swal, 'permissionError', { action: 'delete', resource: 'integration' });

      this.showConfirmDeleteDialog = false;
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        await integrationsService.deleteIntegration(this.handle, this.selectedIntegration.uid);
        showSuccessToast(this.$swal, 'deleteSuccess', { item: this.selectedIntegration.name });
        await this.fetchAddedIntegrations();
      } catch (error) {
        showErrorToast(this.$swal, 'deleteError', { item: 'deleted integrations' }, error?.response?.data);
      } finally {
        this.closeDeleteDialog();
      }
    },

    updateFilter(filter) {
      this.filter = filter;
    },
    toggleTable() {
      this.table = !this.table;
    },
    async applyFilters(filters) {
      this.currentPage = 1; // Reset to first page when applying filters
      
      // Construct query parameters
      const queryParams = new URLSearchParams();
      if (filters.services?.length) {
        queryParams.append('service', filters.services.join(','));
      }
      if (filters.projectUids?.length) {
        queryParams.append('projectUid', filters.projectUids.join(','));
      }
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const response = await integrationsService.getIntegrations(this.handle, queryParams.toString());
        if (response?.data) {
          const mappedItems = response.data.integrations
            .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
            .map((integration) => ({
              uid: integration.uid,
              name: integration.name,
              description: integration.description,
              picUrl: integration.picUrl,
              service: integration.service,
              projectLogos: integration.projectLogos || [],
              projectUids: integration.projectUids || [],
              configuration: integration.configuration,
              status: integration.status,
              type: integration.type,
              syncedAt: dayjs(integration.syncedAt).format('DD/MM/YYYY HH:mm'),
              externalErrors: integration.externalErrors,
              projectDetails:
                integration.projectUids?.map((uid) => ({
                  ...this.projects[uid],
                  uid,
                })) || [],
            }));
          const hasError = response.data.integrations.some((integration) => integration.status !== 'active');
          const inactive = response.data.integrations.some((integration) => integration.status === 'inactive');
          this.setIntegrationError({
            hasError: hasError,
            message: inactive
              ? this.$t('integrations.error.inactiveIntegration')
              : this.$t('integrations.error.integrationError'),
          });
          // Update both originalItems and filteredItems
          this.originalItems = mappedItems;
          this.filteredItems = mappedItems;
          this.totalRows = mappedItems.length;
          this.filteredTotal = response.data.filteredTotal;
        }
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'filtered integrations' }, error?.response?.data);
      }
    },
    async fetchAvailableIntegrations() {
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const response = await integrationsService.getAvailableIntegrations(this.handle);

        if (response.status === 200) {
          const mappedTools = response.data
            ? response.data.map((category) => ({
                ...category,
                items: category.items.map((item) => ({
                  ...item,
                  image: item.image || 'project.png',
                })),
              }))
            : [];

          this.tools = mappedTools;
          this.originalTools = JSON.parse(JSON.stringify(mappedTools)); // Store original copy
          this.totalAll = response.data
            ? response.data.reduce((total, category) => total + category.items.length, 0)
            : 0;
        }
      } catch (error) {
        console.log('Error fetching integrations:', error);
        showErrorToast(this.$swal, 'fetchError', { item: 'integrations' }, error?.response?.data);
      }
    },
    async fetchAddedIntegrations() {
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const params = new URLSearchParams();
        params.append('page', this.currentPage);
        params.append('limit', this.perPage);
        const response = await integrationsService.getIntegrations(this.handle, params.toString());

        if (response?.data) {
          // Set services array
          this.services = response.data.services || [];
          this.totalAdded = response.data.pagination.totalIntegrations || 0;
          this.totalRows = response.data.pagination.total || 0;

          // If no integrations exist, make sure to clear error state
          if (this.totalAdded === 0) {
            this.filter = 'all';
            this.setIntegrationError(false);
            this.originalItems = [];
            this.filteredItems = [];
            return;
          }
          // Set projects object
          this.projects = response.data.projects || {};

          // Map integration items and sort by updatedAt
          const mappedItems = response.data.integrations
            .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
            .map((integration) => ({
              uid: integration.uid,
              name: integration.name,
              description: integration.description,
              picUrl: integration.picUrl,
              service: integration.service,
              projectLogos: integration.projectLogos || [],
              projectUids: integration.projectUids || [],
              configuration: integration.configuration,
              status: integration.status,
              type: integration.type,
              syncedAt: dayjs(integration.syncedAt).format('DD/MM/YYYY HH:mm'),
              externalErrors: integration.externalErrors,
              updatedAt: integration.updatedAt, // Keep the original updatedAt
              projectDetails:
                integration.projectUids?.map((uid) => ({
                  ...this.projects[uid],
                  uid,
                })) || [],
            }));

          const hasError = response.data.integrations.some((integration) => integration.status !== 'active');
          const inactive = response.data.integrations.some((integration) => integration.status === 'inactive');
          this.setIntegrationError({
            hasError: hasError,
            message: inactive
              ? this.$t('integrations.error.inactiveIntegration')
              : this.$t('integrations.error.integrationError'),
          });
          this.originalItems = mappedItems;
          this.filteredItems = mappedItems;
          
        }
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'added integrations' }, error?.response?.data);
        // In case of error fetching integrations, clear error state
        this.setIntegrationError(false);
      }
    },
    handleIntegrationSelect(integration) {
      if (!this._writeIntegration)
        return showErrorToast(this.$swal, 'permissionError', { action: 'add', resource: 'integration' });

      // Handle custom app selection
      if (integration.name === 'Custom App') {
        this.$router.push({
          name: 'create-integration',
          params: {
            selectedIntegration: {
              name: 'Custom App',
              description: 'Create a custom integration with external systems',
              authTypes: ['none'],
            },
          },
        });
      } else {
        this.$router.push({
          name: 'create-integration',
          params: {
            selectedIntegration: {
              name: integration.name,
              description: integration.description,
              authTypes: integration.authTypes,
            },
          },
        });
      }
    },
    checkUrlStatus() {
      const errorParam = this.$route.query.error;
      const successParam = this.$route.query.success;
      if (errorParam) {
        if (errorParam === 'only_orgs_can_integrate') {
          showErrorToast(this.$swal, this.$t(`integrations.error.onlyOrgsCanIntegrate`));
        } else if (errorParam === 'handle_not_found') {
          showErrorToast(this.$swal, this.$t(`integrations.error.handleNotFound`));
        } else {
          showErrorToast(this.$swal, this.$t(`error.internalServerError`));
        }
        // Clean up the URL by removing the error parameter
        const query = { ...this.$route.query };
        delete query.error;
        this.$router.replace({ query });
      } else if (successParam) {
        showSuccessToast(this.$swal, this.$t(`integrations.success.reauth_success`));
        const query = { ...this.$route.query };
        delete query.success;
        this.$router.replace({ query });
      }
    },
    showErrorDetails(item) {
      this.selectedIntegration = item;
      this.selectedItemErrors = item.externalErrors || [];
      this.showErrorSidebar = true;
    },
    async handlePersonalToken(integration) {
      const integrationsService = makeIntegrationsService(this.$api);
      const response = await integrationsService.personalTokenRedirect(this.handle, integration.service, integration.uid);
      const redirectUrl = response.data?.url || response.data;
      window.location.href = redirectUrl;
    },
    onUpdatePagination(options) {
      const newPage = options.page;
      const newItemsPerPage = options.itemsPerPage;

      if (newPage !== this.currentPage || newItemsPerPage !== this.perPage) {
        this.currentPage = newPage;
        this.perPage = newItemsPerPage;
        this.fetchAddedIntegrations();
      }
    },
  },
};
</script>

<style scoped>
.integration-loader {
  min-height: 500px;
}
.placeholder-img {
  max-width: 438px;
  width: 100%;
}
.integration-height {
  min-height: calc(100vh - 24px - 6.25rem);
  height: 100%;
}
.empty-integration-container,
.available-integration-container {
  min-height: 500px; /* or whatever height you want to set */
}
</style>
