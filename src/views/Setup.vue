<template>
  <div class="d-flex align-center app-height-global">
    <div class="not-found">
      <div class="position-relative img-placeholder">
        <Loader class="pt-0 position-absolute" />
        <img
          src="@/assets/png/setup-logo.png"
          alt="Setup Illustration"
          class="error-image"
        >
      </div>
      <div class="mt-4">
        <h1>{{ $t('setup.databaseCreation') }}</h1>
        <p class="mb-0">
          {{ $t('setup.redirectNotice') }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapMutations } from 'vuex';
import ProjectsService from '@/services/api/project';
import Loader from '@/components/base/Loader';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import makeOrgService from '@/services/api/org';
import { startPolling, stopPolling } from '@/utils/util';
import { showErrorToast } from '@/utils/toast';

export default {
  name: 'Setup',
  components: {
    Loader,
  },
  mixins: [colorPreferencesMixin],
  data() {
    return {
      pollInterval: 10000,
      pollErrorCount: 0,
    };
  },
  computed: {
    ...mapState({
      currentAccount: (state) => state.user.currentAccount,
    }),
    ...mapGetters({
      signupOrgDetails: 'user/getSignupOrgDetails',
    }),
  },
  mounted() {
    this.startPolling();
  },
  beforeDestroy() {
    this.stopPolling();
  },
  methods: {
    ...mapMutations({
      setOrgs: 'user/setOrgs',
      setCurrentAccount: 'user/setCurrentAccount',
      setSignupOrgDetails: 'user/setSignupOrgDetails',
    }),
    startPolling() {
      this.pollInterval = startPolling(this.pollProjects, 10000);
    },
    stopPolling() {
      stopPolling(this.pollInterval);
      this.pollInterval = null;
    },
    async pollProjects() {
      if (!this.currentAccount || !this.currentAccount.handle) {
        console.error('Current account or handle is not available');
        return;
      }

      const projectService = ProjectsService(this.$api);
      const searchParams = new URLSearchParams();
      searchParams.set('status', 'active');
      searchParams.set('includeCount', true);
      searchParams.set('poll', true);

      try {
        const response = await projectService.getProjects(this.currentAccount.handle, searchParams.toString());

        if (Array.isArray(response.data.items)) {
          this.updatePreferences(this.currentAccount.handle);
          let dest;
          if (this.signupOrgDetails) {
            await this.createOrganization();
            return;
          } else {
            dest = { name: 'ProjectsView', params: { handle: this.currentAccount.handle } };
            this.$router.replace(dest);
          }
        }
      } catch (error) {
        this.pollErrorCount += 1;
        if (this.pollErrorCount >= 8) {
          console.log('Polling stopped due to multiple errors', error);
          this.$router.push('/settings/organizations');
          return;
        }
      }
    },
    async createOrganization() {
      try {
        const orgService = makeOrgService(this.$api);
        const response = await orgService.newOrg(this.signupOrgDetails);

        const { uid, name, handle, roleName, createdBy, createdAt } = response.data;

        const org = {
          uid,
          name,
          handle,
          roleName,
          createdBy,
          createdAt,
        };

        let selectedHandle = { ...org, type: 'org', avatar_url: '' };
        this.setOrgs([org]);
        this.setCurrentAccount(selectedHandle);
        this.setSignupOrgDetails(null);
      } catch (error) {
        console.error('Error creating organization:', error);
        if (error.response && error.response.status === 409) {
          this.setSignupOrgDetails(null);
          this.$router.push('/settings/organizations');

          showErrorToast(this.$swal, 'invalidOrgHandle', {}, error?.response?.data);
        } else {
          showErrorToast(this.$swal, 'createError', { item: 'organization' }, error?.response?.data);
        }
        return;
      }
    },
  },
};
</script>

<style scoped>
.not-found {
  text-align: center;
  padding: 48px;
  max-width: 600px;
  margin: 0 auto;
}

.error-image {
  max-width: 100%;
  height: auto;
}
.img-placeholder {
  width: 100%;
  height: 100%;
  min-width: 504px;
  min-height: 388px;
  display: flex;
  justify-content: center;
  align-items: center;
}
h1 {
  font-size: 24px;
  margin-bottom: 16px;
}

p {
  margin-bottom: 24px;
  text-align: center;
}
</style>
