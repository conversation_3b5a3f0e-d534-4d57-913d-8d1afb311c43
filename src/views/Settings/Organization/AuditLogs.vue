<template>
  <v-container
    fluid
    class="pr-0 pb-0"
  >
    <audit-section-header
      :title="$t('settingsPage.auditLog')"
      :action-text="$t('settingsPage.exportAuditLog')"
      :api-filter="apiFilters"
    />
    <v-container
      class="pa-6 mt-5 white rounded-lg h-full"
      fluid
    >
      <v-row class="align-center">
        <v-col
          cols="6"
          sm="6"
        >
          <div class="d-flex flex-row justify-start align-center">
            <v-responsive
              v-if="!skeletonLoaderState"
              class="ma-0"
              max-width="344"
            >
              <v-text-field
                v-model="searchFilter"
                class="text-field mt-0 mr-3 rounded-lg field-theme custom-prepend pa-0"
                background-color="#F9F9FB"
                :placeholder="$t('search_by_name')"
                clear-icon="mdi-close-circle"
                clearable
                height="38px"
                hide-details
                @click:clear="searchFilter = ''"
              >
                <template #prepend-inner>
                  <SearchIcon />
                </template>
              </v-text-field>
            </v-responsive>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-3"
              width="344"
              height="40"
              type="button"
            />
            <AuditLogFilter
              :filter-options="filterOptions"
              :current-filters="appliedFilters"
              @applyFilters="applyFilters"
              @clear-filters="clearFilters"
            />
          </div>
        </v-col>
        <v-col
          cols="6"
          sm="6"
          class="d-flex justify-end"
        >
          <SettingsMenu table-type="auditLogHeader" />
        </v-col>
      </v-row>
      <v-row>
        <FilterChips
          :filters="appliedFilters || {}"
          :results-count="items.length"
          @update-filters="handleFilters"
          @clear-filters="clearFilters"
        />
      </v-row>
      <v-card
        class="py-6 mt-3 app-height-global"
        rounded="lg"
        elevation="0"
        width="100%"
      >
        <AuditLogTable
          :filtered-headers="filteredHeaders"
          :filtered-items="items"
          :item-key="itemKey"
          :total-items="totalRows"
          :current-page="currentPage"
          :items-per-page="perPage"
          @open-revert-modal="showRevertConfirmationDialog"
        />
        <Pagination
          v-if="totalRows > 0"
          :page="currentPage"
          :items-per-page="perPage"
          :total-pages="totalPages"
          :total-items="totalRows"
          @update:pagination="onUpdatePagination"
        />
      </v-card>
    </v-container>
    <ProjectDiscardDialog
      v-model="showConfirmRestartDialog"
      :title="$t('auditLog.revert_dialog.title', { name: selectedItem?.tablename || '' })"
      @close="closeRestartDialog"
    >
      <template #content>
        <v-flex class="mt-4 flex-column">
          <p class="text-start">
            {{ $t('auditLog.revert_dialog.recommend') }}
          </p>
        </v-flex>
        <p class="text-body-1 text-left font-weight-light mt-3">
          <v-checkbox
            v-model="dontShowAgain"
            class="field-theme"
            :ripple="false"
            off-icon="icon-checkbox-off"
            on-icon="icon-checkbox-on"
            :hide-details="true"
          >
            <template #label>
              <span class="fs-14px text-theme-label">{{ $t('dontShowAgain') }}</span>
            </template>
          </v-checkbox>
        </p>
      </template>
      <template #footer>
        <v-row>
          <v-col cols="6">
            <v-btn
              depressed
              height="40px"
              width="100%"
              class="text-capitalize btn-theme rounded-lg black--text mt-2"
              background-color="#F2F4F7"
              @click="closeRestartDialog"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn
              depressed
              height="40px"
              width="100%"
              class="text-capitalize btn-theme rounded-lg white--text mt-2"
              color="primary"
              @click="handleRevert"
            >
              {{ $t('projects.create_project.close_dialog.confirm_button') }}
            </v-btn>
          </v-col>
        </v-row>
      </template>
    </ProjectDiscardDialog>
  </v-container>
</template>

<script>
  import { mapGetters, mapActions } from 'vuex';
  import { debounce } from 'lodash';
  import SearchIcon from '@/assets/svg/search-icon.svg';
  import SettingsMenu from '@/components/Project/SettingsMenu.vue';
  import AuditSectionHeader from '@/components/Form/AuditSectionHeader.vue';
  import AuditLogFilter from '@/components/Settings/AuditLog/AuditLogFilter.vue';
  import FilterChips from "@/components/base/FilterChips.vue";
  import ProjectDiscardDialog from '@/components/Project/ProjectDiscardDialog';
  import AuditLogTable from '@/components/Settings/AuditLog/AuditLogTable.vue';
  import makeAuditLogService from '@/services/api/auditLog';
  import handleLoading from "@/mixins/loader.js";
  import { showErrorToast, showSuccessToast } from '@/utils/toast';
  import { setLocalStorageItem, getLocalStorageItem } from '@/utils/util.js';

  let initializeAuditLogService;
  export default {
    name: 'Audit',

    components: {
      AuditSectionHeader,
      AuditLogFilter,
      FilterChips,
      SettingsMenu,
      SearchIcon,
      ProjectDiscardDialog,
      AuditLogTable,
    },
    mixins: [handleLoading],
    data() {
      return {
        searchFilter: '',
        searchQuery: '',
        headers: [],
        appliedFilters: null,
        apiFilters: null,
        itemKey: 'uid',
        items: [],
        selectedItem: null,
        showConfirmRestartDialog: false,
        totalRows: 0,
        currentPage: 1,
        perPage: 10,
        loading: false,
        dontShowAgain: false,
        filterOptions: {
          projectNames: [],
          actors: [],
          actions: [],
          entityNames: [],
        },
      };
    },
    computed: {
      ...mapGetters({
        dynamicHeaders:'headers/dynamicHeaders',
      }),
      filteredHeaders() {
        return this.headers.filter((header) => header.checked);
      },
      totalPages() {
        return Math.ceil(this.totalRows / this.perPage);
      },
    },

    watch: {
      searchFilter(newVal) {
        this.currentPage = 1;
        this.searchQuery = newVal.trim();
        this.debouncedGetAuditLogs();
      },
    },

    created(){
      if(!this.dynamicHeaders.auditLogHeader) {
        this.initializeHeaders({ type: 'auditLogHeader' });
      }
      initializeAuditLogService = makeAuditLogService(this.$api);
      this.headers = this.dynamicHeaders.auditLogHeader;
      this.fetchAuditLogs();
      this.getAuditLogsFilters();
      this.debouncedGetAuditLogs = debounce(() => this.fetchAuditLogs(), 300);
    },
  
    methods: {
      ...mapActions("headers", ['initializeHeaders']),

      getAuditLogsFilters() {
        initializeAuditLogService.getAuditLogFilters(this.$route.params.handle)
          .then((response) => {
            const data = response.data;
            this.filterOptions = {
              projectNames: data.projects.map((project) => ({
                value: project.value,
                label: project.label,
              })),
              actors: data.actors.map((actor) => ({
                value: actor.value,
                label: actor.label,
              })),
              actions: data.actions.map((action) => ({
                value: action.value,
                label: action.label,
              })),
              entityNames: data.tablenames.map((tablename) => ({
                value: tablename.value,
                label: tablename.label,
              })),
            };
          })
          .catch((error) => {
            showErrorToast(this.$swal, 'fetchError', { item: 'auditLog' }, error?.response?.data);
          });
      },

      async fetchAuditLogs() {
        this.loading = true;
        this.showSkeletonLoader();
        try {
          const offset = (this.currentPage - 1) * this.perPage;
          const param = new URLSearchParams({
            limit: this.perPage,
            offset,
          });

          if (this.searchQuery.trim()) {
            param.append('q', this.searchQuery.trim());
          }

          if (this.apiFilters) {
            (this.apiFilters.actor || []).forEach((act) => param.append('actor', act));
            (this.apiFilters.entity_name || []).forEach((ent) => param.append('tablename', ent));
            (this.apiFilters.action || []).forEach((act) => param.append('action', act));
            (this.apiFilters.project_name || []).forEach((proj) => param.append('projectUid', proj));

            if (this.apiFilters.date_range?.from) {
              param.append('from', this.apiFilters.date_range.from);
            }
            if (this.apiFilters.date_range?.to) {
              param.append('to', this.apiFilters.date_range.to);
            }
          }

          const response = await initializeAuditLogService.getAuditLogs(
            this.$route.params.handle,
            param.toString()
          );
          this.items = response?.data?.data;
          this.totalRows = response?.data?.pagination?.total;
        } catch (error) {
          showErrorToast(this.$swal, this.$t('fetchError', { item: this.$t("auditLog.title") }), {}, error?.response?.data);
          this.items = [];
          this.totalRows = 0;
        } finally {
          this.hideSkeletonLoader();
        }
      },
      applyFilters(filters) {
        this.appliedFilters = filters?.ui || null;
        this.apiFilters = filters?.api || null;
        this.currentPage = 1;
        this.fetchAuditLogs();
      },
      clearFilters() {
        this.appliedFilters = null;
        this.apiFilters = null;
        this.currentPage = 1;
        this.fetchAuditLogs();
      },
      handleFilters(filters) {
        this.appliedFilters = filters;
        this.apiFilters = this.extractApiFilters(filters);
        this.currentPage = 1;
        this.fetchAuditLogs();
      },
      extractApiFilters(uiFilters) {
        if (!uiFilters) return null;
        const apiFilters = {};

        Object.keys(uiFilters).forEach((key) => {
          const filter = uiFilters[key];

          if (filter.type === 'array' && filter.value?.length > 0) {
            apiFilters[key] = filter.value.map((item) =>
              typeof item === 'object' && item !== null ? item.uid || item.name : item
            );
          } else if (filter.type === 'dateRange' && (filter.value.start || filter.value.end)) {
            apiFilters.startDate = filter.value.start;
            apiFilters.endDate = filter.value.end;
          }
        });

        return apiFilters;
      },
      showRevertConfirmationDialog(item) {
        const dontShowRevertDialog = getLocalStorageItem('dontShowRevertDialog');
        if (dontShowRevertDialog) {
          this.selectedItem = item;
          this.handleRevert();
          return;
        }
        this.selectedItem = item;
        this.showConfirmRestartDialog = true;
      },
      async handleRevert() {
        try {
          if (this.dontShowAgain) {
            setLocalStorageItem('dontShowRevertDialog', true);
          }
          await initializeAuditLogService.revertAuditLog(
            this.$route.params.handle,
            this.selectedItem.id
          );
          showSuccessToast(this.$swal, this.$t('toast.revertSuccess', { item: this.selectedItem?.tablename || this.$t("auditLog.title") }));
          this.closeRestartDialog();
          this.fetchAuditLogs();
        } catch (error) {
          showErrorToast(this.$swal, this.$t('toast.revertError'), { item: this.selectedItem?.entityName || this.$t("auditLog.title") }, error?.response?.data);
          this.closeRestartDialog();
        }
      },
      closeRestartDialog() {
        this.showConfirmRestartDialog = false;
        this.selectedItem = null;
      },
      async onUpdatePagination(options) {
        const newPage = options.page;
        const newItemsPerPage = options.itemsPerPage;
        if (newPage !== this.currentPage || newItemsPerPage !== this.perPage) {
          this.currentPage = newPage;
          this.perPage = newItemsPerPage;
          await this.fetchAuditLogs();
        }
      },
    },
  }
</script>