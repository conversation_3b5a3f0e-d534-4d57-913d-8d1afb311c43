<template>
  <v-container fluid>
    <section-header
      :title="$t('settingsPage.authentication')"
      :has-action="false"
    />

    <v-card
      v-if="currentAccount.isOrg"
      class="py-6 px-6 mt-3"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-row>
        <v-col
          cols="12"
          sm="8"
          offset-sm="2"
          md="6"
          offset-md="3"
        >
          <p class="text-left text-h6">
            {{ $t('authentication.singleSignOn') }}
          </p>

          <div class="d-flex align-center justify-space-between">
            <div>
              <p class="text-body-2 mb-0">
                {{ $t('authentication.enableSingleSignOn') }}
              </p>
            </div>

            <div class="d-flex align-center">
              <v-btn
                v-if="config && config.isActive"
                color="#F2F4F7"
                height="40"
                :depressed="true"
                class="text-capitalize btn-theme font-weight-semibold"
                elevation="0"
                @click="onEditSSOConfig"
              >
                {{ $t('edit') }}
              </v-btn>
              <v-switch
                v-model="enableSingleSignOn"
                color="blue"
                inset
                hide-details
                class="mt-0 ml-3"
                @change="onChangeSingleSignOn"
              />
            </div>
          </div>
        </v-col>
      </v-row>
    </v-card>
    <SetupAuthenticationNav v-model="showSetupNav" />
    <SetupSSO 
      v-model="showSetupSSO"
      :enable-single-sign-on="enableSingleSignOn"
      :sso-config="config"
      @setup-success="handleSSOSaved"
      @modalClosed="onModalClosed"
    />
  </v-container>
</template>

<script>
import { createNamespacedHelpers } from 'vuex';
import SectionHeader from '@/components/Form/SectionHeader.vue';
import SetupAuthenticationNav from '@/components/Settings/Authentication/SetupAuthenticationNav.vue';
import SetupSSO from '@/components/Settings/Authentication/SetupSSO.vue';
import makeOrgService from '@/services/api/org';

const { mapState } = createNamespacedHelpers('user');

let orgService;

export default {
  name: 'Authentication',

  components: {
    SectionHeader,
    SetupAuthenticationNav,
    SetupSSO,
  },

  data () {
    return {
      showSetupNav: false,
      enableSingleSignOn: false,
      showSetupSSO: false,
      config: null,
    }
  },
  computed: {
    ...mapState(['currentAccount']),
  },
  created() {
    orgService = makeOrgService(this.$api);
  },
  async mounted() {
    if(this.config) return;
    await this.getSSOConfig();
  },
  methods: {
    async getSSOConfig() {
      try {
        const response = await orgService.getSSOConfig(this.currentAccount.handle);
        this.config = response.data;
        this.enableSingleSignOn = response.data.isActive;
      } catch (error) {
        console.error('Error fetching SSO config:', error);
      }
    },
    async onChangeSingleSignOn() {
      if (!this.currentAccount.isOrg) return;
      if (!this.config) {
        this.showSetupSSO = true;
        return;
      }
      const payload = {
        isActive: this.enableSingleSignOn,
      };
      
      try {
        const response = await orgService.updateSSOConfig(this.currentAccount.handle, this.config.uid, payload);
        this.config = response.data;
        this.enableSingleSignOn = response.data.isActive;
        await this.getSSOConfig();
      } catch (error) {
        console.error('Error updating SSO config:', error);
        this.enableSingleSignOn = !this.enableSingleSignOn;
      }
    },
    onEditSSOConfig() {
      this.showSetupSSO = true
    },
    handleSSOSaved(config) {
      if(config === null) {
        this.enableSingleSignOn = false;
      }
      this.config = config;
    },
    onModalClosed(action) {
      if(this.config) {
        this.enableSingleSignOn = this.config.isActive;
        return;
      }
      this.enableSingleSignOn = !!action;
    }
  }
}
</script>