<template>
  <v-card
    class="h-default d-flex justify-center align-center mt-3 app-height-global"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <v-form
      ref="form"
      v-model="validForm"
      lazy-validation
    >
      <v-row>
        <v-col
          cols="12"
          sm="8"
          md="6"
          offset-sm="2"
          offset-md="3"
        >
          <v-row>
            <v-col
              cols="12"
              class="text-center"
            >
              <template #badge>
                <upload-avatar
                  profile-image="org"
                  media-type="profile-picture"
                  @uploaded="updatedAvatar"
                />
              </template>
              <Avatar
                v-if="org.avatarUrl"
                :size="36" 
                :avatar="org"
              />
            </v-col>

            <v-col
              cols="12"
              class="pb-0"
            >
              <v-text-field
                class="font-weight-medium body-2 rounded-lg text-left mb-1"
                :value="org.name"
                :label="$t('organization.organization_name')"
                filled
                readonly
                hide-details
              />
            </v-col>

            <v-col cols="12">
              <v-text-field
                class="font-weight-medium body-2 rounded-lg text-left mb-1"
                :value="formattedDate(org.createdAt)"
                :label="$t('organization.join_date')"
                filled
                readonly
                hide-details
              />
            </v-col>

            <v-col
              cols="12"
              class="pb-4 mt-5 text-left"
            >
              <template v-if="_isOwner">
                <p class="font-weight-bold text-h6">
                  {{ $t('organization.deleteOrganization') }}
                </p>
                <p class="font-weight-medium body-2">
                  {{ $t('organization.deleteOrganizationNotice') }}
                </p>
                <v-btn
                  color="error"
                  width="200px"
                  elevation="0"
                  class="white--text text-capitalize mt-2"
                  @click="onDeleteOrganization()"
                >
                  {{ $t('organization.deleteOrganization') }}
                </v-btn>
              </template>
              <template v-else>
                <v-btn
                  color="error"
                  width="200px"
                  elevation="0"
                  class="white--text text-capitalize mt-2"
                  @click="onLeaveOrganization()"
                >
                  {{ $t('organization.leaveOrganization') }}
                </v-btn>
              </template>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-form>

    <DeleteOrganizationConfirmDialog
      v-model="showDeleteConfirmDialog"
      @delete="deleteOrganization"
    />
    <LeaveOrganizationConfirmDialog
      v-model="showLeaveConfirmDialog"
      @leave="leaveOrganization"
    />
  </v-card>
</template>

<script>
import UploadAvatar from '@/components/Profile/UploadAvatar.vue';
import LeaveOrganizationConfirmDialog from '@/components/Settings/Organization/LeaveOrganizationConfirmDialog.vue';
import DeleteOrganizationConfirmDialog from '@/components/Settings/Organization/DeleteOrganizationConfirmDialog.vue';
import { useProjectView } from '@/composables/modules/project/index';
import makeUserService from '@/services/api/user';
import makeOrgService from '@/services/api/org';
import { handleDuplicateMixin } from '@/mixins/handleDuplicate';
import { formattedDate } from '@/utils/util';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import Avatar from "@/components/base/Avatar.vue"
import { createNamespacedHelpers ,mapGetters, mapActions } from 'vuex';
const { mapState } = createNamespacedHelpers('user');

let userService;
let orgService;

export default {
  name: 'CreateUpdateOrganization',

  components: {
    UploadAvatar,
    LeaveOrganizationConfirmDialog,
    DeleteOrganizationConfirmDialog,
    Avatar
  },

  mixins: [handleDuplicateMixin],
  data() {
    return {
      orgNameValidation: [
        value => !!value || this.$t('error.requiredField'),
        value => (value?.length >= 2 && value?.length <= 50) || this.$t('min2max50Chars')
      ],
      validForm: false,
      org: {
        uid: '',
        avatarUrl: '',
        name: '',
        handle: '',
        createdBy: '',
        createdAt: null,
      },
      showLeaveConfirmDialog: false,
      showDeleteConfirmDialog: false,
    }
  },
  watch: {
    'org.handle': {
      handler: 'usernameInUse',
      immediate: true,
    },
  },

  computed: {
    ...mapState(['orgs', 'user', 'currentAccount','userName']),
    ...mapGetters({
      isOrgAdmin: 'user/isOrgAdmin',
      getOrg: 'user/getOrg',
      user: 'user/user',
    }),
    _isOwner() {
      return this.authorityTo('owner');
    },
    isEditMode() {
      return !!this.org.uid
    },
    orgHandleHint() {
      if (this.org.handle === '') {
        return this.$t('orgAccountNameLabel')
      }
      if (!this.handleError && this.handleAvailable) {
        return "handle is available"
      }
      return ''
    },
    accountNameValidation() {
      const defaultRules = [
        value => !!value || this.$t('error.requiredField'),
        value => /^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/.test(value) || this.$t('invalidUsername'),

      ]
      if (this.handleError) {
        return [
          ...defaultRules,
        ]
      }
      return defaultRules
    }
  },

  mounted() {
    this.org = this.getOrg(this.$route.params.handle);
  },

  created() {
    orgService = makeOrgService(this.$api);
    userService = makeUserService(this.$api);
  },

  methods: {
    ...mapActions({
      setOrgs: 'user/setOrgs',
      setLoading: 'setLoading',
      setCurrentAccount: 'user/setCurrentAccount',
    }),
    formattedDate,
    updatedAvatar(avatarURL) {
      this.org.avatarUrl = avatarURL;
    },

    onUpdateOrganization() {
      const isValidForm = this.$refs.form.validate()

      if (!isValidForm) {
        return
      }

      this.$emit(this.isEditMode ? 'update' : 'create', this.org)
    },

    onLeaveOrganization() {
      this.showLeaveConfirmDialog = true
    },
    onDeleteOrganization(){
      this.showDeleteConfirmDialog = true
    },
    resetHandle(){
      const { setProject } = useProjectView();
      const handle = {
        handle: this.user.handle,
        name: `${this.user.firstName} ${this.user.lastName}`,
        type: 'user',
        avatarUrl: this.user.avatar,
        roleName: 'owner',
        uid: this.user.uid,
      };

      this.setCurrentAccount(handle);
      setProject(null);

      this.$router.push({
        name: 'Account',
      });
    },
    async leaveOrganization() {
      this.showLeaveConfirmDialog = false;
      try {
        await orgService.leaveOrg(
          this.$store.state.user.user.handle,
          this.$route.params.handle
        );

        showSuccessToast(
          this.$swal,
          this.$t('memberDeleted', {
            orgname: this.org.name,
            username: `${this.$store.state.user.user.firstName} ${this.$store.state.user.user.lastName}`,
          })
        );

        // Refresh orgs list
        const { data } = await userService.getOrgs();
        this.setOrgs(data.orgs);
        
        // Navigate back to organizations page
        this.resetHandle();

      } catch (error) {
        showErrorToast(
          this.$swal,
          this.$t('memberNotDeleted', {
            orgname: this.org.name,
            username: `${this.$store.state.user.user.firstName} ${this.$store.state.user.user.lastName}`,
          }),
          {},
          error?.response?.data
        );
      }
    },
    async deleteOrganization(password) {
      this.setLoading({
        loading: true,
        loadingText: this.$t('organization.deletingOrganization')
      })
      try {
        await orgService.deleteOrg(this.currentAccount.handle, {
          password,
        })
        const filterdOrgs = this.orgs.filter(item => item.uid !== this.currentAccount.uid)

        this.setOrgs(filterdOrgs || [])

        showSuccessToast(this.$swal, this.$t('organization.organizationDeleted'))

        // After org is deleted, we redirect the page to personal settings
        this.resetHandle();
      } catch (err) {
        showErrorToast(this.$swal, err.response?.data?.message || err.response.data[0]?.msg)
      } finally {
        this.setLoading({
          loading: false,
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.h-default {
  height: calc(100vh - 100px);
}

.custom-btn {
  transition: background-color .4s ease-in-out;
}

</style>
