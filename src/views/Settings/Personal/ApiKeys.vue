<template>
  <v-container
    fluid
    class="pr-0 pb-0"
  >
    <section-header
      :title="$t('settingsPage.apiKeys')"
      :action-text="$t('settingsPage.createApiKey')"
      :write-entity="_writeApiKey"
      @create="onCreateApiKey"
    />

    <v-card
      class="py-6 px-6 mt-3 app-height-global"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-row
        justify="space-between"
        class="align-center"
      >
        <v-col
          cols="12"
          md="auto"
          class="d-flex align-center"
        >
          <SearchComponent
            :search="filter.name"
            :placeholder="$t('placeHolder.searchByName')"
            @update:search="filter.name = $event"
          />
        </v-col>
      </v-row>

      <ApiKeyTable
        :headers="headers"
        :items="filteredApiKeys"
        item-key="uid"
        :delete-entity="_deleteApiKey"
        @edit="onEditApiKey"
        @delete="onDeleteApiKey"
      />
    </v-card>

    <CreateUpdateApiKeyDialog
      v-model="showCreateUpdateApiKeyDialog"
      :data="selectedApiKey"
      :projects="projects"
      @create-api-key="createApiKey"
      @update-api-key="updateApiKey"
      @close-dialog="showCreateUpdateApiKeyDialog = false"
    />

    <NewApiKeyCopyDialog
      v-model="showNewApiKeyCopyDialog"
      :new-api-key="newApiKey"
      @close-dialog="showNewApiKeyCopyDialog = false"
    />

    <DeleteConfirmDialog
      v-model="showDeleteConfirmDialog"
      :api-key="selectedApiKey"
      @delete="deleteApiKey"
    />
  </v-container>
</template>

<script>
import { mapGetters, createNamespacedHelpers } from 'vuex';
import * as _ from 'lodash'

import makeAccessTokenService from '@/services/api/accessToken';
import { showSuccessToast, showErrorToast } from '@/utils/toast';

import SectionHeader from '@/components/Form/SectionHeader.vue';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import ApiKeyTable from '@/components/Settings/ApiKey/ApiKeyTable.vue';
import CreateUpdateApiKeyDialog from '@/components/Settings/ApiKey/CreateUpdateApiKeyDialog.vue';
import NewApiKeyCopyDialog from '@/components/Settings/ApiKey/NewApiKeyCopyDialog.vue';
import DeleteConfirmDialog from '@/components/Settings/ApiKey/DeleteConfirmDialog.vue';
import handleLoading from '@/mixins/loader.js'
import ProjectsService from '@/services/api/project';


const { mapState } = createNamespacedHelpers('user');

export default {
  name: 'ApiKeys',

  components: {
    SectionHeader,
    ApiKeyTable,
    CreateUpdateApiKeyDialog,
    SearchComponent,
    NewApiKeyCopyDialog,
    DeleteConfirmDialog,
  },
  mixins: [handleLoading],
  data() {
    return {
      filter: {
        name: '',
      },
      projects: [],
      originalApiKeys: [],
      filteredApiKeys: [],
      headers: [
        {
          text: this.$t('name'),
          align: 'start',
          sortable: true,
          value: 'name',
          class: 'elevation-0 rounded-l-lg',
        },
        //{
        //  text: this.$t('common.createdBy'),
        //  value: 'createdBy',
        //  sortable: true,
        //},
        {
          text: this.$t('expiration'),
          value: 'expiresAt',
          sortable: true,
        },
        //{
        //  text: this.$t('settingsPage.lastUsedAt'),
        //  value: 'lastUsedAt',
        //  sortable: true,
        //},
        {
          text: '',
          value: 'uid',
          sortable: false,
          class: 'rounded-r-lg',
          width: 130,
        },
      ],
      showCreateUpdateApiKeyDialog: false,
      selectedApiKey: {
        uid: '',
        name: '',
        expiresAt: null,
        permissions: [],
      },
      isLoading: false,
      errorMessage: '',
      showNewApiKeyCopyDialog: false,
      newApiKey: '',
      showDeleteConfirmDialog: false,
    }
  },

  computed: {
    ...mapState([ 'currentAccount','user']),
    ...mapGetters({
      currentAccount: 'user/currentAccount',
      _user: 'user/user',
    }),
    _writeApiKey() {
      return this.authorityTo('write_key')
    },
    _deleteApiKey() {
      return this.authorityTo('delete_key')
    },
  },

  watch: {
    'filter.name': {
      handler: _.debounce(function () {
        this.filterApiKeys()
      }, 500),
    },
  },

  mounted() {
    this.loadAccessTokens();
    this.getProjects();
  },

  methods: {
    async loadAccessTokens() {
      try {
        this.showSkeletonLoader();
        const accessTokenService = makeAccessTokenService(this.$api);
        const response = await accessTokenService.getUserAccessTokens(this.currentAccount.handle);
        this.originalApiKeys = response.data;
      } catch (err) {
        showErrorToast(this.$swal, err.response.data.message)
      } finally {
        this.hideSkeletonLoader();
      }

      this.filterApiKeys()
    },
    async getProjects() {
      const makeProjectService = ProjectsService(this.$api);

      const handle = this._user?.handle;
      
      if(!handle)
        return showErrorToast(this.$swal, 'fetchError', { item: 'Projects' });

      const response = await makeProjectService.getProjects(handle);
      this.projects = response.data.items;
    },

    onCreateApiKey() {
      this.selectedApiKey = {
        uid: '',
        name: '',
        expiresAt: null,
        permissions: [],
      }

      this.showCreateUpdateApiKeyDialog = true
    },

    async createApiKey(apiKey) {
      this.showCreateUpdateApiKeyDialog = false

      const accessTokenService = makeAccessTokenService(this.$api);

      try {
        const response = await accessTokenService.newUserAccessToken(this.currentAccount.handle, apiKey)

        this.newApiKey = response.data.secretKey
        this.showNewApiKeyCopyDialog = true

        await this.loadAccessTokens()
      } catch (err) {
        showErrorToast(this.$swal, err.response.data.message)
      } finally {
        this.isLoading = false
      }
    },

    onEditApiKey(item) {
      this.selectedApiKey = {
        uid: item.uid,
        name: item.name,
        expiresAt: item.expiresAt,
        permissions: [],
      }

      this.showCreateUpdateApiKeyDialog = true
    },

    async updateApiKey(apiKey) {
      this.showCreateUpdateApiKeyDialog = false

      const accessTokenService = makeAccessTokenService(this.$api);

      try {
        await accessTokenService.updateAccessToken(this.currentAccount.handle, apiKey)

        await this.loadAccessTokens()
      } catch (err) {
        showErrorToast(this.$swal, err.response.data.message)
      } finally {
        this.isLoading = false
      }
    },

    onDeleteApiKey(item) {
      this.selectedApiKey = {
        uid: item.uid,
        name: item.name,
        expiresAt: item.expiresAt,
        permissions: [],
      }

      this.showDeleteConfirmDialog = true
    },

    async deleteApiKey() {
      this.showDeleteConfirmDialog = false

      const accessTokenService = makeAccessTokenService(this.$api);

      try {
        await accessTokenService.deleteUserAccessToken(this.currentAccount.handle, this.selectedApiKey.uid)

        showSuccessToast(this.$swal, 'API Key is deleted')

        this.originalApiKeys = this.originalApiKeys.filter(item => item.uid !== this.selectedApiKey.uid)
      } catch (err) {
        showErrorToast(this.$swal, err.response.data.message)
      } finally {
        this.isLoading = false
      }

      this.filterApiKeys()
    },

    filterApiKeys() {
      let filteredApiKeys = _.cloneDeep(this.originalApiKeys)

      if (this.filter.name) {
        filteredApiKeys = filteredApiKeys.filter(item => item.name.includes(this.filter.name))
      }

      this.filteredApiKeys = filteredApiKeys
    }
  }
}
</script>
