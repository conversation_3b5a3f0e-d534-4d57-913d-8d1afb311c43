import { marked } from 'marked';
import DOMPurify from 'dompurify';

// TestRail markdown syntax mapping
const testRailToStandardMarkdown = {
  bold: (text) => text.replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, '**$1**'),
  italic: (text) => text.replace(/(?<!_)_([^_]+)_(?!_)/g, '*$1*'),
  strikethrough: (text) => text.replace(/(?<!~)~([^~]+)~(?!~)/g, '~~$1~~'),
  code: (text) => text.replace(/\[code\]([^[]+)\[\/code\]/g, '`$1`'),
  codeBlock: (text) => text.replace(/\[code\]\n?([^[]+?)\n?\[\/code\]/g, '```\n$1\n```'),
  steps: (text) => {
    let stepCounter = 1;
    return text.replace(/\[step\]([\s\S]+?)\[\/step\]/g, (match, content) => {
      return `${stepCounter++}. ${content.trim()}`;
    });
  },
  tables: (text) => {
    return text.replace(/\|\|([^|]+)\|\|/g, '| $1 |')
      .replace(/\|([^|]+)\|/g, '| $1 |');
  },
  images: (text) => {
    // Convert TestRail image attachments to TestFiesta format
    // Pattern: ![](index.php?/attachments/get/1000000009)
    return text.replace(/!\[\]\(index\.php\?\/attachments\/get\/(\d+)\)/g, (match, attachmentId) => {
      // Convert to TestFiesta attachment URL format
      const tfAttachmentUrl = `/api/attachments/${attachmentId}`;
      return `![TestRail Image](${tfAttachmentUrl})`;
    });
  }
};

// Standard markdown to TestRail conversion
const standardToTestRailMarkdown = {
  bold: (text) => text.replace(/\*\*([^*]+)\*\*/g, '*$1*'),
  italic: (text) => text.replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, '_$1_'),
  strikethrough: (text) => text.replace(/~~([^~]+)~~/g, '~$1~'),
  code: (text) => text.replace(/`([^`]+)`/g, '[code]$1[/code]'),
  codeBlock: (text) => text.replace(/```\n?([^`]+?)\n?```/g, '[code]\n$1\n[/code]'),
  images: (text) => {
    // Convert standard markdown images back to TestRail format if needed
    return text.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, src) => {
      // If it's a TestFiesta attachment, convert back to TestRail format
      if (src.startsWith('/api/attachments/')) {
        const attachmentId = src.split('/').pop();
        return `![](index.php?/attachments/get/${attachmentId})`;
      }
      return match;
    });
  }
};

// Detect if content contains TestRail markdown
export function isTestRailMarkdown(content) {
  if (!content) return false;
  
  const testRailPatterns = [
    /\[code\][\s\S]*?\[\/code\]/,
    /\[step\][\s\S]*?\[\/step\]/,
    /(?<!\*)\*[^*]+\*(?!\*)/,
    /(?<!_)_[^_]+_(?!_)/,
    /(?<!~)~[^~]+~(?!~)/,
    /!\[\]\(index\.php\?\/attachments\/get\/\d+\)/
  ];
  
  return testRailPatterns.some(pattern => pattern.test(content));
}

// Convert TestRail markdown to standard markdown
export function convertTestRailMarkdown(content) {
  if (!content) return '';
  
  let converted = content;
  
  // Apply all conversions
  Object.values(testRailToStandardMarkdown).forEach(converter => {
    converted = converter(converted);
  });
  
  return converted;
}

// Convert standard markdown to TestRail format
export function convertToTestRailMarkdown(content) {
  if (!content) return '';
  
  let converted = content;
  
  // Apply all conversions in reverse
  Object.values(standardToTestRailMarkdown).forEach(converter => {
    converted = converter(converted);
  });
  
  return converted;
}

// Format content for preview (HTML)
export function formatContent(content) {
  if (!content) return '';
  
  try {
    // First convert TestRail markdown if needed
    let processedContent = isTestRailMarkdown(content) 
      ? convertTestRailMarkdown(content) 
      : content;
    
    // Convert markdown to HTML
    const html = marked(processedContent);
    
    // Sanitize HTML
    return DOMPurify.sanitize(html);
  } catch (error) {
    console.error('Error formatting content:', error);
    return content;
  }
}

// Image upload utility function
export async function uploadImage(file, options = {}) {
  try {
    const {
      handle,
      apiService,
      params = {},
      store,
      mediaType = 'attachment'
    } = options;

    // Validate required parameters
    if (!handle || !apiService || !store) {
      throw new Error('Missing required parameters: handle, apiService, and store are required');
    }

    // Use the Vuex store uploadToServer action which handles signed URLs
    const result = await store.dispatch('attachment/uploadToServer', {
      handle,
      mediaType,
      file,
      apiService,
      params
    });

    // Return the appropriate URL based on the result
    if (result && typeof result === 'object') {
      // For attachment uploads, construct the URL
      const baseUrl = `${import.meta.env.VITE_APP_SERVER_BASEURL}/${handle}`;
      if (result.uid && params.projectKey) {
        return `${baseUrl}/cases/attachments/${result.uid}/object`;
      } else if (result.uid) {
        return `${baseUrl}/attachments/${result.uid}/object`;
      }
    }
    
    // Fallback for direct URL returns
    return result || result.url || result.path || result.src;
  } catch (error) {
    console.error('Image upload failed:', error);
    throw error;
  }
}

// Validate image file
export function validateImageFile(file, maxSize = 10 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']) {
  if (!file) {
    throw new Error('No file provided');
  }
  
  if (!allowedTypes.includes(file.type)) {
    throw new Error(`File type ${file.type} not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }
  
  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    throw new Error(`File size ${Math.round(file.size / (1024 * 1024))}MB exceeds maximum ${maxSizeMB}MB`);
  }
  
  return true;
}

// Extract images from TestRail content for migration
export function extractTestRailImages(content) {
  if (!content) return [];
  
  const imagePattern = /!\[\]\(index\.php\?\/attachments\/get\/(\d+)\)/g;
  const images = [];
  let match;
  
  while ((match = imagePattern.exec(content)) !== null) {
    images.push({
      originalUrl: match[0],
      attachmentId: match[1],
      testRailUrl: `index.php?/attachments/get/${match[1]}`,
      tfUrl: `/api/attachments/${match[1]}`
    });
  }
  
  return images;
} 