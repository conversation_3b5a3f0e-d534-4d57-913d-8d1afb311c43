import * as forge from 'node-forge';

export const newSymmetricKey = () => {
  const symmetricEncryptionKey = forge.util
    .createBuffer(forge.random.getBytesSync(32))
    .getBytes();

  return symmetricEncryptionKey;
};

const newIV = () => forge.random.getBytesSync(12);

export function encryptHybrid(symmetricKey, data) {
  const iv = newIV();

  try {
    const cipher = forge.cipher.createCipher('AES-GCM', symmetricKey);
    cipher.start({ iv });
    cipher.update(forge.util.createBuffer(data, 'utf8'));
    cipher.finish();

    return {
      iv,
      tag: cipher.mode.tag,
      encryptedKey: symmetricKey,
      encryptedData: cipher.output.getBytes(),
    };
  } catch (error) {
    throw new Error(
      'Failed to encrypt data. Ensure the symmetric key, iv and data are valid.',
    );
  }
}

export  function encryptObject(dataObject, latestPublicKey) {
  const publicKey = forge.pki.publicKeyFromPem(forge.util.decode64(latestPublicKey.key));
  const symmetricKey = newSymmetricKey();
  
  const encryptedFields = {};
  const fieldMetadata = {};
  
  for (const [key, value] of Object.entries(dataObject)) {
    if (value) {
      const { iv, tag, encryptedData } =  encryptHybrid(symmetricKey, value);
      
      encryptedFields[key] = forge.util.encode64(encryptedData);
      
      fieldMetadata[key] = {
        iv: forge.util.encode64(iv),
        tag: forge.util.encode64(tag.bytes()),
      };
    }
  }
  
  const rsaEncryptedKey = publicKey.encrypt(symmetricKey, 'RSA-OAEP');
  
  const symmetricKeyData = {
    fieldMetadata,
    version: latestPublicKey.version,
    key: forge.util.encode64(rsaEncryptedKey),
    service: latestPublicKey.service,
    publicKeyUid: latestPublicKey.uid,
  };
  
  return { encryptedFields, symmetricKeyData };
}
