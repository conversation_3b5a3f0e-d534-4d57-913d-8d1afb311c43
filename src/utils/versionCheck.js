import * as Sentry from '@sentry/vue';

let reloadInProgress = false;

export const forceReloadWithNoCache = async (currentVersion, expectedVersion, store, isManualOverride = false) => {
  if (reloadInProgress) {
    return false;
  }
  
  reloadInProgress = true;

  if (!isManualOverride && store?.getters && !store.getters['versions/canFrontendReload']) {
    const reloadCount = store.getters['versions/frontendReloadCount'] || 0;
    
    Sentry.captureException(new Error('Frontend version reload limit exceeded'), {
      level: 'warning',
      tags: {
        component: 'version-management',
        issue_type: 'frontend-reload-limit-exceeded'
      },
      extra: {
        currentAppVersion: currentVersion,
        expectedVersion: expectedVersion,
        reloadAttempts: reloadCount,
        versions: store?.state?.versions,
        isManualOverride
      }
    });
    
    reloadInProgress = false;
    return false;
  }

  if (store?.dispatch) {
    store.dispatch('versions/trackFrontendReload');
  }
  
  sessionStorage.setItem('versionReloadInProgress', 'true');
  
  try {
    await fetch(window.location.href, {
      headers: {
        "Pragma": "no-cache",
        "Expires": "-1",
        "Cache-Control": "no-cache",
        "x-tf-frontend-version": expectedVersion,
      }
    });
  } catch (error) {
    console.error('Error pre-fetching page for frontend version update:', error);
  }


  // This is the request it directly on the backend.
  let searchParams = new URLSearchParams(window.location.search);
  searchParams.set("fv", expectedVersion);
  window.location.search = searchParams.toString();
  window.location.reload();
  return true;
};

export const checkFrontendVersionMismatch = (store, currentAppVersion) => {
  if (!store || !currentAppVersion) {
    return false;
  }

  // Don't redirect when on key pages that may cause a loop
  if (/signup|login|confirmEmail|forgotPassword|resetPassword|twoFactor/.test(window.location.href)) {
    return false;
  }

  const justReloaded = sessionStorage.getItem('versionReloadInProgress');
  if (justReloaded) {
    sessionStorage.removeItem('versionReloadInProgress');    
    return false;
  }
  
  const versionToCheck = store?.state?.versions?.frontendVersion;
  
  if (!versionToCheck) {
    return false;
  }
  
  const isVersionMismatch = currentAppVersion !== versionToCheck;
  
  if (isVersionMismatch) {
    if (store.getters['versions/isFrontendMismatchHandled'](versionToCheck)) {
      return false;
    }
    
    if (!store.getters['versions/canFrontendReload']) {
      const reloadCount = store.getters['versions/frontendReloadCount'] || 0;
      
      store.dispatch('versions/markFrontendMismatchHandled', versionToCheck);

      Sentry.captureMessage('Frontend reload limit exceeded, graceful degradation triggered', {
        level: 'warning',
        tags: {
          component: 'version-management',
          issue_type: 'frontend-reload-limit-exceeded'
        },
        extra: {
          currentVersion: currentAppVersion,
          expectedVersion: versionToCheck,
          reloadCount,
          versions: store.state.versions
        }
      });
      
      return false; 
    }
    
    const reloadResult = forceReloadWithNoCache(currentAppVersion, versionToCheck, store);
    return reloadResult !== false;
  }
  
  return false;
};


export const cleanupVersionCheck = () => {
  const justReloaded = sessionStorage.getItem('versionReloadInProgress');
  if (justReloaded) {
    sessionStorage.removeItem('versionReloadInProgress');
  }
};
