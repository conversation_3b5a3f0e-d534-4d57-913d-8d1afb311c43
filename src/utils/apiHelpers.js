export const handleVersionHeaders = (headers, store) => {
  if (!headers || !store) return;

  const frontendVersion = headers['x-tf-frontend-version'];
  const backendVersion = headers['x-tf-backend-version'];
  
  if (frontendVersion && frontendVersion !== store.state.versions?.frontendVersion) {
    // Header is set by the backend which allows the version to persist between
    //   refreshes since it is sent with frontend requests.
    document.cookie = `fv=${frontendVersion}; path=/`;
    store.commit('versions/setFrontendVersion', frontendVersion);
  } 
  
  if (backendVersion && backendVersion !== store.state.versions?.backendVersion) {
    store.commit('versions/setBackendVersion', backendVersion);
  }
  
};

export const handleLogoutReset = async (store, router) => {
  if (!store || !router) return;
  
  store.commit('user/setUser', null);
  store.commit('user/setOrgs', null);
  
  localStorage.setItem('user', JSON.stringify(null));
  localStorage.setItem('orgs', JSON.stringify(null));

  router.push({ name: 'Login' });
};
