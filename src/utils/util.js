import { format, addDays, toDate } from 'date-fns';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { testResultFileTypes, projectImageTypes, orgImageTypes, profileImageTypes, orgVideoTypes } from '@/constants/fileTypes';
dayjs.extend(relativeTime);

const mimeMap = Object.fromEntries(
  [...testResultFileTypes, ...projectImageTypes, ...orgImageTypes, ...profileImageTypes, ...orgVideoTypes]
    .map(type => [type, true])
);

export const getExtension = (mimeType) => mimeMap[mimeType] ? mimeType : "txt";

export const formatDate = (date, f = 'MMM do HH:mm, yyyy', d = '') => {
  if (!date) return d;
  if (typeof date === 'string' || typeof date === 'number') {
    date = new Date(date);
  }


  const offset = date.getTimezoneOffset();
  let test = toDate(date);

  if (offset !== null && !isNaN(offset)) {
    const needsMiddayShift = offset < 0;
    const utcDate = new Date(date.getTime() + offset * 60000);
    const shiftedUtcDate = needsMiddayShift
      ? new Date(Date.UTC(
          utcDate.getUTCFullYear(),
          utcDate.getUTCMonth(),
          utcDate.getUTCDate(),
          12, 0, 0, 0
        ))
      : utcDate;
    test = new Date(shiftedUtcDate.getTime() - offset * 60000);
  }

  let test1 = format(test, f);
  return test1;
};

export const utcToLocalDate = (date) => {
  return toDate(date, { timeZone: 'Etc/GMT' });
};

export const getNumForDate = (num) => {
  if (num >= 10) {
    return num;
  } else {
    return '0' + num;
  }
};

export const getUTCDateString = (date) => {
  return (
    new Date(date).getFullYear() +
    '-' +
    getNumForDate(new Date(date).getMonth() + 1) +
    '-' +
    getNumForDate(new Date(date).getUTCDate()) +
    ' ' +
    getNumForDate(new Date(date).getUTCHours()) +
    ':' +
    getNumForDate(new Date(date).getUTCMinutes()) +
    ':' +
    getNumForDate(new Date(date).getUTCSeconds())
  );
};
/*
Compares 2 dates
if 0, date is same
if 1, first date is later than second date
if 2, The first date precedes the second date.
if 3, The first and second dates are exactly the same.
*/
export const compareDates = (firstDate, secondDate) => {
  let value = -1;
  if (
    firstDate.getFullYear() === secondDate.getFullYear() &&
    firstDate.getMonth() == secondDate.getMonth() &&
    firstDate.getDay() == secondDate.getDay()
  ) {
    value = 0;
  } else {
    if (firstDate > secondDate) {
      value = 1;
    } else if (firstDate < secondDate) {
      value = 2;
    } else {
      value = 3;
    }
  }
  return value;
};

export const getDayofWeek = (date) => {
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  return days[date.getDay()];
};

export const formattedDate = (input_date) => {
  const date = new Date(input_date);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const year = date.getFullYear();
  return `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}/${year}`;
}


export const addDaysFrom = (days, startDate = new Date()) => {
  return addDays(startDate, days)
}


export const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export function redirectToMappedRoute(route, router, defaultRouteName, defaultRouteParams = {}) {
  const routeMap = {
    activeAddMilestone: 'MilestoneTestActivities',
    activeAddTestPlan: 'TestPlanAddRuns',
    activeAddTestPlanDuplicate: 'TestPlanDuplicateAddRuns'
  };
  
  const targetRouteName = Object.keys(routeMap).find((param) => 
    route.query[param]
  )
    ? routeMap[Object.keys(routeMap).find((param) => route.query[param])]
    : defaultRouteName;
  const params = {
    handle: route.params.handle,
    key: route.params.key,
    ...defaultRouteParams,
  };

  router.push({
    name: targetRouteName,
    params,
  });
}

export function redirectWithQueryParams(route, router, defaultRouteName, params = {}, defaultRouteQuery = {}) {

  const queryKeys = ['activeAddMilestone', 'activeAddTestPlan', 'activeAddTestPlanDuplicate'];
  
  const query = queryKeys.reduce((acc, key) => {
    if (route.query[key]) {
      acc[key] = route.query[key];
    }
    return acc;
  }, { ...defaultRouteQuery }); 

  router.push({
    name: defaultRouteName,
    params: {
      ...params,
      handle: route.params.handle,
      key: route.params.key,
    },
    query,
  });
}

export const timeAgo = (date) => {
  if (!date) return '-';
  return dayjs(date).fromNow();
};

export const generateDateUid = () => {
  const now = new Date();
  const yy = now.getFullYear().toString().slice(2);
  const dd = String(now.getDate()).padStart(2, '0'); 
  const hh = String(now.getHours()).padStart(2, '0'); 
  const mm = String(now.getMinutes()).padStart(2, '0'); 
  const ss = String(now.getSeconds()).padStart(2, '0'); 

  return `${yy}${dd}${hh}${mm}${ss}`;
};

export function startPolling(callback, interval = 3000) {
  const pollingInterval = setInterval(callback, interval);
  return pollingInterval;
}

export function stopPolling(pollingInterval) {
  if (pollingInterval) {
    clearInterval(pollingInterval);
  }
}

//  Checks if a URL segment looks like an identifier (ID) based on common patterns
export function isUrlSegmentId(segment) {
  if (!segment || typeof segment !== 'string') {
    return false;
  }
  
  return (
    /^\d+$/.test(segment) || 
    /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(segment) || // UUID
    /^[a-z0-9]{20,}$/i.test(segment) || 
    /^[a-z]+\d+$/i.test(segment) ||
    /^\d+[a-z]+$/i.test(segment) 
  );
}

// Builds a clean base path from a route by removing ID segments
export function buildCleanRoutePath(currentPath, newProjectKey) {
  if (!currentPath || !newProjectKey) {
    return '/';
  }
  
  const pathSegments = currentPath.split('/').filter(segment => segment !== '');
  const baseSegments = [];
  
  for (let i = 0; i < pathSegments.length; i++) {
    const segment = pathSegments[i];
    
    if (i === 0 || i === 1) {
      baseSegments.push(i === 1 ? newProjectKey : segment); 
      continue;
    }
    
    if (!isUrlSegmentId(segment)) {
      baseSegments.push(segment);
    } else {
      break;
    }
  }
  
  return '/' + baseSegments.join('/');
}

export function getLocalStorageItem(key) {
  return JSON.parse(localStorage.getItem(key));
}

export function setLocalStorageItem(key, value) {
  localStorage.setItem(key, JSON.stringify(value));
}
