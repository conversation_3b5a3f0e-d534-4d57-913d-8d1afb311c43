import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import relativeTime from 'dayjs/plugin/relativeTime';
import advancedFormat from 'dayjs/plugin/advancedFormat';

// Extend dayjs with required plugins
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);
dayjs.extend(advancedFormat);

/**
 * Composable for consistent date formatting across the application
 * Handles timezone conversion, null values, and provides consistent formatting options
 */
export const useDateFormatter = () => {
  /**
   * Get the user's timezone
   * @returns {string} The user's timezone
   */
  const getUserTimezone = () => {
    return dayjs.tz.guess();
  };

  /**
   * Checks if a date value is valid and not null/undefined
   * @param {*} date - The date value to check
   * @returns {boolean} True if date is valid
   */
  const isValidDate = (date) => {
    if (!date || date === null || date === undefined) {
      return false;
    }
    
    // Check if it's a valid dayjs/Date object
    const dayjsDate = dayjs(date);
    return dayjsDate.isValid();
  };

  /**
   * Format a date with timezone support and null handling
   * @param {string|Date|dayjs} date - The date to format
   * @param {string} format - The format string (default: 'MM/DD/YYYY')
   * @param {string} fallback - Fallback value for null/invalid dates (default: '-')
   * @param {string} timezone - Target timezone (default: user's timezone)
   * @returns {string} Formatted date string or fallback
   */
  const formatDate = (date, format = 'MM/DD/YYYY', fallback = '-', timezone = null) => {
    if (!isValidDate(date)) {
      return fallback;
    }

    try {
      const targetTimezone = timezone || getUserTimezone();
      
      // Parse the date and convert to target timezone
      let parsedDate = dayjs.utc(date);
      
      // Convert to target timezone
      parsedDate = parsedDate.tz(targetTimezone);
      
      return parsedDate.format(format);
    } catch (error) {
      console.warn('Date formatting error:', error, 'for date:', date);
      return fallback;
    }
  };

  /**
   * Format date with time
   * @param {string|Date|dayjs} date - The date to format
   * @param {string} fallback - Fallback value for null/invalid dates
   * @returns {string} Formatted date with time
   */
  const formatDateTime = (date, fallback = '-') => {
    return formatDate(date, 'MM/DD/YYYY HH:mm:ss', fallback);
  };

  /**
   * Format date for input fields (YYYY-MM-DD)
   * @param {string|Date|dayjs} date - The date to format
   * @param {string} fallback - Fallback value for null/invalid dates
   * @returns {string} Formatted date for inputs
   */
  const formatDateForInput = (date, fallback = '') => {
    return formatDate(date, 'YYYY-MM-DD', fallback);
  };

  /**
   * Format date in a readable format (e.g., "Jan 15, 2024")
   * @param {string|Date|dayjs} date - The date to format
   * @param {string} fallback - Fallback value for null/invalid dates
   * @returns {string} Formatted readable date
   */
  const formatReadableDate = (date, fallback = '-') => {
    return formatDate(date, 'MMM DD, YYYY', fallback);
  };

  /**
   * Format date in a compact format (e.g., "01/15/24")
   * @param {string|Date|dayjs} date - The date to format
   * @param {string} fallback - Fallback value for null/invalid dates
   * @returns {string} Formatted compact date
   */
  const formatCompactDate = (date, fallback = '-') => {
    return formatDate(date, 'MM/DD/YY', fallback);
  };

  /**
   * Format relative time (e.g., "2 hours ago")
   * @param {string|Date|dayjs} date - The date to format
   * @param {string} fallback - Fallback value for null/invalid dates
   * @returns {string} Relative time string
   */
  const formatRelativeTime = (date, fallback = '-') => {
    if (!isValidDate(date)) {
      return fallback;
    }

    try {
      return dayjs(date).fromNow();
    } catch (error) {
      console.warn('Relative time formatting error:', error, 'for date:', date);
      return fallback;
    }
  };

  /**
   * Legacy support for the old formattedDate function
   * @param {string|Date|dayjs} date - The date to format
   * @returns {string} Formatted date in MM/DD/YYYY format
   */
  const formattedDate = (date) => {
    return formatDate(date, 'MM/DD/YYYY', '-');
  };

  /**
   * Format due date with special handling for overdue dates
   * @param {string|Date|dayjs} date - The due date to format
   * @param {string} fallback - Fallback value for null/invalid dates
   * @returns {object} Object with formatted date and isOverdue flag
   */
  const formatDueDate = (date, fallback = '-') => {
    if (!isValidDate(date)) {
      return {
        formatted: fallback,
        isOverdue: false,
        isEmpty: true
      };
    }

    try {
      const now = dayjs();
      const dueDate = dayjs(date);
      const isOverdue = dueDate.isBefore(now, 'day');
      
      return {
        formatted: formatDate(date, 'MM/DD/YYYY', fallback),
        isOverdue,
        isEmpty: false
      };
    } catch (error) {
      console.warn('Due date formatting error:', error, 'for date:', date);
      return {
        formatted: fallback,
        isOverdue: false,
        isEmpty: true
      };
    }
  };

  /**
   * Format created/updated dates consistently
   * @param {string|Date|dayjs} date - The date to format
   * @param {string} fallback - Fallback value for null/invalid dates
   * @returns {string} Formatted date
   */
  const formatCreatedAt = (date, fallback = '-') => {
    return formatCompactDate(date, fallback);
  };

  /**
   * Get date range display string
   * @param {string|Date|dayjs} startDate - Start date
   * @param {string|Date|dayjs} endDate - End date
   * @param {string} format - Format for dates
   * @returns {string} Formatted date range
   */
  const formatDateRange = (startDate, endDate, format = 'MM/DD/YYYY') => {
    const start = formatDate(startDate, format, '');
    const end = formatDate(endDate, format, '');
    
    if (start && end) {
      return `${start} - ${end}`;
    } else if (start) {
      return `From ${start}`;
    } else if (end) {
      return `Until ${end}`;
    }
    
    return '-';
  };

  return {
    // Core formatting functions
    formatDate,
    formatDateTime,
    formatDateForInput,
    formatReadableDate,
    formatCompactDate,
    formatRelativeTime,
    formatDateRange,
    
    // Specialized formatters
    formatDueDate,
    formatCreatedAt,
    
    // Legacy support
    formattedDate,
    
    // Utility functions
    isValidDate,
    getUserTimezone,
    
    // Direct dayjs access for advanced usage
    dayjs
  };
}; 