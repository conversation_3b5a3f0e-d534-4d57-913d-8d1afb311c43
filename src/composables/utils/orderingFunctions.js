export function isDescendant(parent, child) {
  if (!parent.children) return false;
  return parent.children.some((item) => item.uid === child?.uid || isDescendant(item, child));
}
export function calculateFractionalOrder(afterOrder, beforeOrder) {
  return (afterOrder + beforeOrder) / 2;
}
export function checkForRebalance(afterOrder, beforeOrder, precision = 3) {
  const avg = calculateFractionalOrder(afterOrder, beforeOrder);
  const decimalPart = avg.toString().split('.')[1];
  return decimalPart && decimalPart.length > precision;
}

function truncateDecimals(num, decimals = 4) {
  const factor = Math.pow(10, decimals);
  return Math.trunc(num * factor) / factor;
}

// getRebalanceWindow calculates the window of folders around a
// center folder(which is the folder after which the new folder will be inserted)
// and returns an object with start, mid, and end folders.
// It ensures that the newFolder is inserted after the center folder.
export function getRebalanceWindow({children:children, center:center, newFolder:newFolder, windowSize = 10}) {
  if (!Array.isArray(children) || children.length === 0) return [];

  let centerIndex = children.findIndex((folder) => folder.uid === center.uid);

  // Calculate window boundaries
  const half = Math.floor(windowSize / 2);
  let start = Math.max(0, centerIndex - half);
  let end = Math.min(children.length, start + windowSize);

  // Adjust start if we're at the end
  if (end - start < windowSize) {
    start = Math.max(0, end - windowSize);
  }

  const rWindow = children.slice(start, end);
  // Remove newFolder if it already exists
  const idx = rWindow.findIndex((f) => f.uid === newFolder.uid);
  if (idx !== -1) rWindow.splice(idx, 1);
  // Recalculate centerIndex after removing newFolder
  centerIndex = children.findIndex((folder) => folder.uid === center.uid);
  // Insert newFolder after leftFolder
  rWindow.splice(centerIndex + 1, 0, newFolder);

  // Return mid (excluding first and last)
  return { start: rWindow[0], mid: rWindow.slice(1, rWindow.length - 1), end: rWindow[rWindow.length - 1] };
}

// Rebalance the folder orders in the mid array
export function rebalanceOrder(start, mid, end, precision = 4) {

  // Provide defaults if start or end are null/undefined
  const safeStart = (typeof start.position === 'number') ? start.position : 0;
  const safeEnd = (typeof end.position === 'number') ? end.position : safeStart + 10 * (mid.length + 1);
  if (!Array.isArray(mid) || mid.length === 0) {
    throw new Error('mid must be a non-empty array');
  }

  const gap = (safeEnd - safeStart) / (mid.length + 1);

  if (gap <= 0) {
    throw new Error(`Invalid gap: ${gap}. Ensure safeEnd > safeStart.`);
  }
  // Rebalance the folder orders in the mid array
  return mid.map((folder, i) => ({
    ...folder,
    position: parseFloat(truncateDecimals(safeStart + gap * (i + 1), precision)),
  }));
}
