import { ref, reactive, computed } from 'vue';


  // Cache for storing relations by entity type -> entity UID -> relation type -> data
  const relationCache = ref({
    milestone: {}, // milestoneUid -> { relationType -> relationData }
    case: {},      // caseUid -> { relationType -> relationData }
    run: {},       // runUid -> { relationType -> relationData }
    plan: {}       // planUid -> { relationType -> relationData }
  });

  const processedMilestoneCache = ref([]);
  const processedPlanCache = ref([]);
  const processedRunCache = ref([]);
  const processedCaseCache = ref([]);




export const useRelations = () => {
  const relationsLoading = ref(false);
  
  // Individual loading states for each relation type
  const relationLoadingStates = reactive({
    tag: false,
    milestone: false, 
    milestoneCount: false,
    runCount: false,
    planCount: false,
    caseCount: false,
    config: false
  });


  

  const resetRelationLoadingStates = () => {
    Object.keys(relationLoadingStates).forEach(key => {
      relationLoadingStates[key] = false;
    });
  };

  const setRelationLoading = (relationType, isLoading) => {
    if (relationType in relationLoadingStates) {
      relationLoadingStates[relationType] = isLoading;
    }
  };

  const clearRelationCache = () => {
    Object.keys(relationCache.value).forEach(entityType => {
      relationCache.value[entityType] = {};
    });
  };

  const clearEntityTypeCache = (entityType) => {
    if (entityType in relationCache.value) {
      relationCache.value[entityType] = {};
    }
  };

  const clearEntityCache = (entityType, entityUids) => {
    if (!(entityType in relationCache.value)) return;
    entityUids.forEach(entityUid => {
      delete relationCache.value[entityType][entityUid];
    });
  };

  const isRelationCached = (entityType, entityUid, relationType) =>
  {
    return relationCache.value[entityType]?.[entityUid]?.[relationType] !== undefined;
  };

  const getCachedRelation = (entityType, entityUid, relationType) => {
    return relationCache.value[entityType]?.[entityUid]?.[relationType];
  };

  const setCachedRelation = (entityType, entityUid, relationType, relationData) => {
  
    relationCache.value[entityType][entityUid] = {
      ...relationCache.value[entityType][entityUid],
      [relationType]: relationData
    };
    if (entityType === 'milestone') {
      processedMilestoneCache.value.push(entityUid);
    } else if (entityType === 'plan') {
      processedPlanCache.value.push(entityUid);
    } else if (entityType === 'run') {
      processedRunCache.value.push(entityUid);
    } else if (entityType === 'case') {
      processedCaseCache.value.push(entityUid);
    }

  };

  const getEntityTypeFromServiceMethod = (serviceMethod) => {
    if (serviceMethod.includes('Plan')) return 'plan';
    if (serviceMethod.includes('Run')) return 'run';
    if (serviceMethod.includes('Case')) return 'case';
    if (serviceMethod.includes('Milestone')) return 'milestone';
    return 'unknown';
  };

  const filterEntitiesNeedingRelations = (entityType, entities, relationTypes, entityUidField) => {
    const entitiesNeedingFetch = {};
    relationTypes.forEach(relationType => {
      entitiesNeedingFetch[relationType] = entities.filter(entity => {
        const entityUid = entity[entityUidField];
        return !isRelationCached(entityType, entityUid, relationType);
      });
    });
    return entitiesNeedingFetch;
  };

  const applyCachedRelations = (entityType, entities, relationTypes, entityUidField) => {
    entities.forEach(entity => {
      const entityUid = entity[entityUidField];
      relationTypes.forEach(relationType => {
        if (isRelationCached(entityType, entityUid, relationType)) {
          const cachedData = getCachedRelation(entityType, entityUid, relationType);
          switch (relationType) {
            case 'tag':
              entity.tags = cachedData;
              break;
            case 'milestone':
            case 'milestoneCount':
              entity.testMilestones = cachedData;
              break;
            case 'runCount':
              entity.runCount = cachedData;
              break;
            case 'planCount':
              entity.planCount = cachedData;
              break;
            case 'caseCount':
              entity.caseCount = cachedData;
              break;
            case 'config':
              entity.configs = cachedData;
              break;
            default:
              entity[relationType] = cachedData;
          }
        }
      });
    });
  };

  const processRelationResult = (relationType, entities, relations, entityUidField = 'uid', entityType = 'unknown') => {
  
    entities.forEach(entity => {
      const entityId = entity[entityUidField];
      const entityRelations = relations[entityId] || [];
      let processedRelationData;
      switch (relationType) {
        case 'tag': {
          const tags = Array.isArray(entityRelations) ? (entityRelations.length > 0 ? entityRelations : (entity.tags || [])) : (entityRelations || entity.tags || []);
          entity.tags = tags;
          processedRelationData = tags;
          break;
        }
        case 'milestone':
        case 'milestoneCount':
          entity.testMilestones = entityRelations;
          processedRelationData = entityRelations;
          break;
        case 'runCount': {
          const runCount = Array.isArray(entityRelations) ? (entityRelations.length > 0 ? entityRelations[0] : 0) : (entityRelations || 0);
          entity.runCount = runCount;
          processedRelationData = runCount;
          break;
        }
        case 'planCount': {
          const planCount = Array.isArray(entityRelations) ? (entityRelations.length > 0 ? entityRelations[0] : 0) : (entityRelations || 0);
          entity.planCount = planCount;
          processedRelationData = planCount;
          break;
        }
        case 'caseCount': {
          const caseCount = Array.isArray(entityRelations) ? (entityRelations.length > 0 ? entityRelations[0] : 0) : (entityRelations || 0);
          entity.caseCount = caseCount;
          processedRelationData = caseCount;
          break;
        }
        case 'config':
          entity.configs = entityRelations;
          processedRelationData = entityRelations;
          break;
        default:
          entity[relationType] = entityRelations;
          processedRelationData = entityRelations;
      }
      if (entityType !== 'unknown') {
        setCachedRelation(entityType, entityId, relationType, processedRelationData);
      }
    });
    setRelationLoading(relationType, false);
  };

  // Only progressive, cache-aware loading remains
  const fetchRelations = async (
    service,
    handle,
    projectKey,
    entities,
    relationTypes = ['tag'],
    entityUidField = 'uid',
    serviceMethod = 'getRelations'
  ) => {
    if (!entities || entities.length === 0) return entities;
    const entityType = getEntityTypeFromServiceMethod(serviceMethod);
    applyCachedRelations(entityType, entities, relationTypes, entityUidField);
    const entitiesNeedingFetch = filterEntitiesNeedingRelations(entityType, entities, relationTypes, entityUidField);
    const hasEntitiesNeedingFetch = relationTypes.some(relationType => 
      entitiesNeedingFetch[relationType].length > 0
    );
    if (!hasEntitiesNeedingFetch) {
      return entities;
    }
    try {
      relationsLoading.value = true;
      relationTypes.forEach(relationType => {
        if (entitiesNeedingFetch[relationType].length > 0) {
          setRelationLoading(relationType, true);
        }
      });
      const relationPromises = relationTypes.map(async (relationType) => {
        const entitiesToFetch = entitiesNeedingFetch[relationType];
        if (entitiesToFetch.length === 0) {
          return { relationType, status: 'cached', data: null };
        }
        const entityUidsToFetch = entitiesToFetch.map(entity => entity[entityUidField]);
        try {
          const response = await service[serviceMethod](handle, projectKey, relationType, entityUidsToFetch);
          processRelationResult(relationType, entitiesToFetch, response.data, entityUidField, entityType);
          return { relationType, status: 'success', data: response.data };
        } catch (error) {
          console.warn(`Failed to fetch ${relationType} relations:`, error);
          setRelationLoading(relationType, false);
          return { relationType, status: 'error', error };
        }
      });
      await Promise.allSettled(relationPromises);
      return entities;
    } catch (error) {
      console.warn('Failed to fetch relations:', error);
      return entities;
    } finally {
      relationsLoading.value = false;
    }
  };

  const fetchPlanRelations = async (planService, handle, projectKey, plans) => {
    return fetchRelations(
      planService,
      handle,
      projectKey,
      plans,
      ['tag', 'milestoneCount', 'runCount'],
      'uid',
      'getPlanRelations'
    );
  };

  const fetchRunRelations = async (runService, handle, projectKey, runs) => {
    return fetchRelations(
      runService,
      handle,
      projectKey,
      runs,
      ['tag', 'milestone', 'config'],
      'uid',
      'getRunRelations'
    );
  };

  const fetchCaseRelations = async (caseService, handle, projectKey, cases) => {
    return fetchRelations(
      caseService,
      handle,
      projectKey,
      cases,
      ['tag'],
      'testCaseRef',
      'getCaseRelations'
    );
  };

  const fetchMilestoneRelations = async (milestoneService, handle, projectKey, milestones) => {
    return fetchRelations(
      milestoneService,
      handle,
      projectKey,
      milestones,
      ['runCount', 'tag', 'planCount', 'caseCount'],
      'uid',
      'getMilestoneRelations'
    );
  };

  // Helper to get a reactive computed for isRelationCached
  const useIsRelationCached = (entityType, entityUid, relationType) => {
    return computed(() => isRelationCached(entityType, entityUid, relationType));
  };

  return {
    relationsLoading,
    relationLoadingStates,
    relationCache,
    resetRelationLoadingStates,
    setRelationLoading,
    processRelationResult,
    fetchRelations,
    fetchPlanRelations,
    fetchRunRelations,
    fetchCaseRelations,
    fetchMilestoneRelations,
    clearRelationCache,
    clearEntityTypeCache,
    clearEntityCache,
    isRelationCached,
    getCachedRelation,
    setCachedRelation,
    processedMilestoneCache,
    processedPlanCache,
    processedRunCache,
    processedCaseCache,
    useIsRelationCached, // <-- export the new helper
  };
}; 