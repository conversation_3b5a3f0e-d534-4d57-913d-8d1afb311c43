import { computed } from 'vue'
import { useStore } from '@/main'
import { useRoute } from 'vue-router/composables'
import makeHandleService from '@/services/api/handle'
import { showErrorToast } from '@/utils/toast'

export function useColorPreferences(api, swal) {
  const store = useStore()
  const route = useRoute()
  const handleService = makeHandleService(api)

  const currentAccount = computed(() => store.state.user.currentAccount)
  const isOrg = computed(() => currentAccount.value && (currentAccount.value.type === 'org' || currentAccount.value.isOrg))
  const handle = computed(() => route.params.handle || (currentAccount.value && currentAccount.value.handle))

  // Getter wrappers
  const getUserStatusColorsByEntityType = (entityType) => store.getters['user/getUserStatusColorsByEntityType'](entityType)
  const getUserPriorityColorsByEntityType = (entityType) => store.getters['user/getUserPriorityColorsByEntityType'](entityType)
  const getUserPreferences = () => store.getters['user/getUserPreferences']
  const getOrgStatusColorsByEntityType = (entityType) => store.getters['user/getOrgStatusColorsByEntityType'](handle.value, entityType)
  const getOrgPriorityColorsByEntityType = (entityType) => store.getters['user/getOrgPriorityColorsByEntityType'](handle.value, entityType)
  const getOrgPreferences = () => store.getters['user/getOrgPreferences'](handle.value)

  // Mutation/Action wrappers
  const setOrgPreferences = (prefs) => store.commit('user/setOrgPreferences', prefs)
  const setUserPreferences = (prefs) => store.dispatch('user/setUserPreferences', prefs)

  function getDefaultStatus(statuses) {
    return statuses.find(status => status.isDefault === true)
  }

  function getDefaultPriority(priorities) {
    return priorities.find(priority => priority.isDefault === true)
  }

  function getPriorities(entityType) {
    return isOrg.value ? getOrgPriorityColorsByEntityType(entityType) : getUserPriorityColorsByEntityType(entityType)
  }

  function getStatuses(entityType) {
    return isOrg.value ? getOrgStatusColorsByEntityType(entityType) : getUserStatusColorsByEntityType(entityType)
  }

  function generateExecutionsProgress(completedFreq) {
    if (!completedFreq) return
    const statusColors = isOrg.value 
      ? store.getters['user/getOrgStatusColorsByEntityType'](handle.value, 'testExecution')
      : store.getters['user/getUserStatusColorsByEntityType']('testExecution')
    const result = {}
    for (const item of statusColors) {
      const { id, name } = item
      result[name.toLowerCase()] = {
        id,
        name,
        count: completedFreq[id] || 0
      }
    }
    return result
  }

  function getObjectCount(frequency) {
    let count = 0
    for (let key in frequency) {
      if (Object.hasOwn(frequency, key)) {
        count += frequency[key]
      }
    }
    return count
  }

  function isStatusesHasDefault(entityType) {
    const statuses = isOrg.value ? getOrgStatusColorsByEntityType(entityType) : getUserStatusColorsByEntityType(entityType)
    return statuses.some(status => status.isDefault === true)
  }

  function getEntityCompletedStatuses(entityType) {
    const statuses = isOrg.value ? getOrgStatusColorsByEntityType(entityType) : getUserStatusColorsByEntityType(entityType)
    return statuses.filter(element => element.isCompleted).map(item => item.id)
  }
  function getEntityTodoStatuses(entityType) {
      const statuses = this.isOrg
        ? this.getOrgStatusColorsByEntityType(this.handle, entityType)
        : this.getUserStatusColorsByEntityType(entityType);
      return statuses.filter(element => !element.isCompleted).map(item => item.id);
  }
  function isPrioritiesHasDefault(entityType) {
    const priorities = isOrg.value ? getOrgPriorityColorsByEntityType(entityType) : getUserPriorityColorsByEntityType(entityType)
    return priorities.some(priority => priority.isDefault === true)
  }

  function getPriorityColor(priorityId, priorities) {
    return priorities.find(priority => priority.id == priorityId)?.color || '#0c111d'
  }

  function getStatusColor(statusId, statuses) {
    return statuses.find(status => status.id == statusId)?.color || '#0c111d'
  }

  function getStatusColorByName(statusName, statuses) {
    const name = statusName?.toLowerCase()?.replace(/\s+/g, '')
    return statuses.find(status => status.name?.toLowerCase()?.replace(/\s+/g, '') === name)?.color || '#0c111d'
  }

  function getPriorityColorByName(priorityName, priorities) {
    const name = priorityName?.toLowerCase()?.replace(/\s+/g, '')
    return priorities.find(priority => priority.name?.toLowerCase()?.replace(/\s+/g, '') === name)?.color || '#0c111d'
  }

  function getStatusName(statusId, statuses) {
    return statuses.find(status => status.id == statusId)?.name || ''
  }

  function getPriorityName(priorityId, priorities) {
    return priorities.find(priority => priority.id == priorityId)?.name || ''
  }

  function generateRandomHexColor() {
    return `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`
  }

  async function updatePreferences(handleObj) {
    const currentHandle = handleObj?.handle || handle.value
    const currentTime = new Date().getTime()
    const fifteenMinutes = 15 * 60 * 1000
    try {
      if (handleObj?.type === 'org') {
        const orgPrefs = getOrgPreferences()
        const timestamp = orgPrefs?.timestamp
        if (!timestamp || currentTime - timestamp > fifteenMinutes) {
          const preferences = await handleService.getPreferences(currentHandle)
          const preferencesData = preferences.data.preferences || preferences.data
          const timestampNow = new Date().getTime()
          setOrgPreferences({
            handle: currentHandle,
            preferences: { ...preferencesData, timestamp: timestampNow }
          })
        } else {
          const preferences = getOrgPreferences()
          setOrgPreferences({
            handle: currentHandle,
            preferences: { ...preferences, timestamp: currentTime }
          })
        }
      } else {
        const userPrefs = getUserPreferences()
        const timestamp = userPrefs?.timestamp
        if (!timestamp || currentTime - timestamp > fifteenMinutes) {
          const preferences = await handleService.getPreferences(currentHandle)
          const preferencesData = preferences.data.preferences || preferences.data
          const timestampNow = new Date().getTime()
          setUserPreferences({ ...preferencesData, timestamp: timestampNow })
        } else {
          const preferences = getUserPreferences()
          setUserPreferences({ ...preferences, timestamp: currentTime })
        }
      }
    } catch (err) {
      if (swal) {
        showErrorToast(swal, err.response?.data?.message)
      } else {
        console.error(err.response?.data?.message)
      }
    }
  }

  return {
    currentAccount,
    isOrg,
    handle,
    getDefaultStatus,
    getDefaultPriority,
    getPriorities,
    getStatuses,
    generateExecutionsProgress,
    getObjectCount,
    isStatusesHasDefault,
    getEntityCompletedStatuses,
    getEntityTodoStatuses,
    isPrioritiesHasDefault,
    getPriorityColor,
    getStatusColor,
    getStatusColorByName,
    getPriorityColorByName,
    getStatusName,
    getPriorityName,
    generateRandomHexColor,
    updatePreferences
  }
}