import { debounce, memoize } from 'lodash';

const cache = new Map();
const pendingRequests = new Map();

const createCacheKey = memoize((queryKey) => {
  return Array.isArray(queryKey) ? queryKey.join('-') : String(queryKey);
});

const debouncedCacheCleanup = debounce(() => {
  const now = Date.now();
  const staleTime = 30000; 
  
  for (const [key, value] of cache.entries()) {
    if (now - value.timestamp > staleTime) {
      cache.delete(key);
    }
  }
}, 5000); 

export function executeApiQuery(queryKey, queryFn, options = {}) {
  const { debounce: enableDebounce = true, staleTime = 30000 } = options;
  
  const cacheKey = createCacheKey(queryKey);
  
  return new Promise(async (resolve, reject) => {
    try {
      if (enableDebounce) {
        const cached = cache.get(cacheKey);
        const now = Date.now();
        
        if (cached && (now - cached.timestamp) < staleTime) {
          resolve(cached.data);
          return;
        }
      }

      if (pendingRequests.has(cacheKey)) {
        const pendingResult = await pendingRequests.get(cacheKey);
        resolve(pendingResult);
        return;
      }

    
      const requestPromise = queryFn();
      
      pendingRequests.set(cacheKey, requestPromise);

      const result = await requestPromise;

      if (enableDebounce) {
        cache.set(cacheKey, {
          data: result,
          timestamp: Date.now()
        });
        
        debouncedCacheCleanup();
      }

      pendingRequests.delete(cacheKey);

      resolve(result);
    } catch (error) {
      pendingRequests.delete(cacheKey);
      reject(error);
    }
  });
}