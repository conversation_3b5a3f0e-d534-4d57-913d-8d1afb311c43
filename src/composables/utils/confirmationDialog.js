import { ref, computed } from 'vue';

export function useConfirmationDialog(initialProps = {}) {
  // Reactive state
  const isVisible = ref(false);
  const title = ref(initialProps.title || '');
  const content = ref(initialProps.content || '');
  const contentPart2 = ref(initialProps.contentPart2 || '');
  const btnLabel = ref(initialProps.btnLabel || 'Confirm');
  const color = ref(initialProps.color || 'primary');
  const milestoneName = ref(initialProps.milestoneName || '');

  // Computed properties
  const dialog = computed({
    get() {
      return isVisible.value;
    },
    set(val) {
      isVisible.value = val;
    }
  });

  // Methods
  const show = (props = {}) => {
    if (props.title !== undefined) title.value = props.title;
    if (props.content !== undefined) content.value = props.content;
    if (props.contentPart2 !== undefined) contentPart2.value = props.contentPart2;
    if (props.btnLabel !== undefined) btnLabel.value = props.btnLabel;
    if (props.color !== undefined) color.value = props.color;
    if (props.milestoneName !== undefined) milestoneName.value = props.milestoneName;
    
    isVisible.value = true;
  };

  const hide = () => {
    isVisible.value = false;
  };

  const close = () => {
    hide();
  };

  const confirm = (callback) => {
    if (typeof callback === 'function') {
      callback();
    }
    hide();
  };

  const cancel = (callback) => {
    if (typeof callback === 'function') {
      callback();
    }
    hide();
  };

  // Update individual properties
  const updateTitle = (newTitle) => {
    title.value = newTitle;
  };

  const updateContent = (newContent) => {
    content.value = newContent;
  };

  const updateContentPart2 = (newContentPart2) => {
    contentPart2.value = newContentPart2;
  };

  const updateBtnLabel = (newBtnLabel) => {
    btnLabel.value = newBtnLabel;
  };

  const updateColor = (newColor) => {
    color.value = newColor;
  };

  const updateMilestoneName = (newMilestoneName) => {
    milestoneName.value = newMilestoneName;
  };

  return {
    // Reactive state
    isVisible,
    title,
    content,
    contentPart2,
    btnLabel,
    color,
    milestoneName,

    // Computed properties
    dialog,

    // Methods
    show,
    hide,
    close,
    confirm,
    cancel,
    updateTitle,
    updateContent,
    updateContentPart2,
    updateBtnLabel,
    updateColor,
    updateMilestoneName
  };
}
