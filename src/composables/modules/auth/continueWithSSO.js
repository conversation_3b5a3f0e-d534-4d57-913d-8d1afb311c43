import { ref, computed, getCurrentInstance, onMounted } from 'vue';
import makeAuthService from '@/services/api/auth';
import makeUserService from '@/services/api/user';
import { showErrorToast, showSuccessToast } from '@/utils/toast';



export function useContinueWithSSO() {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;

  // Services
  let authService;
  let userService;

  // Reactive state
  const loading = ref(false);

  // Computed properties
  const currentAccount = computed(() => {
    return proxy.$store.getters['user/currentAccount'];
  });

  const orgHandle = computed(() => {
    return proxy.$route.params.handle;
  });

  // Methods
  const initializeServices = () => {
    authService = makeAuthService(proxy.$api);
    userService = makeUserService(proxy.$api);
  };

  const initSession = (sessionData) => {
    return proxy.$store.dispatch('user/initSession', sessionData);
  };

  const updatePreferences = (handle) => {
    // This method was called in the original component but not defined
    // It might be from a mixin - we'll implement a basic version
    console.log('Updating preferences for handle:', handle);
  };

  const handleSSOLogin = async (token) => {
    loading.value = true;
    
    try {
      const response = await authService.validateSSOLogin(token);
      const user = response.data;
      const orgResponse = await userService.getOrgs(user.uid);
      const { orgs } = orgResponse.data;

      let account = {
        handle: user.handle,
        type: 'user',
        name: `${user.firstName} ${user.lastName}`,
        roleName: 'owner',
        avatarUrl: user.avatarUrl
      };

      const savedAccount = currentAccount.value;
      if (savedAccount) {
        const matchingOrg = orgs.find((org) => org.uid === savedAccount.uid);
        if (matchingOrg) {
          account = matchingOrg;
        }
      }

      await initSession({ user, currentAccount: account, orgs });
      updatePreferences(account.handle);

      proxy.$router.push({
        name: 'Workspace',
        params: { handle: account.handle },
      });

      const createdAtMs = new Date(user.createdAt).getTime();
      // Consider the user new if created within the last 60 seconds
      const ageInSeconds = (Date.now() - createdAtMs) / 1000;
      
      // gtag event for sso sign in
      if (process.env.NODE_ENV === 'production' && ageInSeconds <= 60) {
        proxy.$gtag.event('sso-signup', {
          event_category: 'user',
          event_label: `User: ${user.handle}`,
          value: 1
        });
      } else if (process.env.NODE_ENV === 'production') {
        proxy.$gtag.event('sso-signin', {
          event_category: 'user',
          event_label: `User: ${user.handle}`,
          value: 1
        });
      }

      showSuccessToast(proxy.$swal, proxy.$t('loginSuccess'));
    } catch (error) {
      showErrorToast(proxy.$swal, error?.response?.data?.errors || error?.response?.data?.message || error?.message || proxy.$t('problemProcessingRequest'));
      
      proxy.$router.push({
        name: 'Login'
      });
    } finally {
      loading.value = false;
    }
  };

  const redirectToSSO = () => {
    if (!orgHandle.value) {
      proxy.$router.push({ name: 'Login' });
      return;
    }

    // Redirect to SSO provider
    const ssoUrl = `${process.env.VUE_APP_API_URL}/auth/sso/${orgHandle.value}`;
    window.location.href = ssoUrl;
  };

  const login = async () => {
    loading.value = true;
    try {
      const res = await authService.loginWithSSO(orgHandle.value);
      window.location.href = res.data.url;
    } catch (error) {
      if (error.response?.status === 404) {
        showErrorToast(proxy.$swal, proxy.$t('error.ssoSigninFailed'));
      } else {
        showErrorToast(proxy.$swal, 'error', { item: 'Authentication' }, error?.response?.data?.errors || error?.response?.data?.message || error?.message);
      }
    } finally {
      loading.value = false;
    }
  };

  // Initialize when composable is used
  onMounted(async () => {
    initializeServices();
    
    const token = proxy.$route.query.loginToken;
    
    if (!orgHandle.value) {
      proxy.$router.push({ name: 'Login' });
      return;
    }
    
    if (token) {
      await handleSSOLogin(token);
    } else {
      // If no token, redirect to SSO provider
      redirectToSSO();
    }
  });

  return {
    // Reactive state
    loading,

    // Computed properties
    currentAccount,
    orgHandle,

    // Methods
    handleSSOLogin,
    redirectToSSO,
    login,
    initializeServices,
  };
}
