import { ref, computed, getCurrentInstance, watch, onMounted } from 'vue';
import makeOrgService from '@/services/api/org';
import makeUserService from '@/services/api/user';
import makeBillingService from '@/services/api/billing';
import makeHandleService from '@/services/api/handle';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import { orgImageTypes } from '@/constants/fileTypes.js';
import { debounce } from 'debounce';

export function useCreateOrg() {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;

  // Services
  let orgService;
  let userService;

  // Reactive state
  const validForm = ref(false);
  const isLoading = ref(false);
  const loadingSubscriptionPlans = ref(false);
  const subscriptionPlans = ref([]);
  const file = ref(null);

  const org = ref({
    name: '',
    handle: '',
  });

  // Handle validation state (from handleDuplicateMixin)
  const handleError = ref(false);
  const handleAvailable = ref(false);
  const handleRequestState = ref({ isLoading: false, isAvailable: false, error: '' });

  // Form refs
  const formRef = ref(null);

  // Computed properties
  const orgNameValidation = computed(() => [
    value => !!value || proxy.$t('error.requiredField'),
    value => (value.length >= 2 && value.length <= 50) || proxy.$t('min2max50Chars')
  ]);

  const accountNameValidation = computed(() => {
    const defaultRules = [
      value => !!value || proxy.$t('error.requiredField'),
      value => /^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/.test(value) || proxy.$t('invalidUsername'),
    ];
    
    if (handleError.value) {
      return [...defaultRules];
    }
    return defaultRules;
  });

  const orgHandleHint = computed(() => {
    if (org.value.handle === '') {
      return proxy.$t('orgAccountNameLabel');
    }
    if (!handleError.value && handleAvailable.value) {
      return 'handle is available';
    }
    return '';
  });

  const pricePerMonth = computed(() => {
    if (subscriptionPlans.value.length) {
      const price = subscriptionPlans.value.find(plan => plan.amount != 0)?.amount;
      return price ? `$${price/100}` : '$0';
    }
    return '$0';
  });

  // Methods
  const initializeServices = () => {
    orgService = makeOrgService(proxy.$api);
    userService = makeUserService(proxy.$api);
  };

  const setOrgs = (orgs) => {
    return proxy.$store.dispatch('user/setOrgs', orgs);
  };



  const validate = () => {
    return formRef.value && formRef.value.validate();
  };

  const reset = () => {
    return formRef.value && formRef.value.reset();
  };

  const resetValidation = () => {
    return formRef.value && formRef.value.resetValidation();
  };

  const checkUsername = async (username) => {
    handleRequestState.value.error = '';
    handleRequestState.value.isLoading = true;
    handleError.value = false;
    handleAvailable.value = false;
    
    try {
      const handleService = makeHandleService(proxy.$api);
      const response = await handleService.handleInUse(username);
      
      if (response.data.handleInUse) {
        handleAvailable.value = false;
        handleError.value = true;
        handleRequestState.value.error = proxy.$t('handleInUse', { handle: username });
      } else {
        handleAvailable.value = true;
        handleError.value = false;
        handleRequestState.value.error = '';
      }
    } catch (err) {
      if (err.status == 409) {
        handleAvailable.value = false;
        handleError.value = true;
        handleRequestState.value.error = proxy.$t('handleInUse', { handle: username });
      } else {
        handleRequestState.value.error = proxy.$t('problemProcessingRequest');
      }
    } finally {
      handleRequestState.value.isLoading = false;
    }
  };

  const usernameInUse = debounce(function (orgHandle) {
    if (orgHandle && orgHandle.length > 2 && !handleRequestState.value.isLoading) {
      checkUsername(orgHandle);
    }
  }, 1000);

  const createOrganization = async () => {
    const isValidForm = validate();

    if (!isValidForm) {
      return;
    }

    isLoading.value = true;
    const data = {
      name: org.value.name,
      handle: org.value.handle,
    };

    try {
      const response = await orgService.newOrg(data);
      
      const { 
        uid,
        name,
        handle,
        roleName,
        createdBy,
        createdAt,
      } = response.data;
      
      const orgData = {
        uid,
        name,
        handle,
        roleName,
        createdBy,
        createdAt,
        type: "org"
      };

      // Get current orgs and add the new one
      const userResponse = await userService.getOrgs(proxy.$store.state?.user?.user?.uid);
      await setOrgs(userResponse.data?.orgs || []);

      showSuccessToast(proxy.$swal, proxy.$t('success.organizationCreated'));
      
      // gtag event for organization creation
      if (process.env.NODE_ENV === 'production') {
        proxy.$gtag.event('create_organization', {
          event_category: 'organization',
          event_label: `Org: ${orgData.handle}`,
          value: 1
        });
      }

      // Redirect to workspace
      proxy.$router.push({
        name: 'Workspace',
        params: { handle: orgData.handle },
      });
    } catch (err) {
      showErrorToast(proxy.$swal, err.response?.data?.message || err.response?.data?.error || err.response?.data[0]?.msg || proxy.$t('error.problemProcessingRequest'));
    } finally {
      isLoading.value = false;
    }
  };

  const skipToWorkspace = async () => {
    try {
      // Get user handle from store state to navigate to personal workspace
      const userHandle = proxy.$store.state.user.user?.handle;
      
      if (userHandle) {
        proxy.$router.push({
          name: 'Workspace',
          params: { handle: userHandle },
        });
      } else {
        // Fallback in case user handle is not available
        const userResponse = await userService.getOrgs(proxy.$store.state?.user?.user?.uid);
        const orgs = userResponse.data?.orgs || [];
        
        if (orgs.length > 0) {
          // Navigate to the first available organization
          proxy.$router.push({
            name: 'Workspace',
            params: { handle: orgs[0].handle },
          });
        } else {
          // If no orgs available, show error
          showErrorToast(proxy.$swal, proxy.$t('error.unableToSkip'));
        }
      }
    } catch (err) {
      showErrorToast(proxy.$swal, proxy.$t('error.problemProcessingRequest'));
    }
  };

  const getSubscriptionPlans = async () => {
    loadingSubscriptionPlans.value = true;
    try {
      const billingService = makeBillingService(proxy.$api);
      const response = await billingService.getSubscriptionPlans({
        model: 'org',
      });
      subscriptionPlans.value = response.data;
    } catch (error) {
      showErrorToast(proxy.$swal, 'fetchError', { item: 'subscription plans' }, error?.response?.data);
    } finally {
      loadingSubscriptionPlans.value = false;
    }
  };

  // Watch for handle changes
  watch(() => org.value.handle, (newHandle) => {
    usernameInUse(newHandle);
  }, { immediate: true });

  // Initialize when composable is used
  onMounted(async () => {
    initializeServices();
    await getSubscriptionPlans();
  });

  return {
    // Reactive state
    validForm,
    isLoading,
    loadingSubscriptionPlans,
    subscriptionPlans,
    file,
    org,
    handleError,
    handleAvailable,
    handleRequestState,
    orgImageTypes,

    // Form refs
    formRef,

    // Computed properties
    orgNameValidation,
    accountNameValidation,
    orgHandleHint,
    pricePerMonth,

    // Methods
    validate,
    reset,
    resetValidation,
    createOrganization,
    skipToWorkspace,
    getSubscriptionPlans,
    checkUsername,
    usernameInUse,
    initializeServices,
  };
}
