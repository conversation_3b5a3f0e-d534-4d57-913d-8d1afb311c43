import { ref, computed, getCurrentInstance } from 'vue';
import makeAuthService from '@/services/api/auth';
import makeUserService from '@/services/api/user';
import { emailOrUsernameValidationRules, requiredFieldValidationRules } from '@/utils/validation';
import { showSuccessToast, showErrorToast } from '@/utils/toast';



export function useLogin() {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;

  // Services
  let authService;
  let userService;

  // Reactive state
  const showSSOLogin = ref(false);
  const status = ref('not_accepted');
  const loginForm = ref(true);
  const visiblePassword = ref(false);
  const signinBtnLoading = ref(false);
  const ssoSignInBtnLoading = ref(false);
  const handleError = ref(false);

  const loginInfo = ref({
    email: '',
    password: '',
  });

  const ssoLoginInfo = ref({
    orgHandle: '',
  });

  // Form refs
  const formRef = ref(null);
  const ssoFormRef = ref(null);
  const observerRef = ref(null);
  const ssoObserverRef = ref(null);

  // Computed properties
  const emailOrUsernameValidation = computed(() => 
    emailOrUsernameValidationRules({ $t: proxy.$t })
  );

  const passwordValidation = computed(() => 
    requiredFieldValidationRules({ $t: proxy.$t })
  );

  const accountNameValidation = computed(() => {
    const defaultRules = [
      value => !!value || proxy.$t('error.requiredField'),
      value => /^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/.test(value) || proxy.$t('invalidOrgHandle'),
    ];
    
    if (handleError.value) {
      return [...defaultRules];
    }
    return defaultRules;
  });

  const currentAccount = computed(() => {
    // Access Vuex getter through the store
    return proxy.$store.getters['user/currentAccount'];
  });

  // Methods
  const initializeServices = () => {
    authService = makeAuthService(proxy.$api);
    userService = makeUserService(proxy.$api);
  };

  const validate = () => {
    return formRef.value && formRef.value.validate();
  };

  const reset = () => {
    return formRef.value && formRef.value.reset();
  };

  const resetValidation = () => {
    return formRef.value && formRef.value.resetValidation();
  };

  const ssoValidate = () => {
    return ssoFormRef.value && ssoFormRef.value.validate();
  };

  const ssoReset = () => {
    return ssoFormRef.value && ssoFormRef.value.reset();
  };

  const ssoResetValidation = () => {
    return ssoFormRef.value && ssoFormRef.value.resetValidation();
  };

  const initSession = (sessionData) => {
    return proxy.$store.dispatch('user/initSession', sessionData);
  };

  const updatePreferences = (handle) => {
    // This method was called in the original component but not defined
    // It might be from a mixin - we'll implement a basic version
    console.log('Updating preferences for handle:', handle);
  };

  const handleGoogleLogin = async (loginToken) => {
    try {
      const response = await authService.validateGoogleLogin(loginToken);
      const user = response.data;

      const orgResponse = await userService.getOrgs(user.uid);
      const { orgs } = orgResponse.data;

      let account = {
        handle: user.handle,
        type: 'user',
        name: `${user.firstName} ${user.lastName}`,
        roleName: 'owner',
        avatarUrl: user.avatarUrl
      };

      const savedAccount = currentAccount.value;
      if (savedAccount) {
        const matchingOrg = orgs.find((org) => org.uid === savedAccount.uid);
        if (matchingOrg) {
          account = matchingOrg;
        }
      }

      await initSession({ user, currentAccount: account, orgs });
      updatePreferences(account.handle);

      showSuccessToast(proxy.$swal, proxy.$t('loginSuccess'));

      proxy.$router.push({
        name: 'Workspace',
        params: { handle: account.handle },
      });
    } catch (error) {
      showErrorToast(proxy.$swal, error?.response?.data?.errors || error?.response?.data?.message || error?.message || proxy.$t('problemProcessingRequest'));
    }
  };

  const login = async () => {
    if (!validate()) return;

    signinBtnLoading.value = true;
    
    try {
      const response = await authService.login(loginInfo.value);
      const { user } = response.data;
      const orgResponse = await userService.getOrgs(user.uid);
      const { orgs } = orgResponse.data;

      let account = {
        handle: user.handle,
        type: 'user',
        name: `${user.firstName} ${user.lastName}`,
        roleName: 'owner',
        avatarUrl: user.avatarUrl
      };

      const savedAccount = currentAccount.value;
      if (savedAccount) {
        const matchingOrg = orgs.find((org) => org.uid === savedAccount.uid);
        if (matchingOrg) {
          account = matchingOrg;
        }
      }

      await initSession({ user, currentAccount: account, orgs });
      updatePreferences(account.handle);

      showSuccessToast(proxy.$swal, proxy.$t('loginSuccess'));

      const comingFromAuth = ['/login', '/signup', '/setup', '/'].includes(proxy.$router.history._startLocation) || 
        proxy.$router.history._startLocation.startsWith('/resetPassword');
      const dest = comingFromAuth
        ? {
          name: 'Workspace',
          params: { handle: account.handle },
        }
        : proxy.$router.history._startLocation;
      proxy.$router.replace(dest);
    } catch (error) {
      showErrorToast(proxy.$swal, error?.response?.data?.errors || error?.response?.data?.message || error?.message || proxy.$t('problemProcessingRequest'));
    } finally {
      signinBtnLoading.value = false;
    }
  };

  const ssoLogin = () => {
    if (ssoValidate()) {
      ssoSignInBtnLoading.value = true;
      proxy.$router.push({
        name: 'continueWithSSO',
        params: { handle: ssoLoginInfo.value.orgHandle }
      });
    }
  };

  // Initialize services when composable is used
  initializeServices();

  return {
    // Reactive state
    showSSOLogin,
    status,
    loginForm,
    visiblePassword,
    loginInfo,
    ssoLoginInfo,
    signinBtnLoading,
    ssoSignInBtnLoading,
    handleError,

    // Form refs
    formRef,
    ssoFormRef,
    observerRef,
    ssoObserverRef,

    // Computed properties
    emailOrUsernameValidation,
    passwordValidation,
    accountNameValidation,
    currentAccount,

    // Methods
    validate,
    reset,
    resetValidation,
    ssoValidate,
    ssoReset,
    ssoResetValidation,
    login,
    ssoLogin,
    handleGoogleLogin,
    initializeServices,
  };
}
