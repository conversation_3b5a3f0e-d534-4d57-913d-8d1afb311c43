import { ref, computed, watch, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router/composables';
import { t } from '@/i18n'
import i18n from '@/i18n';
import makeAuthService from "@/services/api/auth";
import makeOrgService from "@/services/api/org";
import { useStore, $api } from '@/main';
import { emailValidationRules, passwordValidationRules, 
        firstNameValidation as firstNameValidationRules, lastNameValidation as lastNameValidationRules, usernameValidation } from "@/utils/validation";
import { createSelectedHandle } from '@/utils/accountUtils';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import Swal from 'sweetalert2';
import makeHandleService from '@/services/api/handle';
import { debounce } from 'debounce';
import { useGtag } from '@/composables/utils/useGtag'




const user = ref({
    handle: "",
    firstName: "",
    lastName: "",
    email: "",
    password: "",
});

const step = ref(1)
const { trackEvent } = useGtag()

export function useRegister() {
  // Access global plugins from imports
  const router = useRouter();
  const route = useRoute();
  const store = useStore();

  
  
  const visiblePassword = ref(false);
  const visibleConfirmPassword = ref(false);
  const confirmPassword = ref("");
  const invite = ref(null);
  const signupBtnLoading = ref(false);
  const showDeclineDialog = ref(false);
  const declineReason = ref(null);
  const otherReason = ref('');
  const formRef = ref(null);
  const observerRef = ref(null);
  const inviteObserverRef = ref(null);
  const handleRequestState = ref({ isLoading: false, isAvailable: false, error: '' });

  // Computed properties
  const emailValidation = computed(() => emailValidationRules({ $t: t }));
  const firstNameValidation = computed(() => firstNameValidationRules({ $t: t }));
  const lastNameValidation = computed(() => lastNameValidationRules({ $t: t }));
  const passwordValidation = computed(() => passwordValidationRules({ $t: t }));
  const usernameValidations = computed(() => usernameValidation({ $t: t }));
  const disableEmail = computed(() => !!invite.value);
  
  const passwordConfirmationRule = computed(() => {
    return () => (user.value.password === confirmPassword.value) || t('passwordsMustMatch');
  });
  
  const isFormValid = computed(() => {
    return user.value.handle &&
           user.value.firstName &&
           user.value.lastName &&
           user.value.email &&
           user.value.password &&
           confirmPassword.value &&
           user.value.password === confirmPassword.value;
  });

  // Methods
  const validate = () => {
    return formRef.value && formRef.value.validate();
  };
  
  const reset = () => {
    return formRef.value && formRef.value.reset();
  };
  
  const resetValidation = () => {
    return formRef.value && formRef.value.resetValidation();
  };

  const resetForm = () => {
    user.value = {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
    };
    
    // Wait for the next tick to reset the form validators
    setTimeout(() => {
      if (invite.value) {
        inviteObserverRef.value && inviteObserverRef.value.reset();
      } else {
        observerRef.value && observerRef.value.reset();
      }
    });
  };

  const saveUser = async () => {
    if (!validate()) return;
    
    signupBtnLoading.value = true;
    const authService = new makeAuthService($api);
    
    try {
      const response = await authService.register(user.value);
      const userData = response.data.user;
      // gtag event for signup
      if(process.env.NODE_ENV === 'production') {
        trackEvent('signup', {
          event_category: 'user',
          event_label: `User: ${userData.handle}`,
          value: 1
        });
      }

      await store.dispatch('user/initSession', {
        user: userData,
        currentAccount: {
          handle: userData.handle,
          type: "user",
          name: `${userData.firstName} ${userData.lastName}`,
          roleName: "owner"
        }
      });

      if (invite.value) {
        router.push({ name: 'GetInvite', params: invite.value });
      } else {
        step.value = 2;
      }
    } catch (error) {
      let errorMessage;
      if (Array.isArray(error.response.data.error)) {
        errorMessage = error.response.data.errors.join(' ');
      } else {
        errorMessage = error?.response?.data?.errors || error?.response?.data?.message || error?.message
      }
      
      showErrorToast(Swal, errorMessage);
    } finally {
      signupBtnLoading.value = false;
    }
  };

  const checkForInvite = (params) => {
    if (!params?.token || !params.org) return;

    const inviteData = { handle: params.org, token: params.token };
    makeOrgService($api)
      .validateInvite(inviteData)
      .then((response) => {
        invite.value = {
          ...inviteData,
          organization: response.data.name,
          senderName: `${response.data.sender.firstName} ${response.data.sender.lastName}`,
        };
        user.value.email = response.data.email;
      })
      .catch((e) => {
        console.error(e);
        showErrorToast(Swal, t('invalidInvite'));
      });
  };

  const completeRegistration = async () => {
    if (!formRef.value || typeof formRef.value.validate !== 'function') {
      console.error('Form reference is not available');
      showErrorToast(Swal, t('formValidationError'));
      return;
    }

    if (!formRef.value.validate()) {
      return;
    }

    signupBtnLoading.value = true;

    try {
      const authService = new makeAuthService($api);
      const response = await authService.register(user.value);
      
      if (response && response.data && response.data.user) {
        const userData = response.data.user;


        if(process.env.NODE_ENV === 'production') {
          trackEvent('signup', {
            event_category: 'user',
            event_label: `User: ${userData.handle}`,
            value: 1
          });
        }

        await store.dispatch('user/initSession', {
          user: userData,
          currentAccount: {
            handle: userData.handle,
            type: "user",
            name: `${userData.firstName} ${userData.lastName}`,
            roleName: "owner"
          }
        });

        if (invite.value) {
          await handleInvite(true);
        } else {
          router.push('/setup');
        }
      } else {
        throw new Error(t('registrationFailed'));
      }
    } catch (error) {
      let errorMessage = Array.isArray(error.response?.data?.error)
        ? error.response.data.errors.join(' ')
        : error.response?.data?.message || error.message;
      showErrorToast(Swal, errorMessage);
    } finally {
      signupBtnLoading.value = false;
    }
  };

  const handleInvite = async (accept, reason) => {
    const orgService = makeOrgService($api);
    try {
      if (accept) {
        const response = await orgService.acceptInvite({
          handle: invite.value.handle,
          token: invite.value.token,
        });

        if (response.status === 200) {
          const selectedHandle = createSelectedHandle(
            route.query.org,
            invite.value.organization,
            'org',
            'member'
          );

          await store.dispatch('user/setCurrentAccount', selectedHandle);
          showSuccessToast(Swal, 'invitationUpdate', { action: 'Accepted' });
          router.replace({ name: 'ProjectsView', params: { handle: invite.value.handle } });
        }
      } else {
        await orgService.declineInvite({
          handle: invite.value.handle,
          token: invite.value.token,
          reason: reason,
        });
        showErrorToast(Swal, 'invitationUpdate', { action: 'Declined' });
        router.push('/');
      }
    } catch (error) {
      showErrorToast(Swal, error?.response?.data?.errors || error?.response?.data?.message || error?.message);
    }
  };

  const declineInvite = () => {
    showDeclineDialog.value = true;
  };

  const confirmDecline = async () => {
    const reason = declineReason.value === 'other' ? otherReason.value : declineReason.value;
    await handleInvite(false, reason);
    showDeclineDialog.value = false;
  };

  const cancelDecline = () => {
    showDeclineDialog.value = false;
  };

  const checkUsername = async (username) => {
    handleRequestState.value.error = '';
    handleRequestState.value.isLoading = true;
    try {
      const handleService = new makeHandleService($api);
      const response = await handleService.handleInUse(username);
      if (response.data.handleInUse) {
        handleRequestState.value.isAvailable = false;
        handleRequestState.value.error = i18n.t('handleInUse', { handle: username });
      } else {
        handleRequestState.value.isAvailable = true;
        handleRequestState.value.error = '';
      }
    } catch (err) {
      if(err.status == 409){
        handleRequestState.value.isAvailable = false;
        handleRequestState.value.error = i18n.t('handleInUse', { handle: username });
      }else{
        handleRequestState.value.error = t('problemProcessingRequest');
      }
    } finally {
      handleRequestState.value.isLoading = false;
    }
  };

  // Handle username check
  const usernameInUse = debounce(function (orgHandle) {
      if (orgHandle.length > 2 && !this.handleRequestState.isLoading) {
        checkUsername(orgHandle);
      }
  }, 1000);

  // Initialize component
  watch(() => route.query, (newQuery) => {
    checkForInvite(newQuery);
  }, { immediate: true });

    const onCreateOrganization = (orgDetails) =>{
      store.commit('user/setSignupOrgDetails', orgDetails);
      if(process.env.NODE_ENV === 'production') {
        trackEvent('create_organization', {
          event_category: 'organization',
          event_label: `Org: ${orgDetails.handle}`,
          value: 1
        });
      }
      
      router.push('/setup');
    }

    onMounted(async () => {
      const token = route.query.signupToken;
      if (token) {
        try {
          const authService = makeAuthService($api);
          const response = await authService.validateGoogleSignUp(token)
          if (response.status === 200 && response.data) {
            const user = response.data;
            await store.dispatch('user/initSession', {
              user: user,
              currentAccount: {
                handle: user.handle,
                type: "user",
                name: `${user.firstName} ${user.lastName}`,
                roleName: "owner"
              }
            });

            // gtag event for google sign up
            if(process.env.NODE_ENV === 'production') {
              trackEvent('google-signup', {
                event_category: 'user',
                event_label: `User: ${user.handle}`,
                value: 1
              });
            }

            step.value = 2;
          }
        } catch (error) {
          showErrorToast(Swal, error?.response?.data?.errors || error?.response?.data?.message || error?.message);
        }
      }
    })
  return {
    // Refs
    user,
    visiblePassword,
    visibleConfirmPassword,
    confirmPassword,
    invite,
    signupBtnLoading,
    showDeclineDialog,
    step,
    declineReason,
    otherReason,
    formRef,
    observerRef,
    inviteObserverRef,
    handleRequestState,
    // Computed
    emailValidation,
    firstNameValidation,
    lastNameValidation,
    passwordValidation,
    usernameValidations,
    disableEmail,
    passwordConfirmationRule,
    isFormValid,
    
      // Methods
    onCreateOrganization,
    validate,
    reset,
    resetValidation,
    resetForm,
    saveUser,
    checkForInvite,
    completeRegistration,
    handleInvite,
    declineInvite,
    confirmDecline,
    cancelDecline,
    usernameInUse
  };
} 
