import { ref, computed, getCurrentInstance } from 'vue';
import makeAuthService from '@/services/api/auth';


export function useCreatePassword() {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;

  // Services
  let authService;

  // Reactive state
  const visiblePassword = ref(false);
  
  const user = ref({
    handle: '',
    firstName: '',
    lastName: '',
    email: '',
    password: '',
  });

  // Computed properties
  const emailValidation = computed(() => [
    (v) => !!v || proxy.$t('emailRequired'),
    (v) => /.+@.+\..+/.test(v) || proxy.$t('validEmail'),
  ]);

  const nameValidation = computed(() => [
    (v) => !!v || proxy.$t('inputRequired'),
    (v) => (v && v.length >= 2) || proxy.$t('min2Chars'),
  ]);

  const passwordValidation = computed(() => [
    (v) => !!v || proxy.$t('passwordRequired'),
    (v) => (v && v.length >= 6) || proxy.$t('min6Chars'),
  ]);

  const usernameValidation = computed(() => [
    (v) => !!v || proxy.$t('requiredField'),
    (v) =>
      /^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/.test(v) ||
      proxy.$t('invalidUsername'),
  ]);

  // Methods
  const initializeService = () => {
    authService = makeAuthService(proxy.$api);
  };

  const setUser = (userData) => {
    proxy.$store.commit('user/setUser', userData);
  };

  const initSettings = () => {
    return proxy.$store.dispatch('user/initSettings');
  };

  const saveUser = async () => {
    try {
      const response = await authService.register(user.value);
      const userData = response.data.user;

      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));
      await initSettings();

      await proxy.$swal({
        title: proxy.$t('signUpsuccess'),
        icon: 'success',
        showConfirmButton: false,
        position: 'top-end',
        timer: 2000,
        toast: true,
      });

      // Handle navigation logic from original component
      const startLocation = proxy.$router.history._startLocation;
      const currentPath = proxy.$router.currentRoute.path;

      if (
        startLocation !== '/' &&
        startLocation !== '/signup' &&
        startLocation !== '/login' &&
        startLocation !== currentPath
      ) {
        proxy.$router.push(startLocation).catch((e) => {
          console.log(e);
        });
      } else {
        proxy.$router.push('/').catch((e) => {
          console.log(e);
        });
      }
    } catch (error) {
      console.error('Error creating user:', error);

      proxy.$swal({
        icon: 'error',
        title: proxy.$t('error'),
        text: error,
      });
    }
  };

  // Initialize service when composable is used
  initializeService();

  return {
    // Reactive state
    user,
    visiblePassword,

    // Computed properties
    emailValidation,
    nameValidation,
    passwordValidation,
    usernameValidation,

    // Methods
    saveUser,
    setUser,
    initSettings,
    initializeService,
  };
}
