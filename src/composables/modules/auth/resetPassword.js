import { ref, computed, getCurrentInstance, onBeforeMount } from 'vue';
import makeAuthService from '@/services/api/auth';
import { showSuccessToast, showErrorToast } from '@/utils/toast';

export function useResetPassword() {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;

  // Services
  let authService;

  // Reactive state
  const confirmation = ref('');
  const password = ref('');
  const token = ref(null);
  const visiblePassword = ref(false);

  // Computed properties
  const passwordConfirmationRule = computed(() => {
    return () => (password.value === confirmation.value) || proxy.$t('passwordsMustMatch');
  });

  const isFormValid = computed(() => {
    return password.value && 
           confirmation.value && 
           password.value === confirmation.value;
  });

  // Methods
  const initializeService = () => {
    authService = makeAuthService(proxy.$api);
  };

  const verifyResetToken = async () => {
    token.value = proxy.$route.query.token;
    
    try {
      const response = await authService.verifyEmail(token.value);
      if (response.status === 200) {
        showSuccessToast(proxy.$swal, 'verifiedResetlink');
      }
    } catch (error) {
      showErrorToast(proxy.$swal, 'error', { text: error });
      proxy.$router.push('/login').catch(e => {
        console.log(e);
      });
    }
  };

  const resetPassword = async () => {
    token.value = proxy.$route.query.token;
    
    try {
      const response = await authService.resetPassword({
        token: token.value,
        password: password.value,
      });

      proxy.$router.push('/login').catch(e => {
        console.log(e);
      });
      
      showSuccessToast(proxy.$swal, 'resetPasswordSuccess', { message: response.data.message });
    } catch (error) {
      showErrorToast(proxy.$swal, 'resetPasswordError', { error: error.response.data.error });
    }
  };

  // Initialize service and verify token when composable is used
  onBeforeMount(() => {
    initializeService();
    verifyResetToken();
  });

  return {
    // Reactive state
    confirmation,
    password,
    token,
    visiblePassword,

    // Computed properties
    passwordConfirmationRule,
    isFormValid,

    // Methods
    resetPassword,
    verifyResetToken,
    initializeService,
  };
}
