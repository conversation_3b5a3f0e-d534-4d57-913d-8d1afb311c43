import { ref, computed, getCurrentInstance } from 'vue';
import makeAuthService from '@/services/api/auth';
import { showSuccessToast, showErrorToast } from '@/utils/toast';

export function useForgotPassword() {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;

  // Services
  let authService;

  // Reactive state
  const isLoading = ref(false);
  const email = ref('');

  // Computed properties
  const emailValidation = computed(() => [
    v => !!v || proxy.$t('emailRequired'), 
    v => /.+@.+\..+/.test(v) || proxy.$t('validEmail')
  ]);

  // Methods
  const initializeService = () => {
    authService = makeAuthService(proxy.$api);
  };

  const sendLink = async () => {
    isLoading.value = true;
    
    try {
      const response = await authService.forgotPassword({ email: email.value });
      
      if (response.status === 200) {
        showSuccessToast(proxy.$swal, 'passwordResetEmailSent');
        
        proxy.$router.push({
          path: '/confirmEmail',
          query: {
            email: email.value
          }
        });
      }
    } catch (error) {
      showErrorToast(proxy.$swal, 'passwordResetError', { error: error.response.data.error });
    } finally {
      isLoading.value = false;
    }
  };

  // Initialize service when composable is used
  initializeService();

  return {
    // Reactive state
    isLoading,
    email,

    // Computed properties
    emailValidation,

    // Methods
    sendLink,
    initializeService,
  };
}
