import { ref, getCurrentInstance, onMounted } from 'vue';
import makeAuthService from '@/services/api/auth';

export function useConfirmEmail() {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;

  // Services
  let authService;

  // Reactive state
  const isLoading = ref(false);
  const email = ref('');

  // Methods
  const initializeService = () => {
    authService = makeAuthService(proxy.$api);
  };

  const sendLink = async () => {
    isLoading.value = true;
    
    try {
      const response = await authService.forgotPassword({ email: email.value });
      
      if (response.status === 200) {
        proxy.$swal({
          icon: 'success',
          title: proxy.$t('password reset email sent'),
          timer: '2000',
          showConfirmButton: false,
        });
        
        isLoading.value = false;
        
        proxy.$router.push({
          path: '/confirmEmail',
          query: {
            email: email.value
          }
        });
      }
    } catch (error) {
      isLoading.value = false;
      proxy.$swal({
        icon: 'error',
        title: proxy.$t('password reset error'),
        text: error.response.data.error,
        timer: '2000',
        showConfirmButton: false,
      });
    }
  };

  // Initialize when composable is used
  onMounted(() => {
    initializeService();
    email.value = proxy.$route.query.email || '';
  });

  return {
    // Reactive state
    isLoading,
    email,

    // Methods
    sendLink,
    initializeService,
  };
}
