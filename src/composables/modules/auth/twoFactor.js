import { ref, getCurrentInstance } from 'vue';

export function useTwoFactor() {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;

  // Reactive state
  const drawer = ref(true);
  const radioGroup = ref(1);
  const step = ref(2);

  // Methods
  const nextStep = () => {
    if (step.value < 4) {
      step.value++;
    }
  };

  const previousStep = () => {
    if (step.value > 1) {
      step.value--;
    }
  };

  const closeDrawer = () => {
    drawer.value = false;
  };

  const openDrawer = () => {
    drawer.value = true;
  };

  const skipTwoFactor = () => {
    // Navigate to the next page or close the setup
    proxy.$router.push('/dashboard');
  };

  const setupTwoFactor = () => {
    openDrawer();
  };

  return {
    // Reactive state
    drawer,
    radioGroup,
    step,

    // Methods
    nextStep,
    previousStep,
    closeDrawer,
    openDrawer,
    skipTwoFactor,
    setupTwoFactor,
  };
}
