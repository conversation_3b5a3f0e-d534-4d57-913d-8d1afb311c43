import { ref, computed } from 'vue';
import { useRoute } from 'vue-router/composables';
import makeDefectService from '@/services/api/defect';
import makeIntegrationsService from '@/services/api/integrations';
import makeTagService from '@/services/api/tag';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { useStore, $api } from '@/main';
import { t } from '@/i18n';
import convertToADF from 'html-to-adf-converter';
import Swal from 'sweetalert2';

export const useCreateDefect = (props) => {
  const route = useRoute();
  const store = useStore();

  // Reactive data
  const isLoading = ref(true);
  const integrations = ref([]);
  const integrationsForSelect = ref([]);
  const currentIntegration = ref({});
  const testfiestaProjectUid = ref(null);
  const selectedIntegration = ref({
    integrationUid: null,
    service: null,
    tags: [],
  });
  const attachments = ref([]);
  const searchQuery = ref('');
  const totalIntegrations = ref(0);
  const defectName = ref('');
  const selectedDefect = ref(null);
  const existingDefects = ref([]);
  const defectDescription = ref('');
  const saveLoading = ref(false);
  const githubTags = ref([]);
  const isLoadingTags = ref(false);
  const isLoadingDefects = ref(false);
  const services = ref([]);
  const selectedService = ref('');
  const serviceIndex = ref(0);
  const customDefectIds = ref([]);

  // Integration state for complex integrations like Jira
  const integrationState = ref({
    jiraOrganizations: [],
    serviceProjects: [],
    resourceId: null,
    externalProjectUid: null,
    externalProjectName: null,
    typeId: null,
    issueTypes: [],
    fieldData: [],
    blankIssue: {
      fields: {},
    },
    assignees: [],
    reporters: [],
    sprints: [],
    boards: [],
    selectedBoard: null,
    selectedSprint: null,
  });

  // Validation rules
  const fieldRules = ref({
    required: (v) => !!v || t('defect.createNewDefectDialog.fieldRequired'),
  });

  const rules = ref({
    required: (v) => !!v || t('defect.createNewDefectDialog.fieldRequired'),
  });

  // Computed properties
  const isJiraDefectCreation = computed(() => {
    return selectedIntegration.value.service === 'jira' && props.actionSelected === 'Create new defect';
  });

  const actionButtonText = computed(() => {
    if (props.actionSelected === 'Create new defect') {
      return t('defect.createNewDefectDialog.title');
    }
    return t('defect.createNewDefectDialog.addDefect');
  });

  const filteredDefects = computed(() => {
    let defects = [...existingDefects.value];

    // First filter by integration if one is selected
    if (selectedIntegration.value.integrationUid) {
      defects = defects.filter(
        (defect) => Number(defect.integrationSourceUid) === Number(selectedIntegration.value.integrationUid)
      );
    }

    // Then filter by search query if one exists
    if (searchQuery.value) {
      defects = defects.filter((defect) => defect.name.toLowerCase().includes(searchQuery.value.toLowerCase()));
    }

    return defects;
  });

  const filteredDefectsForLinking = computed(() => {
    let defects = [...existingDefects.value];
    
    // Filter by integration
    if (selectedIntegration.value.integrationUid) {
      defects = defects.filter(defect => 
        Number(defect.integrationSourceUid) === Number(selectedIntegration.value.integrationUid)
      );
    }
    
    // Filter by project scope if we have a selected project
    if (integrationState.value.externalProjectUid) {
      if (selectedIntegration.value.service === 'jira') {
        // For Jira, use externalProjectUid
        defects = defects.filter(defect => 
          String(defect.projectScope) === String(integrationState.value.externalProjectUid)
        );
      } else if (selectedIntegration.value.service === 'github') {
        // For GitHub, use project name
        defects = defects.filter(defect => 
          String(defect.projectScope) === String(integrationState.value.externalProjectName)
        );
      }
    }
    
    // Filter by search query
    if (searchQuery.value) {
      defects = defects.filter(defect => 
        defect.name.toLowerCase().includes(searchQuery.value.toLowerCase())
      );
    }
    
    return defects;
  });

  const isAllSelected = computed(() => {
    return githubTags.value.length > 0 && selectedIntegration.value.tags.length === githubTags.value.length;
  });

  const isIndeterminate = computed(() => {
    return selectedIntegration.value.tags.length > 0 && selectedIntegration.value.tags.length < githubTags.value.length;
  });

  const customIntegrationsForCreate = computed(() => {
    return integrations.value
      .filter((integration) => integration.service === 'custom')
      .map((integration) => ({
        uid: integration.uid,
        name: integration.name,
        addUrl: integration.configuration?.addUrl || '',
      }));
  });

  // Methods
  const getServiceName = (service) => {
    switch (service) {
      case 'jira':
        return 'Jira';
      case 'github':
        return 'GitHub';
      default:
        return service;
    }
  };

  const closeDrawerComposable = () => {
    // Reset all selections
    selectedIntegration.value = {
      integrationUid: null,
      service: null,
      tags: [],
    };
    currentIntegration.value = {};
    defectName.value = '';
    defectDescription.value = '';
    return { closeDialog: true };
  };

  const openCustomIntegrationUrl = (integration) => {
    // Find the full integration object to get the addUrl
    const fullIntegration = integrations.value.find(i => i.uid === integration.value);
    if (fullIntegration?.configuration?.addUrl) {
      window.open(fullIntegration.configuration.addUrl, '_blank');
    } else {
      showErrorToast(
        Swal,
        t('defect.createNewDefectDialog.noAddUrlConfigured'),
        {},
        {}
      );
    }
  };

  const initializeData = async () => {
    await Promise.all([
      fetchIntegrationData(),
      fetchExistingDefects(),
      fetchDefectTags()
    ]);
    
    // After fetching integration data, try to restore saved preferences
    await restoreIntegrationPreferences();
  };

  const setServiceFromProps = () => {
    if (props.service && services.value.includes(props.service)) {
      selectedService.value = props.service;
      serviceIndex.value = services.value.indexOf(props.service);
      
      // Update integrations for select based on the selected service
      integrationsForSelect.value = integrations.value
        .filter((i) => i.service === selectedService.value)
        .map((i) => ({
          title: i.name,
          value: i.uid,
          service: i.service,
        }));
      
      // Add custom integrations to the dropdown
      const customIntegrations = integrations.value.filter((i) => i.service === 'custom');
      const customIntegrationOptions = customIntegrations.map((i) => ({
        title: i.name,
        value: i.uid,
        service: i.service,
      }));
      integrationsForSelect.value = [...integrationsForSelect.value, ...customIntegrationOptions];
    } else if (props.service) {
      // Try to match service with case-insensitive comparison
      const normalizedService = props.service.toLowerCase();
      const matchingService = services.value.find(s => s.toLowerCase() === normalizedService);
      if (matchingService) {
        selectedService.value = matchingService;
        serviceIndex.value = services.value.indexOf(matchingService);
        
        // Update integrations for select based on the selected service
        integrationsForSelect.value = integrations.value
          .filter((i) => i.service === selectedService.value)
          .map((i) => ({
            title: i.name,
            value: i.uid,
            service: i.service,
          }));
        
        // Add custom integrations to the dropdown
        const customIntegrations = integrations.value.filter((i) => i.service === 'custom');
        const customIntegrationOptions = customIntegrations.map((i) => ({
          title: i.name,
          value: i.uid,
          service: i.service,
        }));
        integrationsForSelect.value = [...integrationsForSelect.value, ...customIntegrationOptions];
      }
    }
  };

  const fetchIntegrationData = async () => {
    isLoading.value = true;
    try {
      const integrationsService = makeIntegrationsService($api);
      const handle = route.params.handle;
      const params = props.selectedProjectKey
        ? `service=jira,github,custom&projectKey=${props.selectedProjectKey}`
        : `service=jira,github,custom&projectKey=${route.params.key}`;
      const response = await integrationsService.getIntegrations(handle, params);

      if (!response?.data) {
        totalIntegrations.value = 0;
        return;
      }

      totalIntegrations.value = response.data.pagination?.total || 0;

      if (totalIntegrations.value === 0) {
        return;
      }

      // Identify the testfiesta project UID from route
      let currentProjectKey = route.params.key;
      if (props.selectedProjectKey) {
        currentProjectKey = props.selectedProjectKey;
      }
      const foundEntry = Object.entries(response.data.projects).find(
        ([, project]) => project.key === currentProjectKey
      );

      if (!foundEntry) {
        showErrorToast(Swal, t('defect.createNewDefectDialog.projectNotFound'));
        return;
      }

      testfiestaProjectUid.value = foundEntry[0];

      // Filter integrations - include custom integrations regardless of projectConfigurations
      integrations.value = response.data.integrations.filter((i) => {
        if (i.service === 'custom') {
          return true; // Include all custom integrations
        }
        return i.configuration?.projectConfigurations?.length; // For other services, check projectConfigurations
      });
      services.value = response.data.services.filter((service) => service === 'github' || service === 'jira' || service === 'custom');
      selectedService.value = services.value[serviceIndex.value];
      
      // Set service from props if provided (after services are loaded)
      setServiceFromProps();
      
      // Prepare for the integration dropdown - include custom integrations
      integrationsForSelect.value = integrations.value
        .filter((i) => i.service === selectedService.value)
        .map((i) => ({
          title: i.name,
          value: i.uid,
          service: i.service,
        }));
      
      // Add custom integrations to the dropdown
      const customIntegrations = integrations.value.filter((i) => i.service === 'custom');
      const customIntegrationOptions = customIntegrations.map((i) => ({
        title: i.name,
        value: i.uid,
        service: i.service,
      }));
      integrationsForSelect.value = [...integrationsForSelect.value, ...customIntegrationOptions];
      
      // Check and set default integration if no preference is restored
      checkAndSetDefaultIntegration();
      
      // Auto-select single integration if available
      autoSelectSingleIntegration();
    } catch (err) {
      console.error('Integration Error:', err);
      showErrorToast(
        Swal,
        t('defect.createNewDefectDialog.failedToFetchIntegrations'),
        {},
        err?.response?.data
      );
    } finally {
      isLoading.value = false;
    }
  };

  const resetIntegrationState = () => {
    integrationState.value = {
      jiraOrganizations: [],
      serviceProjects: [],
      resourceId: null,
      externalProjectUid: null,
      externalProjectName: null,
      typeId: null,
      issueTypes: [],
      fieldData: [],
      blankIssue: {
        fields: {},
      },
      assignees: [],
      reporters: [],
      sprints: [],
      boards: [],
      selectedBoard: null,
      selectedSprint: null,
    };
  };

  const onIntegrationSelectedComposable = () => {
    const chosenUID = selectedIntegration.value.integrationUid;
    if (!chosenUID) {
      currentIntegration.value = {};
      selectedIntegration.value.service = null;
      resetIntegrationState();
      return;
    }

    const found = integrations.value.find((i) => i.uid === chosenUID);
    if (!found) {
      currentIntegration.value = null;
      selectedIntegration.value.service = null;
      resetIntegrationState();
      return;
    }

    // Handle custom create new defect case
    if (found.service === 'custom' && props.actionSelected === 'Create new defect') {
      openCustomIntegrationUrl({ value: chosenUID });
      selectedIntegration.value.integrationUid = null; // Reset selection
      showSuccessToast(
        Swal,
        t('defect.createNewDefectDialog.customIntegrationOpened')
      );
      return { closeDialog: true };
    }

    // Reset state before setting new integration
    resetIntegrationState();
    customDefectIds.value = [];

    currentIntegration.value = found;
    selectedIntegration.value.service = found.service.toLowerCase();
    
    // Save the selected integration preference
    saveIntegrationPreference();
  };

  const handleFileChange = (event) => {
    const files = Array.from(event.target.files);
    files.forEach((file) => {
      const attachment = {
        file,
        fileName: file.name,
        extension: file.name.split('.').pop(),
        size: Math.round(file.size / 1024),
        progress: 0,
        failed: false,
      };
      attachments.value.push(attachment);
    });
    if (event.target) {
      event.target.value = '';
    }
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    files.forEach((file) => {
      const attachment = {
        file,
        fileName: file.name,
        extension: file.name.split('.').pop(),
        size: Math.round(file.size / 1024),
        progress: 0,
        failed: false,
      };
      attachments.value.push(attachment);
    });
  };

  const removeAttachment = (index) => {
    attachments.value.splice(index, 1);
  };

  const uploadDefectAttachments = async (defectId) => {
    if (!attachments.value.length) return;

    const handle = route.params.handle;
    let projectKey = route.params.key;
    if (props.selectedProjectKey) {
      projectKey = props.selectedProjectKey;
    }
    const defectService = makeDefectService($api);
    const mediaType = 'attachment';

    try {
      const uploadPromises = attachments.value.map((attachment) => {
        const params = {
          handle,
          projectKey,
          defectId,
          relatedTo: 'defect',
        };

        return store.dispatch('attachment/uploadToServer', {
          handle,
          mediaType,
          file: attachment.file,
          apiService: defectService,
          params,
        });
      });

      return await Promise.all(uploadPromises);
    } catch (error) {
      if (error?.status == 507) {
        showErrorToast(Swal, t('error.uploadedAttachments'), {}, 'limitReached', handle);
      } else {
        console.error('Failed to upload attachments:', error);
      }
      throw error;
    }
  };

  const saveDefectComposable = async (integrationDefectComponentRef) => {
    // Create defect validation
    if (props.actionSelected === 'Create new defect') {
              if (!selectedIntegration.value.integrationUid) {
          showErrorToast(Swal, t('defect.createNewDefectDialog.integrationSelectError'));
          return;
        }
        if (!defectName.value?.trim()) {
          showErrorToast(Swal, t('defect.createNewDefectDialog.nameError'));
          return;
        }
        if (!defectDescription.value?.trim()) {
          showErrorToast(Swal, t('defect.createNewDefectDialog.descriptionError'));
          return;
        }
      if (selectedIntegration.value.service === 'jira') {
        if (!integrationDefectComponentRef?.validateStep(2)) {
          return;
        }
      }
    }

          // Select defect validation
      if (props.actionSelected === 'Select defects') {
        if (!selectedDefect.value) {
          showErrorToast(Swal, t('defect.createNewDefectDialog.defectSelectError'));
          return;
        }
      }

      // Custom integration validation for link existing defect
      if (props.actionSelected === 'Link existing defect' && selectedIntegration.value.service === 'custom') {
        if (!customDefectIds.value || customDefectIds.value.length === 0) {
          showErrorToast(Swal, t('defect.createNewDefectDialog.customDefectIdsRequired'));
          return;
        }
      }

    const data = integrationDefectComponentRef?.makeDefectData();
    saveLoading.value = true;
    
    try {
      const handle = route.params.handle;
      let projectKey = route.params.key;
      if (props.selectedProjectKey) {
        projectKey = props.selectedProjectKey;
      }
      const defectService = makeDefectService($api);

      let description = defectDescription.value.trim();
      
      // Convert HTML description to ADF for Jira
      if (selectedIntegration.value.service === 'jira' && description) {
        try {
          // Convert HTML to ADF format
          description = convertToADF(description);
        } catch (error) {
          console.error('Error converting HTML to ADF:', error);
          // Fallback to original description if conversion fails
        }
      }

      // Create the defect data without files
      const defectData = {
        name: defectName.value.trim(),
        description: description,
        tags: selectedIntegration.value.service === 'github' ? selectedIntegration.value.tags : undefined,
        executionUid: props.execution?.uid,
        resultUid: props.resultUid,
        testRunUid: props.execution?.testRunUid,
        testCaseUid: props.execution?.testCaseRef,
        integrationUid: selectedIntegration.value.integrationUid,
        ...data,
      };

      // Add custom defect IDs for custom integrations
      if (props.actionSelected === 'Link existing defect' && selectedIntegration.value.service === 'custom') {
        defectData.customDefectIds = customDefectIds.value;
      }

      let response;
      if (props.actionSelected === 'Create new defect') {
        response = await defectService.createDefect(handle, projectKey, defectData);
        selectedDefect.value = response.data.uid;
      } else if (props.actionSelected === 'Link existing defect' && selectedIntegration.value.service === 'custom') {
        // Use the new external link API for custom integrations
        response = await defectService.linkExternalDefect(handle, projectKey, defectData);
      } else {
        response = await defectService.linkDefect(handle, projectKey, selectedDefect.value, defectData);
      }

      // If we have attachments, upload them
      if (attachments.value.length > 0) {
        try {
          await uploadDefectAttachments(selectedDefect.value);
        } catch (error) {
          console.error('Failed to upload attachments:', error);
          showErrorToast(
            Swal,
            t('defect.createNewDefectDialog.failedToUploadAttachments'),
            {},
            error?.response?.data
          );
        }
      }

      showSuccessToast(
        Swal,
        props.actionSelected === 'Create new defect' ? t('success.defectCreated') : t('success.defectLinked')
      );
      saveLoading.value = false;
      return { closeDialog: true, defectCreated: true };
    } catch (error) {
      console.error('Failed to save defect:', error);
      showErrorToast(
        Swal,
        t('defect.createNewDefectDialog.defectCreateFailed'),
        {},
        error?.response?.data
      );
      if (error.response?.data?.data) {
        showErrorToast(Swal, error.response.data.data);
      }
    } finally {
      saveLoading.value = false;
    }
  };

  const fetchExistingDefects = async () => {
    isLoadingDefects.value = true;
    try {
      const defectService = makeDefectService($api);
      const handle = route.params.handle;
      let projectKey = route.params.key;
      if (props.selectedProjectKey) {
        projectKey = props.selectedProjectKey;
      }
      
      // Use the selected service or fallback to the first available service
      const serviceToUse = selectedService.value || (services.value.length > 0 ? services.value[0] : 'jira');
      
      const response = await defectService.getDefects(handle, projectKey, {
        offset: 0,
        limit: 9999,
        integrationService: serviceToUse,
        status: 'open',
      });

      // Filter out closed defects using the same logic as the table
      existingDefects.value = response.data.items
        .map((defect) => {
          let displayName = defect.name;
          
          // Add external ID before the name
          if (defect.externalId) {
            if (selectedService.value === 'github') {
              // For GitHub, use # prefix
              displayName = `#${defect.externalId}: ${defect.name}`;
            } else {
              // For other services (like Jira), use externalId: name format
              displayName = `${defect.externalId}: ${defect.name}`;
            }
          }
          
          return {
            id: defect.uid,
            name: displayName,
            externalId: defect.externalId,
            integrationSourceUid: defect.integrationSourceUid,
            projectScope: defect.customFields?.projectScope,
          };
        });
    } catch (err) {
      console.error('Failed to fetch defects:', err);
              showErrorToast(
          Swal,
          t('defect.createNewDefectDialog.failedToFetchDefects'),
          {},
          err?.response?.data
        );
    } finally {
      isLoadingDefects.value = false;
    }
  };

  const fetchDefectTags = async () => {
    try {
      const tagService = makeTagService($api);
      const response = await tagService.getTags(route.params.handle, 'defects');
      githubTags.value = response.data;
    } catch (err) {
      console.error('Error fetching defect tags:', err);
      showErrorToast(Swal, t('defect.createNewDefectDialog.failedToFetchTags'), {}, err?.response?.data);
    }
  };

  const handleCancelComposable = () => {
    return closeDrawerComposable();
  };

  const handleActionComposable = async (integrationDefectComponentRef) => {
    if (isJiraDefectCreation.value) {
      if (!validateStep1()) {
        return;
      }
    } else {
      if (props.actionSelected === 'Create new defect' && !integrationDefectComponentRef?.validateStep(1)) {
        return;
      }
    }
    return await saveDefectComposable(integrationDefectComponentRef);
  };

  const toggleSelectAll = () => {
    if (isAllSelected.value) {
      selectedIntegration.value.tags = [];
    } else {
      selectedIntegration.value.tags = githubTags.value.map((tag) => tag.name);
    }
  };

  const removeTag = (tagName) => {
    const index = selectedIntegration.value.tags.indexOf(tagName);
    if (index >= 0) {
      selectedIntegration.value.tags.splice(index, 1);
    }
  };

  const toggleTag = (item) => {
    const index = selectedIntegration.value.tags.indexOf(item.name);
    if (index === -1) {
      selectedIntegration.value.tags.push(item.name);
    } else {
      selectedIntegration.value.tags.splice(index, 1);
    }
  };

  const isTagSelected = (tagName) => {
    return selectedIntegration.value.tags.includes(tagName);
  };

  const isTextTruncated = (text) => {
    // Only show tooltip for text that's likely to be truncated in the dropdown
    if (!text) return false;
    
    // For GitHub defects with # prefix, they're more likely to be truncated
    if (text.startsWith('#')) {
      return text.length > 45;
    }
    
    // For other defects (like Jira), check if they have external ID format
    if (text.includes(': ')) {
      return text.length > 50;
    }
    
    // For plain text without external ID, use a higher threshold
    return text.length > 60;
  };

  const toggleServiceComposable = () => {
    serviceIndex.value = (serviceIndex.value + 1) % services.value.length;
    selectedService.value = services.value[serviceIndex.value];

    // Reset selections when switching services
    selectedIntegration.value = {
      integrationUid: null,
      service: null,
      tags: [],
    };
    resetIntegrationState();
    fetchExistingDefects();
    
    // Update integrations list for the new service
    integrationsForSelect.value = integrations.value
      .filter((i) => i.service === selectedService.value)
      .map((i) => ({
        title: i.name,
        value: i.uid,
        service: i.service,
      }));
    
    // Add custom integrations to the dropdown
    const customIntegrations = integrations.value.filter((i) => i.service === 'custom');
    const customIntegrationOptions = customIntegrations.map((i) => ({
      title: i.name,
      value: i.uid,
      service: i.service,
    }));
    integrationsForSelect.value = [...integrationsForSelect.value, ...customIntegrationOptions];
    
    // Check if there's a saved preference for this service
    const handle = route.params.handle;
    let projectKey = route.params.key;
    if (props.selectedProjectKey) {
      projectKey = props.selectedProjectKey;
    }
    
    const savedPreference = store.getters['defects/getLastSelectedIntegration'](handle, projectKey, selectedService.value);
    
    if (savedPreference && savedPreference.service === selectedService.value) {
      // Check if the saved integration is still available for this service
      const availableIntegration = integrations.value.find(i => 
        i.uid === savedPreference.integrationUid && 
        i.service === selectedService.value
      );
      
      if (availableIntegration) {
        selectedIntegration.value = {
          integrationUid: savedPreference.integrationUid,
          service: savedPreference.service,
          tags: []
        };
        currentIntegration.value = availableIntegration;
      }
    }
    
    // Auto-select single integration if available for the new service
    autoSelectSingleIntegration();
    
    // Save the service preference separately
    store.dispatch('defects/setCreateDefectIntegrationPreference', {
      handle,
      projectKey,
      service: selectedService.value,
      integrationUid: null // Clear integration when changing service
    });
  };

  const updateIntegrationStateComposable = ({ key, value }) => {
    integrationState.value[key] = value;
  };

  const saveIntegrationPreference = () => {
    // If service prop is provided, don't save service preference - use the prop
    if (props.service) {
      return;
    }
    
    const handle = route.params.handle;
    let projectKey = route.params.key;
    if (props.selectedProjectKey) {
      projectKey = props.selectedProjectKey;
    }
    
    // Only save if we have an integration selected
    if (selectedIntegration.value.integrationUid && selectedIntegration.value.service) {
      store.dispatch('defects/setLastSelectedIntegration', {
        handle,
        projectKey,
        service: selectedIntegration.value.service,
        integrationUid: selectedIntegration.value.integrationUid
      });
    }
  };

  const restoreIntegrationPreferences = async () => {
    // If service prop is provided, don't restore service preference from store - use the prop
    if (props.service) {
      return;
    }
    
    const handle = store.state.user.currentAccount.handle;
    let projectKey = route.params.key;
    if (props.selectedProjectKey) {
      projectKey = props.selectedProjectKey;
    }
    
    // First get the service preference
    const servicePreference = store.getters['defects/getCreateDefectIntegrationPreference'](handle, projectKey);
    
    if (servicePreference && servicePreference.service) {
      // First, set the service and update service index
      selectedService.value = servicePreference.service;
      const foundServiceIndex = services.value.findIndex(s => s === servicePreference.service);
      if (foundServiceIndex !== -1) {
        serviceIndex.value = foundServiceIndex;
      }
      
      // Update integrations for select dropdown for this service
      integrationsForSelect.value = integrations.value
        .filter((i) => i.service === selectedService.value)
        .map((i) => ({
          title: i.name,
          value: i.uid,
          service: i.service,
        }));
      
      // Add custom integrations to the dropdown
      const customIntegrations = integrations.value.filter((i) => i.service === 'custom');
      const customIntegrationOptions = customIntegrations.map((i) => ({
        title: i.name,
        value: i.uid,
        service: i.service,
      }));
      integrationsForSelect.value = [...integrationsForSelect.value, ...customIntegrationOptions];
      
      // Fetch existing defects for the restored service
      await fetchExistingDefects();
      
      // Then get the integration preference for that service
      const savedPreference = store.getters['defects/getLastSelectedIntegration'](handle, projectKey, servicePreference.service);
      
      // If there's a saved integration UID, try to restore it
      if (savedPreference.integrationUid) {
        const availableIntegration = integrations.value.find(i => 
          i.uid === savedPreference.integrationUid && 
          i.service === savedPreference.service
        );
        
        if (availableIntegration) {
          // Set the saved integration
          selectedIntegration.value = {
            integrationUid: savedPreference.integrationUid,
            service: savedPreference.service,
            tags: []
          };
          currentIntegration.value = availableIntegration;
        }
        // If saved integration is not available, autoSelectSingleIntegration will handle it
      }
      
      // Auto-select single integration if available for the restored service
      autoSelectSingleIntegration();
    }
  };

  const checkAndSetDefaultIntegration = () => {
    // If no saved preference and integrations are available, set a default
    if (!selectedIntegration.value.integrationUid && !selectedService.value && integrations.value.length > 0) {
      // Group integrations by service
      const integrationsByService = {};
      integrations.value.forEach(integration => {
        if (!integrationsByService[integration.service]) {
          integrationsByService[integration.service] = [];
        }
        integrationsByService[integration.service].push(integration);
      });
      
      // Priority order: jira, github, custom
      const priorityServices = ['jira', 'github', 'custom'];
      
      for (const service of priorityServices) {
        if (integrationsByService[service] && integrationsByService[service].length > 0) {
          // Set the first available integration for this service
          const defaultIntegration = integrationsByService[service][0];
          selectedIntegration.value = {
            integrationUid: defaultIntegration.uid,
            service: defaultIntegration.service,
            tags: []
          };
          currentIntegration.value = defaultIntegration;
          selectedService.value = defaultIntegration.service;
          
          // Update service index
          const foundServiceIndex = services.value.findIndex(s => s === defaultIntegration.service);
          if (foundServiceIndex !== -1) {
            serviceIndex.value = foundServiceIndex;
          }
          
          // Save this as preference
          saveIntegrationPreference();
          break;
        }
      }
    }
  };

  const autoSelectSingleIntegration = () => {
    // For each service, if there's only one integration, auto-select it
    const integrationsByService = {};
    integrations.value.forEach(integration => {
      if (!integrationsByService[integration.service]) {
        integrationsByService[integration.service] = [];
      }
      integrationsByService[integration.service].push(integration);
    });
    
    // Check if current service has only one integration and none is selected
    if (selectedService.value && integrationsByService[selectedService.value] && 
        integrationsByService[selectedService.value].length === 1) {
      
      // Only auto-select if no integration is currently selected OR if the selected integration is not available
      const shouldAutoSelect = !selectedIntegration.value.integrationUid || 
        !integrations.value.find(i => i.uid === selectedIntegration.value.integrationUid);
      
      if (shouldAutoSelect) {
        const singleIntegration = integrationsByService[selectedService.value][0];
        selectedIntegration.value = {
          integrationUid: singleIntegration.uid,
          service: singleIntegration.service,
          tags: []
        };
        currentIntegration.value = singleIntegration;
        
        // Save this as preference
        saveIntegrationPreference();
      }
    }
  };

  const saveRepoProjectPreference = (repoProjectUid, repoProjectName, resourceId) => {
    const handle = route.params.handle;
    let projectKey = route.params.key;
    if (props.selectedProjectKey) {
      projectKey = props.selectedProjectKey;
    }
    
    // Only save if we have an integration selected
    if (selectedIntegration.value.integrationUid) {
      store.dispatch('defects/setLastSelectedRepoProject', {
        handle,
        projectKey,
        integrationUid: selectedIntegration.value.integrationUid,
        repoProjectUid,
        repoProjectName,
        resourceId
      });
    }
  };

  const restoreRepoProjectPreference = () => {
    const handle = route.params.handle;
    let projectKey = route.params.key;
    if (props.selectedProjectKey) {
      projectKey = props.selectedProjectKey;
    }
    
    // Only restore if we have an integration selected
    if (selectedIntegration.value.integrationUid) {
      const savedPreference = store.getters['defects/getLastSelectedRepoProject'](
        handle, 
        projectKey, 
        selectedIntegration.value.integrationUid
      );
      
      if (savedPreference && savedPreference.repoProjectUid) {
        // Return the saved preference for the integration component to use
        return savedPreference;
      }
    }
    
    return null;
  };

  const autoSelectSingleRepoProject = (repoProjects) => {
    // If no saved preference and only one repo/project available, auto-select it
    if (repoProjects && repoProjects.length === 1) {
      const singleRepoProject = repoProjects[0];
      return {
        repoProjectUid: singleRepoProject.value || singleRepoProject.uid || singleRepoProject.id,
        repoProjectName: singleRepoProject.title || singleRepoProject.name,
        resourceId: null // Will be set by the component when auto-selecting
      };
    }
    
    return null;
  };

  const getSavedJiraOrgPreference = () => {
    const handle = route.params.handle;
    let projectKey = route.params.key;
    if (props.selectedProjectKey) {
      projectKey = props.selectedProjectKey;
    }
    
    // Only check if we have an integration selected
    if (selectedIntegration.value.integrationUid) {
      const savedPreference = store.getters['defects/getLastSelectedRepoProject'](
        handle, 
        projectKey, 
        selectedIntegration.value.integrationUid
      );
      
      if (savedPreference && savedPreference.resourceId) {
        return savedPreference.resourceId;
      }
    }
    
    return null;
  };

  // Direct functions that should replace wrapper methods
  const closeDrawer = (emit) => {
    const result = closeDrawerComposable();
    if (result?.closeDialog && emit) {
      emit('closeDialog');
    }
    return result;
  };

  const onIntegrationSelected = (emit) => {
    const result = onIntegrationSelectedComposable();
    if (result?.closeDialog && emit) {
      emit('closeDialog');
    }
    return result;
  };

  const saveDefect = async (integrationDefectComponentRef, emit) => {
    const result = await saveDefectComposable(integrationDefectComponentRef);
    if (result?.defectCreated && emit) {
      emit('defectCreated');
    }
    if (result?.defectLinked && emit) {
      emit('defectLinked');
    }
    if (result?.closeDialog && emit) {
      emit('closeDialog');
    }
    return result;
  };

  const handleCancel = (emit) => {
    const result = handleCancelComposable();
    if (result?.closeDialog && emit) {
      emit('closeDialog');
    }
    return result;
  };

  const handleAction = async (integrationDefectComponentRef, emit) => {
    const result = await handleActionComposable(integrationDefectComponentRef);
    if (result?.defectCreated && emit) {
      emit('defectCreated');
    }
    if (result?.defectLinked && emit) {
      emit('defectLinked');
    }
    if (result?.closeDialog && emit) {
      emit('closeDialog');
    }
    return result;
  };

  const validateStep1 = () => {
    if (!selectedIntegration.value.integrationUid) {
      showErrorToast(Swal, t('defect.createNewDefectDialog.integrationSelectError'));
      return false;
    }
    if (!defectName.value) {
      const item = selectedService.value === 'github' ? 'title' : 'summary';
      showErrorToast(Swal, t('defect.createNewDefectDialog.nameError', { item }));
      return false;
    }
    if (!defectDescription.value) {
      showErrorToast(Swal, t('defect.createNewDefectDialog.descriptionError'));
      return false;
    }
    return true;
  };

  const toggleService = () => {
    serviceIndex.value = (serviceIndex.value + 1) % services.value.length;
    selectedService.value = services.value[serviceIndex.value];

    // Reset selections when switching services
    selectedIntegration.value = {
      integrationUid: null,
      service: null,
      tags: [],
    };
    resetIntegrationState();
    fetchExistingDefects();
    
    // Update integrations list for the new service
    integrationsForSelect.value = integrations.value
      .filter((i) => i.service === selectedService.value)
      .map((i) => ({
        title: i.name,
        value: i.uid,
        service: i.service,
      }));
    
    // Add custom integrations to the dropdown
    const customIntegrations = integrations.value.filter((i) => i.service === 'custom');
    const customIntegrationOptions = customIntegrations.map((i) => ({
      title: i.name,
      value: i.uid,
      service: i.service,
    }));
    integrationsForSelect.value = [...integrationsForSelect.value, ...customIntegrationOptions];
    
    // Check if there's a saved preference for this service
    const handle = route.params.handle;
    let projectKey = route.params.key;
    if (props.selectedProjectKey) {
      projectKey = props.selectedProjectKey;
    }
    
    const savedPreference = store.getters['defects/getLastSelectedIntegration'](handle, projectKey, selectedService.value);
    
    if (savedPreference && savedPreference.service === selectedService.value) {
      // Check if the saved integration is still available for this service
      const availableIntegration = integrations.value.find(i => 
        i.uid === savedPreference.integrationUid && 
        i.service === selectedService.value
      );
      
      if (availableIntegration) {
        selectedIntegration.value = {
          integrationUid: savedPreference.integrationUid,
          service: savedPreference.service,
          tags: []
        };
        currentIntegration.value = availableIntegration;
      }
    }
    
    // Auto-select single integration if available for the new service
    autoSelectSingleIntegration();
    
    // Save the service preference separately
    store.dispatch('defects/setCreateDefectIntegrationPreference', {
      handle,
      projectKey,
      service: selectedService.value,
      integrationUid: null // Clear integration when changing service
    });
  };

  const updateIntegrationState = ({ key, value }) => {
    integrationState.value[key] = value;
  };

  const onProjectChanged = (projectData) => {
    // Update the integration state with the new project data
    integrationState.value.externalProjectUid = projectData.externalProjectUid;
    integrationState.value.externalProjectName = projectData.externalProjectName;
    integrationState.value.resourceId = projectData.resourceId;
  };

  return {
    // State
    isLoading,
    integrations,
    integrationsForSelect,
    currentIntegration,
    testfiestaProjectUid,
    selectedIntegration,
    attachments,
    searchQuery,
    totalIntegrations,
    defectName,
    selectedDefect,
    existingDefects,
    defectDescription,
    saveLoading,
    githubTags,
    isLoadingTags,
    isLoadingDefects,
    services,
    selectedService,
    serviceIndex,
    customDefectIds,
    integrationState,
    fieldRules,
    rules,

    // Computed
    isJiraDefectCreation,
    actionButtonText,
    filteredDefects,
    filteredDefectsForLinking,
    isAllSelected,
    isIndeterminate,
    customIntegrationsForCreate,

    // Methods
    getServiceName,
    closeDrawerComposable,
    openCustomIntegrationUrl,
    initializeData,
    fetchIntegrationData,
    resetIntegrationState,
    onIntegrationSelectedComposable,
    handleFileChange,
    handleDrop,
    removeAttachment,
    uploadDefectAttachments,
    saveDefectComposable,
    fetchExistingDefects,
    fetchDefectTags,
    handleCancelComposable,
    handleActionComposable,
    validateStep1,
    toggleSelectAll,
    removeTag,
    toggleTag,
    isTagSelected,
    isTextTruncated,
    toggleServiceComposable,
    updateIntegrationStateComposable,
    saveIntegrationPreference,
    restoreIntegrationPreferences,
    checkAndSetDefaultIntegration,
    autoSelectSingleIntegration,
    saveRepoProjectPreference,
    restoreRepoProjectPreference,
    autoSelectSingleRepoProject,
    getSavedJiraOrgPreference,
    // Direct functions (not wrappers)
    closeDrawer,
    onIntegrationSelected,
    saveDefect,
    handleCancel,
    handleAction,
    toggleService,
    updateIntegrationState,
    onProjectChanged,
  };
};


//http://localhost:5050/core/ok/integrations/2/data?type=assignees&externalProjectUid=10013&resourceId=a8e3149c-94d8-4481-a6a0-338d22219fbc
//http://localhost:5050/core/ok/integrations/2/data?type=issueTypes&resourceId=a8e3149c-94d8-4481-a6a0-338d22219fbc&externalProjectUid=10013