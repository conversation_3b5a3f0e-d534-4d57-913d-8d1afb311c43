import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import makeDefectService from '@/services/api/defect';
import makeTagService from '@/services/api/tag';
import makeIntegrationsService from '@/services/api/integrations';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { usePermissions } from '@/composables/utils/permissions';
// We'll use the store directly for skeleton loading to match the component's mixin
import { useStore, $api } from '@/main';
import { t } from '@/i18n';
import dayjs from 'dayjs';
import Swal from 'sweetalert2';
import convertToADF from 'html-to-adf-converter';

  export const useDefectsIndex = () => {
  const route = useRoute();
  const router = useRouter();
  const { authorityTo } = usePermissions();
  const store = useStore();
  
  // Use Vuex store actions for skeleton loading to match component mixin
  const showSkeletonLoader = () => store.dispatch('skeletons/showSkeletonLoader');
  const hideSkeletonLoader = () => store.dispatch('skeletons/hideSkeletonLoader');

  const defectsCache = ref([]);
  const totalDefectsCache = ref(0);
  const lastFetchedPage = ref(0);
  const lastFetchedFilter = ref('');
  const lastFetchedSearch = ref('');
  const lastFetchedHandle = ref('');
  const lastFetchedProjectKey = ref('');
  const lastFetchedIntegration = ref('');
  const lastFetchedActiveState = ref('');
  const hasInitiallyLoaded = ref(false);

  // Reactive data
  const defects = ref([]);
  const selectedDefect = ref(null);
  const originalDefects = ref([]);
  const filteredDefects = ref([]);
  const priorities = ref([]);
  const statuses = ref([]);
  const systemTags = ref([]);
  const statusScopes = ref([]);
  const closedDefects = ref(0);
  const openDefects = ref(0);
  const activeState = ref('active');
  const selectedIntegration = ref(null);
  const integrationsList = ref(['Jira', 'Github']);

  // Pagination state
  const currentPage = ref(1);
  const itemsPerPage = ref(10);
  const totalItems = computed(() => {
    if (!hasInitiallyLoaded.value && defectsCache.value.length === 0) {
      return 0;
    }
    return totalDefectsCache.value;
  });
  const totalPages = computed(() => Math.ceil(totalDefectsCache.value / itemsPerPage.value));

  // Filter state
  const filter = ref({
    name: '',
    priorities: [],
    statuses: [],
    startDate: null,
    endDate: null,
    tags: [],
  });

  // Filter count
  const filterCount = ref(0);

  // Dialog states
  const showDetailDialog = ref(false);
  const showEditDialog = ref(false);
  const editLoading = ref(false);
  const isLoadingStatusScopes = ref(false);

  // Loading states
  const isInitializing = ref(false);
  const countsLoaded = ref(false);

  // Helper function to check if we need to fetch new data
  const needsDataFetch = (pageNumber, pageSize, filterValue, searchValue, handleValue, projectKeyValue, integrationValue, activeStateValue) => {
    // Always fetch if context changed
    if (filterValue !== lastFetchedFilter.value ||
        searchValue !== lastFetchedSearch.value ||
        handleValue !== lastFetchedHandle.value ||
        projectKeyValue !== lastFetchedProjectKey.value ||
        integrationValue !== lastFetchedIntegration.value ||
        activeStateValue !== lastFetchedActiveState.value) {
      return true;
    }

    // Calculate required data range
    const requiredEndIndex = pageNumber * pageSize;
    const currentCachedCount = defectsCache.value.length;
    
    // Need to fetch if we don't have enough cached data
    return requiredEndIndex > currentCachedCount && currentCachedCount < totalItems.value;
  };

  // Helper function to clear cache
  const clearCache = () => {
    defectsCache.value = [];
    totalDefectsCache.value = 0;
    lastFetchedPage.value = 0;
    lastFetchedFilter.value = '';
    lastFetchedSearch.value = '';
    lastFetchedHandle.value = '';
    lastFetchedProjectKey.value = '';
    lastFetchedIntegration.value = '';
    lastFetchedActiveState.value = '';
    hasInitiallyLoaded.value = false;
    countsLoaded.value = false;
  };

  // Function to reset all module-level cache (for component destruction)
  const resetModuleCache = () => {
    defectsCache.value = [];
    totalDefectsCache.value = 0;
    lastFetchedPage.value = 0;
    lastFetchedFilter.value = '';
    lastFetchedSearch.value = '';
    lastFetchedHandle.value = '';
    lastFetchedProjectKey.value = '';
    lastFetchedIntegration.value = '';
    lastFetchedActiveState.value = '';
    hasInitiallyLoaded.value = false;
    countsLoaded.value = false;
  };

  // Comprehensive clear function to reset all state when navigating away
  const clearAllState = () => {
    // Clear module-level cache
    clearCache();
    
    // Clear all reactive data
    defects.value = [];
    selectedDefect.value = null;
    filteredDefects.value = [];
    priorities.value = [];
    statuses.value = [];
    systemTags.value = [];
    statusScopes.value = [];
    closedDefects.value = 0;
    openDefects.value = 0;
    activeState.value = 'active';
    selectedIntegration.value = null;
    integrationsList.value = ['Jira', 'Github']; // Reset to default values
    
    // Reset pagination
    currentPage.value = 1;
    itemsPerPage.value = 10;
    
    // Clear filters
    filter.value = {
      name: '',
      priorities: [],
      statuses: [],
      startDate: null,
      endDate: null,
      tags: [],
    };
    
    // Reset filter count
    filterCount.value = 0;
    
    // Clear dialogs
    showDetailDialog.value = false;
    showEditDialog.value = false;
    editLoading.value = false;
    isLoadingStatusScopes.value = false;
    
    // Reset loading states
    isInitializing.value = false;
  };

  const forceRefresh = async () => {
    clearCache();
    await getDefects(true);
  };

  // Computed properties
  const hasInitiallyLoadedComputed = computed(() => {
    return originalDefects.value.length > 0;
  });

  const currentSelectedDefectIndex = computed(() => {
    return filteredDefects.value.findIndex(defect => defect.uid === selectedDefect.value?.uid);
  });

  const isSelectedDefectFirstIndex = computed(() => {
    return currentSelectedDefectIndex.value === 0;
  });

  const isSelectedDefectLastIndex = computed(() => {
    return currentSelectedDefectIndex.value === filteredDefects.value.length - 1;
  });

  const nextDefect = computed(() => {
    const currentIndex = currentSelectedDefectIndex.value;
    return currentIndex >= 0 && currentIndex < filteredDefects.value.length - 1 
      ? filteredDefects.value[currentIndex + 1] 
      : null;
  });

  const previousDefect = computed(() => {
    const currentIndex = currentSelectedDefectIndex.value;
    return currentIndex > 0 
      ? filteredDefects.value[currentIndex - 1] 
      : null;
  });

  const hasActiveFilters = computed(() => {
    return (
      filter.value.priorities.length > 0 ||
      filter.value.statuses.length > 0 ||
      filter.value.startDate ||
      filter.value.endDate ||
      filter.value.tags.length > 0
    );
  });

  const activeFiltersCount = computed(() => {
    let count = 0;
    if (filter.value.priorities.length) count++;
    if (filter.value.statuses.length) count++;
    if (filter.value.startDate || filter.value.endDate) count++;
    if (filter.value.tags.length) count++;
    return count;
  });

  // Watch for filter changes and update count
  watch(filter, () => {
    countFilter();
  }, { deep: true });

  const paginatedDefects = computed(() => {
    return filteredDefects.value;
  });

  const writeDefect = computed(() => {
    return authorityTo('write_defect');
  });

  const deleteDefect = computed(() => {
    return authorityTo('delete_defect');
  });

  const readDefect = computed(() => {
    return authorityTo('read_defect');
  });

  // Methods
  const getDefectsOpenCount = async () => {
    try {
      const defectService = makeDefectService($api);
      const response = await defectService.getDefectsOpenCount(
        store.state.user.currentAccount.handle, 
        route.params.key,
        selectedIntegration.value.toLowerCase()
      );
      openDefects.value = Number(response.data.count);
      return Number(response.data.count);
    } catch (err) {
      showErrorToast(Swal, t('toast.fetchError', { item: t('defects') }), {}, err?.response?.data);
      return 0;
    }
  };

  const getDefectsClosedCount = async () => {
    try {
      const defectService = makeDefectService($api);
      const response = await defectService.getDefectsClosedCount(
        store.state.user.currentAccount.handle, 
        route.params.key,
        selectedIntegration.value.toLowerCase()
      );
      closedDefects.value = Number(response.data.count);
      return Number(response.data.count);
    } catch (err) {
      showErrorToast(Swal, t('toast.fetchError', { item: t('defects') }), {}, err?.response?.data);
      return 0;
    }
  };

  const loadCounts = async () => {
    if (!countsLoaded.value) {
      await getDefectsOpenCount();
      await getDefectsClosedCount();
      countsLoaded.value = true;
    }
  };

  const getDefects = async (forceRefresh = false) => {
    try {
      // Show skeleton loader for force refresh
      if (forceRefresh) {
        showSkeletonLoader();
      }
      
      if (isInitializing.value && !forceRefresh) {
        return;
      }
      
      const currentFilter = JSON.stringify(filter.value);
      const currentSearch = filter.value.name?.trim() || '';
      const currentHandle = store.state.user.currentAccount.handle;
      const currentProjectKey = route.params.key;
      const currentIntegration = selectedIntegration.value;
      const currentActiveState = activeState.value;
      const pageNumber = currentPage.value;
      const pageSize = itemsPerPage.value;

      if (!forceRefresh && !needsDataFetch(pageNumber, pageSize, currentFilter, currentSearch, currentHandle, currentProjectKey, currentIntegration, currentActiveState)) {
        // We have enough cached data, update the displayed defects
        const startIndex = (pageNumber - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        filteredDefects.value = defectsCache.value.slice(startIndex, endIndex);
        return;
      }

      const contextChanged = currentFilter !== lastFetchedFilter.value ||
        currentSearch !== lastFetchedSearch.value ||
        currentHandle !== lastFetchedHandle.value ||
        currentProjectKey !== lastFetchedProjectKey.value ||
        currentIntegration !== lastFetchedIntegration.value ||
        currentActiveState !== lastFetchedActiveState.value;

      if (contextChanged || forceRefresh) {
        clearCache();
      }

      const queryParams = {
        limit: pageSize,
        offset: (pageNumber - 1) * pageSize,
        integrationService: currentIntegration,
      };

      if (currentSearch) {
        queryParams.q = currentSearch;
      }

      if (filter.value.priorities.length > 0) {
        queryParams.priorities = filter.value.priorities;
      }

      if (filter.value.statuses.length > 0) {
        queryParams.statuses = filter.value.statuses;
      }

      if (filter.value.tags.length > 0) {
        queryParams.tags = filter.value.tags;
      }

      if (filter.value.startDate) {
        queryParams.startDate = filter.value.startDate;
      }

      if (filter.value.endDate) {
        queryParams.endDate = filter.value.endDate;
      }

      if (currentActiveState === 'closed') {
        queryParams.state = 'closed';
      } else {
        queryParams.state = 'open';
      }

      const defectService = makeDefectService($api);
      const response = await defectService.getDefects(currentHandle, currentProjectKey, queryParams);
      
      const newDefects = response.data.items
        .map((defect) => ({
          uid: defect.uid,
          id: defect.externalId,
          name: defect.name,
          priority: defect.priority,
          creator: defect.creator || defect.customFields?.creator?.displayName,
          status: defect.status,
          originalUpdatedAt: defect.updatedAt,
          updatedAt: dayjs(defect.updatedAt).format('DD/MM/YYYY'),
          issueType: defect.customFields?.issuetype?.name,
          description: defect.customFields?.description,
          executions: defect.executions || [],
          externalId: defect.customFields?.key || defect.customFields?.number,
          projectName: defect.customFields?.project?.name || defect.customFields?.projectScope,
          archivedAt: defect.archivedAt,
          state: defect.customFields?.state,
          webUrl: defect.customFields?.webUrl,
          attachments: defect.attachments || [],
          repository: defect.customFields?.projectScope,
          customFields: {
            ...defect.customFields,
            tags:
              defect.customFields?.tags
                ?.map((tagId) => {
                  const tag = systemTags.value.find((t) => Number(t.uid) === Number(tagId));
                  return tag
                    ? {
                        id: tag.uid,
                        name: tag.name,
                        color: tag.customFields?.color,
                      }
                    : null;
                })
                .filter(Boolean) || [],
          },
          projectUid: defect.projectUid,
        }));

      if (contextChanged || forceRefresh) {
        defectsCache.value = newDefects;
      } else {
        defectsCache.value.push(...newDefects);
      }

      totalDefectsCache.value = response.data.count || response.data.total || 0;
      originalDefects.value = defectsCache.value;
      
      // Set displayed defects for current page
      const displayStartIndex = (pageNumber - 1) * pageSize;
      const displayEndIndex = displayStartIndex + pageSize;
      filteredDefects.value = defectsCache.value.slice(displayStartIndex, displayEndIndex);

      lastFetchedPage.value = pageNumber;
      lastFetchedFilter.value = currentFilter;
      lastFetchedSearch.value = currentSearch;
      lastFetchedHandle.value = currentHandle;
      lastFetchedProjectKey.value = currentProjectKey;
      lastFetchedIntegration.value = currentIntegration;
      lastFetchedActiveState.value = currentActiveState;
      
      hasInitiallyLoaded.value = true;
      
      showSuccessToast(Swal, t('defect.fetchSuccess'));
    } catch (err) {
      showErrorToast(Swal, t('defect.fetchError'), {}, err?.response?.data);
    } finally {
      // Hide skeleton loader only if we showed it for force refresh
      if (forceRefresh) {
        hideSkeletonLoader();
      }
    }
  };

  const refreshDefects = async () => {
    currentPage.value = 1;
    await getDefects(true);
  };

  const filterDefects = async () => {
    currentPage.value = 1;
    clearCache();
    showSkeletonLoader();
    try {
      await getDefects();
    } finally {
      hideSkeletonLoader();
    }
  };

  const clearFilter = async () => {
    filter.value = {
      name: '',
      priorities: [],
      statuses: [],
      startDate: null,
      endDate: null,
      tags: [],
    };
    clearCache();
    await filterDefects();
  };

  const updateFilterCondition = async (data) => {
    filter.value = {
      ...filter.value,
      priorities: data.priorities,
      statuses: data.statuses,
      startDate: data.startDate,
      endDate: data.endDate,
      tags: data.tags,
    };
    clearCache();
    await filterDefects();
  };

  const getDefectPriorities = async () => {
    try {
      const defectService = makeDefectService($api);
      const response = await defectService.getDefectPriorities(
        store.state.user.currentAccount.handle, 
        route.params.key
      );
      priorities.value = response.data;
    } catch (err) {
      console.error('Error fetching priorities:', err);
      showErrorToast(Swal, t('toast.fetchError', { item: t('defects') }), {}, err?.response?.data);
    }
  };

  const getDefectStatuses = async () => {
    try {
      const defectService = makeDefectService($api);
      const response = await defectService.getDefectStatuses(
        store.state.user.currentAccount.handle, 
        route.params.key
      );
      statuses.value = response.data;
    } catch (err) {
      console.error('Error fetching statuses:', err);
      showErrorToast(Swal, t('toast.fetchError', { item: t('defects') }), {}, err?.response?.data);
    }
  };

  const getDefectTags = async () => {
    try {
      const tagService = makeTagService($api);
      const response = await tagService.getTags(store.state.user.currentAccount.handle, 'defects');
      systemTags.value = response.data;
    } catch (err) {
      console.error('Error fetching defect tags:', err);
      showErrorToast(Swal, t('toast.fetchError', { item: t('defects') }), {}, err?.response?.data);
    }
  };

  const getDefectStatusScopes = async (defectUid) => {
    try {
      isLoadingStatusScopes.value = true;
      const defectService = makeDefectService($api);
      const response = await defectService.getDefectStatusScopes(
        store.state.user.currentAccount.handle, 
        route.params.key, 
        defectUid
      );
      statusScopes.value = response.data;
    } catch (err) {
      console.error('Error fetching status scopes:', err);
      showErrorToast(Swal, t('toast.fetchError', { item: t('defects') }), {}, err?.response?.data);
    } finally {
      isLoadingStatusScopes.value = false;
    }
  };

  const setActiveState = async (state) => {
    activeState.value = state;
    await filterDefects();
  };

  const updatePagination = async (options) => {
    const newPage = options.page;
    const newItemsPerPage = options.itemsPerPage;
    
    const pageChanged = newPage !== currentPage.value;
    const itemsPerPageChanged = newItemsPerPage !== itemsPerPage.value;
    
    currentPage.value = newPage;
    itemsPerPage.value = newItemsPerPage;
    
    if (pageChanged || itemsPerPageChanged) {
      await getDefects();
    }
  };

  const checkAndSetDefaultIntegration = async () => {
    try {
      // Discover available integrations for this project
      const integrationsService = makeIntegrationsService($api);
      
      const params = `service=jira,github`;
      const response = await integrationsService.getIntegrations(store.state.user.currentAccount.handle, params);
      
      if (!response?.data) {
        selectedIntegration.value = 'Jira';
        return;
      }
      
      if (response?.data?.integrations && Array.isArray(response.data.integrations)) {
        const foundEntry = Object.entries(response.data.projects || {}).find(
          ([, project]) => project.key === route.params.key
        );
        
        if (foundEntry) {
          const testfiestaProjectUid = foundEntry[0];
          
          const projectIntegrations = response.data.integrations.filter(integration => 
            integration.configuration?.projectConfigurations?.some(config => 
              config.projects && config.projects[testfiestaProjectUid]
            )
          );
          
          if (projectIntegrations.length > 0) {
            const hasJira = projectIntegrations.some(i => i.service === 'jira');
            const hasGithub = projectIntegrations.some(i => i.service === 'github');

            // If only one service available, default to that
            if (hasJira && !hasGithub) {
              selectedIntegration.value = 'Jira';
            } else if (hasGithub && !hasJira) {
              selectedIntegration.value = 'Github';
            } else if (hasJira && hasGithub) {
              // If both available, default to Jira
              selectedIntegration.value = 'Jira';
            } else {
              // Fallback
              selectedIntegration.value = 'Jira';
            }
            
            // Save the discovered preference
            saveProjectIntegrationPreference();
          } else {
            selectedIntegration.value = 'Jira';
            saveProjectIntegrationPreference();
          }
          return;
        } else {
          selectedIntegration.value = 'Jira';
          saveProjectIntegrationPreference();
          return;
        }
      } else {
        selectedIntegration.value = 'Jira';
        saveProjectIntegrationPreference();
      }
    } catch (error) {
      console.error('Error fetching integrations for default selection:', error);
      selectedIntegration.value = 'Jira';
      saveProjectIntegrationPreference();
    }
  };

  const saveProjectIntegrationPreference = () => {
    store.dispatch('defects/setProjectIntegrationPreference', {
      handle: store.state.user.currentAccount.handle,
      projectKey: route.params.key,
      integration: selectedIntegration.value
    });
  };

  const handleIntegrationChange = async (integration) => {
    if (selectedIntegration.value !== integration) {
      showDetailDialog.value = false;
      selectedDefect.value = {
        uid: '',
        id: '',
        name: '',
        priority: '',
        creator: '',
        status: '',
        updatedAt: null,
        comments: [],
        description: '',
        attachments: null,
      };
      
      router.replace({
        name: 'Defects',
        params: {
          handle: store.state.user.currentAccount.handle,
          key: route.params.key,
        },
      }).catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          console.warn('Router navigation error:', err);
        }
      });
      
      selectedIntegration.value = integration;
      currentPage.value = 1;
      clearCache();
      countsLoaded.value = false;
      
      // Show loading skeleton immediately
      showSkeletonLoader();
      await loadCounts();
      hideSkeletonLoader();
    }
  };

  const init = async () => {
    isInitializing.value = true;
    
    if (route.params.integration) {
      // Integration specified in route
      selectedIntegration.value = route.params.integration.charAt(0).toUpperCase() + route.params.integration.slice(1).toLowerCase();
      saveProjectIntegrationPreference();
    } else {
      // Check for stored preference first
      const savedProjectIntegration = store.getters['defects/getProjectIntegrationPreference'](
        store.state.user.currentAccount.handle, 
        route.params.key
      );
      
      if (savedProjectIntegration) {
        selectedIntegration.value = savedProjectIntegration;
      } else {
        // No stored preference, discover available integrations
        await checkAndSetDefaultIntegration();
      }
    }
    
    isInitializing.value = false;
    
    // Always load initial data after init
    await loadInitialData();
  };

  const loadInitialData = async () => {
    try {
      // Don't call showSkeletonLoader here as it should already be called by the component
      await Promise.all([
        getDefects(), 
        getDefectPriorities(), 
        getDefectStatuses(), 
        getDefectTags()
      ]);
      if (openDefects.value === 0 && closedDefects.value === 0) {
        loadCounts();
      }
    } catch (error) {
      showErrorToast(Swal, t('toast.fetchError', { item: t('defects') }), {}, error?.response?.data);
    } finally {
      hideSkeletonLoader();
    }
  };

  // MANAGEMENT FUNCTIONS
  const onViewDefect = async (item) => {
    try {
      const defectService = makeDefectService($api);
      
      const defectResponse = await defectService.getDefect(
        store.state.user.currentAccount.handle, 
        route.params.key, 
        item.uid
      );
      const defect = defectResponse.data;
      
      selectedDefect.value = {
        uid: defect.uid,
        id: defect.customFields?.key || defect.customFields?.number,
        name: defect.name,
        priority: defect.priority,
        creator: defect.creator || defect.customFields?.creator?.displayName,
        status: defect.status,
        updatedAt: dayjs(defect.updatedAt).format('DD/MM/YYYY'),
        description: defect.customFields?.description,
        projectUid: defect.projectUid,
        externalId: defect.customFields?.key || defect.customFields?.number,
        projectName: defect.customFields?.project?.name || defect.customFields?.projectScope,
        issueType: defect.customFields?.issuetype?.name,
        state: defect.customFields?.state,
        webUrl: defect.customFields?.webUrl,
        attachments: defect.attachments || [],
        archivedAt: defect.archivedAt,
        executions: [], // Will be loaded by ViewDefectDialog
        comments: defect.comments || [],
        customFields: {
          ...defect.customFields,
          tags: defect.customFields?.tags
                  ?.map((tagId) => {
                    const tag = systemTags.value.find((t) => Number(t.uid) === Number(tagId));
                    return tag
                      ? {
                          id: tag.uid,
                          name: tag.name,
                          color: tag.customFields?.color,
                        }
                      : null;
                  })
                  .filter(Boolean) || [],
        },
      };
      
      // Simple approach like cases - just set the values and let the component handle it
      showDetailDialog.value = true;
      
      router.replace({
        name: 'DefectDetail',
        params: {
          handle: store.state.user.currentAccount.handle,
          key: route.params.key,
          integration: selectedIntegration.value,
          defectId: defect.uid,
        },
      }).catch(err => {
        if (err.name !== 'NavigationDuplicated') {
          console.warn('Router navigation error:', err);
        }
      });
      
    } catch (err) {
      console.error('Error fetching defect details:', err);
      showErrorToast(Swal, t('toast.fetchError', { item: t('defect') }), {}, err?.response?.data);
    }
  };

  const onAddNewComment = async (comment) => {
    try {
      const defectService = makeDefectService($api);
      const response = await defectService.addComment(
        store.state.user.currentAccount.handle, 
        route.params.key, 
        {
          uid: selectedDefect.value.uid,
          comment,
        }
      );
      
      if (selectedDefect.value.comments) {
        selectedDefect.value.comments.splice(0, 0, response.data);
      }
      
      await getDefects(true);
    } catch (err) {
      showErrorToast(Swal, t('toast.createError', { item: t('comment') }), {}, err?.response?.data);
    }
  };

  const onEditDefect = (item) => {
    selectedDefect.value = {
      uid: item.uid || '',
      id: item.id || '',
      name: item.name || '',
      priority: item.priority || '',
      creator: item.creator || '',
      status: item.status || '',
      updatedAt: item.updatedAt || null,
      comments: item.comments || [],
      customFields: item.customFields || {},
      state: item.state,
      description: item.description,
      attachments: item.attachments,
      archivedAt: item.archivedAt,
    };

    getDefectStatusScopes(item.uid);
    showEditDialog.value = true;
  };

  const onCloseDefect = async (item) => {
    const payload = {
      uid: item.uid,
      name: item.name,
      state: 'closed',
      customFields: {
        tags: item.customFields.tags.map(({ id, ...rest }) => ({
          uid: id,
          ...rest,
        })),
      },
    };
    await editDefect(payload);
  };

  const onReopenDefect = async (item) => {
    const payload = {
      uid: item.uid,
      name: item.name,
      state: 'open',
      customFields: {
        tags: item.customFields.tags.map(({ id, ...rest }) => ({
          uid: id,
          ...rest,
        })),
      },
    };
    await editDefect(payload);
  };

  const editDefect = async (item) => {
    try {
      editLoading.value = true;
      const defectService = makeDefectService($api);
      await defectService.updateDefect(
        store.state.user.currentAccount.handle, 
        route.params.key, 
        item
      );

      await getDefects(true);
      showSuccessToast(Swal, t('toast.updateSuccess', { item: t('defect') }));
      showEditDialog.value = false;
      getDefectsClosedCount();
      getDefectsOpenCount();
    } catch (err) {
      showErrorToast(Swal, t('toast.updateError', { item: t('defect') }), {}, err?.response?.data);
    } finally {
      editLoading.value = false;
    }
  };

  const closeDetailView = () => {
    showDetailDialog.value = false;
    selectedDefect.value = {
      uid: '',
      id: '',
      name: '',
      priority: '',
      creator: '',
      status: '',
      updatedAt: null,
      comments: [],
      description: '',
      attachments: null,
    };
    
    router.replace({
      name: 'Defects',
      params: {
        handle: store.state.user.currentAccount.handle,
        key: route.params.key,
      },
    }).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        console.warn('Router navigation error:', err);
      }
    });
  };

  const closeEditDialog = () => {
    showEditDialog.value = false;
    statusScopes.value = [];
    isLoadingStatusScopes.value = false;
  };

  const viewPreviousDefect = () => {
    const currentIndex = filteredDefects.value.findIndex(defect => defect.uid === selectedDefect.value?.uid);
    const previousIndex = currentIndex - 1;
    
    if (previousIndex >= 0) {
      const previousDefect = filteredDefects.value[previousIndex];
      if (previousDefect) {
        onViewDefect(previousDefect);
      }
    }
  };

  const viewNextDefect = () => {
    const currentIndex = filteredDefects.value.findIndex(defect => defect.uid === selectedDefect.value?.uid);
    const nextIndex = currentIndex + 1;
    
    if (nextIndex < filteredDefects.value.length) {
      const nextDefect = filteredDefects.value[nextIndex];
      if (nextDefect) {
        onViewDefect(nextDefect);
      }
    }
  };

  const getPriorityName = (priorityId, priorityArray = null) => {
    const prioritiesData = priorityArray || priorities.value;
    const priority = prioritiesData.find((p) => Number(p.id) === Number(priorityId));
    return priority?.name || t('none');
  };

  const getStatusName = (statusId, statusArray = null) => {
    const statusesData = statusArray || statuses.value;
    const status = statusesData.find((s) => Number(s.id) === Number(statusId));
    return status?.name || t('none');
  };

  const getTagName = (tagId) => {
    const tag = systemTags.value.find((t) => t.uid === tagId);
    return tag?.name || tagId;
  };

  const getTagInfo = (tagId) => {
    const tag = systemTags.value.find((t) => t.uid === tagId);
    return tag
      ? {
          id: tag.uid,
          name: tag.name,
          color: tag.customFields?.color,
        }
      : null;
  };

  // Table-specific methods
  const formatUpdatedAt = (updatedAt) => {
    return formatDate(updatedAt, 'MM/dd/yy');
  };

  const getPriorityColor = (priorityId, priorityArray = null) => {
    const prioritiesData = priorityArray || priorities.value;
    const priority = prioritiesData.find((p) => Number(p.id) === Number(priorityId));
    return priority?.color || '#0c111d';
  };

  const getStatusColor = (statusId, statusArray = null) => {
    const statusesData = statusArray || statuses.value;
    const status = statusesData.find((s) => Number(s.id) === Number(statusId));
    return status?.color || '#0c111d';
  };

  const getErrorMessage = (status) => {
    if (status === 'error') {
      return t('integrations.error.integrationError');
    } else if (status === 'inactive') {
      return t('integrations.error.inactiveIntegration');
    }
    return '';
  };

  const getDefaultPriority = () => {
    return priorities.value.find((p) => p.isDefault)?.id;
  };

  const getCompletedStatuses = () => {
    return statuses.value.filter((s) => s.isCompleted).map((s) => s.id);
  };

  const getPriorityByName = (name) => {
    const normalizedName = name?.toLowerCase()?.replace(/\s+/g, '');
    return priorities.value.find((p) => p.name?.toLowerCase()?.replace(/\s+/g, '') === normalizedName);
  };

  const getStatusByName = (name) => {
    const normalizedName = name?.toLowerCase()?.replace(/\s+/g, '');
    return statuses.value.find((s) => s.name?.toLowerCase()?.replace(/\s+/g, '') === normalizedName);
  };

  const getMenuItems = (integrationType, activeState) => {
    if (integrationType === 'Jira') {
      return [{ title: t('edit') }];
    }
    else if (integrationType === 'Github' && activeState === 'closed') {
      return [{ title: t('reopen') }];
    }
    else if (integrationType === 'Github' && activeState === 'active') {
      return [{ title: t('edit') }, { title: t('close') }];
    }
    return [{ title: t('edit') }, { title: t('close') }];
  };

  const getDefectExecutions = async (defectUid) => {
    const defectService = makeDefectService($api);
    const handle = store.state.user.currentAccount.handle;
    const key = route.params.key;
    return await defectService.getDefectExecutions(handle, key, defectUid);
  };

  const getDefectAttachments = async (defectUid) => {
    const defectService = makeDefectService($api);
    const handle = store.state.user.currentAccount.handle;
    const key = route.params.key;
    return await defectService.getDefectAttachments(handle, key, defectUid);
  };

  const getDefectRuns = async (defectUid) => {
    const defectService = makeDefectService($api);
    const handle = store.state.user.currentAccount.handle;
    const key = route.params.key;
    return await defectService.getDefectRuns(handle, key, defectUid);
  };

  const sanitizeHTML = (html) => {
    if (!html || html === '') {
      return '';
    }
    // Note: DOMPurify would need to be imported if used
    return html; // For now, return as is
  };

  const formatDate = (date) => {
    if (!date) {
      return '';
    }

    // Handle both date string and Date object cases
    try {
      if (typeof date === 'string') {
        // If date is already in DD/MM/YYYY format, return as is
        if (/^\d{2}\/\d{2}\/\d{4}$/.test(date)) {
          return date;
        }
        // Otherwise parse and format
        const parsedDate = new Date(date);
        if (isNaN(parsedDate.getTime())) {
          return '';
        }
        return dayjs(parsedDate).format('DD/MM/YYYY');
      } else if (date instanceof Date) {
        return dayjs(date).format('DD/MM/YYYY');
      }
      return '';
    } catch (error) {
      console.warn('Error formatting date:', error);
      return '';
    }
  };

  // Edit dialog specific methods
  const prepareEditData = (data) => {
    const priorityId = data.priority ? Number(data.priority) : '';
    const statusId = data.status ? Number(data.status) : '';

    // Parse the description - now only HTML
    let parsedDescription = '';
    if (data.description) {
      parsedDescription = sanitizeHTML(data.description);
    }

    return {
      uid: data.uid || '',
      name: data.name || '',
      priority: priorityId,
      status: statusId,
      state: data.state,
      states: ['open', 'closed'],
      description: parsedDescription,
      tags: data.customFields?.tags?.map((tag) => ({
        uid: tag.id,
        name: tag.name,
        color: tag.color,
      })) || [],
    };
  };

  const validateEditForm = (formRef) => {
    return formRef.validate();
  };

  const prepareEditPayload = (defect, selectedIntegration) => {
    // Convert HTML description to ADF for Jira
    let description = defect.description;
    if (selectedIntegration === 'Jira' && description) {
      try {
        // Convert HTML to ADF format
        description = convertToADF(description);
      } catch (error) {
        console.error('Error converting HTML to ADF:', error);
        // Fallback to original description if conversion fails
      }
    }

    return {
      uid: defect.uid,
      name: defect.name,
      state: defect.state,
      description: description,
      ...(defect.priority && { priority: defect.priority }),
      ...(defect.status && { status: defect.status }),
      ...(selectedIntegration === 'Github' && {
        customFields: {
          tags: defect.tags,
        },
      }),
    };
  };

  const handleTagToggle = (defect, tag) => {
    const index = defect.tags.findIndex((t) => t.name === tag.name);
    if (index === -1) {
      defect.tags.push({
        name: tag.name,
        uid: tag.uid,
      });
    } else {
      defect.tags.splice(index, 1);
    }
  };

  const handleTagRemove = (defect, tag) => {
    const index = defect.tags.findIndex((t) => t.name === tag.name);
    if (index >= 0) {
      defect.tags.splice(index, 1);
    }
  };

  const handleSelectAllTags = (defect, tags) => {
    if (defect.tags.length === tags.length) {
      defect.tags = [];
    } else {
      defect.tags = tags.map((tag) => ({
        name: tag.name,
        uid: tag.uid,
      }));
    }
  };

  const isTagSelected = (defect, tag) => {
    return defect.tags.some((t) => t.name === tag.name);
  };

  const getFilteredStatuses = (statuses, statusScopes, currentStatus) => {
    if (!statusScopes || !statusScopes.length) {
      return [];
    }
    
    const scopedStatuses = statuses.filter((status) => statusScopes.includes(String(status.id)));
    
    if (currentStatus) {
      const currentStatusObj = statuses.find(status => status.id === currentStatus);
      if (currentStatusObj && !scopedStatuses.find(s => s.id === currentStatusObj.id)) {
        scopedStatuses.push(currentStatusObj);
      }
    }
    
    return scopedStatuses;
  };

  const isAllTagsSelected = (defect, tags) => {
    return tags.length > 0 && defect.tags.length === tags.length;
  };

  const isIndeterminateTags = (defect, tags) => {
    return defect.tags.length > 0 && !isAllTagsSelected(defect, tags);
  };

  // FILTER FUNCTIONS
  const countFilter = () => {
    filterCount.value =
      filter.value.priorities.length +
      filter.value.statuses.length +
      (filter.value.startDate || filter.value.endDate ? 1 : 0) +
      filter.value.tags.length;
  };

  const removePriority = async (priorityId) => {
    filter.value.priorities = filter.value.priorities.filter((p) => p !== priorityId);
    countFilter();
    await filterDefects();
  };

  const removeStatus = async (statusId) => {
    filter.value.statuses = filter.value.statuses.filter((s) => s !== statusId);
    countFilter();
    await filterDefects();
  };

  const removeDate = async () => {
    filter.value.startDate = null;
    filter.value.endDate = null;
    countFilter();
    await filterDefects();
  };

  const removeTag = async (tagId) => {
    filter.value.tags = filter.value.tags.filter((t) => t !== tagId);
    countFilter();
    await filterDefects();
  };

  const removePriorityFilter = async (priority) => {
    filter.value.priorities = filter.value.priorities.filter((p) => p !== priority);
    countFilter();
    await filterDefects();
  };

  const removeStatusFilter = async (status) => {
    filter.value.statuses = filter.value.statuses.filter((s) => s !== status);
    countFilter();
    await filterDefects();
  };

  const removeDateFilter = async () => {
    filter.value.startDate = null;
    filter.value.endDate = null;
    countFilter();
    await filterDefects();
  };

  const removeTagFilter = async (tagId) => {
    filter.value.tags = filter.value.tags.filter((t) => t !== tagId);
    countFilter();
    await filterDefects();
  };

  const clearAllFilters = async () => {
    filter.value = {
      name: '',
      priorities: [],
      statuses: [],
      startDate: null,
      endDate: null,
      tags: [],
    };
    countFilter();
    await filterDefects();
  };

  return {
    // State
    defects,
    selectedDefect,
    originalDefects,
    filteredDefects,
    priorities,
    statuses,
    systemTags,
    statusScopes,
    closedDefects,
    openDefects,
    activeState,
    selectedIntegration,
    integrationsList,
    filter,
    filterCount,
    showDetailDialog,
    showEditDialog,
    editLoading,
    isLoadingStatusScopes,
    currentPage,
    itemsPerPage,
    totalItems,
    totalPages,
    hasInitiallyLoaded: hasInitiallyLoadedComputed,
    
    // Computed
    currentSelectedDefectIndex,
    isSelectedDefectFirstIndex,
    isSelectedDefectLastIndex,
    nextDefect,
    previousDefect,
    hasActiveFilters,
    activeFiltersCount,
    paginatedDefects,
    writeDefect,
    deleteDefect,
    readDefect,
    
    // Core Methods
    getDefects,
    refreshDefects,
    filterDefects,
    clearFilter,
    updateFilterCondition,
    getDefectPriorities,
    getDefectStatuses,
    getDefectTags,
    getDefectStatusScopes,
    getDefectsOpenCount,
    getDefectsClosedCount,
    loadCounts,
    setActiveState,
    updatePagination,
    checkAndSetDefaultIntegration,
    saveProjectIntegrationPreference,
    handleIntegrationChange,
    clearCache,
    forceRefresh,
    init,
    loadInitialData,
    resetModuleCache,
    clearAllState,
    
    // Management Methods
    onViewDefect,
    onAddNewComment,
    onEditDefect,
    onCloseDefect,
    onReopenDefect,
    editDefect,
    closeDetailView,
    closeEditDialog,
    viewPreviousDefect,
    viewNextDefect,
    getPriorityName,
    getStatusName,
    getTagName,
    getTagInfo,
    formatUpdatedAt,
    getPriorityColor,
    getStatusColor,
    getErrorMessage,
    getDefaultPriority,
    getCompletedStatuses,
    getPriorityByName,
    getStatusByName,
    getMenuItems,
    getDefectExecutions,
    getDefectAttachments,
    getDefectRuns,
    sanitizeHTML,
    formatDate,
    prepareEditData,
    validateEditForm,
    prepareEditPayload,
    handleTagToggle,
    handleTagRemove,
    handleSelectAllTags,
    isTagSelected,
    getFilteredStatuses,
    isAllTagsSelected,
    isIndeterminateTags,
    
    // Filter Methods
    countFilter,
    removePriority,
    removeStatus,
    removeDate,
    removeTag,
    removePriorityFilter,
    removeStatusFilter,
    removeDateFilter,
    removeTagFilter,
    clearAllFilters,
  };
};
