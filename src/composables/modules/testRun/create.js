import { ref, computed, reactive, onMounted } from 'vue';
import { $api, useStore } from '@/main';
import { useRouter, useRoute } from 'vue-router/composables';
import makeMilestonesService from '@/services/api/milestone';
import makeTagsService from '@/services/api/tag';
import makeRunService from '@/services/api/run';
import makeCasesService from '@/services/api/case';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { requiredAndMax255FieldValidationRules } from '@/utils/validation';
import { useColorPreferences } from '@/composables/utils/colorPreferences';
import { useRunsIndex } from '@/composables/modules/testRun/index';
import Swal from 'sweetalert2';


const milestones = ref([]);
const tags = ref([]);
const cases = ref([]);
const showCases = ref(false);
const onCreateLoading = ref(false);
const statuses = ref([]);
const priorities = ref([]);
const selectedCases = ref([]);
const selectedRun = reactive({
  name: '',
  description: '',
  milestoneUids: [],
  priority: null,
  tags: [],
  dueAt: false,
});
const menuDueDate = ref(false);
const dueDate = ref('');
const isCreateModalOpen = ref(false);

export const useCreateTestRun = () => {
  const route = useRoute();
  const router = useRouter();
  const store = useStore();

  const runForm = ref(null);
  const fileInput = ref(null);

  const currentAccount = computed(() => store.state.user.currentAccount);

  // Initialize validation rules (pass empty object, adjust if needed)
  const runNameRule = requiredAndMax255FieldValidationRules({});

  // Import cache clearing function
  const { clearCache } = useRunsIndex();

  const menuOpen = ref(false);
  const showConfirmBackDialog = ref(false);
  const search = ref('');
 



  const activeMilestones = computed(() => milestones.value.filter((m) => !m.archivedAt && !m.deletedAt));
  const isTestPlanCreate = computed(() => route.query.redirectTo === 'TestPlanCreate');

  // Initialize services
  const milestoneService = makeMilestonesService($api);
  const tagService = makeTagsService($api);
  const runsService = makeRunService($api);
  const casesService = makeCasesService($api);





  onMounted(() => {
    const { getPriorities, getStatuses, getDefaultPriority } = useColorPreferences($api, Swal);
    priorities.value = getPriorities('testRun').filter((element) => !element.archived);
    statuses.value = getStatuses('testRun').filter((element) => !element.archived);
    if(!selectedRun.priority){
      selectedRun.priority = getDefaultPriority(priorities.value);
    }
    init();
  });


  const getCases = async (folderUID) => {
    if (!folderUID) return;
    try {
      const handle = route.params.handle;
      const projectKey = route.params.key;
      const response = await casesService.getFolderCases(handle, projectKey, folderUID);
      const casesData = response.data.cases || [];
      
      // Fetch tag relations for all cases if they exist
      if (casesData && casesData.length > 0) {
        const caseUids = casesData.map(c => c.testCaseRef);
        try {
          const tagRelationsRes = await casesService.getCaseRelations(handle, projectKey, 'tag', caseUids);
          if (tagRelationsRes.status === 200) {
            const tagRelations = tagRelationsRes.data;
            
                                // Map tags to cases - tagRelations is an object with testCaseRef as keys
          casesData.forEach(caseItem => {
            const caseTags = tagRelations[caseItem.testCaseRef] || [];
            caseItem.tags = caseTags.map(tag => ({
              uid: tag.uid,
              name: tag.name
            }));
          });
          }
        } catch (tagErr) {
          console.warn('Failed to fetch tag relations:', tagErr);
          // Continue without tags rather than failing completely
        }
      }
      
      cases.value = casesData;
    } catch (err) {
      showErrorToast(Swal, 'fetchError', { item: 'Cases' }, err?.response?.data);
    }
  };

  const getMilestones = async () => {
    try {
      const handle = route.params.handle;
      const projectKey = route.params.key;
      const response = await milestoneService.getMilestones(handle, projectKey);
      milestones.value = response.data.items.map((m) => ({
        name: m.name,
        uid: m.uid,
        archivedAt: m.archivedAt,
        deletedAt: m.deletedAt,
      }));
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'milestones' }, error?.response?.data);
      milestones.value = [];
    }
  };

  const getAllTags = async () => {
    try {
      const handle = route.params.handle;
      const response = await tagService.getTags(handle, 'runs');
      if (response.status === 200) {
        tags.value = response.data.map((tag) => ({
          name: tag.name,
          uid: tag.uid
        }));
      }
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'tags' }, error?.response?.data);

    }
  };

  const init = async () => {
    try {
      await getMilestones();
      await getAllTags();
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'Milestone' }, error?.response?.data);
    }
  };

  const milestoneSelection = (uid) => {
    return selectedRun.milestoneUids.some((item) => item === uid);
  };

  const onRemoveSelectedMilestone = (uid) => {
    const index = selectedRun.milestoneUids.findIndex((item) => item === uid);
    if (index !== -1) {
      selectedRun.milestoneUids.splice(index, 1);
    }
  };

  const onRemoveSelectedTags = (uid) => {
    const index = selectedRun.tags.findIndex((item) => item.uid === uid);
    if (index !== -1) {
      selectedRun.tags.splice(index, 1);
    }
  };

  const tagsSelection = (uid) => {
    return selectedRun.tags.some((item) => item.uid === uid);
  };

  const resetForm = () => {
    selectedRun.name = '';
    selectedRun.description = '';
    selectedRun.milestoneUids = [];
    selectedRun.priority = null;
    selectedRun.tags = [];
    selectedCases.value = [];
    if (runForm.value) {
      runForm.value.resetValidation();
    }
  };

  const createTestRuns = async () => {
    if (runForm.value?.validate && runForm.value.validate()) {
      const payload = {
        name: selectedRun.name,
        ...(selectedRun.description.trim() && { description: selectedRun.description.trim() }),
        customFields: {
          assign: currentAccount.value.handle,
          progress: 0,
          star: true,
          caseCount: 0,
          archived: false,
        },
        tagUids: selectedRun.tags?.map((tag) => tag.uid) ?? [],
        priority: selectedRun.priority?.id,
        milestoneUids: selectedRun.milestoneUids,
        projectKey: route.params.key,
        ...(selectedRun.dueAt && { dueAt: new Date(selectedRun.dueAt) }),
      };

      if (selectedCases.value.length) {
        const selectedCasesMapped = selectedCases.value.map((item) => item.testCaseRef);
        payload.customFields.caseCount = selectedCasesMapped.length;
        payload.caseUids = selectedCasesMapped;
      }

      onCreateLoading.value = true;
      const fullpath = `/${route.params.handle}/${route.params.key}/cases/`
      try {
        const response = await runsService.createTestRun(route.params.handle, route.params.key, payload);
        showSuccessToast(Swal, 'createSuccess', { item: 'Test Run' });
        resetForm();
        // Clear run cache so the new run appears immediately
        clearCache();
        if (route.query.redirectTo === 'MilestoneCreate') {
          router.replace({ 
            name: 'MilestoneCreate', 
            params: { handle: route.params.handle, key: route.params.key },
            query: { testRunId: response.data.uid }
          });
        } else if (isTestPlanCreate.value) {
          router.replace({ name: 'TestPlanCreate', params: { handle: route.params.handle, key: route.params.key } });
        } else if (route.fullPath === fullpath){
          isCreateModalOpen.value = false; 
        } else {
          router.replace({ name: 'Runs', params: { handle: route.params.handle, key: route.params.key } });
        }
      } catch (error) {
        showErrorToast(Swal, 'createError', { item: 'Test Run' }, error?.response?.data);
      } finally {
        onCreateLoading.value = false;
        showCases.value = false;
      }
    }
  };

  const openFileDialog = (fileInputRef) => {
    if (fileInputRef && fileInputRef.value) {
      fileInputRef.value.click();
    }
  };

  const onBackShowCases = () => {
    showCases.value = false;
    menuOpen.value = false;
  };

  const handleAddCases = () => {
    if (runForm.value?.validate && runForm.value.validate()) {
      showCases.value = true;
    }
  };

  const onDateChange = (val) => {
    menuDueDate.value = false;
    selectedRun.dueAt = val;
  };

  const handleBackClick = () => {
    showConfirmBackDialog.value = true;
  };

  const onAddCases = () => {
    showCases.value = false;
    menuOpen.value = false;
  };

  const handleCloseClick = () => {
    showConfirmBackDialog.value = false;
  };

  const handleDuplicateClick = () => {
    showConfirmBackDialog.value = false;
    router.push({ name: 'TestRunDuplicate', params: { handle: route.params.handle, key: route.params.key } });
  };

  const handleConfirmClick = () => {
    showConfirmBackDialog.value = false;
    // Clear cache to ensure fresh data when returning to test runs index
    clearCache();
    if (route.query.redirectTo === 'MilestoneCreate') {
      router.replace({ 
        name: 'MilestoneCreate', 
        params: { handle: route.params.handle, key: route.params.key }
      });
    } 
    else if (isTestPlanCreate.value) {
      router.replace({ name: 'TestPlanCreate', params: { handle: route.params.handle, key: route.params.key } });
    } 
    else {
      router.replace({ name: 'Runs', params: { handle: route.params.handle, key: route.params.key } });
    }
  };

  const updateCases = (newCases) => {
    cases.value = newCases;
  };

  return {
    runNameRule,
    selectedRun,
    menuOpen,
    showConfirmBackDialog,
    search,
    selectedCases,
    milestones,
    tags,
    cases,
    showCases,
    onCreateLoading,
    statuses,
    priorities,
    activeMilestones,
    isTestPlanCreate,
    dueDate,
    menuDueDate,
    isCreateModalOpen,
    onDateChange,
    getCases,
    init,
    milestoneSelection,
    onRemoveSelectedMilestone,
    onRemoveSelectedTags,
    tagsSelection,
    createTestRuns,
    openFileDialog,
    handleAddCases,
    handleBackClick,
    onAddCases,
    handleCloseClick,
    handleDuplicateClick,
    handleConfirmClick,
    getMilestones,
    getAllTags,
    updateCases,
    runForm,
    fileInput,
    onBackShowCases,
    resetForm,
  };
};
