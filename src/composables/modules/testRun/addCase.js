import { ref, computed, watch, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore } from '@/main';
import { useLoading } from '@/composables/utils/loading';
import { usePermissions } from '@/composables/utils/permissions';
import { showErrorToast } from '@/composables/utils/toast';
import { useTestCasesIndex } from '@/composables/modules/cases/index';
import { tableSelectedCases } from '@/composables/modules/cases/list';

export function useRunAddCase(props = {}) {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;
  
  // Composables
  const route = useRoute();
  const router = useRouter();
  const store = useStore();
  const { showSkeletonLoader, hideSkeletonLoader, skeletonLoaderState } = useLoading();
  const { authorityTo } = usePermissions();
  
  // Use existing test cases composable
  const { 
    searchTerm: composableSearchTerm,
    filters: composableFilters,
    cases: composableCases, 
    folders: composableFolders, 
    getCases: composableGetCases, 
    getFolders: composableGetFolders,
    totalCases: composableTotalCases,
    casesLoading: composableCasesLoading,
    currentPage: composableCurrentPage,
    itemsPerPage: composableItemsPerPage,
    totalPages: composableTotalPages,
    updatePagination: composableUpdatePagination,
    relationsLoading: composableRelationsLoading,
    applySearch: composableApplySearch,
    applyFilters: composableApplyFilters,
    clearFilters: composableClearFilter,
  } = useTestCasesIndex();

  // Reactive state
  const showConfirmBackDialog = ref(false);
  const showAddDialog = ref(false);
  const tableFilter = ref(false);
  const cases = ref([]);
  const folders = ref([]);
  const selectedCases = ref([]);
  const isColumnFilter = ref(false);
  const isLoading = ref(false);
  const confirmDialogTitle = ref('');
  const confirmDialogContent = ref('');
  const confirmDialogContentPartTwo = ref('');
  const confirmDialogBtnLabel = ref('');
  const confirmDialogBtnColor = ref('primary');
  const confirmDialogRunName = ref('');
  const confirmType = ref('');
  const casesCount = ref(0);
  const drawer = ref(false);
  const selectedCase = ref(null);
  const folderUid = ref(null);
  const showTestFolder = ref(false);
  const selectedCaseCount = ref();


  // Computed properties
  const currentAccount = computed(() => store.getters['user/currentAccount']);
  
  const deleteActivity = computed(() => authorityTo('delete_activity'));
  const deleteEntity = computed(() => authorityTo('delete_entity'));
  const writeEntity = computed(() => authorityTo('write_entity'));
  
  const isEditView = computed(() => route.name === 'TestRunEdit');
  
  const selectedCasesCount = computed(() => props.value ? props.value?.length : 0);
  
  const selectedRunCases = computed({
    get() {
      return props.value; 
    },
    set(value) {
      selectedCases.value = value;
    }
  });
  
  const selectedRunCasesCount = computed({
    get() {
      return selectedRunCases.value?.length;
    },
    set(value) {
      selectedRunCases.value = value;
    }
  });
  
  const paginationData = computed(() => ({
    total: composableTotalCases.value,
    totalPages: composableTotalPages.value,
  }));

  const anySelectedCases = computed(() => selectedCases.value?.length > 0);

  // Watchers
  watch(composableCases, (newCases) => {
    cases.value = newCases;
    casesCount.value = composableTotalCases.value;
  }, { immediate: true });

  watch(composableFolders, (newFolders) => {
    folders.value = newFolders;
  }, { immediate: true });

  watch(() => props.value, (newValue) => {
    // Only update if there's actually a change to prevent infinite loops
    if (JSON.stringify(selectedCases.value) !== JSON.stringify(newValue || [])) {
      selectedCases.value = newValue || [];
      
      // If parent resets to empty array, also clear the persistent selected cases
      if (!newValue || newValue.length === 0) {
        tableSelectedCases.value = [];
      }
    }
  }, { immediate: true });

  watch(showTestFolder, (newValue) => {
    composableClearFilter();
    if (newValue) {
      getCases(folderUid.value);
    } else {
      getAllCases();
    }
  });

  // Methods
  const selectDefaultFolder = () => {
    if (folders.value && folders.value.length > 0 && !folderUid.value) {
      const initialSelectedFolder = route.params.folderUid ?? folders.value[0].uid;
      folderUid.value = initialSelectedFolder;
      getCases(initialSelectedFolder);
    }
  };

  const getAllCases = async () => {
    // Wait for folders to be loaded if they're not available yet
    if (!folders.value || folders.value.length === 0) {
      await getFolders();
    }
    if (folders.value && folders.value.length > 0) {
      await composableGetCases(folders.value[0].uid, composableSearchTerm.value, composableFilters.value, false, true);
    }
  };

  const getCases = async (folderUID, fetchAll = false) => {
    if(!folderUID) return;
    folderUid.value = folderUID;

    await composableGetCases(folderUID, composableSearchTerm.value, composableFilters.value, false, fetchAll);

    if (isEditView.value && route.params.folderUid !== folderUID) {
      try {
       await router.replace({
         name: route.name,
         params: {
          ...route.params,
          folderUid: folderUID
        },
        query: {
          ...route.query,
          ...(route.query.isPlanRerunEdit && route.params.planId
            ? { isPlanRerunEdit: 'true', planId: route.params.planId }
            : {})
        }
      });
    } catch (error) {
       if (error?.name === 'NavigationDuplicated' || (error?.message && error.message.includes('NavigationDuplicated'))) {
           return; 
       }
       showErrorToast(proxy.$swal, 'fetchError', { item: 'cases' }, error?.response?.data);
     }
    }
  };

  const getFolders = async () => {
    await composableGetFolders();
  };

  const handleConfirmBtnClick = (type) => {
    showAddDialog.value = false;

    if (type == 'cancel')
      router.replace({
        name: 'Runs',
        params: {
          handle: route.params.handle,
          key: route.params.key
        }
      });
  };

  const handleCases = (cases) => {
    selectedCases.value = cases;
    proxy.$emit('input', selectedCases.value);
    
    // Ensure tableSelectedCases is also updated
    tableSelectedCases.value = cases;
  };



  const handleBackClick = () => {
    proxy.$emit('back');
  };

  const handleAddClick = (type) => {
    if (type == 'add') {
      confirmDialogTitle.value = proxy.$t('testruns.test_case.addcase.title');
      confirmDialogContent.value = proxy.$t('testruns.test_case.addcase.content');
      confirmDialogContentPartTwo.value = '';
      confirmDialogBtnLabel.value = proxy.$t('add');
      confirmDialogBtnColor.value = 'primary';
      confirmDialogRunName.value = '';
      confirmType.value = 'add';
      showAddDialog.value = true;
    } else if (type == 'remove') {
      confirmDialogTitle.value = proxy.$t('testruns.test_case.removecase.title');
      confirmDialogContent.value = proxy.$t('testruns.test_case.removecase.content');
      confirmDialogContentPartTwo.value = '';
      confirmDialogBtnLabel.value = proxy.$t('remove');
      confirmDialogBtnColor.value = 'danger';
      confirmDialogRunName.value = '';
      confirmType.value = 'remove';
      showAddDialog.value = true;
    } else if (type == 'cancel') {
      confirmDialogTitle.value = proxy.$t('testruns.edit_testrun.title');
      confirmDialogContent.value = proxy.$t('testruns.edit_testrun.content');
      confirmDialogContentPartTwo.value = '';
      confirmDialogBtnLabel.value = proxy.$t('testruns.edit_testrun.btn_label');
      confirmDialogBtnColor.value = 'primary';
      confirmDialogRunName.value = '';
      confirmType.value = 'cancel';
      showAddDialog.value = true;
    }
  };

  const onAddTestCase = () => {
    if(route.params.id && route.params.folderUid){
      router.push({
        name: 'CreateTestCases',
        params: {
        handle: route.params.handle,
        key: route.params.key,
      },
      query: {
        redirectTo: 'TestRunEdit',
        runId: route.params.id,
        folderUid: route.params.folderUid,
        },
      });
    }else{
      router.push({
        name: 'CreateTestCases',
        params: {
          handle: route.params.handle,
          key: route.params.key,
        },
        query: {
          redirectTo: 'TestRunCreate',
        },
      });
    }
  };

  const handleCloseClick = () => {
    proxy.$emit('close');
  };

  const handleConfirmClick = () => {
    showConfirmBackDialog.value = false;
    router.replace({
      name: 'TestRunCreate',
      params: {
        handle: route.params.handle,
        key: route.params.key
      },
    });
  };

  const changeFilter = (filter) => {
    tableFilter.value = filter;
  };

  const updateCasesData = (newCases) => {
    cases.value = newCases;
  };

  const UpdateCases = (selectedCount) => {
    selectedCaseCount.value = selectedCount;
  };

  // Public method to reset the component (can be called by parent via $refs)
  const reset = () => {
    resetComponentState();
  };

  const resetComponentState = () => {
    // Reset local component state
    selectedCases.value = [];
    casesCount.value = 0;
    drawer.value = false;
    selectedCase.value = null;
    tableFilter.value = false;
    
    // Reset composable state by resetting to first page
    if (composableCurrentPage.value) {
      composableCurrentPage.value = 1;
    }
    
    // Clear the persistent selected cases from the list composable
    tableSelectedCases.value = [];
    
    // Emit empty selected cases to parent  
    proxy.$emit('input', []);
    
    // Force reactivity update
    proxy.$nextTick(() => {
      proxy.$forceUpdate();
    });
  };

  async function getCasesAfterMounted(val){
      if(showTestFolder.value) await getCases(val);
    }

  function updatePagination(val){
    composableUpdatePagination(val, !showTestFolder.value)
  }

  // Initialize method
  const init = async () => {
    try {
      await getFolders();

      if (route.params.folderUid) {
        folderUid.value = route.params.folderUid;
        await getCases(route.params.folderUid, true);
      } else {
        await getAllCases();
        selectDefaultFolder();
      }
    
      // Safely iterate over cases if they exist
      if (cases.value && Array.isArray(cases.value)) {
        cases.value.forEach((cse) => {
          proxy.$set(cse, 'toBeSelected', false);
        });
      }
      
      selectedCases.value = props.value || [];
      tableFilter.value = false;
    } catch (error) {
      console.error('Error during component initialization:', error);
      // Don't block the UI if there's an error, just log it
    }
  };

  return {
    // Reactive state
    showConfirmBackDialog,
    showAddDialog,
    tableFilter,
    cases,
    folders,
    selectedCases,
    isColumnFilter,
    isLoading,
    confirmDialogTitle,
    confirmDialogContent,
    confirmDialogContentPartTwo,
    confirmDialogBtnLabel,
    confirmDialogBtnColor,
    confirmDialogRunName,
    confirmType,
    casesCount,
    drawer,
    selectedCase,
    folderUid,
    skeletonLoaderState,
    showTestFolder,

    // Computed properties
    currentAccount,
    deleteActivity,
    deleteEntity,
    writeEntity,
    isEditView,
    selectedCasesCount,
    selectedRunCases,
    selectedRunCasesCount,
    paginationData,
    anySelectedCases,

    // Composable properties
    composableTotalCases,
    composableCasesLoading,
    composableCurrentPage,
    composableItemsPerPage,
    composableUpdatePagination,
    composableRelationsLoading,
    composableApplySearch,
    composableApplyFilters,
    composableClearFilter,

    // Methods
    init,
    selectDefaultFolder,
    getAllCases,
    getCases,
    getFolders,
    handleConfirmBtnClick,
    handleCases,
    handleBackClick,
    handleAddClick,
    onAddTestCase,
    handleCloseClick,
    handleConfirmClick,
    changeFilter,
    updateCasesData,
    reset,
    resetComponentState,
    showSkeletonLoader,
    hideSkeletonLoader,
    getCasesAfterMounted,
    updatePagination,
    UpdateCases
  };
}
