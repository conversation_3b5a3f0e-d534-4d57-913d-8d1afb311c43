import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore, $api } from '@/main';
import { useLoading } from '@/composables/utils/loading';
import { useNetworkError } from '@/composables/utils/networkError';
import { usePermissions } from '@/composables/utils/permissions';
import { showErrorToast } from '@/composables/utils/toast';
import makeRunService from '@/services/api/run';

import Swal from 'sweetalert2';

export const useRunsFailedSyncs = () => {
  const store = useStore();
  const route = useRoute();
  const router = useRouter();

  // Composables
  const { showSkeletonLoader, hideSkeletonLoader, isLoading: skeletonLoaderState } = useLoading();
  const { handleNetworkError } = useNetworkError();
  const { authorityTo } = usePermissions();

  // Services
  let runsService;

  // Computed properties
  const writeEntity = computed(() => authorityTo('write_entity'));
  const deleteEntity = computed(() => authorityTo('delete_entity'));
  const readEntity = computed(() => authorityTo('read_entity'));
  const dynamicHeaders = computed(() => store.getters['headers/dynamicHeaders']);

  // Data properties
  const runData = ref();
  const headers = ref([]);
  const tableLoading = ref(false);
  const breadCrumbs = ref([]);
  const failedSyncs = ref([
    {
      uid: '1',
      integration: 'TestRail',
      name: 'Successfully login',
      date: '03/12/24 17:34:19',
      milestone: 'Release 1.1',
      testPlan: 'Functionality Test Plan',
      testRun: 'Test run 23/04',
    },
    {
      uid: '2',
      integration: 'TestRail',
      name: 'Performance',
      date: '03/12/24 17:34:19',
      milestone: 'User Onboarding',
      testPlan: 'User experience test plan',
      testRun: 'Test run 23/04',
    },
    {
      uid: '3',
      integration: 'TestRail',
      name: 'Integration',
      date: '03/12/24 17:34:19',
      milestone: 'User Onboarding',
      testPlan: 'Functionality Test Plan',
      testRun: 'Smoke test run (Chrome)',
    },
    {
      uid: '4',
      integration: 'TestRail',
      name: 'Compatibility',
      date: '03/12/24 17:34:19',
      milestone: 'User Onboarding',
      testPlan: 'Functionality Test Plan',
      testRun: 'Smoke test run (Chrome)',
    },
    {
      uid: '5',
      integration: 'TestRail',
      name: 'Security',
      date: '03/12/24 17:34:19',
      milestone: 'Release 1.1',
      testPlan: 'Functionality Test Plan',
      testRun: 'Test run 23/04',
    },
    {
      uid: '6',
      integration: 'TestRail',
      name: 'Integration',
      date: '03/12/24 17:34:19',
      milestone: 'Release 2.1',
      testPlan: 'User experience test plan',
      testRun: 'Smoke test run (Chrome)',
    },
    {
      uid: '7',
      integration: 'TestRail',
      name: 'Load',
      date: '03/12/24 17:34:19',
      milestone: 'Release 1.1',
      testPlan: 'User experience test plan',
      testRun: 'Test run 23/04',
    },
    {
      uid: '8',
      integration: 'TestRail',
      name: 'User experience',
      date: '03/12/24 17:34:19',
      milestone: 'Release 2.1',
      testPlan: 'User experience test plan',
      testRun: 'User experience test run',
    },
  ]);

  // Methods
  const init = async () => {
    try {
      showSkeletonLoader();
      await fetchTestRun();
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'test runs' }, error?.response?.data);
    } finally {
      await new Promise((resolve) =>
        setTimeout(() => {
          setBreadCrumbs(runData.value.name);
          resolve();
        }, 1000)
      );
      hideSkeletonLoader();
    }
  };
  async function fetchTestRun() {
    if (!readEntity.value) return;
    const handle = route.params.handle;
    const projectKey = route.params.key;
    tableLoading.value = true;

    try {
      const runService = makeRunService($api);
      await runService.getTestRunById;
      const response = await runsService.getTestRunById(handle, projectKey, route.params.id);
      runData.value = response.data;
    } catch (error) {
      handleNetworkError(error);
    } finally {
      tableLoading.value = false;
    }
  }

  function setBreadCrumbs(testRunName) {
    breadCrumbs.value = [
      { text: 'Test Runs', disabled: false, sub: 'All', href: 'TestRunsIndex' },
      { text: testRunName, disabled: false, sub: 'TestRuns', href: 'TestRunDetail' },
      { text: 'Failed Syncs', disabled: true, sub: 'All', href: 'TestRunDetail' },
    ];
  }

  function handleBackClick() {
    router.push({
      name: 'TestRunCase',
      params: {
        handle: route.params.handle,
        key: route.params.key,
        id: route.params.id,
      },
    });
  }

  // Initialize services and headers
  const initializeServices = () => {
    runsService = makeRunService($api);
  };
  const initializeHeaders = () => {
    if (!dynamicHeaders.value.failedSyncsTable) {
      store.dispatch('headers/initializeHeaders', { type: 'failedSyncsTable' });
    }
    headers.value = dynamicHeaders.value.failedSyncsTable;
  };

  // Lifecycle hooks
  onMounted(async () => {
    initializeServices();
    initializeHeaders();

    await init();
  });

  return {
    writeEntity,
    deleteEntity,
    breadCrumbs,
    runData,
    skeletonLoaderState,
    failedSyncs,
    headers,
    fetchTestRun,
    setBreadCrumbs,
    handleBackClick,
  };
};