import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { $api } from '@/main';
import { useRoute } from 'vue-router/composables';
import Swal from 'sweetalert2';
import makeExecutionService from '@/services/api/execution.js';
import makeRunService from '@/services/api/run.js';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { getRebalanceWindow, rebalanceOrder } from '@/composables/utils/orderingFunctions';
import DragIndicator from '@/components/Execution/DragIndicator.vue';
import Vue from 'vue';

export function useTestExecutionList(props, emit, context = null) {
  const route = useRoute();

  const localCaseItems = ref([]);
  const executionsService = makeExecutionService($api);
  const runService = makeRunService($api);
  const edgeNeighbors = ref({ prev: null, next: null });

  // Get selectedRows from the component instance
  const getSelectedRows = () => {
    if (context && context.selectedRows) {
      return context.selectedRows;
    }
    return [];
  };

  const displayTableData = computed(() => {
    if (props.isImport) {
      return props.caseItems;
    }
    return localCaseItems.value?.filter((item) => item.active !== false) || [];
  });
  function getDecimalPrecision(num) {
    if (!isFinite(num)) return 0;
    const s = num.toString();
    if (s.indexOf('.') === -1) return 0;
    return s.split('.')[1].length;
  }
  // Create a custom drag image showing the count of test cases
  function createDragImage(count) {
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.top = '-1000px';
    container.style.zIndex = '9999';
    container.style.pointerEvents = 'none';

    const ComponentClass = Vue.extend(DragIndicator);
    const instance = new ComponentClass({
      propsData: { count },
    });

    instance.$mount();
    container.appendChild(instance.$el);

    return container;
  }
  function initializeDraggable() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach(() => {
        const tableRows = document.querySelectorAll('#execution-table tbody tr');
        tableRows.forEach((row) => applyDragFunctionality(row));
      });
    });

    // Start observing the table
    const tableSelector = '#execution-table tbody';
    const checkTable = setInterval(() => {
      const table = document.querySelector(tableSelector);
      if (table) {
        clearInterval(checkTable);
        observer.observe(table, {
          childList: true,
          subtree: true,
        });

        // Apply to existing rows
        const tableRows = document.querySelectorAll('#execution-table tbody tr');
        tableRows.forEach((row) => applyDragFunctionality(row));
      }
    }, 100);

    // Cleanup interval after 10 seconds if table is not found
    setTimeout(() => clearInterval(checkTable), 10000);
  }
  function applyDragFunctionality(row) {
    if (row.draggable === true && row.dataset.dragApplied === 'true') {
      return;
    }
    row.draggable = true;
    row.dataset.dragApplied = 'true';
    const allRows = Array.from(row.parentNode.children);
    const targetIndex = allRows.indexOf(row);
    const isFirstRow = targetIndex === 0;
    row.addEventListener('dragstart', (e) => {
      const regex = /uid-(\d+)/;
      const uid = regex.exec(row.className)[1];
      e.target.classList.add('dragging');
      window.isFileDragging = true;
      window.executionUid = uid;

      const selectedRows = getSelectedRows();
      const draggedExecutionIsSelected = selectedRows.some(
        (selectedExecution) => selectedExecution.uid === uid || selectedExecution.uid === parseInt(uid)
      );
      if (draggedExecutionIsSelected && selectedRows.length > 1) {
        window.selectedExecutionUids = selectedRows.map((selectedExecution) => selectedExecution.uid);
        window.isMultipleExecutionsDrag = true;
      } else {
        window.selectedExecutionUids = null;
        window.isMultipleExecutionsDrag = false;
      }

      const dragCount = draggedExecutionIsSelected && selectedRows.length > 1 ? selectedRows.length : 1;

      // Create and use custom drag image
      const dragImage = createDragImage(dragCount);
      document.body.appendChild(dragImage);

      // Position the drag image at the cursor location
      const offsetX = dragImage.offsetWidth / 2;
      const offsetY = dragImage.offsetHeight / 2;

      // Set the custom drag image
      e.dataTransfer.setDragImage(dragImage, offsetX, offsetY);

      // Remove the element after the drag operation starts
      setTimeout(() => {
        document.body.removeChild(dragImage);
      }, 0);
    });

    row.addEventListener('dragend', (e) => {
      e.target.classList.remove('dragging');
      window.isMultipleExecutionsDrag = false;
      window.selectedExecutionUids = null;
    });

    // Add drop functionality for reordering
    row.addEventListener('dragover', (e) => {
      if (window.isFileDragging && window.executionUid) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';

        // Calculate drop position based on mouse position
        const rect = e.target.closest('tr').getBoundingClientRect();
        const offsetY = e.clientY - rect.top;
        const percent = (offsetY / rect.height) * 100;

        // Remove existing drop indicators
        row.classList.remove('drop-above', 'drop-below');

        // Add appropriate drop indicator
        if (isFirstRow && percent < 20) {
          row.classList.add('drop-above');
        } else if (percent > 70) {
          row.classList.add('drop-below');
        }
      }
    });

    row.addEventListener('dragleave', (e) => {
      if (!row.contains(e.relatedTarget)) {
        row.classList.remove('drop-above', 'drop-below');
      }
    });

    row.addEventListener('drop', (e) => {
      if (window.isFileDragging && window.executionUid) {
        e.preventDefault();

        const draggedUid = window.executionUid;
        const targetUid = row.className.match(/uid-(\d+)/)?.[1];

        if (draggedUid && targetUid && draggedUid !== targetUid) {
          // Calculate drop position
          const rect = e.target.closest('tr').getBoundingClientRect();
          const offsetY = e.clientY - rect.top;
          const percent = (offsetY / rect.height) * 100;
          // const dropPosition = percent < 50 ? 'before' : 'after';
          // const isLastRow = targetIndex === allRows.length - 1;

          let shouldReorder = false;
          let dropPosition = 'after';

          if (isFirstRow && percent < 20) {
            shouldReorder = true;
            dropPosition = 'before';
          } else if (percent > 70) {
            shouldReorder = true;
            dropPosition = 'after';
          }

          if (shouldReorder) {
            // Get selected execution IDs for multi-drag
            const executionUids = window.isMultipleExecutionsDrag ? window.selectedExecutionUids : [draggedUid];

            // Emit reorder event

            handleReorderExecutions({
              draggedUids: executionUids,
              targetUid: targetUid,
              position: dropPosition,
              row,
            });
          }
        }

        // Clean up visual indicators
        row.classList.remove('drop-above', 'drop-below');
      }
    });
  }

  function addGlobalDragListeners() {
    document.addEventListener('dragover', (e) => {
      if (window.isFileDragging) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
      }
    });

    document.addEventListener('dragenter', (e) => {
      if (window.isFileDragging) {
        e.preventDefault();
      }
    });
  }

  async function loadEdgeNeighbors() {
    try {
      const handle = route.params.handle;
      const projectKey = route.params.key;
      const runUid = route.params.id;
      const currentPage = Number(props.currentPage || 1);
      const perPage = Number(props.itemsPerPage || 10);
      const hasPrev = currentPage > 1;
      const hasNext = props.totalItems ? currentPage * perPage < Number(props.totalItems) : false;
      const offset = currentPage - 1 <= 0 ? 0 : currentPage - 1;

      let prev = null;
      let next = null;

      if (hasPrev) {
        const prevResp = await runService.getTestRunExecutions(handle, projectKey, runUid, {
          limit: 1,
          offset: offset * perPage - 1,
        });
        const prevItems = prevResp?.data?.items || [];
        prev = prevItems[prevItems.length - 1] || null;
      }
      if (hasNext) {
        const nextResp = await runService.getTestRunExecutions(handle, projectKey, runUid, {
          limit: 1,
          offset: (offset + 1) * perPage,
        });
        const nextItems = nextResp?.data?.items || [];
        next = nextItems[0] || null;
      }

      edgeNeighbors.value = { prev, next };
    } catch (e) {
      edgeNeighbors.value = { prev: null, next: null };
    }
  }

  function assignPositions(draggedUids, basePosition, increment = 0.0001) {
    return draggedUids.reduce((acc, uid, idx) => {
      acc[uid] = Number(basePosition) + idx * increment;
      return acc;
    }, {});
  }

  async function handleReorderExecutions(reorderData) {
    const { draggedUids, targetUid, position, row } = reorderData;
    const payload = {};
    const handle = route.params.handle;
    const projectKey = route.params.key;

    const targetExec = localCaseItems.value.find((item) => item.uid.toString() === targetUid);
    if (!targetExec) return;

    const beforeUid = row.previousElementSibling?.className?.match(/uid-(\d+)/)?.[1] || null;
    const afterUid = row.nextElementSibling?.className?.match(/uid-(\d+)/)?.[1] || null;

    const findPos = (uid) => {
      const it = localCaseItems.value.find((i) => i.uid.toString() === uid);
      return it?.position != null ? Number(it.position) : null;
    };

    let leftPos = null;
    let rightPos = null;
    const targetPos = Number(targetExec.position);

    if (position === 'before') {
      // drop before target -> neighbors are (beforeUid, target)
      leftPos = beforeUid
        ? findPos(beforeUid)
        : edgeNeighbors.value.prev?.position != null
        ? Number(edgeNeighbors.value.prev.position)
        : null;
      rightPos = targetPos;
      if (leftPos == null) leftPos = rightPos - 10;
    } else {
      // 'after'
      // drop after target -> neighbors are (target, afterUid)
      leftPos = targetPos;
      rightPos = afterUid
        ? findPos(afterUid)
        : edgeNeighbors.value.next?.position != null
        ? Number(edgeNeighbors.value.next.position)
        : null;
      if (rightPos == null) rightPos = leftPos + 10;
    }

    // If left >= right (no gap) or precision will be too deep, force rebalance
    const canMid = leftPos != null && rightPos != null && leftPos < rightPos;
    let newPosition = canMid ? (leftPos + rightPos) / 2 : position === 'before' ? targetPos - 10 : targetPos + 10;

    // If there is no room (left >= right) or decimal precision too deep, rebalance
    const needsRebalance = !canMid || getDecimalPrecision(newPosition) > 4 || leftPos >= rightPos;
    payload.executionUids = draggedUids;

    if (needsRebalance) {
      // assign temporary position so rebalance window calc can include the dragged item
      const draggedItem = localCaseItems.value.find((item) => item.uid == draggedUids[0]);

      if (!draggedItem) {
        payload.positions = assignPositions(draggedUids, newPosition);
      } else {
        draggedItem.position = newPosition;
        const { start, mid, end } = getRebalanceWindow({
          children: localCaseItems.value,
          center: targetExec,
          newFolder: draggedItem,
        });
        const rebalanced = rebalanceOrder(start, mid, end);
        payload.positions = rebalanced.reduce((acc, item) => {
          acc[item.uid] = item.position;
          return acc;
        }, {});
        payload.executionUids = Object.keys(payload.positions);
      }
    } else {
      payload.positions = assignPositions(draggedUids, newPosition);
    }
    const overrideMap = applyLocalReorder(payload);
    if (context?.$emit) context.$emit('local-positions-override', overrideMap);

    try {
      await executionsService.updateExecutions(handle, projectKey, payload);
      showSuccessToast(Swal, 'updateSuccess', { item: 'Test Execution Order' });
    } catch (error) {
      showErrorToast(Swal, 'updateError', { item: 'Test Execution Order' }, error?.response?.data);
    }
  }

  function applyLocalReorder(payload) {
    if (!payload || !payload.executionUids || payload.executionUids.length === 0) return;
    const positions = payload.positions;

    const byId = new Map(localCaseItems.value.map((it) => [String(it.uid), it]));
    Object.keys(positions).forEach((uid) => {
      const item = byId.get(String(uid));
      if (item) item.position = positions[uid];
    });

    localCaseItems.value = [...localCaseItems.value].sort(
      (a, b) => Number(a?.position ?? 0) - Number(b?.position ?? 0)
    );
    return positions;
  }

  // Lifecycle hooks and watchers
  watch(
    () => props.caseItems,
    (newVal) => {
      localCaseItems.value = newVal;
      initializeDraggable();
    },
    { immediate: true }
  );
  watch(
    () => displayTableData.value,
    (newVal, oldVal) => {
      if (!oldVal || newVal.length !== oldVal.length) {
        nextTick(() => {
          initializeDraggable();
        });
      }
    }
  );
  onMounted(async () => {
    initializeDraggable();
    addGlobalDragListeners();
    loadEdgeNeighbors();
  });

  watch(
    () => [props.currentPage, props.itemsPerPage, props.totalItems],
    () => {
      loadEdgeNeighbors();
    }
  );

  return {
    displayTableData,
    addGlobalDragListeners,
    handleReorderExecutions,
  };
}
