import { ref } from 'vue';
import { $api } from '@/main';
import makeProjectsService from '@/services/api/project';
import { showErrorToast } from '@/composables/utils/toast';
import Swal from 'sweetalert2';

export const useTestRunCount = () => {
  const activeCount = ref(0);
  const archivedCount = ref(0);
  const loading = ref(false);

  const projectService = makeProjectsService($api);

  const getEntityCount = async (handle, projectKey) => {
    try {
      loading.value = true;
      const response = await projectService.getEntityCount(handle, projectKey, 'run');
      
      if (response.status === 200) {
        activeCount.value = response.data.active || 0;
        archivedCount.value = response.data.archived || 0;
      }
      
      return {
        active: activeCount.value,
        archived: archivedCount.value
      };
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'test run counts' }, error?.response?.data);
      return {
        active: 0,
        archived: 0
      };
    } finally {
      loading.value = false;
    }
  };

  return {
    activeCount,
    archivedCount,
    loading,
    getEntityCount
  };
}; 