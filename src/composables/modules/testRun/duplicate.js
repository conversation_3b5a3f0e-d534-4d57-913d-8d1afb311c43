import { ref, computed, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore } from '@/main';
import { useLoading } from '@/composables/utils/loading';
import { usePermissions } from '@/composables/utils/permissions';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { redirectToMappedRoute } from '@/utils/util';
import makePlanService from '@/services/api/plan';
import makeMilestonesService from '@/services/api/milestone';
import makeConfigurationService from '@/services/api/configuration';
import makeTagService from '@/services/api/tag';
import makeRunService from '@/services/api/run';

export function useRunDuplicate() {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;
  
  // Composables
  const route = useRoute();
  const router = useRouter();
  const store = useStore();
  const { showSkeletonLoader, hideSkeletonLoader, skeletonLoaderState } = useLoading();
  const { authorityTo } = usePermissions();

  // Services
  let planService;
  let runsService;
  let milestoneService;
  let configurationService;
  let tagService;

  // Reactive state
  const planName = ref('');
  const displayedRuns = ref('all');
  const appliedFilters = ref({});
  const createButtonLoading = ref(false);
  const searchFilter = ref('');
  const addToMilestoneDialog = ref(false);
  const addToTestPlansDialog = ref(false);
  const duplicateAndApplyConfigDialog = ref(false);
  const loading = ref(false);
  const selecteditems = ref([]);
  const filter = ref('ongoing');
  const showConfirmDialog = ref(false);
  const confirmDialogTitle = ref('');
  const confirmDialogContent = ref('');
  const confirmDialogContentPartTwo = ref('');
  const confirmDialogItems = ref([]);
  const confirmDialogBtnLabel = ref('');
  const confirmDialogBtnColor = ref('primary');
  const confirmDialogRunName = ref('');
  const confirmType = ref('');
  const isProjectMenuCollapsed = ref(false);
  const isToPlanExpanded = ref(false);
  const plans = ref([]);
  const milestones = ref([]);
  const configurations = ref([]);
  const tags = ref([]);
  const showCases = ref(false);
  const selectedCases = ref([]);
  const duplicatedRun = ref({});
  const isLoading = ref(false);
  const items = ref([]);

  // Computed properties
  const currentAccount = computed(() => store.getters['user/currentAccount']);
  const dynamicHeaders = computed(() => store.getters['headers/dynamicHeaders']);
  
  const writeEntity = computed(() => authorityTo('write_entity'));
  const deleteEntity = computed(() => authorityTo('delete_entity'));
  const readEntity = computed(() => authorityTo('read_entity'));

  const filteredHeaders = computed(() => {
    const filtered = headers.value.filter((header) => header.checked);
    return filtered;
  });

  const filteredRuns = computed(() => {
    let filtered = runs.value;
    if (searchFilter.value) {
      filtered = filtered.filter((item) => matchesFilter(item));
    }
    if (isFilter.value && appliedFilters.value) {
      filtered = filtered.filter((item) => matchApplyFilter(item));
    }
    return filtered;
  });

  const isSelected = computed(() => {
    if (selecteditems.value.length > 0)
      return true;
    return false;
  });

  const hasOneSelectedItem = computed(() => {
    return selecteditems.value.length === 1;
  });

  const runs = computed(() => {
    // Add defensive check to ensure items is always an array
    const itemsArray = Array.isArray(items.value) ? items.value : [];
    let filtered = itemsArray.filter((item) => {
      if(displayedRuns.value == 'unlinked' && item.testPlanUid)
        return false;
      if(Number.isInteger(displayedRuns.value) && item.uid !== displayedRuns.value)
        return false;
      return (item.archivedAt == ( filter.value === 'ongoing' ? null : item.archivedAt ? item.archivedAt : false ));
    });
    return filtered.map((item) => {        
      return {
      ...item
      };
    });
  });

  const activeItemCount = computed(() => {
    const itemsArray = Array.isArray(items.value) ? items.value : [];
    return itemsArray.filter((item) => item.archivedAt == null).length;
  });

  const archivedItemCount = computed(() => {
    const itemsArray = Array.isArray(items.value) ? items.value : [];
    return itemsArray.filter((item) => item.archivedAt).length;
  });

  const isFilterArchived = computed(() => {
    return filter.value === 'archived';
  });

  const activeMilestones = computed(() => {
    const milestonesArray = Array.isArray(milestones.value) ? milestones.value : [];
    return milestonesArray.filter((milestone) => milestone && !milestone?.archivedAt && !milestone?.deletedAt);
  });

  const getActivePlans = computed(() => {
    const plansArray = Array.isArray(plans.value) ? plans.value : [];
    return plansArray.filter(plan => plan && plan.archivedAt == null);
  });

  const getActivePlansCount = computed(() => {
    const plansArray = Array.isArray(plans.value) ? plans.value : [];
    return plansArray.filter(plan => plan && plan.archivedAt == null).length;
  });

  const isFilter = computed(() => {
    return Object.keys(appliedFilters.value).length > 0;
  });

  const headers = ref([]);

  // Initialize services
  const initializeServices = () => {
    planService = makePlanService(proxy.$api);
    runsService = makeRunService(proxy.$api);
    milestoneService = makeMilestonesService(proxy.$api);
    configurationService = makeConfigurationService(proxy.$api);
    tagService = makeTagService(proxy.$api);
  };

  // Methods
  const updateFilter = (newFilter) => {
    filter.value = newFilter;
  };

  const createTestPlan = async () => { 
    if(!isProjectArchived.value) {
    const payload = {
      name: planName.value,
      source: "testfiesta",
      status: '',
      milestoneUids: [],
      priority: '',
      runs: [],
      customFields: {
        archived: false
      }
   };
   try {
     createButtonLoading.value = true;
      const response = await planService.createTestPlan(
      route.params.handle,
      route.params.key,
      payload
     );
     if (response.status === 200) {
      planName.value = ""; 
      router.push({ name: 'TestPlans' }); 
     }
    } catch (err) {
      showErrorToast(proxy.$swal, err.response?.data?.message || 'Internal server error');
    } finally {
      createButtonLoading.value = false;
   }
  }
  };

  const onBackShowCases = () => {
    selecteditems.value = [];
    showCases.value = false;
  };

  const setselected = (selectedItems) => {
    selecteditems.value = selectedItems;
  };

  const handleAddToMilestoneDialog = (item) => {
    selecteditems.value = [item];
    addToMilestoneDialog.value = true;
  };

  const onCloseAddToMilestoneDialog = () => {
    addToMilestoneDialog.value = false;
  };

  const handleAddToTestPlansDialog = (item) => {
    selecteditems.value = [item];
    addToTestPlansDialog.value = true;
  };

  const onCloseAddToTestPlansDialog = () => {
    addToTestPlansDialog.value = false;
  };

  const handleDuplicateAndApplyConfigDialog = (item) => {
    selecteditems.value = [item];
    duplicateAndApplyConfigDialog.value = true;
  };

  const onCloseDuplicateAndApplyConfigDialog = () => {
    duplicateAndApplyConfigDialog.value = false;
  };

  const toggleProjectMenu = () => {
    isProjectMenuCollapsed.value = !isProjectMenuCollapsed.value;
  };

  const searchCollapsedMenu = () => {
    isProjectMenuCollapsed.value = !isProjectMenuCollapsed.value;
    proxy.$nextTick(() => {
      if (proxy.$refs.searchField) {
        proxy.$refs.searchField.focus();
      }
    });
  };

  const unlinkedCollapsedMenu = () => {
    isProjectMenuCollapsed.value = !isProjectMenuCollapsed.value;
    displayedRuns.value = 'unlinked';
  };

  const linkedCollapsedMenu = () => {
    isProjectMenuCollapsed.value = !isProjectMenuCollapsed.value;
    isToPlanExpanded.value = true;
  };

  const onToPlanExpanded = () => {
    isToPlanExpanded.value = !isToPlanExpanded.value;
  };

  const getMilestones = async () => {
    const handle = route.params.handle;
    const projectKey = route.params.key;

    try {
      const response = await milestoneService.getMilestones(handle, projectKey);
      // Add defensive check to ensure milestones is always an array
      milestones.value = Array.isArray(response.data?.items) ? response.data.items : [];
      return milestones.value;
    } catch (err) {
      // Ensure milestones is set to empty array on error
      milestones.value = [];
      showErrorToast(proxy.$swal, 'fetchError', { item: 'milestones' }, err?.response?.data);
      return [];
    }
  };

  const getTestPlans = async () => {
    const handle = route.params.handle;
    const projectKey = route.params.key;

    try {
      const response = await planService.getPlans(handle, projectKey, 1000, 0);
      // Add defensive check to ensure plans is always an array
      plans.value = Array.isArray(response.data?.items) ? response.data.items : [];
      return plans.value;
    } catch (err) {
      // Ensure plans is set to empty array on error
      plans.value = [];
      showErrorToast(proxy.$swal, 'fetchError', { item: 'test plans' }, err?.response?.data);
      return [];
    }
  };

  const getConfigurations = async () => {
    const handle = route.params.handle;
    const projectKey = route.params.key;

    try {
      const response = await configurationService.getConfigurations(handle, projectKey, 10, 0);
      // Add defensive check to ensure configurations is always an array
      configurations.value = Array.isArray(response.data?.configurations) ? response.data.configurations : [];
      return configurations.value;
    } catch (err) {
      // Ensure configurations is set to empty array on error
      configurations.value = [];
      showErrorToast(proxy.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      return [];
    }
  };

  const getTags = async () => {
    const handle = route.params.handle;
    try {
      const response = await tagService.getTags(handle, 'runs');
      // Add defensive check to ensure tags is always an array
      tags.value = Array.isArray(response.data) ? response.data : [];
      return tags.value;
    } catch (err) {
      // Ensure tags is set to empty array on error
      tags.value = [];
      showErrorToast(proxy.$swal, 'fetchError', { item: 'tags' }, err?.response?.data);
      return [];
    }
  };

  const createTestRuns = async () => {
    let payload = {};

      payload = {
        ...duplicatedRun.value
      };

      payload.name = proxy.$t('copyOf') + ' ' + duplicatedRun.value.name;

    if (selectedCases.value.length) {
      const selectedCasesData = selectedCases.value.map(item => item.testCaseRef);
      payload.customFields.caseCount = selectedCasesData.length;
      payload.cases = selectedCasesData;
    }

      try {

        isLoading.value = true;

        await runsService.createTestRun(route.params.handle, route.params.key, payload);
        showSuccessToast(proxy.$swal, 'addSuccess', { item: 'Test Run' });

        redirectToMappedRoute(route, router, 'Runs', {
          handle: route.params.handle,
          key: route.params.key,
        });

      } catch (error) {
        showErrorToast(proxy.$swal, 'addError', { item: 'Failed to create Test Run' }, error?.response?.data);
      } finally {
        isLoading.value = false;
      }
  };

  const handleConfirmDialog = async (type) => {
    showConfirmDialog.value = true;
    switch (type) {
      case 'multi_archive':
        confirmDialogTitle.value = proxy.$t('testruns.archive_dialog.title_multi');
        confirmDialogContent.value = proxy.$t('testruns.archive_dialog.content');
        confirmDialogContentPartTwo.value = '';
        confirmDialogBtnLabel.value = proxy.$t('testruns.archive_dialog.btn_label');
        confirmDialogBtnColor.value = 'primary';
        confirmDialogRunName.value = '';
        confirmType.value = 'multi_archive';
        break;
      case 'multi_unarchive':
        confirmDialogTitle.value = proxy.$t('testruns.unarchive_dialog.title_multi');
        confirmDialogContent.value = proxy.$t('testruns.unarchive_dialog.content');
        confirmDialogContentPartTwo.value = '';
        confirmDialogBtnLabel.value = proxy.$t('testruns.unarchive_dialog.btn_label');
        confirmDialogBtnColor.value = 'primary';
        confirmDialogRunName.value = '';
        confirmType.value = 'multi_unarchive';
        break;
      case 'multi_delete':
        confirmDialogTitle.value = proxy.$t('testruns.delete_dialog.title');
        confirmDialogContent.value = proxy.$t('testruns.delete_dialog.content');
        confirmDialogContentPartTwo.value = (filter.value == 'ongoing' ? proxy.$t('testruns.delete_dialog.content_part2') : '');
        confirmDialogBtnLabel.value = proxy.$t('testruns.delete_dialog.btn_label');
        confirmDialogBtnColor.value = 'danger';
        confirmDialogRunName.value = '';
        confirmType.value = 'multi_delete';
        break;
      case 'multi_milestone':
        confirmDialogTitle.value = proxy.$t('testruns.milestone_dialog.title');
        confirmDialogContent.value = proxy.$t('testruns.milestone_dialog.content');
        confirmDialogItems.value = await getMilestones();
        confirmDialogContentPartTwo.value = '';
        confirmDialogBtnLabel.value = proxy.$t('testruns.milestone_dialog.btn_label');
        confirmDialogRunName.value = '';
        confirmType.value = 'multi_milestone';
        confirmDialogBtnColor.value = 'primary';
        break;
        case 'multi_testplan':
          confirmDialogTitle.value = proxy.$t('testruns.testplan_dialog.title');
          confirmDialogContent.value = proxy.$t('testruns.testplan_dialog.content');
          confirmDialogItems.value = await getTestPlans();
          confirmDialogContentPartTwo.value = '';
          confirmDialogBtnLabel.value = proxy.$t('testruns.testplan_dialog.btn_label');
          confirmDialogRunName.value = '';
          confirmType.value = 'multi_testplan';
          confirmDialogBtnColor.value = 'primary';
        break;
    }
  };

  const handleConfirmBtnClick = (type, items) => {
    switch (type) {
      case 'multi_archive':
        handleConfirmArchiveClick(true);
        break;
      case 'multi_unarchive':
        handleConfirmArchiveClick(false);
        break;
      case 'multi_delete':
        handleConfirmDeleteMultiClick();
        break;
      case 'multi_milestone':
        handleConfirmMilestoneClick(items);
        break;
      case 'multi_testplan':
        handleConfirmTestPlanClick(items);
      break;
    }
  };

  const handleCloseClick = () => {
    showConfirmDialog.value = false;
    selecteditems.value = [];
    confirmDialogItems.value = [];
    addToMilestoneDialog.value = false;
    addToTestPlansDialog.value = false;
    duplicateAndApplyConfigDialog.value = false;
  };

  const handleConfirmArchiveClick = async (status) => {
    var runs = [];
    selecteditems.value.map((item) => {
      var run = {
        customFields: {
          ...item.customFields,
        },
        archived: status,
        name: item.name,
        uid: item.uid
      };
      runs = [...runs, run];
    });

    try {
      await runsService.updateTestRuns(route.params.handle, route.params.key, { runs: runs });
      await refreshData();
      showSuccessToast(proxy.$swal, 'archiveSuccess', { item: 'Test run' });
    } catch (error) {
      showErrorToast(proxy.$swal, 'archiveError', { item: 'Test run' }, error?.response?.data);
    } finally {
      handleCloseClick();
    }
  };

  const handleConfirmDeleteMultiClick = async () => {
    var runIds = [];
    selecteditems.value.map((item) => {
      runIds = [...runIds, item.uid];
    });

    try {
      await runsService.deleteTestRuns(route.params.handle, route.params.key, { runIds: runIds });
      await refreshData();
      showSuccessToast(proxy.$swal, 'deleteSuccess', { item: 'Test runs' });
    } catch (error) {
      showErrorToast(proxy.$swal, 'deleteError', { item: 'Test runs' }, error?.response?.data);
    } finally {
      handleCloseClick();
    }
  };

  const handleAddMilestone = async (items) => {
    const handle = route.params.handle;
    const projectKey = route.params.key;
    const runIds = selecteditems.value.map(item => item.uid);
    const payload = {
      runIds
    };

    if(items.length) {
      for (const element of items) {
        try {
          await milestoneService.addRunsToMilestone(handle, projectKey, element, payload);
          showSuccessToast(proxy.$swal, 'addSuccess', { item: 'Test runs to milestone' });
          handleCloseClick();
        } catch (error) {
          showErrorToast(proxy.$swal, 'addError', { item: 'Test runs to milestone' }, error?.response?.data);
        }
      }
      refreshData();
    }
  };

  const handleConfirmTestPlanClick = async (items) => {
    const handle = route.params.handle;
    const projectKey = route.params.key;
    const runIds = selecteditems.value.map(item => item.uid);
    const payload = {
      runs: runIds
    };

    if(items.length) {
      for (const element of items) {
        try {
          await planService.addRunsToTestPlan(handle, projectKey, element, payload);
          showSuccessToast(proxy.$swal, 'addSuccess', { item: 'Test runs to test plan' });
          handleCloseClick();
        } catch (error) {
          showErrorToast(proxy.$swal, 'addError', { item: 'Test runs to test plan' }, error?.response?.data);
        }
      }
      refreshData();
    }
  };

  const handleConfirmMilestoneClick = async (items) => {
    await handleAddMilestone(items);
  };

  const handleDuplicateAndApplyConfig = async () => {
    const handle = route.params.handle;
    const projectKey = route.params.key;
    const runIds = selecteditems.value.map(item => item.uid);

    if(runIds.length) {
      for (const element of runIds) {
        try {
          await runsService.duplicateTestRun(handle, projectKey, element);
          showSuccessToast(proxy.$swal, 'addSuccess', { item: 'Duplicate run created' });
          handleCloseClick();
        } catch (error) {
          showErrorToast(proxy.$swal, 'addError', { item: 'Error in creating duplicating run' }, error?.response?.data);
        }
      }
      refreshData();
    }
  };

  const applyFilters = (filters) => {
    if (filters) {
      appliedFilters.value = filters;
    } else {
      appliedFilters.value = {};
    }
    refreshData();
  };

  const matchApplyFilter = (item) => {
    // Return false if item is null or undefined
    if (!item) {
      return false;
    }

    if (appliedFilters.value?.panelPriority?.length > 0 &&
        !appliedFilters.value.panelPriority.includes(item.priority)) {
      return false;
    }
    if (appliedFilters.value?.panelStatus?.length > 0 &&
        !appliedFilters.value.panelStatus.includes(item.status)) {
      return false;
    }
    if (appliedFilters.value?.panelMilestone?.length > 0 &&
        !appliedFilters.value.panelMilestone.includes(item.customFields?.milestone) &&
        !appliedFilters.value.panelMilestone.includes('None')) {
      return false;
    }

    if (appliedFilters.value?.panelTag?.length > 0 &&
        !appliedFilters.value.panelTag.includes(item.customFields?.tags) &&
        !appliedFilters.value.panelTag.includes('none')) {
      return false;
    }
    if (appliedFilters.value?.testCasesRange?.[0] > item.customFields?.caseCount ||
        appliedFilters.value?.testCasesRange?.[1] < item.customFields?.caseCount) {
      return false;
    }
    if (appliedFilters.value?.progressRange?.[0] > item.customFields?.progress ||
        appliedFilters.value?.progressRange?.[1] < item.customFields?.progress) {
      return false;
    }
    if (appliedFilters.value?.dateRange?.start && appliedFilters.value?.dateRange?.start > item.createdAt ||
        appliedFilters.value?.dateRange?.end && appliedFilters.value?.dateRange?.end < item.createdAt) {
      return false;
    }
    return true;
  };

  const matchesFilter = (item) => {
    // Add defensive checks to prevent errors
    if (!item || !item.name || !searchFilter.value) {
      return false;
    }
    const lowerCaseFilter = searchFilter.value.toLowerCase();
    return item.name.toLowerCase().includes(lowerCaseFilter);
  };

  // Refreshes all data
  const refreshData = async () => {
    let handle = currentAccount.value.handle;
    getProjectRun(handle);
  };

  const getProjectRun = async (handle) => {
    try {
      const response = await runsService.getRuns(handle, route.params.key, { limit: 10, offset: 0 });
      // Add defensive check to ensure items is always an array
      items.value = Array.isArray(response.data?.items) ? response.data.items : [];
    } catch (err) {
      // Ensure items is set to empty array on error
      items.value = [];
      showErrorToast(proxy.$swal, 'fetchError', { item: 'runs' }, err?.response?.data);
      console.error(err);
    }
  };

  const onClickDuplicate = async () => {
    if(hasOneSelectedItem.value){
      duplicatedRun.value = selecteditems.value[0];
      showCases.value = true;
      await getRunById(
        route.params.handle,
        route.params.key,
        duplicatedRun.value.uid
      );
    }
  };

  const getRunById = async (handle, projectKey, runId) => {
    try {
      const response = await runsService.getTestRunById(handle, projectKey, runId);
      // Add defensive check to ensure selectedCases is always an array
      selectedCases.value = Array.isArray(response.data?.runCases) ? response.data.runCases : [];
    } catch (err) {
      // Ensure selectedCases is set to empty array on error
      selectedCases.value = [];
      showErrorToast(proxy.$swal, proxy.$t('testruns.edit_testrun.not_found'), {}, err?.response?.data);
    }
  };

  // Handles row click
  const handleRowClick = (item) => {
    router.push({
        name: 'TestRunCaseEdit',
        params: {
          handle: route.params.handle,
          key: route.params.key,
          id: item.uid,
        }
      });
  };

  const editItem = (item) => {
    router.push({
      name: 'TestRunEdit',
      params: {
        handle: route.params.handle,
        key: route.params.key,
        id: item.uid,
      },
    });
  };

  const confirmArchiveRun = (item) => {
    selecteditems.value = [item];
    handleConfirmDialog('archive');
  };

  const confirmUnArchiveRun = (item) => {
    selecteditems.value = [item];
    handleConfirmDialog('unarchive');
  };

  const confirmDeleteRun = (item) => {
    selecteditems.value = [item];
    handleConfirmDialog('delete');
  };

  const isProjectArchived = computed(() => {
    // This should be implemented based on your project status logic
    return false; // placeholder
  });

  // Initialize method
  const init = async () => {
    try {
      showSkeletonLoader();

      if(!dynamicHeaders.value.run) {
        await store.dispatch('headers/initializeHeaders', { type: 'run' });
      }
      headers.value = dynamicHeaders.value.run;

      const handle = route.params.handle;
      await Promise.all([
        getProjectRun(handle),
        getMilestones(),
        getTestPlans(),
        getConfigurations(),
        getTags()
      ]);
    } catch (error) {
      console.error('Error during initialization:', error);
    } finally {
      hideSkeletonLoader();
    }
  };

  // Initialize services when composable is used
  initializeServices();

  return {
    // Reactive state
    planName,
    displayedRuns,
    appliedFilters,
    createButtonLoading,
    searchFilter,
    addToMilestoneDialog,
    addToTestPlansDialog,
    duplicateAndApplyConfigDialog,
    loading,
    selecteditems,
    filter,
    showConfirmDialog,
    confirmDialogTitle,
    confirmDialogContent,
    confirmDialogContentPartTwo,
    confirmDialogItems,
    confirmDialogBtnLabel,
    confirmDialogBtnColor,
    confirmDialogRunName,
    confirmType,
    isProjectMenuCollapsed,
    isToPlanExpanded,
    plans,
    milestones,
    configurations,
    tags,
    showCases,
    selectedCases,
    duplicatedRun,
    isLoading,
    items,
    headers,
    skeletonLoaderState,

    // Computed properties
    currentAccount,
    dynamicHeaders,
    writeEntity,
    deleteEntity,
    readEntity,
    filteredHeaders,
    filteredRuns,
    isSelected,
    hasOneSelectedItem,
    runs,
    activeItemCount,
    archivedItemCount,
    isFilterArchived,
    activeMilestones,
    getActivePlans,
    getActivePlansCount,
    isFilter,

    // Methods
    updateFilter,
    createTestPlan,
    onBackShowCases,
    setselected,
    handleAddToMilestoneDialog,
    onCloseAddToMilestoneDialog,
    handleAddToTestPlansDialog,
    onCloseAddToTestPlansDialog,
    handleDuplicateAndApplyConfigDialog,
    onCloseDuplicateAndApplyConfigDialog,
    toggleProjectMenu,
    searchCollapsedMenu,
    unlinkedCollapsedMenu,
    linkedCollapsedMenu,
    onToPlanExpanded,
    getMilestones,
    getTestPlans,
    getConfigurations,
    getTags,
    createTestRuns,
    handleConfirmDialog,
    handleConfirmBtnClick,
    handleCloseClick,
    handleConfirmArchiveClick,
    handleConfirmDeleteMultiClick,
    handleAddMilestone,
    handleConfirmTestPlanClick,
    handleConfirmMilestoneClick,
    handleDuplicateAndApplyConfig,
    applyFilters,
    matchApplyFilter,
    matchesFilter,
    refreshData,
    getProjectRun,
    onClickDuplicate,
    getRunById,
    handleRowClick,
    editItem,
    confirmArchiveRun,
    confirmUnArchiveRun,
    confirmDeleteRun,
    init,
    showSkeletonLoader,
    hideSkeletonLoader
  };
}
