import { ref, computed, watch, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore } from '@/main';
import { useLoading } from '@/composables/utils/loading';
import { usePermissions } from '@/composables/utils/permissions';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import makeMilestoneService from '@/services/api/milestone';
import makeRunService from '@/services/api/run';
import { useRunsIndex } from '@/composables/modules/testRun/index';

export function useRunEdit(props = {}) {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;
  
  // Composables
  const route = useRoute();
  const router = useRouter();
  const store = useStore();
  const { showSkeletonLoader, hideSkeletonLoader, skeletonLoaderState } = useLoading();
  const { authorityTo } = usePermissions();
  
  // Use existing runs composable for shared functionality
  const { clearCache, refreshData } = useRunsIndex(props.runViewType);

  // Services
  let runsService;
  let milestoneService;

  // Reactive state
  const selectedItem = ref(null);
  const runCases = ref(null);
  const milestones = ref([]);
  const loading = ref(false);
  const selectedTags = ref([]);
  const selectedMilestones = ref([]);
  const selectedDueDate = ref(null);
  const tags = ref([]);
  const initialSelectedTags = ref([]);
  const initialCases = ref([]);

  // Computed properties
  const currentAccount = computed(() => store.getters['user/currentAccount']);
  
  const writeEntity = computed(() => authorityTo('write_entity'));
  const deleteEntity = computed(() => authorityTo('delete_entity'));
  const readEntity = computed(() => authorityTo('read_entity'));

  const activeMilestones = computed(() => 
    milestones.value.filter(milestone => !milestone.archivedAt && !milestone.deletedAt)
    .map(milestone => ({ name: milestone.name, uid: milestone.uid }))
  );

  const addedCases = computed(() => {
    const initialUids = new Set(initialCases.value?.map((c) => c.testCaseRef));
    const result = runCases.value?.filter((c) => !initialUids.has(c.testCaseRef));
    return result;
  });

  const removedCases = computed(() => {
    if (!runCases.value?.length || !initialCases.value?.length) return [];
    const runUidSet = new Set(runCases.value.map(c => c.testCaseRef));
    return initialCases.value.filter(c => !runUidSet.has(c.testCaseRef));
  });

  // Watchers
  watch(() => route.params.id, (newId, oldId) => {
    // Only reset if we're navigating to a different test run edit page
    if (route.name === 'TestRunEdit' && newId !== oldId) {
      resetComponentState();
      getProjectRun();
      getMilestones();
    }
  });

  // Initialize services
  const initializeServices = () => {
    runsService = makeRunService(proxy.$api);
    milestoneService = makeMilestoneService(proxy.$api);
  };

  // Methods
  const updateItem = (item) => {
    selectedItem.value = item;
  };

  const updateTags = (tags) => {
    selectedTags.value = tags;
  };

  const updateMilestones = (milestones) => {
    selectedMilestones.value = milestones;
  };

  const updateDueDate = (dueDate) => {
    selectedDueDate.value = dueDate;
  };

  const getTags = (tagsList) => {
    tags.value = Array.isArray(tagsList) ? tagsList : [];
  };

  const getProjectRun = async () => {
    try {
      const { handle, key: projectKey, id: runId } = route.params;

      const [response, cases] = await Promise.all([
          runsService.getTestRunById(handle, projectKey, runId),
          runsService.getTestRunCases(handle, projectKey, runId)
      ]);

      selectedItem.value = {
        ...response.data,
        status: response?.data?.status ?? response?.data?.customFields?.status,
        priority: response?.data?.priority ?? response?.data?.customFields?.priority,
        references: response?.data?.references || [],
      };
      const tagUidsFromResponse = Array.isArray(response.data?.customFields?.tagUids)
        ? response.data.customFields.tagUids
        : Array.isArray(response.data?.tags)
          ? response.data.tags.map((t) => t.uid)
          : [];
      selectedTags.value = [...tagUidsFromResponse];
      initialSelectedTags.value = [...tagUidsFromResponse];
      runCases.value = cases.data.items;
      initialCases.value = [...cases.data.items];

    } catch (error) {
      showErrorToast(proxy.$swal, proxy.$t('testruns.edit_testrun.not_found'), {}, error?.response?.data);
      router.push({name: 'Runs'});
    }
  };

  const saveTestRuns = async () => {
    if(selectedItem.value){

      const addedMilestones = selectedMilestones.value?.length > 0 ? milestones.value
        .filter(milestone => selectedMilestones.value.includes(milestone.uid))
        .map(milestone => milestone.uid) : [];

      const removedMilestones = selectedMilestones.value?.length > 0 ? milestones.value
        .filter(milestone => !selectedMilestones.value.includes(milestone.uid))
        .map(milestone => milestone.uid) : [];

      // Compute tag diffs based on initial selected tags rather than relying on the tags list
      const initialTagSet = new Set(initialSelectedTags.value || []);
      const currentTagSet = new Set(selectedTags.value || []);
      const addedTags = [...currentTagSet].filter(uid => !initialTagSet.has(uid));
      const removedTags = [...initialTagSet].filter(uid => !currentTagSet.has(uid));

      const payload = {
        name: selectedItem.value?.name,
        status: selectedItem.value?.status ?? selectedItem.value?.customFields?.status,
        priority: selectedItem.value?.priority ?? selectedItem.value?.customFields?.priority,
      };

      addedMilestones.length && (payload.addMilestoneUids = addedMilestones);
      selectedDueDate.value && (payload.dueAt = selectedDueDate.value);
      removedMilestones.length && (payload.removeMilestoneUids = removedMilestones);
      addedTags.length && (payload.addTagUids = addedTags.map(Number));
      removedTags.length && (payload.removeTagUids = removedTags.map(Number));
      selectedItem.value?.description && (payload.description = selectedItem.value?.description);

      let handle = route.params.handle;

        try {
          loading.value = true;
          const response = await runsService.updateTestRun(handle, route.params.key, route.params.id, payload);
          await runsService.updateTestRunCases(handle, route.params.key, route.params.id, {
            addCaseUids: addedCases.value.map(c => c.uid),
            removeExecUids: removedCases.value.map(c => c.uid)
          });

          if(response.status === 200) {
            showSuccessToast(proxy.$swal, proxy.$t('success.testRunUpdated'));

            if (route.query.redirectTo && route.query.redirectTo === 'TestPlanDetail') {
              router.replace({
                name: route.query.redirectTo,
                params: { ...route.params, planId: route.query.planId }
              });
            } else if(route.query.isPlanRerunEdit && route.query.planId) {
              router.replace({
                name: 'TestPlanRerun',
                params: { ...route.params, planId: route.query.planId }
              });
            } else {
              router.push({
                name: 'Runs', params: {
                  handle: handle,
                  key: route.params.key
                }
              });
            }
          }
        } catch (error) {
          showErrorToast(proxy.$swal, 'updateError', { item: 'test run' }, error?.response?.data);
          console.error('Failed to Update Test Run:', error);
        } finally {
          loading.value = false;
          clearCache();
          refreshData();
        }
    }
  };

  const updateCases = (cases) => {
    runCases.value = cases;
  };

  const resetComponentState = () => {
    // Reset the component state when navigating between different test runs
    selectedItem.value = null;
    runCases.value = null;
    loading.value = false;
    selectedTags.value = [];
    selectedMilestones.value = [];
    selectedDueDate.value = null;
    initialCases.value = [];
  };

  const getMilestones = async () => {
    try {
      const response = await milestoneService.getMilestones(route.params.handle, route.params.key);
      milestones.value = response.data.items;
    } catch (error) {
      showErrorToast(proxy.$swal, 'fetchError', { item: 'milestones' }, error?.response?.data);
      milestones.value = []; // Reset to empty array in case of error
    } 
  };

  // Initialize method
  const init = async () => {
    try {
      showSkeletonLoader();
      await getProjectRun();
      await getMilestones();
    } catch (error) {
      console.error('Error during initialization:', error);
    } finally {
      hideSkeletonLoader();
    }
  };

  // Initialize services when composable is used
  initializeServices();

  return {
    // Reactive state
    selectedItem,
    runCases,
    milestones,
    loading,
    selectedTags,
    selectedMilestones,
    selectedDueDate,
    tags,
    initialCases,
    skeletonLoaderState,

    // Computed properties
    currentAccount,
    writeEntity,
    deleteEntity,
    readEntity,
    activeMilestones,
    addedCases,
    removedCases,

    // Methods
    init,
    updateItem,
    updateTags,
    updateMilestones,
    updateDueDate,
    getTags,
    getProjectRun,
    saveTestRuns,
    updateCases,
    resetComponentState,
    getMilestones,
    showSkeletonLoader,
    hideSkeletonLoader
  };
}
