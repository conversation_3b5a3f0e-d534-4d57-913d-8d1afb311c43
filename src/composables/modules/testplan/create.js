import { ref, computed, onMounted } from 'vue';
import { $api } from '@/main';
import { useRouter, useRoute } from 'vue-router/composables';
import makePlanService from '@/services/api/plan';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { requiredFieldValidationRules } from "@/utils/validation";
import makeMilestonesService from '@/services/api/milestone';
import makeTagsService from '@/services/api/tag';
import { useColorPreferences } from '@/composables/utils/colorPreferences';
import { usePlanIndex } from '@/composables/modules/testplan/index';
import Swal from 'sweetalert2';




  const showRunsForCreatePlan = ref(false);
  const selectedRuns = ref([]);
  const globalConfiguration = ref(null);
  const form = ref({
    name: '',
    description: '',
    milestone: [],
    tags: [],
    status: '',
    priority: '',
  });
const tableFilter = ref('all')
  
export const useCreateTestPlan = () => {


  // Vue Router integration
  const router = useRouter();
  const route = useRoute();

  // Refs for UI elements (like the v-form and menus)
  const planForm = ref(null);
  const menuOpen = ref(false);
  const createButtonLoading = ref(false);

  // Validation rules
  const requiredRule = requiredFieldValidationRules();

  // Create a Plan Service instance using our API helper
  const planService = makePlanService($api);

  // Import cache clearing function
  const { clearCache } = usePlanIndex();

  // ----- Methods to support creating a Test Plan -----

  // Open up the Test Run add view (after validating form)
  const handleTestPlanAddRuns = () => {
    if (planForm.value.validate()) {
      showRunsForCreatePlan.value = true;
      menuOpen.value = false;
    }
  };

  // Updates the global test-run configuration
  const updateGlobalConfiguration = (value) => {
    globalConfiguration.value = value;
  };

  // ----- Milestone helpers -----

  const onRemoveSelectedMilestone = (uid) => {
    const index = form.value.milestone.findIndex(item => item === uid);
    if (index !== -1) {
      form.value.milestone.splice(index, 1);
    }
  };

  const milestoneSelection = (uid) => {
    return form.value.milestone.some(item => item === uid);
  };

  // ----- Tag helpers -----

  const onRemoveSelectedTags = (uid) => {
    const index = form.value.tags.findIndex(item => item === uid);
    if (index !== -1) {
      form.value.tags.splice(index, 1);
    }
  };

  const tagsSelection = (uid) => {
    return form.value.tags.some(item => item === uid);
  };


  const generatePayload = (runs) => {
    const payload = {
      name: form.value.name,
      status: form.value.status?.id,
      priority: form.value.priority?.id,
      testRuns: runs.map(item => ({
        uid: item.uid,
        configuration: item.configuration,
      })),
      tagUids: form.value.tags,
      configuration: globalConfiguration.value || {},
    };

    if (form.value.description) {
      payload.description = form.value.description;
    }
    if (form.value.milestone?.length > 0) {
      payload.milestoneUids = form.value.milestone;
    }

    return payload;
  };

  const resetForm = () => {
    form.value = {
      name: '',
      description: '',
      milestone: [],
      tags: [],
      status: '',
      priority: '',
    };
    selectedRuns.value = [];
    globalConfiguration.value = null;
    if (planForm.value) {
      planForm.value.resetValidation();
    }
  };

  const handleCreateTestPlan = async (runsList) => {
    try {
      createButtonLoading.value = true;
      const payload = generatePayload(runsList);
      const response = await planService.createTestPlan(route.params.handle, route.params.key, payload);
      showSuccessToast(Swal, 'createSuccess', { item: 'Test Plan' });
      resetForm();
      // Clear plan cache so the new plan appears immediately
      clearCache();
      if (route.query.redirectTo === 'MilestoneCreate') {
        router.replace({ 
          name: 'MilestoneCreate', 
          params: { handle: route.params.handle, key: route.params.key },
          query: { testPlanId: response.data.uid }
        });
      } else {
        router.replace({ name: 'TestPlans', params: { handle: route.params.handle, key: route.params.key } });
      }
    } catch (error) {
      showErrorToast(Swal, 'createError', { item: 'Test Plan' }, error?.response?.data);
    } finally {
      createButtonLoading.value = false;
    }
  };

  const createTestPlan = async () => {
    if (planForm.value.validate()) {
      // const runsList = selectedRuns.value.map(item => item.uid);
      await handleCreateTestPlan(selectedRuns.value);
    }
  };



  // ----- Back Navigation & Confirmation -----

  const showConfirmBackDialog = ref(false);

  const handleBackClick = () => {
    showConfirmBackDialog.value = true;
  };

  const handleCloseClick = () => {
    showConfirmBackDialog.value = false;
  };

  const handleConfirmClick = () => {
    showConfirmBackDialog.value = false;
    resetForm();
    // Clear cache to ensure fresh data when returning to test plans index
    clearCache();
    if (route.query.redirectTo === 'MilestoneCreate') {
      router.replace({ 
        name: 'MilestoneCreate', 
        params: { handle: route.params.handle, key: route.params.key }
      });
    } else {
      router.replace({ name: 'TestPlans', params: { handle: route.params.handle, key: route.params.key } });
    }
  };

  // ----- Data fetching for Milestones & Tags -----

  const milestones = ref([]);
  const tags = ref([]);

  const milestoneService = makeMilestonesService($api);
  const tagService = makeTagsService($api);

  const getAllMileStones = async () => {
    try {
      const response = await milestoneService.getMilestones(
        route.params.handle,
        route.params.key
      );
      if (response.status === 200) {
        milestones.value = response.data.items;
      }
    } catch (err) {
      showErrorToast(Swal, 'fetchError', { item: 'Milestones' }, err?.response?.data);
    }
  };

  const getAllTags = async () => {
    try {
      const response = await tagService.getTags(route.params.handle, 'plans');
      if (response.status === 200) {
        tags.value = response.data;
      }
    } catch (err) {
      showErrorToast(Swal, 'fetchError', { item: 'Tags' }, err?.response?.data);
    }
  };

  onMounted(async () => {
    await getAllMileStones();
    await getAllTags();
  });

  // ----- Priorities & Statuses -----

  const statuses = ref([]);
  const priorities = ref([]);
  const { getPriorities, getStatuses, getDefaultPriority, getDefaultStatus } = useColorPreferences($api, Swal);
  priorities.value = getPriorities("testPlan").filter(element => !element.archived);
  statuses.value = getStatuses("testPlan").filter(element => !element.archived);
  form.value.priority = getDefaultPriority(priorities.value);
  form.value.status = getDefaultStatus(statuses.value);

  // ----- Users & Filters (if needed) -----

  const users = ref([]);
  const appliedFilters = ref({
    roles: [],
    projects: [],
    tags: [],
  });

  const toBeSelectedUserLength = computed(() =>
    users.value.filter(user => user.toBeSelected).length
  );

  const clearAll = () => {
    appliedFilters.value = {
      roles: [],
      projects: [],
      tags: [],
    };
  };

  const applyFilters = (filters) => {
    appliedFilters.value = filters;
  };

  // ----- Other Navigation Helpers -----

  const handleAddTestRuns = () => {
    router.push({
      name: 'TestRunCreate',
      params: {
        handle: route.params.handle,
        key: route.params.key,
      },
      query: {
        redirectTo: 'TestPlanCreate',
      }
    });
  };

  const handleTestPlanDuplicate = () => {
    const query = route.query.activeAddMilestone ? { activeAddMilestone: 'true' } : {};
    router.push({
      name: 'TestPlanDuplicate',
      params: {
        handle: route.params.handle,
        key: route.params.key,
      },
      query: query
    });
  };

  const onAddTestRun = () => {
      showRunsForCreatePlan.value = false;
      menuOpen.value = false;
  };

  return {

    // Expose reactive state
    showRunsForCreatePlan,
    selectedRuns,
    globalConfiguration,
    form,
    tableFilter,
    planForm,
    menuOpen,
    createButtonLoading,
    requiredRule,
    // Expose methods related to test plan creation
    handleTestPlanAddRuns,
    updateGlobalConfiguration,
    onRemoveSelectedMilestone,
    milestoneSelection,
    onRemoveSelectedTags,
    tagsSelection,
    generatePayload,
    handleCreateTestPlan,
    createTestPlan,
    // Expose back/confirmation methods
    handleBackClick,
    handleCloseClick,
    handleConfirmClick,
    showConfirmBackDialog,
    onAddTestRun,
    // Expose data for milestones and tags
    milestones,
    tags,
    getAllMileStones,
    getAllTags,
    // Expose priorities and statuses
    statuses,
    priorities,
    // Expose users and filter-related state
    users,
    appliedFilters,
    toBeSelectedUserLength,
    clearAll,
    applyFilters,
    // Navigation helpers
    handleAddTestRuns,
    handleTestPlanDuplicate,
    resetForm,
  };
};