import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore, $api } from '@/main';
import { useLoading } from '@/composables/utils/loading';
import { usePermissions } from '@/composables/utils/permissions';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { useTestPlanCount } from '@/composables/modules/testplan/count';
import { useRelations } from '@/composables/utils/relations';
import { t } from '@/i18n';
import makePlanService from '@/services/api/plan';
import makeMilestonesService from '@/services/api/milestone';
import makeTagService from '@/services/api/tag';
import { debounce } from 'lodash';
import { runStateMap } from '@/constants/grid';
import Swal from 'sweetalert2';

// Module-level state for caching test plans across component instances
const testPlansCache = ref([]);
const totalRowsCache = ref(0);
const lastFetchedPage = ref(0);
const lastFetchedFilter = ref('');
const lastFetchedSearch = ref('');
const lastFetchedHandle = ref('');
const lastFetchedProjectKey = ref('');
const lastFetchedApiFilters = ref(null);
const lastFetchedSortBy = ref([]);
const lastFetchedSortDesc = ref([]);
const hasInitiallyLoaded = ref(false);
const selecteditems = ref([]);
const milestones = ref([]);
const tags = ref([]);
const currentPage = ref(1);
const perPage = ref(10);
const sortBy = ref([]);
const sortDesc = ref([]);



export const usePlanIndex = () => {
  const route = useRoute();
  const router = useRouter();
  const store = useStore();
  
  // Composables
  const { showSkeletonLoader, hideSkeletonLoader, isLoading: skeletonLoaderState } = useLoading();

  const { authorityTo } = usePermissions();
  const { activeCount, archivedCount, getEntityCount } = useTestPlanCount();
  const { relationsLoading, relationLoadingStates, fetchPlanRelations, resetRelationLoadingStates, clearEntityTypeCache } = useRelations();

  // Services
  let planService;
  let milestoneService;
  let tagService;

  // Helper function to check if we need to fetch new data
  const needsDataFetch = (pageNumber, pageSize, filterValue, searchValue, handleValue, projectKeyValue, apiFiltersValue, sortByValue, sortDescValue) => {
    // Always fetch if context changed
    if (filterValue !== lastFetchedFilter.value || 
        searchValue !== lastFetchedSearch.value || 
        handleValue !== lastFetchedHandle.value ||
        projectKeyValue !== lastFetchedProjectKey.value ||
        JSON.stringify(apiFiltersValue) !== JSON.stringify(lastFetchedApiFilters.value) ||
        JSON.stringify(sortByValue) !== JSON.stringify(lastFetchedSortBy.value) ||
        JSON.stringify(sortDescValue) !== JSON.stringify(lastFetchedSortDesc.value)) {
      return true;
    }

    // Calculate required data range
    const requiredEndIndex = pageNumber * pageSize;
    const currentCachedCount = testPlansCache.value.length;
    
    // Need to fetch if we don't have enough cached data
    return requiredEndIndex > currentCachedCount && currentCachedCount < totalRowsCache.value;
  };

  // Helper function to clear cache
  const clearCache = () => {
    testPlansCache.value = [];
    totalRowsCache.value = 0;
    lastFetchedPage.value = 0;
    lastFetchedFilter.value = '';
    lastFetchedSearch.value = '';
    lastFetchedHandle.value = '';
    lastFetchedProjectKey.value = '';
    lastFetchedApiFilters.value = null;
    lastFetchedSortBy.value = [];
    lastFetchedSortDesc.value = [];
    hasInitiallyLoaded.value = false;
    clearEntityTypeCache('plan');
  };

  // Data properties
  const selectedPlan = ref({});
  const appliedFilters = ref(null);
  const apiFilters = ref(null);
  const loading = ref(false);
  const isOpenFilter = ref(false);
  const headers = ref([]);
  const isColumnFilter = ref(false);
  const menuOpen = ref(false);
  const confirmDuplicateDialog = ref(false);
  const isCustomizeDisplayed = ref(false);
  const searchFilter = ref('');
  const bulkDuplicateLoading = ref(false);
  const itemKey = ref('uid');
  const filter = ref('ongoing');
  const rowClass = ref(() => 'test-plan-item');
  const clearSelection = ref(false);
  const isFilter = ref(false);
  const showConfirmDialog = ref(false);
  const showAddToMilestoneDialog = ref(false);
  const dialog = ref({
    confirmDialogTitle: '',
    confirmDialogContent: '',
    confirmDialogContentPartTwo: '',
    confirmDialogBtnLabel: '',
    confirmDialogBtnColor: 'primary',
    confirmDialogName: '',
    confirmType: '',
  });


  // Computed properties
  const writeEntity = computed(() => authorityTo('write_entity'));
  const deleteEntity = computed(() => authorityTo('delete_entity'));
  const currentAccount = computed(() => store.state.user.currentAccount);
  const dynamicHeaders = computed(() => store.getters['headers/dynamicHeaders']);
  const queryParams = computed(() => route.query);
  const currentView = computed(() => queryParams.value.view || 'list');
  
  const activeMilestones = computed(() => {
    return milestones.value.filter(
      (milestone) => !milestone?.archivedAt && !milestone?.deletedAt
    );
  });

  const filteredPlans = computed(() => {
    // During initial load, return empty array to prevent "No matching results" from showing
    if (!hasInitiallyLoaded.value && testPlansCache.value.length === 0) {
      return [];
    }
    // Return the current page slice of cached test plans
    const startIndex = (currentPage.value - 1) * perPage.value;
    const endIndex = startIndex + perPage.value;
    return testPlansCache.value.slice(startIndex, endIndex);
  });

  const filteredHeaders = computed(() => {
    const filtered = headers.value.filter((header) => header.checked);
    if (filtered.length < 9) {
      isColumnFilter.value = true;
    } else {
      isColumnFilter.value = false;
    }
    return filtered;
  });

  const testPlans = computed(() => {
    // Backend now handles filtering by archived status through the API
    // and returns properly formatted data
    return testPlansCache.value?.map((item) => {
      const createdFormat = new Date(item.createdAt);
      const createdAt = `${createdFormat.getFullYear()}-${String(
        createdFormat.getMonth() + 1
      ).padStart(2, '0')}-${new String(createdFormat.getDate()).padStart(2, '0')}`;
      return {
        ...item,
        progress: staticValues(item, runStateMap, 'New'),
        createdAt,
      };
    });
  });

  const seletedItemsCount = computed(() => selecteditems.value.length);
  const hasSelectedItems = computed(() => selecteditems.value.length > 0);
  const totalPages = computed(() => Math.ceil(totalRowsCache.value / perPage.value));

  // Create debounced search function
  let debouncedSearch;

  // Methods
  const init = async () => {
    try {
      showSkeletonLoader();
      let handle = route.params.handle;
      const projectKey = route.params.key;
      await Promise.all([
        getProjectTestPlans(handle),
        getMilestones(),
        getTags(),
        getEntityCount(handle, projectKey),
      ]);
    } catch (error) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'plans' },
        error?.response?.data
      );
    } finally {
      hideSkeletonLoader();
    }
  };

  const initializeServices = () => {
    planService = makePlanService($api);
    milestoneService = makeMilestonesService($api);
    tagService = makeTagService($api);
  };

  const initializeDebouncedSearch = () => {
    debouncedSearch = debounce(() => {
      searchTestPlans();
      loading.value = false;
    }, 500);
  };

  const updateFilter = (newFilter) => {
    filter.value = newFilter;
    selecteditems.value = [];
    currentPage.value = 1;

    clearCache();

    refreshData();
  };

  const getMilestones = async () => {
    const handle = route.params.handle;
    const projectKey = route.params.key;

    try {
      const response = await milestoneService.getMilestones(handle, projectKey);
      milestones.value = response.data?.items;
      return response.data?.items;
    } catch (err) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'milestones' },
        err?.response?.data
      );
      return [];
    }
  };

  const getTags = async () => {
    const handle = route.params.handle;
    try {
      const response = await tagService.getTags(handle, 'plans');
      tags.value = response.data;
    } catch (err) {
      showErrorToast(Swal, t('fetchError'), { item: t('tags') }, err?.response?.data);
      return [];
    }
  };

  const handleDuplicatePlan = (item) => {
    router.push({
      name: 'TestPlanDuplicate',
      params: {
        handle: route.params.handle,
        key: route.params.key,
        planId: item.uid,
      },
    });
  };

  const handleDuplicatePlans = (items) => {
    selecteditems.value = items;
    confirmDuplicateDialog.value = true;
  };

  const closeConfirmDuplicateDialog = () => {
    confirmDuplicateDialog.value = false;
    selecteditems.value = [];
  };

  const handleConfirmBulkDuplicate = async () => {
    if (!writeEntity.value) {
      return;
    }
    bulkDuplicateLoading.value = true;
    const payload = {
      plans: selecteditems.value.map(item => {
        return {
          uid: item.uid,
        };
      }),
    };

    const handle = route.params.handle;
    const projectKey = route.params.key;

    try {
      await planService.duplicateTestPlan(handle, projectKey, payload);
      await refreshData();
      showSuccessToast(Swal, t('success.duplicatedTestPlans'), {item: selecteditems.value?.length});
    } catch (err) {
      showErrorToast(Swal, t('error.duplicatedTestPlans'), {item: selecteditems.value?.length}, err?.response?.data);
    } finally {
      bulkDuplicateLoading.value = false;
      confirmDuplicateDialog.value = false;
      selecteditems.value = [];
    }
  };

  const handleConfirmArchiveClick = async (status) => {
    if (!writeEntity.value) {
      return;
    }

    const successMessage = status? 'archiveSuccess' : 'unarchiveSuccess';

    const errorMessage = status? 'archiveError' : 'unarchiveError';

    try {
      showSkeletonLoader();
      const payload = {
        action: status ? 'archive' : 'unarchive',
        uids: selecteditems.value.map((item) => item.uid),
        cascade: true,
      };

      await planService.updateTestPlans(
        currentAccount.value.handle,
        route.params.key,
        payload
      );

      currentPage.value = 1;
      clearCache();

      const handle = currentAccount.value.handle;
      const projectKey = route.params.key;

      await Promise.all([
        getProjectTestPlans(handle),
        getEntityCount(handle, projectKey)
      ]);

      showSuccessToast(Swal, successMessage, { item: t('plans.titleWithCount', { count: selecteditems.value.length }) });
    } catch (error) {
      showErrorToast(Swal, errorMessage, { item: t('plans.titleWithCount', { count: selecteditems.value.length }) }, error?.response?.data);
    } finally {
      handleCloseClick();
      hideSkeletonLoader();
    }
  };

  const handleConfirmDeleteMultiClick = async () => {
    if (!deleteEntity.value) {
      return;
    }
    let planIds = [];
    selecteditems.value.map((item) => {
      planIds = [...planIds, item.uid];
    });

    let seletedItemsCount = selecteditems.value.length;

    try {
      const payload = {
        action: 'delete',
        uids: planIds,
        cascade: true,
      };
      showSkeletonLoader();
      await planService.updateTestPlans(
        currentAccount.value.handle,
        route.params.key,
        payload
      );
      currentPage.value = 1;
      // Clear cache to ensure fresh data after deletion
      clearCache();
      await refreshData();
      showSuccessToast(
        Swal,
        t('plans.deleteTestPlans'),
        { count: seletedItemsCount }
      );
    } catch (error) {
      showErrorToast(
        Swal,
        t('plans.deleteTestPlans'),
        { count: seletedItemsCount },
        error?.response?.data
      );
    } finally {
      handleCloseClick();
      hideSkeletonLoader();
    }
  };

  const handleConfirmDeleteClick = async () => {
    if (!deleteEntity.value) {
      return;
    }
    try {
      const payload = {
        action: 'delete',
        uids: [selectedPlan.value.uid],
        cascade: true,
      };
      showSkeletonLoader();
      await planService.updateTestPlans(
        currentAccount.value.handle,
        route.params.key,
        payload
      );
      currentPage.value = 1;
      // Clear cache to ensure fresh data after deletion
      clearCache();
      await refreshData();
      showSuccessToast(Swal, t('plans.delete1TestPlan'));
    } catch (error) {
      showErrorToast(
        Swal,
        t('plans.delete1TestPlan'),
        {},
        error?.response?.data
      );
    } finally {
      handleCloseClick();
      hideSkeletonLoader();
    }
  };

  const applyFilters = (filters) => {
    if (filters) {
      if (filters.ui && filters.api) {
        appliedFilters.value = filters.ui;
        apiFilters.value = filters.api;
      } else {
        appliedFilters.value = filters;
        apiFilters.value = null;
      }
      isFilter.value = true;
    } else {
      appliedFilters.value = null;
      apiFilters.value = null;
      isFilter.value = false;
    }
    
    // Reset to first page when applying filters
    currentPage.value = 1;
    selecteditems.value = [];
    refreshData();
  };

  const handleAddToMilestione = (item) => {
    selectedPlan.value = item;
    selecteditems.value = [item];
    showAddToMilestoneDialog.value = true;
  };

  const confirmDeletePlan = (item) => {
    selectedPlan.value = item;
    handleConfirmDialog('delete');
  };

  const getProjectTestPlans = async (handle, showSkeleton = true, pageNumber = currentPage.value, pageSize = perPage.value, forceRefresh = false) => {
    try {
      const key = route.params.key;
      const currentFilter = filter.value;
      const currentSearch = searchFilter.value?.trim() || '';
      const currentApiFilters = apiFilters.value;
      const currentSortBy = sortBy.value;
      const currentSortDesc = sortDesc.value;

      // Check if we need to fetch new data
      if (!forceRefresh && !needsDataFetch(pageNumber, pageSize, currentFilter, currentSearch, handle, key, currentApiFilters, currentSortBy, currentSortDesc)) {
        // We have enough cached data, no need to fetch
        return;
      }

      // If context changed, clear cache and start fresh
      const contextChanged = currentFilter !== lastFetchedFilter.value || 
                           currentSearch !== lastFetchedSearch.value || 
                           handle !== lastFetchedHandle.value ||
                           key !== lastFetchedProjectKey.value ||
                           JSON.stringify(currentApiFilters) !== JSON.stringify(lastFetchedApiFilters.value) ||
                           JSON.stringify(currentSortBy) !== JSON.stringify(lastFetchedSortBy.value) ||
                           JSON.stringify(currentSortDesc) !== JSON.stringify(lastFetchedSortDesc.value);

      if (contextChanged || forceRefresh) {
        clearCache();
      }

      // Calculate what data we need to fetch
      const startIndex = contextChanged ? 0 : testPlansCache.value.length;
      const offset = startIndex;
      const limit = contextChanged ? pageNumber * pageSize : pageSize;

      const queryParams = {
        limit: limit,
        offset: offset
      };
      
      // Add backend sorting parameters
      if (currentSortBy && currentSortBy.length > 0) {
        const frontendSortColumn = currentSortBy[0]; // Take first sort column
        const sortDirection = currentSortDesc[0] ? 'desc' : 'asc';
        
        // Map frontend column names to API field names (case sensitive)
        const sortColumnMapping = {
          'name': 'name',
          'creationdate': 'createdAt'
        };
        
        const apiSortColumn = sortColumnMapping[frontendSortColumn] || frontendSortColumn;
        
        queryParams.orderBy = apiSortColumn;
        queryParams.order = sortDirection;
      }
      
      // Handle archived vs ongoing filter
      if (currentFilter === 'ongoing') {
        queryParams.archived = false;
      } else if (currentFilter === 'archived') {
        queryParams.archived = true; 
      }

      // Add search functionality using backend API
      if (currentSearch) {
        queryParams.search = currentSearch;
      }

      // Apply advanced filters from the filter component
      if (currentApiFilters) {
        Object.entries(currentApiFilters).forEach(([key, value]) => {
          if (Array.isArray(value) && value.length === 0) return;
          if (value === '') return;
          queryParams[key] = value;
        });
      }
      
      if (showSkeleton) {
        showSkeletonLoader();
      }
      const response = await planService.getPlans(handle, key, queryParams);
      
      const newPlans = response.data?.items || [];
      
      if (contextChanged || forceRefresh) {
        // Replace cache with new data
        testPlansCache.value = newPlans;
      } else {
        // Append new data to cache
        testPlansCache.value.push(...newPlans);
      }

      totalRowsCache.value = response.data?.count || 0;

      // Update tracking variables
      lastFetchedPage.value = Math.ceil(testPlansCache.value.length / pageSize);
      lastFetchedFilter.value = currentFilter;
      lastFetchedSearch.value = currentSearch;
      lastFetchedHandle.value = handle;
      lastFetchedProjectKey.value = key;
      lastFetchedApiFilters.value = currentApiFilters;
      lastFetchedSortBy.value = currentSortBy;
      lastFetchedSortDesc.value = currentSortDesc;
      hasInitiallyLoaded.value = true;

      // Fetch relations for the returned plans
      if (testPlansCache.value.length > 0) {
        // Reset relation loading states before fetching
        resetRelationLoadingStates();
        
        fetchPlanRelations(
          planService, 
          handle, 
          key, 
          testPlansCache.value
        ).catch(error => {
          console.warn('Failed to load plan relations:', error);
        });
      }
    } catch (error) {
      showErrorToast(
        Swal,
        'fetchError',
        { item: 'plans' },
        error?.response?.data
      );
      testPlansCache.value = [];
      totalRowsCache.value = 0;
    } finally {
      hideSkeletonLoader();
    }
  };

  const refreshData = async () => {
    let handle = currentAccount.value.handle;
    const projectKey = route.params.key;
    await Promise.all([
      getProjectTestPlans(handle),
      getEntityCount(handle, projectKey)
    ]);
    if (showAddToMilestoneDialog.value) {
      showAddToMilestoneDialog.value = false;
    }
  };

  const searchTestPlans = async () => {
    currentPage.value = 1;
    await getProjectTestPlans(currentAccount.value.handle, false);
  };

  const editItem = (item) => {
    selectedPlan.value = item;
    router.push({
      name: 'TestPlanDetail',
      params: {
        handle: route.params.handle,
        key: route.params.key,
        planId: item.uid,
      },
    });
  };

  const handleRowClick = (item) => {
    router.push({
      name: 'TestPlanRerun',
      params: {
        handle: route.params.handle,
        key: route.params.key,
        planId: item.uid,
      },
    });
  };

  const setselected = (item) => {
    clearSelection.value = false;
    selecteditems.value = item;
  };

  const confirmArchiveTestPlan = (item) => {
    selectedPlan.value = item;
    handleConfirmDialog('archive');
  };

  const confirmUnArchiveTestPlan = (item) => {
    selectedPlan.value = item;
    handleConfirmDialog('unarchive');
  };

  const staticValues = (item, obj, defaultValue) => {
    return obj[item.customFields?.state] || obj[defaultValue];
  };

  const onUpdatePagination = async (options) => {
    const newPage = options.page || currentPage.value;
    const newItemsPerPage = options.itemsPerPage || perPage.value;
    const newSortBy = options.sortBy || [];
    const newSortDesc = options.sortDesc || [];
    
    const oldItemsPerPage = perPage.value;
    
    // Check if anything actually changed
    const pageChanged = newPage !== currentPage.value;
    const itemsPerPageChanged = newItemsPerPage !== perPage.value;
    const sortByChanged = JSON.stringify(newSortBy) !== JSON.stringify(sortBy.value);
    const sortDescChanged = JSON.stringify(newSortDesc) !== JSON.stringify(sortDesc.value);
    
    // Update state
    currentPage.value = newPage;
    perPage.value = newItemsPerPage;
    sortBy.value = newSortBy;
    sortDesc.value = newSortDesc;

    // If items per page decreased, we might have enough cached data
    if (newItemsPerPage < oldItemsPerPage && !sortByChanged && !sortDescChanged) {
      // Check if we have enough cached data for the new page size
      const requiredEndIndex = newPage * newItemsPerPage;
      if (requiredEndIndex <= testPlansCache.value.length) {
        // We have enough cached data, no need to fetch
        return;
      }
    }
    
    // Only trigger API call if something actually changed
    if (pageChanged || itemsPerPageChanged || sortByChanged || sortDescChanged) {
      await getProjectTestPlans(route.params.handle, true, newPage, newItemsPerPage);
    }
  };

  const handleConfirmBtnClick = (type) => {
    switch (type) {
      case 'multi_archive':
        handleConfirmArchiveClick(true);
        break;
      case 'multi_unarchive':
        handleConfirmArchiveClick(false);
        break;
      case 'multi_delete':
        handleConfirmDeleteMultiClick();
        break;
      case 'archive':
        handleConfirmOnArchiveClick(true);
        break;
      case 'unarchive':
        handleConfirmOnArchiveClick(false);
        break;
      case 'delete':
        handleConfirmDeleteClick();
        break;
    }
  };

  const handleCloseClick = () => {
    showConfirmDialog.value = false;
    selectedPlan.value = {};
    selecteditems.value = [];
    clearSelection.value = true;
  };

  const handleConfirmDialog = (type) => {
    showConfirmDialog.value = true;
    switch (type) {
      case 'multi_archive':
        dialog.value.confirmDialogTitle = t('plans.archiveDialog.titleMulti');
        dialog.value.confirmDialogContent = t('plans.archiveDialog.content');
        dialog.value.confirmDialogContentPartTwo = '';
        dialog.value.confirmDialogBtnLabel = t('plans.archiveDialog.btnLabel');
        dialog.value.confirmDialogBtnColor = 'primary';
        dialog.value.confirmDialogName = '';
        dialog.value.confirmType = 'multi_archive';
        break;
      case 'multi_unarchive':
        dialog.value.confirmDialogTitle = t('plans.unArchiveDialog.titleMulti');
        dialog.value.confirmDialogContent = t('plans.unArchiveDialog.content');
        dialog.value.confirmDialogContentPartTwo = '';
        dialog.value.confirmDialogBtnLabel = t('plans.unArchiveDialog.btnLabel');
        dialog.value.confirmDialogBtnColor = 'primary';
        dialog.value.confirmDialogName = '';
        dialog.value.confirmType = 'multi_unarchive';
        break;
      case 'multi_delete':
        dialog.value.confirmDialogTitle = t('plans.deleteDialog.title');
        dialog.value.confirmDialogContent = t('plans.deleteDialog.content');
        dialog.value.confirmDialogContentPartTwo =
          filter.value === 'ongoing' ? t('plans.deleteDialog.contentPart2') : '';
        dialog.value.confirmDialogBtnLabel = t('plans.deleteDialog.btnLabel');
        dialog.value.confirmDialogBtnColor = 'danger';
        dialog.value.confirmDialogName = '';
        dialog.value.confirmType = 'multi_delete';
        break;
      case 'archive':
        dialog.value.confirmDialogTitle = t('plans.archiveDialog.title');
        dialog.value.confirmDialogContent = t('plans.archiveDialog.content');
        dialog.value.confirmDialogContentPartTwo = '';
        dialog.value.confirmDialogBtnLabel = t('plans.archiveDialog.btnLabel');
        dialog.value.confirmDialogBtnColor = 'primary';
        dialog.value.confirmDialogName = selectedPlan.value.name;
        dialog.value.confirmType = 'archive';
        break;
      case 'unarchive':
        dialog.value.confirmDialogTitle = t('plans.unArchiveDialog.title');
        dialog.value.confirmDialogContent = t('plans.unArchiveDialog.content');
        dialog.value.confirmDialogContentPartTwo = '';
        dialog.value.confirmDialogBtnLabel = t('plans.unArchiveDialog.btnLabel');
        dialog.value.confirmDialogBtnColor = 'primary';
        dialog.value.confirmDialogName = selectedPlan.value.name;
        dialog.value.confirmType = 'unarchive';
        break;
      case 'delete':
        dialog.value.confirmDialogTitle = t('plans.deleteDialog.title');
        dialog.value.confirmDialogContent = t('plans.deleteDialog.content');
        dialog.value.confirmDialogContentPartTwo =
          filter.value === 'ongoing' ? t('plans.deleteDialog.contentPart2') : '';
        dialog.value.confirmDialogBtnLabel = t('plans.deleteDialog.btnLabel');
        dialog.value.confirmDialogBtnColor = 'danger';
        dialog.value.confirmDialogName = '';
        dialog.value.confirmType = 'delete';
        break;
    }
  };

  const handleConfirmOnArchiveClick = async (status) => {
    if (!writeEntity.value) {
      return;
    }

    const successMessage = status ? 'archiveSuccess' : 'unarchiveSuccess';

    const errorMessage = status ? 'archiveError' : 'unarchiveError';

    try {
      const payload = {
        action: status ? 'archive' : 'unarchive',
        uids: [selectedPlan.value.uid],
        cascade: true,
      };
      showSkeletonLoader();
      await planService.updateTestPlans(
        currentAccount.value.handle,
        route.params.key,
        payload
      );
      showSuccessToast(Swal, successMessage, { item: 'Plan' });
      currentPage.value = 1;
      // Clear cache to ensure fresh data after archive/unarchive
      clearCache();
      // clearEntityTypeCache('plan');
      await refreshData();
      const handle = currentAccount.value.handle;
      const projectKey = route.params.key;
      
      await getEntityCount(handle, projectKey);
    } catch (error) {
      showErrorToast(Swal, errorMessage, { item: t('plans.title_single') }, error?.response?.data);
    } finally {
      handleCloseClick();
      hideSkeletonLoader();
    }
  };

  const openAddToMilestoneDialog = () => {
    showAddToMilestoneDialog.value = true;
  };

  const handleFilters = (filters) => {
    appliedFilters.value = filters;
    if (filters && filters.ui && filters.api) {
      appliedFilters.value = filters.ui;
      apiFilters.value = filters.api;
    } else {
      apiFilters.value = extractApiFilters(filters);
    }

    isFilter.value = !!filters;
    refreshData();
  };

  const refreshTestPlan = async () => {
    clearCache();
    clearEntityTypeCache('plan');
    await refreshData();
    hideSkeletonLoader();
    selecteditems.value = [];
  };

  const extractApiFilters = (uiFilters) => {
    if (!uiFilters) return null;
    const apiFilters = {};
    Object.entries(uiFilters).forEach(([key, filter]) => {
      if (filter.type === 'array' && filter.value && filter.value.length) {
        if (typeof filter.value[0] === 'object') {
          const ids = filter.value
            .map((item) => item.uid || item.id)
            .filter((id) => id !== null && id !== undefined);

          if (ids.length > 0) {
            if (key === 'panel_priority') apiFilters.priorityUids = ids;
            else if (key === 'panel_status') apiFilters.statusUids = ids;
            else if (key === 'panel_milestone') apiFilters.milestoneUids = ids;
            else if (key === 'panel_tag') apiFilters.tagUids = ids;
          }
        }
      }
    });

    if (uiFilters.dateRange?.value) {
      if (uiFilters.dateRange.value.start) {
        apiFilters.minCreatedAt = uiFilters.dateRange.value.start;
      }
      if (uiFilters.dateRange.value.end) {
        apiFilters.maxCreatedAt = uiFilters.dateRange.value.end;
      }
    }

    return apiFilters;
  };

  const clearFilters = async () => {
    appliedFilters.value = null;
    apiFilters.value = null;
    isFilter.value = false;
    currentPage.value = 1;
    selecteditems.value = [];
    await refreshData();
  };

  const toggleMilestoneDrawer = (newState) => {
    showAddToMilestoneDialog.value = newState;
    init();
  };

  // Watch for search filter changes
  watch(searchFilter, () => {
    loading.value = true;
    if (debouncedSearch) {
      debouncedSearch();
    }
  });

  // Watch for selected project changes
  watch(() => store.getters.selectedProject, () => {
    let handle = currentAccount.value.handle;
    getProjectTestPlans(handle);
  });

  return {
    // Reactive state
    selectedPlan,
    appliedFilters,
    apiFilters,
    loading,
    isOpenFilter,
    headers,
    isColumnFilter,
    menuOpen,
    confirmDuplicateDialog,
    isCustomizeDisplayed,
    searchFilter,
    bulkDuplicateLoading,
    itemKey,
    filter,
    rowClass,
    clearSelection,
    isFilter,
    showConfirmDialog,
    showAddToMilestoneDialog,
    dialog,
    selecteditems,
    milestones,
    tags,
    items: testPlansCache, // Return cached test plans
    totalRows: totalRowsCache, // Return cached total rows
    currentPage,
    perPage,
    sortBy,
    sortDesc,
    skeletonLoaderState,

    // Computed properties
    writeEntity,
    deleteEntity,
    currentAccount,
    dynamicHeaders,
    queryParams,
    currentView,
    activeMilestones,
    filteredPlans,
    filteredHeaders,
    testPlans,
    seletedItemsCount,
    hasSelectedItems,
    totalPages,

    // From other composables
    activeCount,
    archivedCount,
    relationsLoading,
    relationLoadingStates,

    // Methods
    init,
    initializeServices,
    initializeDebouncedSearch,
    updateFilter,
    getMilestones,
    getTags,
    handleDuplicatePlan,
    handleDuplicatePlans,
    closeConfirmDuplicateDialog,
    handleConfirmBulkDuplicate,
    handleConfirmArchiveClick,
    handleConfirmDeleteMultiClick,
    handleConfirmDeleteClick,
    applyFilters,
    handleAddToMilestione,
    confirmDeletePlan,
    getProjectTestPlans,
    refreshData,
    searchTestPlans,
    editItem,
    handleRowClick,
    setselected,
    confirmArchiveTestPlan,
    confirmUnArchiveTestPlan,
    staticValues,
    onUpdatePagination,
    handleConfirmBtnClick,
    handleCloseClick,
    handleConfirmDialog,
    handleConfirmOnArchiveClick,
    openAddToMilestoneDialog,
    handleFilters,
    refreshTestPlan,
    extractApiFilters,
    clearFilters,
    toggleMilestoneDrawer,
    showSkeletonLoader,
    hideSkeletonLoader,
    clearCache, // Expose cache clearing method
    hasInitiallyLoaded,
    clearEntityTypeCache,
  };
}; 