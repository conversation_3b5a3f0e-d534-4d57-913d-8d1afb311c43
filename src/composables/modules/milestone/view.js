import { ref, computed, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useLoading } from '@/composables/utils/loading';
import { usePermissions } from '@/composables/utils/permissions';
import { showErrorToast } from '@/composables/utils/toast';
import makeMilestonesService from '@/services/api/milestone';

export function useMilestoneView() {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;
  
  // Composables
  const route = useRoute();
  const router = useRouter();
  const { showSkeletonLoader, hideSkeletonLoader, skeletonLoaderState } = useLoading();
  const { authorityTo } = usePermissions();

  // Services
  let makeMilestoneService;

  // Reactive state
  const selectedMilestone = ref({});
  const loading = ref(false);

  // Computed properties
  const writeEntity = computed(() => authorityTo('write_entity'));

  // Methods
  const initializeServices = () => {
    makeMilestoneService = makeMilestonesService(proxy.$api);
  };

  const findMilestone = async () => {
    try {
      loading.value = true;
      const handle = route.params.handle;
      const response = await makeMilestoneService.findMilestone(
        handle, 
        route.params.key, 
        route.params.id
      );
      selectedMilestone.value = response.data;
      return response.data;
    } catch (err) {
      showErrorToast(
        proxy.$swal, 
        'fetchError', 
        { item: 'milestone' }, 
        err?.response?.data
      );
      console.error('Failed to fetch milestone:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const handleBack = () => {
    router.replace({ name: 'Milestones' });
  };

  const init = async () => {
    try {
      showSkeletonLoader();
      await findMilestone();
    } catch (error) {
      console.error('Failed to initialize milestone view:', error);
    } finally {
      hideSkeletonLoader();
    }
  };

  // Initialize services when composable is used
  initializeServices();

  return {
    // Reactive state
    selectedMilestone,
    loading,
    skeletonLoaderState,

    // Computed properties
    writeEntity,

    // Methods
    findMilestone,
    handleBack,
    init,
    showSkeletonLoader,
    hideSkeletonLoader
  };
}
