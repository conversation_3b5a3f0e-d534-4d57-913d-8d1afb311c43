import { ref, computed, reactive } from 'vue';
import { $api } from '@/main';
import { useRouter, useRoute } from 'vue-router/composables';
import makeMilestonesService from '@/services/api/milestone';
import makeTagsService from '@/services/api/tag';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { requiredAndMax255FieldValidationRules } from '@/utils/validation';
import { useColorPreferences } from '@/composables/utils/colorPreferences';
import { usePermissions } from '@/composables/utils/permissions';
import { useMilestoneIndex } from '@/composables/modules/milestone/index';
import Swal from 'sweetalert2';
import { t } from '@/i18n'


const form = reactive({
  name: '',
  description: '',
  status: '',
  users: [],
  startDate: null,
  dueDate: null,
  tags: [],
});

const testActivities = reactive({
  testPlans: [],
  testRuns: []
});

const startDate = ref('');
const dueDate = ref('');
const showTestActivites = ref(false);
const milestoneForm = ref(null);

export const useMilestoneCreate = () =>
{
  const router = useRouter();
  const route = useRoute();
  const { getStatuses, getDefaultStatus } = useColorPreferences();
  const { authorityTo } = usePermissions();
  const { clearCache } = useMilestoneIndex();
  const milestoneService = makeMilestonesService($api);
  const tagService = makeTagsService($api);


  const isCreateDisabled = ref(false);
  const showRevertChangesDialog = ref(false);

  const showConfirmBackDialog = ref(false);
  const tags = ref([]);
  const statuses = ref(getStatuses('milestone').filter(element => !element.archived));



  const milestoneNameRule = computed(() => requiredAndMax255FieldValidationRules({ $t: (key) => t(key) }));
  const requiredRule = computed(() => [v => !!v || t('thisFieldIsRequired')]);
  const dueDateRule = computed(() => {
    // Only validate if both dates are provided
    if (!startDate.value || !dueDate.value) return [true];
    return [v => new Date(v) > new Date(startDate.value) || t('milestone.create_milestone.dueDateError')];
  });
  const writeEntity = computed(() => authorityTo('write_entity'))

  async function handleCreateMilestone()
  {
    if (!milestoneForm.value.validate()) return;
    isCreateDisabled.value = true;
    
    const payload = {
      name: form.name,
      description: form.description,
      runIds: testActivities.testRuns.map(item => item.uid),
      planIds: testActivities.testPlans.map(item => item.uid),
      tagUids: form.tags,
      status: form.status ?? getDefaultStatus(statuses.value),
      progress: 0,
    };

    // Only add dates if they are provided
    if (startDate.value) {
      payload.startDate = new Date(startDate.value);
    }
    if (dueDate.value) {
      payload.dueAt = new Date(dueDate.value);
    }

    try {
      await milestoneService.createMilestone(route.params.handle, route.params.key, payload);
      showSuccessToast(Swal, 'createSuccess', { item: 'Milestone' });
      resetForm();
      clearCache();
      router.push({
        name: 'Milestones',
        params: {
          handle: route.params.handle,
          key: route.params.key
        },
      });
    } catch (error) {
      isCreateDisabled.value = false;
      showErrorToast(Swal, 'createError', { item: 'Milestone' }, error?.response?.data);
    }
  }

  function addTestActivities()
  {
    showTestActivites.value = true;
  }

  function handleBackClick()
  {
    if (form.name || form.status || form.description || startDate.value || dueDate.value) {
      showConfirmBackDialog.value = true;
      return;
    }

    clearCache();
    router.replace({
      name: 'Milestones',
      params: {
        handle: route.params.handle,
        key: route.params.key
      },
    });
  }

  function handleCloseClick()
  {
    showConfirmBackDialog.value = false;
  }

  function handleConfirmClick()
  {
    showConfirmBackDialog.value = false;
    resetForm();
    clearCache();
    router.replace({
      name: 'Milestones',
      params: {
        handle: route.params.handle,
        key: route.params.key
      },
    });
  }

  function handleCloseRevert()
  {
    showRevertChangesDialog.value = false;
  }

  function handleConfirmRevert()
  {
    testActivities.testPlans = [];
    testActivities.testRuns = [];
    showTestActivites.value = false;
    showRevertChangesDialog.value = false;
  }

  function handleRevert()
  {
    showRevertChangesDialog.value = true;
  }

  function saveAdded()
  {
    showTestActivites.value = false;
  }

  async function getAllTags()
  {
    try {
      const response = await tagService.getTags(route.params.handle, 'milestones');
      if (response.status === 200) {
        tags.value = response.data;
      }
    } catch (err) {
      showErrorToast(Swal, 'fetchError', { item: 'Tag' }, err?.response?.data);
    }
  }

  function onRemoveSelectedTags(uid)
  {
    const index = form.tags.findIndex((item) => item === uid);
    if (index !== -1) {
      form.tags.splice(index, 1);
    }
  }

  function tagsSelection(uid)
  {
    return form.tags.some((item) => item === uid);
  }

  function resetForm()
  {
    form.name = '';
    form.description = '';
    form.status = '';
    form.users = [];
    form.tags = [];
    startDate.value = '';
    dueDate.value = '';
    testActivities.testPlans = [];
    testActivities.testRuns = [];
    showTestActivites.value = false;
    if (milestoneForm.value) {
      milestoneForm.value.resetValidation();
    }
  }

  return {
    milestoneForm,
    isCreateDisabled,
    showRevertChangesDialog,
    form,
    testActivities,
    startDate,
    dueDate,
    showTestActivites,
    showConfirmBackDialog,
    milestoneNameRule,
    requiredRule,
    dueDateRule,
    handleCreateMilestone,
    addTestActivities,
    handleBackClick,
    handleCloseClick,
    handleConfirmClick,
    handleCloseRevert,
    handleConfirmRevert,
    handleRevert,
    saveAdded,
    statuses,
    tags,
    getAllTags,
    onRemoveSelectedTags,
    tagsSelection,
    resetForm,
    writeEntity
  };
};
