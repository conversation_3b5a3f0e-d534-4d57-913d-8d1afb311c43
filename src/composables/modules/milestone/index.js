import { ref, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore, $api } from '@/main';
import { useLoading } from '@/composables/utils/loading';
import { useNetworkError } from '@/composables/utils/networkError';
import { usePermissions } from '@/composables/utils/permissions';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import { useMilestoneCount } from '@/composables/modules/milestone/count';
import { useRelations } from '@/composables/utils/relations';
import MilestoneService from '@/services/api/milestone';
import makeTagService from '@/services/api/tag';
import { debounce } from 'lodash';
import { DefaultFilter } from '@/constants/grid';
import Swal from 'sweetalert2';

// Module-level state for caching milestones across component instances
const milestonesCache = ref([]);
const totalRowsCache = ref(0);
const lastFetchedPage = ref(0);
const lastFetchedFilter = ref('');
const lastFetchedSearch = ref('');
const lastFetchedHandle = ref('');
const lastFetchedProjectKey = ref('');
const lastFetchedApiFilters = ref(null);
const lastFetchedSortBy = ref([]);
const lastFetchedSortDesc = ref([]);
const hasInitiallyLoaded = ref(false);
const currentPage = ref(1);
const perPage = ref(10);
const sortBy = ref([]);
const sortDesc = ref([]);


export const useMilestoneIndex = () => {
  const route = useRoute();
  const router = useRouter();
  const store = useStore();
  
  // Composables
  const { showSkeletonLoader, hideSkeletonLoader, skeletonLoaderState } = useLoading();
  const { redirectOnError } = useNetworkError();
  const { authorityTo } = usePermissions();
  const { formatDate } = useDateFormatter();
  const { activeCount, archivedCount, getEntityCount } = useMilestoneCount();
  const { relationsLoading, relationLoadingStates, fetchMilestoneRelations, resetRelationLoadingStates, processedMilestoneCache, clearEntityTypeCache } = useRelations();

  // Services
  let makeMilestoneService;
  let tagService;

  // Helper function to check if we need to fetch new data
  const needsDataFetch = (pageNumber, pageSize, filterValue, searchValue, handleValue, projectKeyValue, apiFiltersValue, sortByValue, sortDescValue) => {
    // Always fetch if context changed
    if (filterValue !== lastFetchedFilter.value || 
        searchValue !== lastFetchedSearch.value || 
        handleValue !== lastFetchedHandle.value ||
        projectKeyValue !== lastFetchedProjectKey.value ||
        JSON.stringify(apiFiltersValue) !== JSON.stringify(lastFetchedApiFilters.value) ||
        JSON.stringify(sortByValue) !== JSON.stringify(lastFetchedSortBy.value) ||
        JSON.stringify(sortDescValue) !== JSON.stringify(lastFetchedSortDesc.value)) {
      return true;
    }

    // Calculate required data range
    const requiredEndIndex = pageNumber * pageSize;
    const currentCachedCount = milestonesCache.value.length;
    
    // Need to fetch if we don't have enough cached data
    return requiredEndIndex > currentCachedCount && currentCachedCount < totalRowsCache.value;
  };

  // Helper function to clear cache
  const clearCache = () => {
    milestonesCache.value = [];
    totalRowsCache.value = 0;
    lastFetchedPage.value = 0;
    lastFetchedFilter.value = '';
    lastFetchedSearch.value = '';
    lastFetchedHandle.value = '';
    lastFetchedProjectKey.value = '';
    lastFetchedApiFilters.value = null;
    lastFetchedSortBy.value = [];
    lastFetchedSortDesc.value = [];
    hasInitiallyLoaded.value = false;
    processedMilestoneCache.value = [];
    clearEntityTypeCache('milestone');
  };

  // Data properties
  const tableLoadingState = ref(false);
  const selectedMilestone = ref({});
  const setSelected = ref([]);
  const apiFilters = ref(null);
  const savingInProgress = ref(false);
  const headers = ref([]);
  const filter = ref('open');
  const isMilestoneArchived = ref(false);
  const expanded = ref([]);
  const itemKey = ref('uid');
  const searchFilter = ref('');
  const showFormDialog = ref(false);
  const openedRow = ref(undefined);
  const loading = ref(false);
  const checked = ref(false);
  const showConfirmOpenDialog = ref(false);
  const showConfirmDeleteDialog = ref(false);
  const mainFilter = ref(false);
  const closeFilter = ref(false);
  const statuses = ref([]);
  const tags = ref([]);

  const mainFilters = ref({
    selectedRoles: [],
    testRuns: [0, 9999],
    testCases: [0, 9999],
    users: [0, 100],
    dateRange: {
      start: '',
      end: '',
    },
    dueDateRange: {
      start: '',
      end: '',
    },
    progress: [0, 100],
    panelStatus: [],
  });

  const closedFilters = ref({
    testRuns: [0, 9999],
    testCases: [0, 9999],
    users: [0, 100],
    dateRange: {
      start: '',
      end: '',
    },
  });

  // Computed properties
  const writeEntity = computed(() => authorityTo('write_entity'));
  const deleteEntity = computed(() => authorityTo('delete_entity'));

  const filteredHeaders = computed(() => {
    return headers.value.filter((header) => header.checked);
  });

  const isColumnFilter = computed({
    get() {
      return filteredHeaders.value.length < 7;
    },
  });

  const filteredMilestones = computed(() => {
    // During initial load, return empty array to prevent "No matching results" from showing
    if (!hasInitiallyLoaded.value && milestonesCache.value.length === 0) {
      return [];
    }
    // Return the current page slice of cached milestones
    const startIndex = (currentPage.value - 1) * perPage.value;
    const endIndex = startIndex + perPage.value;
    return milestonesCache.value.slice(startIndex, endIndex);
  });

  const isSearchMode = computed(() => {
    return searchFilter.value && searchFilter.value.trim();
  });

  const totalPages = computed(() => {
    return Math.ceil(totalRowsCache.value / perPage.value);
  });

  const filteredFilters = computed(() => {
    const filters = mainFilters.value;
    return Object.keys(filters)
      .filter((index) => {
        const filter = filters[index];
        switch (index) {
          case 'selectedRoles':
            return !filter.length == 0;
          case 'dateRange':
          case 'dueDateRange':
            return filter.start && filter.end;
          case 'users':
            return !(filter[0] == 0 && filter[1] == 100);
          case 'testCases':
            return !(filter[0] == 0 && filter[1] == 9999);
          case 'testRuns':
            return !(filter[0] == 0 && filter[1] == 9999);
          case 'tags':
            return !(filter[0] == 0 && filter[1] == 100);
          default:
            return false;
        }
      })
      .map((index) => ({ index, filter: filters[index] }));
  });

  // Create debounced search method
  let debouncedGetMilestones;

  // Methods
  const init = async () => {
    try {
      showSkeletonLoader();
      const handle = route.params.handle;
      const projectKey = route.params.key;

      getEntityCount(handle, projectKey).catch(error => {
        console.warn('Failed to load milestone counts:', error);
      });
      
      // Load core data first - milestones
      await getMilestones();
      
      // Hide skeleton loader as soon as core data is loaded
      hideSkeletonLoader();
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'Milestone' }, error?.response?.data);
      hideSkeletonLoader();
    }
  };

  const isDefaultValue = (index) => {
    if (mainFilters.value[index] === DefaultFilter[index]) {
      return true;
    } else {
      return false;
    }
  };

  const confirmCloseMilestone = (item) => {
    selectedMilestone.value = item;
    showConfirmOpenDialog.value = true;
    isMilestoneArchived.value = true;
  };

  const confirmUnArchiveMilestone = (item) => {
    selectedMilestone.value = item;
    showConfirmOpenDialog.value = true;
    isMilestoneArchived.value = false;
  };

  const showDeleteDialog = (item) => {
    selectedMilestone.value = item;
    showConfirmDeleteDialog.value = true;
  };

  const handleCloseClick = () => {
    showConfirmOpenDialog.value = false;
    selectedMilestone.value = {};
  };

  const handleDeleteCloseClick = () => {
    showConfirmDeleteDialog.value = false;
    selectedMilestone.value = {};
  };

  const handleConfirmOpenClick = async () => {
    try {
      showSkeletonLoader();
      await makeMilestoneService.updateMilestone(
        route.params.handle,
        route.params.key,
        selectedMilestone.value.uid,
        {
          ...selectedMilestone.value,
          archived: true,
        }
      );
      showSuccessToast(Swal, 'archiveSuccess', { item: 'Milestone' });
      handleCloseClick();
      // Clear cache to ensure fresh data after archiving
      clearCache();
      await Promise.all([
        getMilestones(),
        getEntityCount(route.params.handle, route.params.key)
      ]);
    } catch (error) {
      showErrorToast(Swal, 'archiveError', { item: 'Milestone' }, error?.response?.data);
    } finally {
      hideSkeletonLoader();
    }
  };

  const handleConfirmDeleteClick = async () => {
    if (!deleteEntity.value) {
      showErrorToast(Swal, 'unauthorizedAction', { action: 'delete' });
      return;
    }
    try {
      showSkeletonLoader();
      await makeMilestoneService.deleteMilestone(
        route.params.handle,
        route.params.key,
        selectedMilestone.value.uid
      );
      showSuccessToast(Swal, 'deleteSuccess', { item: 'Milestone' });
      handleDeleteCloseClick();
      // Clear cache to ensure fresh data after deletion
      clearCache();
      await Promise.all([
        getMilestones(),
        getEntityCount(route.params.handle, route.params.key)
      ]);
    } catch (error) {
      showErrorToast(Swal, 'deleteError', { item: 'Milestone' }, error?.response?.data);
    } finally {
      hideSkeletonLoader();
    }
  };

  const applyFilters = (filters) => {
    if (filters) {
      if (filters.ui && filters.api) {
        mainFilters.value = filters.ui;
        apiFilters.value = filters.api;
      } else {
        mainFilters.value = filters;
        apiFilters.value = null;
      }
      mainFilter.value = true;
      closeFilter.value = false;
    } else {
      mainFilters.value = null;
      apiFilters.value = null;
      closeFilter.value = false;
    }

    currentPage.value = 1; // Reset to first page when changing filter
    getMilestones();
  };

  const applyClosedFilters = (filters) => {
    closedFilters.value = filters;
    mainFilter.value = false;
    closeFilter.value = true;
    filter.value = 'closed';

    if (filters) {
      if (filters.ui && filters.api) {
        closedFilters.value = filters.ui;
        apiFilters.value = filters.api;
      } else {
        closedFilters.value = filters;
        apiFilters.value = null;
      }
      mainFilter.value = false;
      closeFilter.value = true;
    } else {
      closedFilters.value = null;
      apiFilters.value = null;
      closeFilter.value = false;
    }

    currentPage.value = 1; // Reset to first page when changing filter
    getMilestones();
  };

  const updateFilter = async (newFilter) => {
    filter.value = newFilter;
    store.commit('setFilter', newFilter);
    // Reset to first page when changing filter (only in non-search mode)
    if (!isSearchMode.value) {
      currentPage.value = 1;
    }
    await getMilestones(currentPage.value, perPage.value, true); // Force refresh
  };

  const staticValues = (item, obj, defaultValue) => {
    return obj[item.customFields?.state] || obj[defaultValue];
  };

  const getTags = async () => {
    const handle = route.params.handle;
    try {
      const response = await tagService.getTags(handle, 'milestones');
      tags.value = response.data;
    } catch (err) {
      showErrorToast(Swal, 'fetchError', { item: 'tags' }, err?.response?.data);
      return [];
    }
  };

  const getMilestones = async (pageNumber = currentPage.value, pageSize = perPage.value, forceRefresh = false) => {
    loading.value = true;
    try {
      const currentHandle = route.params.handle;
      const currentProjectKey = route.params.key;
      const currentFilter = filter.value;
      const currentSearch = searchFilter.value?.trim() || '';
      const currentApiFilters = apiFilters.value;
      const currentSortBy = sortBy.value;
      const currentSortDesc = sortDesc.value;

      // Check if we need to fetch new data
      if (!forceRefresh && !needsDataFetch(pageNumber, pageSize, currentFilter, currentSearch, currentHandle, currentProjectKey, currentApiFilters, currentSortBy, currentSortDesc)) {
        // We have enough cached data, no need to fetch
        return;
      }

      // If context changed, clear cache and start fresh
      const contextChanged = currentFilter !== lastFetchedFilter.value || 
                           currentSearch !== lastFetchedSearch.value || 
                           currentHandle !== lastFetchedHandle.value ||
                           currentProjectKey !== lastFetchedProjectKey.value ||
                           JSON.stringify(currentApiFilters) !== JSON.stringify(lastFetchedApiFilters.value) ||
                           JSON.stringify(currentSortBy) !== JSON.stringify(lastFetchedSortBy.value) ||
                           JSON.stringify(currentSortDesc) !== JSON.stringify(lastFetchedSortDesc.value);

      if (contextChanged || forceRefresh) {
        clearCache();
      }

      // Use search endpoint when there's a search query
      if (currentSearch) {
        const response = await makeMilestoneService.searchMilestones(
          currentHandle,
          currentProjectKey,
          currentSearch
        );
        
        // Filter by archived status for search results
        let searchResults = response.data.milestones || [];
        if (currentFilter === 'closed') {
          searchResults = searchResults.filter(milestone => milestone.archivedAt);
        } else {
          searchResults = searchResults.filter(milestone => !milestone.archivedAt);
        }
        
        milestonesCache.value = searchResults;
        totalRowsCache.value = searchResults.length;
      } else {
        // Calculate what data we need to fetch
        const startIndex = contextChanged ? 0 : milestonesCache.value.length;
        const offset = startIndex;
        const limit = contextChanged ? pageNumber * pageSize : pageSize;

        // Use regular paginated endpoint when no search
        const queryParams = {
          limit: limit,
          offset: offset,
          archived: currentFilter === 'closed'
        };

        // Add backend sorting parameters
        if (currentSortBy && currentSortBy.length > 0) {
          const frontendSortColumn = currentSortBy[0]; // Take first sort column
          const sortDirection = currentSortDesc[0] ? 'desc' : 'asc';
          
          // Map frontend column names to API field names (case sensitive)
          const sortColumnMapping = {
            'name': 'name',
            'startdate': 'startDate', 
            'due_at': 'dueAt'
          };
          
          const apiSortColumn = sortColumnMapping[frontendSortColumn] || frontendSortColumn;
          
          queryParams.orderBy = apiSortColumn;
          queryParams.order = sortDirection;
        }

        if (currentApiFilters) {
          Object.entries(currentApiFilters).forEach(([key, value]) => {
            if ((Array.isArray(value) && value.length === 0) || value === '') return;
            queryParams[key] = value;
          });
        }

        const response = await makeMilestoneService.getMilestones(
          currentHandle,
          currentProjectKey,
          queryParams
        );

        const newMilestones = response.data.items || [];
        
        if (contextChanged || forceRefresh) {
          // Replace cache with new data
          milestonesCache.value = newMilestones;
        } else {
          // Append new data to cache, preventing duplicates
          const existingUids = new Set(milestonesCache.value.map(milestone => milestone.uid));
          const uniqueNewMilestones = newMilestones.filter(milestone => !existingUids.has(milestone.uid));
          milestonesCache.value.push(...uniqueNewMilestones);
        }

        totalRowsCache.value = response.data.count || 0;
      }
      
      // Update tracking variables
      lastFetchedPage.value = Math.ceil(milestonesCache.value.length / pageSize);
      lastFetchedFilter.value = currentFilter;
      lastFetchedSearch.value = currentSearch;
      lastFetchedHandle.value = currentHandle;
      lastFetchedProjectKey.value = currentProjectKey;
      lastFetchedApiFilters.value = currentApiFilters;
      lastFetchedSortBy.value = currentSortBy;
      lastFetchedSortDesc.value = currentSortDesc;
      hasInitiallyLoaded.value = true;
      
      // Load relations in background without blocking
      if (milestonesCache.value.length > 0) {
        // Reset relation loading states before fetching
        resetRelationLoadingStates();
        fetchMilestoneRelations(
          makeMilestoneService, 
          currentHandle, 
          currentProjectKey, 
          milestonesCache.value
        ).catch(error => {
          console.warn('Failed to load milestone relations:', error);
          showErrorToast(Swal, "fetchError", { item: "relations" }, error?.response?.data);
        });
      }
    } catch (error) {
      redirectOnError(error.response?.status);
      showErrorToast(Swal, 'fetchError', { item: 'milestones' }, error?.response?.data);
      milestonesCache.value = [];
      totalRowsCache.value = 0;
    } finally {
      loading.value = false;
    }
  };

  const getColor = (priority) => {
    switch (priority) {
      case 'Past due':
        return 'font-weight-bold red--text text--lighten-1';
      case 'Blocked':
        return 'font-weight-bold orange--text text--lighten-1';
      case 'Active':
        return 'font-weight-bold green--text text--lighten-1';
      case 'Upcoming':
        return 'font-weight-bold blue--text text--lighten-1';
    }
  };

  const convertToLocal = (timestamp) => {
    return formatDate(timestamp, 'ddd, MMM DD hh:mm A');
  };

  const determineType = (test) => {
    return test.source === 'pinata' ? 'Exploratory' : 'Manual';
  };

  const updateFilteredHeaders = () => {
    headers.value = headers.value.filter((header) => header.isSelected);
  };

  const editMilestone = (item) => {
    selectedMilestone.value = item;

    router.push({
      name: 'MilestoneEdit',
      params: {
        handle: route.params.handle,
        key: route.params.key,
        id: item.uid,
      },
    });
  };

  const unArchiveMilestone = async () => {
    try {
      showSkeletonLoader();
      await makeMilestoneService.updateMilestone(
        route.params.handle,
        route.params.key,
        selectedMilestone.value.uid,
        {
          ...selectedMilestone.value,
          archived: false,
        }
      );
      showSuccessToast(Swal, 'unarchiveSuccess', { item: 'Milestone' });
      handleCloseClick();
      // Clear cache to ensure fresh data after unarchiving
      clearCache();
      await Promise.all([
        getMilestones(),
        getEntityCount(route.params.handle, route.params.key)
      ]);
    } catch (error) {
      showErrorToast(Swal, 'unarchiveError', { item: 'Milestone' }, error?.response?.data);
    } finally {
      hideSkeletonLoader();
    }
  };

  const updateMainFilter = (index) => {
    mainFilters.value[index] = DefaultFilter[index];
  };

  const clearFilters = () => {
    if (filter.value === 'open') {
      mainFilter.value = false;
      mainFilters.value = DefaultFilter;
    } else {
      closeFilter.value = false;
      closedFilters.value = {
        testRuns: [0, 9999],
        testCases: [0, 9999],
        users: [0, 100],
        dateRange: {
          start: '',
          end: '',
        },
      };
    }
    apiFilters.value = null;
    // Reset to first page when changing filter (only in non-search mode)
    if (!isSearchMode.value) {
      currentPage.value = 1;
    }
    getMilestones(currentPage.value, perPage.value, true); // Force refresh
  };

  const updateFilters = (updatedFilters) => {
    const newFilters = JSON.parse(JSON.stringify(updatedFilters));
    if (filter.value === 'open') {
      mainFilters.value = newFilters;
      checkMainFilterStatus();
    } else {
      closedFilters.value = newFilters;
      checkCloseFilterStatus();
    }
    apiFilters.value = updatedFilters;
    // Reset to first page when changing filter (only in non-search mode)
    if (!isSearchMode.value) {
      currentPage.value = 1;
    }
    getMilestones(currentPage.value, perPage.value, true); // Force refresh
  };

  const checkMainFilterStatus = () => {
    const isDefault =
      (!mainFilters.value.panelStatus || mainFilters.value.panelStatus.length === 0) &&
      (!mainFilters.value.tags || mainFilters.value.tags.length === 0) && 
      isDefaultRange(mainFilters.value.testRuns, [0, 9999]) &&
      isDefaultRange(mainFilters.value.testCases, [0, 9999]) &&
      (!mainFilters.value.dateRange.start || !mainFilters.value.dateRange.end) &&
      (!mainFilters.value.dueDateRange.start || !mainFilters.value.dueDateRange.end) &&
      isDefaultRange(mainFilters.value.users, [0, 100]);

    mainFilter.value = !isDefault;
  };

  const checkCloseFilterStatus = () => {
    const isDefault =
      isDefaultRange(closedFilters.value.testRuns, [0, 9999]) &&
      isDefaultRange(closedFilters.value.testCases, [0, 9999]) &&
      (!closedFilters.value.dateRange.start || !closedFilters.value.dateRange.end) &&
      isDefaultRange(closedFilters.value.users, [0, 100]);

    closeFilter.value = !isDefault;
  };

  const isDefaultRange = (range, defaultRange) => {
    if (!range) return true;
    return range[0] === defaultRange[0] && range[1] === defaultRange[1];
  };

  const onUpdatePagination = async (options) => {
    const newPage = options.page || currentPage.value;
    const newItemsPerPage = options.itemsPerPage || perPage.value;
    const newSortBy = options.sortBy || [];
    const newSortDesc = options.sortDesc || [];
    
    const oldItemsPerPage = perPage.value;
    
    // Check if anything actually changed
    const pageChanged = newPage !== currentPage.value;
    const itemsPerPageChanged = newItemsPerPage !== perPage.value;
    const sortByChanged = JSON.stringify(newSortBy) !== JSON.stringify(sortBy.value);
    const sortDescChanged = JSON.stringify(newSortDesc) !== JSON.stringify(sortDesc.value);
    
    // Update state
    currentPage.value = newPage;
    perPage.value = newItemsPerPage;
    sortBy.value = newSortBy;
    sortDesc.value = newSortDesc;

    // If items per page decreased, we might have enough cached data
    if (newItemsPerPage < oldItemsPerPage && !sortByChanged && !sortDescChanged) {
      // Check if we have enough cached data for the new page size
      const requiredEndIndex = newPage * newItemsPerPage;
      if (requiredEndIndex <= milestonesCache.value.length) {
        // We have enough cached data, no need to fetch
        return;
      }
    }
    
    // Only trigger API call if something actually changed
    if (pageChanged || itemsPerPageChanged || sortByChanged || sortDescChanged) {
      await getMilestones(newPage, newItemsPerPage);
    }
  };

  // Initialize services and headers
  const initializeServices = () => {
    makeMilestoneService = MilestoneService($api);
    tagService = makeTagService($api);
  };

  const initializeHeaders = () => {
    const dynamicHeaders = store.getters['headers/dynamicHeaders'];
    if (!dynamicHeaders.milestone) {
      store.dispatch('headers/initializeHeaders', { type: 'milestone' });
    }
    headers.value = dynamicHeaders.milestone;
  };

  const getStatuses = () => {
    statuses.value = store.getters['user/getStatuses'] ? store.getters['user/getStatuses']('milestone') : [];
  };

  // Watchers
  watch(searchFilter, () => {
    // Reset to first page when clearing search (going back to paginated view)
    if (!searchFilter.value || !searchFilter.value.trim()) {
      currentPage.value = 1;
    }
    
    // Use debounced search method
    debouncedGetMilestones();
  });

  watch(filteredMilestones, (newValue) => {
    if (newValue === 'SEARCH_BACKEND') {
      // Handle search backend if needed
    }
  });

  // Lifecycle hooks
  onMounted(async () => {
    initializeServices();
    initializeHeaders();
    getStatuses();
    
    // Create debounced search method
    debouncedGetMilestones = debounce(() => getMilestones(currentPage.value, perPage.value, true), 300);

    await init();
    
    if (route.query.activeClosed === 'true') {
      filter.value = 'closed';
    }
    
    await getTags();
  });

  return {
    // Reactive state
    milestones: milestonesCache, // Return cached milestones
    tableLoadingState,
    selectedMilestone,
    setSelected,
    apiFilters,
    savingInProgress,
    headers,
    filter,
    isMilestoneArchived,
    totalRows: totalRowsCache, // Return cached total rows
    currentPage,
    perPage,
    sortBy,
    sortDesc,
    expanded,
    itemKey,
    searchFilter,
    showFormDialog,
    openedRow,
    loading,
    checked,
    showConfirmOpenDialog,
    showConfirmDeleteDialog,
    mainFilter,
    closeFilter,
    statuses,
    tags,
    mainFilters,
    closedFilters,
    skeletonLoaderState,

    // Computed properties
    writeEntity,
    deleteEntity,
    filteredHeaders,
    isColumnFilter,
    filteredMilestones,
    isSearchMode,
    totalPages,
    filteredFilters,

    // From other composables
    activeCount,
    archivedCount,
    formatDate,
    relationsLoading,
    relationLoadingStates,

    // Methods
    init,
    isDefaultValue,
    confirmCloseMilestone,
    confirmUnArchiveMilestone,
    showDeleteDialog,
    handleCloseClick,
    handleDeleteCloseClick,
    handleConfirmOpenClick,
    handleConfirmDeleteClick,
    applyFilters,
    applyClosedFilters,
    updateFilter,
    staticValues,
    getTags,
    getMilestones,
    getColor,
    convertToLocal,
    determineType,
    updateFilteredHeaders,
    editMilestone,
    unArchiveMilestone,
    updateMainFilter,
    clearFilters,
    updateFilters,
    checkMainFilterStatus,
    checkCloseFilterStatus,
    isDefaultRange,
    onUpdatePagination,
    showSkeletonLoader,
    hideSkeletonLoader,
    clearCache, // Expose cache clearing method
    hasInitiallyLoaded
  };
}; 