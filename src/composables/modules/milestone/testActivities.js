import { ref, computed, watch, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore } from '@/main';
import { useLoading } from '@/composables/utils/loading';
import { usePermissions } from '@/composables/utils/permissions';
import { useRelations } from '@/composables/utils/relations';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import makeRunService from '@/services/api/run';
import makePlanService from '@/services/api/plan';
import makeConfigurationService from '@/services/api/configuration';
import makeTagService from '@/services/api/tag';
import makeMilestonesService from '@/services/api/milestone';
import MilestoneService from '@/services/api/milestone';

export function useMilestoneTestActivities(props = {}) {
  // Get Vue instance for accessing plugins
  const instance = getCurrentInstance();
  const { proxy } = instance;
  
  // Composables
  const route = useRoute();
  const router = useRouter();
  const store = useStore();
  const { showSkeletonLoader, hideSkeletonLoader, skeletonLoaderState } = useLoading();
  const { authorityTo } = usePermissions();
  const { relationsLoading, relationLoadingStates, fetchPlanRelations, fetchRunRelations } = useRelations();

  // Services
  let makeMilestoneService;

  // Reactive state
  const isCreateDisabled = ref(false);
  const tableTestType = ref('plans');
  const tableFilter = ref('all');
  const searchFilter = ref('');
  const appliedRunFilters = ref(null);
  const appliedPlanFilters = ref(null);
  const listView = ref(false);
  const runHeaders = ref([]);
  const planHeaders = ref([]);
  const milestones = ref([]);
  const configurations = ref([]);
  const runItems = ref([]);
  const planItems = ref([]);
  const tags = ref([]);
  const showConfirmBackDialog = ref(true);
  const isProjectMenuCollapsed = ref(false);
  const selectedPlanId = ref('all');
  const showConfirmOpenDialog = ref(false);
  const searchTimeout = ref(null);

  // Pagination state for plans
  const planTotalRows = ref(0);
  const planCurrentPage = ref(1);
  const planPerPage = ref(10);

  // Pagination state for runs
  const runTotalRows = ref(0);
  const runCurrentPage = ref(1);
  const runPerPage = ref(10);

  // Computed properties
  const activeMilestoneFilter = computed(() => store.getters.activeMilestoneFilter);
  const currentAccount = computed(() => store.getters['user/currentAccount']);
  const dynamicHeaders = computed(() => store.getters['headers/dynamicHeaders']);

  const isListView = computed(() => route.query.view === 'list');
  const isDashboardView = computed(() => route.query.view === 'dashboard');

  const getActivePlanItems = computed(() => 
    planItems.value?.filter((plan) => !plan?.archivedAt)
  );

  const getActiveRunItems = computed(() => 
    runItems.value?.filter((run) => !run?.archivedAt)
  );

  const archiveButtonLabel = computed(() => 
    activeMilestoneFilter.value == 'open' ? proxy.$t('archive') : proxy.$t('unarchive')
  );

  const getActivePlanItemsCount = computed(() => planTotalRows.value);
  const getActiveRunItemsCount = computed(() => runTotalRows.value);

  const singularTestType = computed(() => 
    tableTestType.value.substring(0, tableTestType.value.length - 1)
  );

  const selectedTestType = computed(() => {
    const testType = {
      plans: proxy.$t('testPlans'),
      runs: proxy.$t('testRuns')
    };
    return testType[tableTestType.value];
  });

  const filteredRunHeaders = computed(() => 
    runHeaders.value?.filter((header) => header.checked && header.value !== 'actions')
  );

  const filteredPlanHeaders = computed(() => 
    planHeaders.value?.filter((header) => header.checked)
  );

  const isTableTypePlans = computed(() => tableTestType.value === 'plans');

  const filteredTestRuns = computed(() => {
    let items = getActiveRunItems.value;
    
    if (searchFilter.value) {
      items = items.filter(item => matchesFilter(item));
    }
    
    if (appliedRunFilters.value) {
      items = items.filter(item => matchRunApplyFilter(item));
    }
    
    let filtered = items?.filter((item) => {
      if (selectedPlanId.value == 'unlinked' && item.testPlans && item.testPlans.length > 0) {
        return false;
      }
      if (Number.isInteger(selectedPlanId.value)) {
        const hasMatchingPlan = item.testPlans?.some(plan => plan.uid === selectedPlanId.value);
        if (!hasMatchingPlan) {
          return false;
        }
      }
      return true;
    });
    
    return filtered;
  });

  const filteredTestPlans = computed(() => {
    let items = getActivePlanItems.value;
    
    if (searchFilter.value) {
      items = items.filter(item => matchesFilter(item));
    }
    
    if (appliedPlanFilters.value) {
      items = items.filter(item => matchPlanApplyFilter(item));
    }
    
    return items;
  });

  const activeMilestones = computed(() =>
    milestones.value?.filter((milestone) => !milestone?.archivedAt && !milestone?.deletedAt)
  );

  const writeEntity = computed(() => authorityTo('write_entity'));

  // Utility methods for progress calculation
  const generateExecutionsProgress = (frequency) => {
    // Implementation for progress calculation
    return frequency || {};
  };

  const getObjectCount = (frequency) => {
    // Implementation for object count calculation
    return frequency ? Object.keys(frequency).length : 0;
  };

  // Initialize services
  const initializeServices = () => {
    makeMilestoneService = MilestoneService(proxy.$api);
  };

  // Methods
  const initializeHeaders = (payload) => {
    return store.dispatch('headers/initializeHeaders', payload);
  };

  const fetchData = async () => {
    if (!props.isCreate && (!props.value || !props.value.uid)) {
      console.warn('Milestone data not available yet');
      return;
    }

    const handle = route.params.handle;
    const projectKey = route.params.key;

    try {
      showSkeletonLoader();
      
      await Promise.all([
        getAllTestRuns({ handle, projectKey, perPage: runPerPage.value, currentPage: runCurrentPage.value }),
        getPlans({ handle, projectKey, perPage: planPerPage.value, currentPage: planCurrentPage.value })
      ]);
      
      hideSkeletonLoader();
    } catch (error) {
      showErrorToast(proxy.$swal, 'fetchError', { item: 'milestone data' }, error?.response?.data);
      hideSkeletonLoader();
    }
  };

  const handleCloseClick = () => {
    showConfirmOpenDialog.value = !showConfirmOpenDialog.value;
  };

  const handleMilestoneConfirmation = async () => {
    try {
      await makeMilestoneService.updateMilestone(
        route.params.handle,
        route.params.key,
        props.value.uid,
        {
          ...props.value.customFields,
          archived: activeMilestoneFilter.value == 'open',
          name: props.value.name,
        }
      ); 
      showSuccessToast(proxy.$swal, 'closeSuccess', { item: 'Milestone' });
      router.replace({
        name: 'Milestones',
        params: {
          handle: route.params.handle,
          key: route.params.key,
        },
        query: {
          activeClosed: 'true'
        }
      });
    } catch (error) {
      showErrorToast(proxy.$swal, 'closeError', { item: 'Milestone' }, error?.response?.data);
    }
  };

  const getAllTestRuns = async ({ handle, projectKey, perPage, currentPage }) => {
    if (!props.isCreate && (!props.value || !props.value.uid)) {
      console.warn('Cannot fetch runs: milestone data not available');
      return;
    }

    const runService = makeRunService(proxy.$api);
    
    const queryParams = {
      limit: perPage || 10, 
      offset: ((currentPage || 1) - 1) * (perPage || 10)
    };
    
    if (!props.isCreate && props.value && props.value.uid) {
      queryParams.milestoneUids = props.value.uid;
    }
    
    const response = await runService.getRuns(handle, projectKey, queryParams);
    runItems.value = response.data.items;
    runTotalRows.value = response.data?.count || response.data?.total || 0;
    
    if (runItems.value.length > 0) {
      fetchRunRelations(runService, handle, projectKey, runItems.value).catch(error => {
        console.warn('Failed to load run relations:', error);
      });
    }
  };

  const getPlans = async ({ handle, projectKey, perPage, currentPage }) => {
    if (!props.isCreate && (!props.value || !props.value.uid)) {
      console.warn('Cannot fetch plans: milestone data not available');
      return;
    }

    const planService = makePlanService(proxy.$api);
    
    const queryParams = {
      limit: perPage || 10,
      offset: ((currentPage || 1) - 1) * (perPage || 10)
    };
    
    if (!props.isCreate && props.value && props.value.uid) {
      queryParams.milestoneUids = props.value.uid;
    }
    
    const response = await planService.getPlans(handle, projectKey, queryParams);
    planItems.value = response.data.items;
    planTotalRows.value = response.data?.count || response.data?.total || 0;
    
    if (planItems.value.length > 0) {
      fetchPlanRelations(planService, handle, projectKey, planItems.value).catch(error => {
        console.warn('Failed to load plan relations:', error);
      });
    }
  };

  const applyRunFilters = (filters) => {
    appliedRunFilters.value = filters ?? null;
    runCurrentPage.value = 1;
    if (tableTestType.value === 'runs') {
      getAllTestRuns({
        handle: route.params.handle,
        projectKey: route.params.key,
        perPage: runPerPage.value,
        currentPage: runCurrentPage.value
      });
    }
  };

  const applyPlanFilters = (filters) => {
    appliedPlanFilters.value = filters ?? null;
    planCurrentPage.value = 1;
    if (tableTestType.value === 'plans') {
      getPlans({
        handle: route.params.handle,
        projectKey: route.params.key,
        perPage: planPerPage.value,
        currentPage: planCurrentPage.value
      });
    }
  };

  const toggleView = async (view = 'list') => {
    if (route.query.view === view) {
      return;
    }

    const routeName = props.isCreate ? 'MilestoneCreate' : 'MilestoneView';

    const routeConfig = {
      name: routeName,
      params: {
        ...route.params,
      },
      query: {
        view: view
      },
    };

    if (!props.isCreate && props.value && props.value.uid) {
      routeConfig.params.id = props.value.uid;
    }

    await router.push(routeConfig);
  };

  const matchRunApplyFilter = (item) => {
    if (appliedRunFilters.value?.panel_priority?.length > 0 &&
      !appliedRunFilters.value.panel_priority.includes(item.priority)) {
      return false;
    }
    if (appliedRunFilters.value?.panel_status?.length > 0 &&
      !appliedRunFilters.value.panel_status.includes(item.status)) {
      return false;
    }
    if (appliedRunFilters.value?.panel_milestone?.length > 0 &&
      !appliedRunFilters.value.panel_milestone.includes(item.customFields?.milestone) &&
      !(appliedRunFilters.value.panel_milestone.includes('None') && !item.customFields?.milestone)) {
      return false;
    }
    if (appliedRunFilters.value?.panel_tag?.length > 0) {
      let tagExists = false;
      const itemTags = item.customFields?.tags || [];
      if (appliedRunFilters.value.panel_tag.includes('none') && itemTags.length === 0) {
          tagExists = true;
      } else {
          for (const tag of appliedRunFilters.value.panel_tag) {
              if (itemTags.includes(tag)) {
                  tagExists = true;
                  break;
              }
          }
      }
      if (!tagExists) return false;
    }
    if (appliedRunFilters.value?.dateRange?.start && appliedRunFilters.value?.dateRange.start > item.createdAt ||
      appliedRunFilters.value?.dateRange?.end && appliedRunFilters.value?.dateRange.end < item.createdAt) {
      return false;
    }
    return true;
  };

  const matchPlanApplyFilter = (item) => {
    if (appliedPlanFilters.value?.panel_priority?.length > 0 &&
      !appliedPlanFilters.value.panel_priority.includes(item.priority)) {
      return false;
    }
    if (appliedPlanFilters.value?.panel_status?.length > 0 &&
      !appliedPlanFilters.value.panel_status.includes(item.status)) {
      return false;
    }
    if (appliedPlanFilters.value?.panel_tag?.length > 0) {
      let tagExists = false;
      const itemTags = item.customFields?.tags || [];
      if (appliedPlanFilters.value.panel_tag.includes('none') && itemTags.length === 0) {
          tagExists = true;
      } else {
          for (const tag of appliedPlanFilters.value.panel_tag) {
            if (itemTags.includes(tag)) {
              tagExists = true;
              break;
            }
          }
      }
      if (!tagExists) return false;
    }
    if (appliedPlanFilters.value?.dateRange?.start && appliedPlanFilters.value?.dateRange.start > item.createdAt ||
      appliedPlanFilters.value?.dateRange?.end && appliedPlanFilters.value?.dateRange.end < item.createdAt) {
      return false;
    }
    return true;
  };

  const onClickPlanActive = (planId) => {
    selectedPlanId.value = planId;
  };

  const handleTestPlans = (value) => {
    if (JSON.stringify(props.value.testPlans) !== JSON.stringify(value)) {
      const updatedValue = { ...props.value, testPlans: value };
      proxy.$emit('input', updatedValue);
    }
  };

  const handleTestRuns = (value) => {
    if (JSON.stringify(props.value.testRuns) !== JSON.stringify(value)) {
      const updatedValue = { ...props.value, testRuns: value };
      proxy.$emit('input', updatedValue);
    }
  };

  const getTags = async () => {
    const handle = route.params.handle;
    const tagService = makeTagService(proxy.$api);
    try {
      const response = await tagService.getTags(handle, 'runs');
      tags.value = response.data;
    } catch (err) {
      showErrorToast(proxy.$swal, 'fetchError', { item: 'tags' }, err?.response?.data);
      return [];
    }
  };

  const getMilestones = async () => {
    const handle = route.params.handle;
    const projectKey = route.params.key;

    const milestoneService = makeMilestonesService(proxy.$api);
    try {
      const response = await milestoneService.getMilestones(handle, projectKey);
      milestones.value = response.data?.items;
      return response.data?.items;
    } catch (err) {
      showErrorToast(proxy.$swal, 'fetchError', { item: 'milestones' }, err?.response?.data);
      return [];
    }
  };

  const getConfigurations = async () => {
    const handle = route.params.handle;
    const projectKey = route.params.key;

    const configurationService = makeConfigurationService(proxy.$api);
    try {
      const response = await configurationService.getConfigurations(handle, projectKey, 10, 0);
      configurations.value = response.data?.configurations;
      return response.data?.configurations;
    } catch (err) {
      showErrorToast(proxy.$swal, 'fetchError', { item: 'configurations' }, err?.response?.data);
      return [];
    }
  };

  // Watchers
  watch(() => props.value?.uid, (newUid, oldUid) => {
    if ((newUid && newUid !== oldUid) || props.isCreate) {
      fetchData();
    }
  }, { immediate: true });

  watch(searchFilter, () => {
    if (tableTestType.value === 'runs') {
      runCurrentPage.value = 1;
    } else {
      planCurrentPage.value = 1;
    }
    clearTimeout(searchTimeout.value);
    searchTimeout.value = setTimeout(() => {
      fetchData();
    }, 300);
  });

  // Initialize services when composable is used
  initializeServices();

  // Initialize headers and data
  const init = async () => {
    if (!dynamicHeaders.value.run) {
      await initializeHeaders({ type: 'run' });
    }
    if (!dynamicHeaders.value.plan) {
      await initializeHeaders({ type: 'plan' });
    }

    const validViews = ['list', 'dashboard'];
    const view = validViews.includes(route.query.view) ? route.query.view : 'list';
    await toggleView(view);

    runHeaders.value = dynamicHeaders.value.run;
    planHeaders.value = dynamicHeaders.value.plan;

    await Promise.all([
      getMilestones(),
      getConfigurations(),
      getTags()
    ]);
  };

  return {
    // Reactive state
    isCreateDisabled,
    tableTestType,
    tableFilter,
    searchFilter,
    appliedRunFilters,
    appliedPlanFilters,
    listView,
    runHeaders,
    planHeaders,
    milestones,
    configurations,
    runItems,
    planItems,
    tags,
    showConfirmBackDialog,
    isProjectMenuCollapsed,
    selectedPlanId,
    showConfirmOpenDialog,
    planTotalRows,
    planCurrentPage,
    planPerPage,
    runTotalRows,
    runCurrentPage,
    runPerPage,
    searchTimeout,
    skeletonLoaderState,

    // Computed properties
    activeMilestoneFilter,
    currentAccount,
    dynamicHeaders,
    isListView,
    isDashboardView,
    getActivePlanItems,
    getActiveRunItems,
    archiveButtonLabel,
    getActivePlanItemsCount,
    getActiveRunItemsCount,
    singularTestType,
    selectedTestType,
    filteredRunHeaders,
    filteredPlanHeaders,
    isTableTypePlans,
    filteredTestRuns,
    filteredTestPlans,
    activeMilestones,
    relationsLoading,
    relationLoadingStates,
    writeEntity,

    // Methods
    init,
    initializeHeaders,
    fetchData,
    handleCloseClick,
    handleMilestoneConfirmation,
    getAllTestRuns,
    getPlans,
    applyRunFilters,
    applyPlanFilters,
    toggleView,
    matchRunApplyFilter,
    matchPlanApplyFilter,
    onClickPlanActive,
    handleTestPlans,
    handleTestRuns,
    getTags,
    getMilestones,
    getConfigurations,
    openCreateTest,
    handleConfirmClick,
    changeFilter,
    changeTestType,
    matchesFilter,
    onUpdatePlanPagination,
    onUpdateRunPagination,
    onTestPlanRowClick,
    onTestRunRowClick,
    generateExecutionsProgress,
    getObjectCount,
    showSkeletonLoader,
    hideSkeletonLoader,
    fetchPlanRelations,
    fetchRunRelations
  };

  // Additional methods that were in the original component
  function openCreateTest() {
    if (isTableTypePlans.value) {
      router.push({
        name: 'TestPlanCreate',
        params: {
          handle: route.params.handle,
          key: route.params.key
        },
        query: {
          redirectTo: 'MilestoneCreate',
        }
      });
    } else {
      router.push({
        name: 'TestRunCreate',
        params: {
          handle: route.params.handle,
          key: route.params.key
        },
        query: {
          redirectTo: 'MilestoneCreate',
        }
      });
    }
  }

  function handleConfirmClick() {
    showConfirmBackDialog.value = false;
    router.replace({
      name: 'MilestoneCreate',
      params: {
        handle: route.params.handle,
        key: route.params.key
      },
      query: {
        activeEdit: 'true'
      }
    });
  }

  function changeFilter(filter) {
    tableFilter.value = filter;
  }

  function changeTestType(type) {
    tableTestType.value = type;
  }

  function matchesFilter(item) {
    const lowerCaseFilter = searchFilter.value.toLowerCase();
    const nameMatch = item.name.toLowerCase().includes(lowerCaseFilter);
    return nameMatch;
  }

  function onUpdatePlanPagination(options) {
    const newPage = options.page;
    const newItemsPerPage = options.itemsPerPage;

    if (newPage !== planCurrentPage.value || newItemsPerPage !== planPerPage.value) {
      planCurrentPage.value = newPage;
      planPerPage.value = newItemsPerPage;
      getPlans({
        handle: route.params.handle,
        projectKey: route.params.key,
        perPage: planPerPage.value,
        currentPage: planCurrentPage.value
      });
    }
  }

  function onUpdateRunPagination(options) {
    const newPage = options.page;
    const newItemsPerPage = options.itemsPerPage;

    if (newPage !== runCurrentPage.value || newItemsPerPage !== runPerPage.value) {
      runCurrentPage.value = newPage;
      runPerPage.value = newItemsPerPage;
      getAllTestRuns({
        handle: route.params.handle,
        projectKey: route.params.key,
        perPage: runPerPage.value,
        currentPage: runCurrentPage.value
      });
    }
  }

  function onTestPlanRowClick(plan) {
    router.push({
      name: 'TestPlanRerun',
      params: {
        handle: route.params.handle,
        key: route.params.key,
        planId: plan.uid,
      },
    });
  }

  function onTestRunRowClick(run) {
    router.push({
      name: 'TestRunCaseEdit',
      params: {
        handle: route.params.handle,
        key: route.params.key,
        id: run.uid,
      },
    });
  }
}
