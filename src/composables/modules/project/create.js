import { ref, computed, reactive, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore, $api } from '@/main';
import { debounce } from 'debounce';
import { users } from '@/constants/data.js';
import makeProjectsService from '@/services/api/project';
import { showErrorToast } from '@/composables/utils/toast';
import { projectImageTypes } from '@/constants/fileTypes.js';
import { 
  requiredRule, 
  min2CharsRule, 
  projectKeyMax10CharsRule, 
  projectKeyFormatRule, 
  maxProjectNameLengthRule, 
  maxDescriptionLengthRule 
} from "@/utils/validation";
import { useProjectsManagement } from '@/composables/modules/project/view';
import { t } from '@/i18n';
import Swal from 'sweetalert2';

export const useProjectCreate = () => {
  const route = useRoute();
  const router = useRouter();
  const store = useStore();
  const { clearCache } = useProjectsManagement();

  // Reactive form data
  const form = reactive({
    projectName: '',
    projectKey: '',
    description: '',
    users: [],
  });

  // UI state
  const isCreateDisabled = ref(false);
  const imageSrc = ref('');
  const file = ref(null);
  const showConfirmBackDialog = ref(false);
  const showAddUsersView = ref(false);
  const tableFilter = ref('all');
  const search = ref('');

  // Users data (loaded from constants)
  const usersData = ref([...users]);

  // Applied filters
  const appliedFilters = reactive({
    roles: [],
    projects: [],
    tags: [],
  });

  // Validation rules - using computed to ensure they work with Vue 3
  const validationRules = computed(() => ({
    required: requiredRule(),
    min2Chars: min2CharsRule(),
    projectKeyMax10Chars: projectKeyMax10CharsRule(),
    projectKeyFormat: projectKeyFormatRule(),
    maxProjectNameLength: maxProjectNameLengthRule(),
    maxDescriptionLength: maxDescriptionLengthRule(),
  }));

  // Computed properties
  const imageStyle = computed(() => {
    return imageSrc.value ? { backgroundImage: `url(${imageSrc.value})` } : {};
  });

  const toBeSelectedUserLength = computed(() => {
    return usersData.value.filter((user) => user.toBeSelected).length;
  });

  const selectedUserLength = computed(() => {
    return usersData.value.filter((user) => user.selected).length;
  });

  const isAnyFilterApplied = computed(() => {
    return appliedFilters.roles.length || appliedFilters.projects.length || appliedFilters.tags.length;
  });

  const ruleStatus = computed(() => {
    const value = form.projectKey;
    return {
      required: validationRules.value.required(value) === true,
      min2Chars: validationRules.value.min2Chars(value) === true,
      projectKeyMax10Chars: validationRules.value.projectKeyMax10Chars(value) === true,
      projectKeyFormat: validationRules.value.projectKeyFormat(value) === true,
    };
  });

  // Refs for form elements
  const formRef = ref(null);
  const projectKeyRef = ref(null);
  const avatarRef = ref(null);

  // Project validation
  const validateProjectKey = async (key) => {
    const handle = route.params.handle;
    const projectService = makeProjectsService($api);
    
    try {
      const projectExists = await projectService.validateProjectKey(handle, key);
      return projectExists?.status === 200;
    } catch (error) {
      return false; 
    }
  };

  const isHandleAvailable = debounce(async (event) => {
    await updateProjectKey(event);
  }, 1000);

  const updateProjectKey = async (key) => {
    if (
      !projectKeyRef.value?.errorBucket?.length && 
      !(await validateProjectKey(key))
    ) {
      if (projectKeyRef.value) {
        nextTick(() => {
          if (projectKeyRef.value) {
            projectKeyRef.value.errorBucket = [
              t('projects.create_project.projectKeyExists', { projectKey: key })
            ];
          }
        });
      }
    }
  };

  const validateForm = () => {
    return formRef.value?.validate() || false;
  };

  // Project creation logic
  const createProject = async () => {
    if (!validateForm()) return;
    
    if (projectKeyRef.value?.errorBucket?.length) {
      return;
    }

    isCreateDisabled.value = true;
    
    const payload = {
      name: form.projectName,
      key: form.projectKey,
      customFields: {
        description: form.description,
        status: "active",
        star: false
      },
    };

    try {
      const response = await store.dispatch('project/add', { 
        swal: Swal, 
        handle: route.params.handle, 
        payload 
      });
      
      const project = response.data;
      
      // Clear project cache after successful creation
      clearCache();
      
      await router.push({ name: "ProjectsView" });

      // Handle file upload if present
      if (project.uid && file.value) {
        const handle = store.getters['user/currentAccount'].handle;
        const mediaType = 'attachment';
        const projectService = makeProjectsService($api);
        const params = {
          handle,
          projectKey: form.projectKey
        };
        
        try {
          await store.dispatch('attachment/uploadToServer', {
            handle, 
            mediaType, 
            file: file.value, 
            apiService: projectService, 
            params
          });
        } catch (error) {
          if (error?.status === 507) {
            showErrorToast(Swal, 'Upload limit reached', {}, 'limitReached', handle);
          } else {
            showErrorToast(Swal, error?.response?.data?.message);
          }
        }
      }
    } catch (error) {
      showErrorToast(Swal, error?.response?.data?.message);
      console.error("Failed to create project:", error);
    } finally {
      isCreateDisabled.value = false;
    }
  };

  // File handling methods
  const openFileDialog = () => {
    avatarRef.value?.showModalUpload();
  };

  const handleFileChange = (uploadedFile) => {
    if (projectImageTypes.includes(uploadedFile.type)) {
      previewImage(uploadedFile);
    }
  };

  const handleDrop = (event) => {
    const droppedFile = event.dataTransfer.files[0];
    previewImage(droppedFile);
  };

  const previewImage = (imageFile) => {
    if (imageFile) {
      const reader = new FileReader();
      reader.onload = (e) => {
        imageSrc.value = e.target.result;
      };
      reader.readAsDataURL(imageFile);
      file.value = imageFile;
    }
  };

  const removeImage = () => {
    imageSrc.value = '';
    file.value = null;
  };

  // Navigation methods
  const handleBackClick = () => {
    if (showAddUsersView.value) {
      showAddUsersView.value = false;
    } else {
      showConfirmBackDialog.value = true;
    }
  };

  const handleCloseClick = () => {
    showConfirmBackDialog.value = false;
  };

  const handleConfirmClick = () => {
    showConfirmBackDialog.value = false;
    router.replace({ name: 'ProjectsView' });
  };

  // User management methods
  const closeShowAddUsersView = () => {
    showAddUsersView.value = false;
  };

  const changeFilter = (filter) => {
    tableFilter.value = filter;
  };

  const addUsers = () => {
    usersData.value.forEach((user) => {
      if (user.toBeSelected) {
        user.selected = true;
        user.toBeSelected = false;
      }
    });
  };

  const removeUsers = () => {
    usersData.value.forEach((user) => {
      if (user.toBeSelected) {
        user.selected = false;
        user.toBeSelected = false;
      }
    });
  };

  const updateUser = (user) => {
    const index = usersData.value.findIndex((item) => item.id === user.id);
    if (index !== -1) {
      user.selected = tableFilter.value === 'all';
      user.toBeSelected = false;
    }
  };

  const clearAllFilters = () => {
    appliedFilters.roles = [];
    appliedFilters.projects = [];
    appliedFilters.tags = [];
  };

  const applyFilters = (filters) => {
    Object.assign(appliedFilters, filters);
  };

  // Initialize users data on mount
  const initializeUsers = () => {
    usersData.value.forEach((user) => {
      if (!Object.prototype.hasOwnProperty.call(user, 'toBeSelected')) {
        user.toBeSelected = false;
      }
    });
  };

  return {
    // Reactive data
    form,
    isCreateDisabled,
    imageSrc,
    file,
    showConfirmBackDialog,
    showAddUsersView,
    tableFilter,
    search,
    usersData,
    appliedFilters,
    
    // Computed properties
    imageStyle,
    toBeSelectedUserLength,
    selectedUserLength,
    isAnyFilterApplied,
    ruleStatus,
    validationRules,
    
    // Refs
    formRef,
    projectKeyRef,
    avatarRef,
    
    // Constants
    projectImageTypes,
    
    // Methods
    validateProjectKey,
    isHandleAvailable,
    updateProjectKey,
    validateForm,
    createProject,
    openFileDialog,
    handleFileChange,
    handleDrop,
    previewImage,
    removeImage,
    handleBackClick,
    handleCloseClick,
    handleConfirmClick,
    closeShowAddUsersView,
    changeFilter,
    addUsers,
    removeUsers,
    updateUser,
    clearAllFilters,
    applyFilters,
    initializeUsers,
  };
};
