import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore, $api } from '@/main';
import { useLoading } from '@/composables/utils/loading';
import { useNetworkError } from '@/composables/utils/networkError';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import ProjectsService from '@/services/api/project';
import { usePermissions } from '@/composables/utils/permissions';
import { useProjectView } from '@/composables/modules/project/index';
import Swal from 'sweetalert2';
import { debounce } from 'lodash';

// Module-level state for caching projects across component instances
const projectsCache = ref([]);
const totalRowsCache = ref(0);
const totalActiveCache = ref(0);
const totalArchivedCache = ref(0);
const lastFetchedPage = ref(0);
const lastFetchedFilter = ref('');
const lastFetchedSearch = ref('');
const lastFetchedHandle = ref('');

export const useProjectsManagement = () => {
  const route = useRoute();
  const router = useRouter();
  const store = useStore();

  const { setProject } = useProjectView();
  // Network Error Handling
  const { redirectOnError } = useNetworkError();

  // Loading State
  const { isLoading: skeletonLoaderState, showSkeletonLoader, hideSkeletonLoader } = useLoading();
  const tableLoadingState = ref(false);

  // Data properties
  const handle = ref(route.params.handle);
  const selectedProject = ref(null);
  const filter = ref('active');
  const isFilterApplied = ref(false);
  const search = ref('');
  const table = ref(true);
  const options = ref(['active', 'archived']);
  const membersState = ref({
    isLoading: false,
    hasError: false,
    errorMessage: '',
  });

  // Pagination state
  const currentPage = ref(1);
  const perPage = ref(10);

  const headers = ref([]);
  const itemKey = ref('uid');
  const rowClass = () => 'project-item';
  const showConfirmDeleteDialog = ref(false);
  const showConfirmArchiveDialog = ref(false);
  const showConfirmUnarchiveDialog = ref(false);

  // API Service
  const makeProjectService = ProjectsService($api);

  const { authorityTo } = usePermissions();

  // Init headers
  if (!store.getters['headers/dynamicHeaders'].projectsView) {
    store.dispatch('headers/initializeHeaders', { type: 'projectsView' });
  }
  headers.value = store.getters['headers/dynamicHeaders'].projectsView;

  // Computed properties
  const currentAccount = computed(() => store.state.user.currentAccount);

  const writeProject = computed(() => {
    return authorityTo('write_project');
  });

  const deleteProjectPermission = computed(() => {
    return authorityTo('delete_project');
  });

  const filteredHeaders = computed(() => {
    const filtered = filteredMenuHeaders.value.filter((header) => header.checked);
    return filtered;
  });

  const filteredMenuHeaders = computed(() => {
    const filtered = headers.value.filter((header) => header.text != 'Actions');
    return filtered;
  });

  const totalPages = computed(() => {
    return Math.ceil(totalRowsCache.value / perPage.value);
  });

  const filteredItems = computed(() => {
    // Return the current page slice of cached projects
    const startIndex = (currentPage.value - 1) * perPage.value;
    const endIndex = startIndex + perPage.value;
    return projectsCache.value.slice(startIndex, endIndex);
  });

  const totalActiveCount = computed(() => {
    return totalActiveCache.value;
  });

  const totalArchivedCount = computed(() => {
    return totalArchivedCache.value;
  });

  const getActiveProjects = computed(() => {
    return projectsCache.value.filter((project) => !project.archivedAt);
  });

  const getActiveProjectCount = computed(() => {
    return getActiveProjects.value.length;
  });

  const getArchivedProjects = computed(() => {
    return projectsCache.value.filter((project) => project.archivedAt);
  });

  const getArchivedProjectCount = computed(() => {
    return getArchivedProjects.value.length;
  });

  // Helper function to check if we need to fetch new data
  const needsDataFetch = (pageNumber, pageSize, filterValue, searchValue, handleValue) => {
    // Always fetch if context changed (filter, search, handle)
    if (
      filterValue !== lastFetchedFilter.value ||
      searchValue !== lastFetchedSearch.value ||
      handleValue !== lastFetchedHandle.value
    ) {
      return true;
    }

    // Calculate required data range
    const requiredEndIndex = pageNumber * pageSize;
    const currentCachedCount = projectsCache.value.length;

    // Need to fetch if we don't have enough cached data
    return requiredEndIndex > currentCachedCount && currentCachedCount < totalRowsCache.value;
  };

  // Helper function to clear cache
  const clearCache = () => {
    projectsCache.value = [];
    totalRowsCache.value = 0;
    totalActiveCache.value = 0;
    totalArchivedCache.value = 0;
    lastFetchedPage.value = 0;
    lastFetchedFilter.value = '';
    lastFetchedSearch.value = '';
    lastFetchedHandle.value = '';
  };

  // Methods
  const init = async (promiseList) => {
    showSkeletonLoader();
    tableLoadingState.value = true;
    try {
      await Promise.all(promiseList);
    } catch (error) {
      if (error?.response?.status) {
        redirectOnError(error.response.status);
      }
    } finally {
      hideSkeletonLoader();
      tableLoadingState.value = false;
    }
  };

  const getProjects = async (
    additionalParams = {},
    status,
    routeHandle,
    pageNumber = currentPage.value,
    pageSize = perPage.value,
    forceRefresh = false
  ) => {
    try {
      const currentHandle = routeHandle || handle.value;
      const currentFilter = status || filter.value;
      const currentSearch = search.value?.trim() || '';
      tableLoadingState.value = true;
      showSkeletonLoader();
      // Check if we need to fetch new data
      if (!forceRefresh && !needsDataFetch(pageNumber, pageSize, currentFilter, currentSearch, currentHandle)) {
        // We have enough cached data, no need to fetch
        return;
      }

      // If context changed, clear cache and start fresh
      const contextChanged =
        currentFilter !== lastFetchedFilter.value ||
        currentSearch !== lastFetchedSearch.value ||
        currentHandle !== lastFetchedHandle.value;

      if (contextChanged || forceRefresh) {
        clearCache();
      }

      // Calculate what data we need to fetch
      const startIndex = contextChanged ? 0 : projectsCache.value.length;
      const offset = startIndex;
      const limit = contextChanged ? pageNumber * pageSize : pageSize;

      const params = {
        limit: limit,
        offset: offset,
        status: currentFilter,
        includeCount: true,
        orderBy: 'name',
        order: 'asc',
        ...additionalParams,
      };

      // Add search query if present
      if (currentSearch) {
        params.q = currentSearch;
      }

      const searchParams = new URLSearchParams();
      Object.keys(params).forEach((key) => {
        if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
          searchParams.set(key, params[key]);
        }
      });

      const response = await makeProjectService.getProjects(currentHandle, searchParams.toString());

      const newProjects = response.data.items || [];

      if (contextChanged || forceRefresh) {
        // Replace cache with new data
        projectsCache.value = newProjects;
      } else {
        // Append new data to cache
        projectsCache.value.push(...newProjects);
      }

      // Update metadata
      totalRowsCache.value = response.data.count || 0;
      totalActiveCache.value = response.data?.meta?.totalActive || 0;
      totalArchivedCache.value = response.data?.meta?.totalArchived || 0;

      // Update tracking variables
      lastFetchedPage.value = Math.ceil(projectsCache.value.length / pageSize);
      lastFetchedFilter.value = currentFilter;
      lastFetchedSearch.value = currentSearch;
      lastFetchedHandle.value = currentHandle;
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'projects' }, error?.response?.data);
    } finally {
      tableLoadingState.value = false;
      hideSkeletonLoader();
    }
  };

  const onUpdatePagination = async (options) => {
    const newPage = options.page;
    const newItemsPerPage = options.itemsPerPage;

    const oldItemsPerPage = perPage.value;

    currentPage.value = newPage;
    perPage.value = newItemsPerPage;

    // If items per page decreased, we might have enough cached data
    if (newItemsPerPage < oldItemsPerPage) {
      // Check if we have enough cached data for the new page size
      const requiredEndIndex = newPage * newItemsPerPage;
      if (requiredEndIndex <= projectsCache.value.length) {
        // We have enough cached data, no need to fetch
        return;
      }
    }

    // If items per page increased or we need more data, fetch additional data
    await getProjects({}, filter.value, handle.value, newPage, newItemsPerPage);
  };

  const updateFilter = async (newFilter) => {
    showSkeletonLoader();
    try {
      // Reset to first page when changing filters
      currentPage.value = 1;
      await getProjects({}, newFilter, handle.value, 1, perPage.value, true); // Force refresh
      filter.value = newFilter;
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'projects' }, error?.response?.data);
    } finally {
      hideSkeletonLoader();
    }
  };

  const applySearch = async (searchQuery) => {
    search.value = searchQuery;
    // Reset to first page when searching
    currentPage.value = 1;
    await getProjects({}, filter.value, handle.value, 1, perPage.value, true); // Force refresh
  };

  const debouncedApplySearch = debounce(applySearch, 500);

  const updateSearch = (newSearch) => {
    debouncedApplySearch(newSearch);
  };

  const clearSearch = () => {
    debouncedApplySearch.cancel();
    applySearch('');
  };

  const toggleTable = (isTable) => {
    table.value = isTable;
  };

  const closeDeleteDialog = () => {
    showConfirmDeleteDialog.value = false;
    selectedProject.value = null;
  };

  const confirmDeleteProject = (project) => {
    showConfirmDeleteDialog.value = true;
    selectedProject.value = project;
  };

  const deleteProject = async () => {
    try {
      await makeProjectService.deleteProject(handle.value, selectedProject.value.key);
      showSuccessToast(Swal, 'deleteSuccess', { item: 'projects' });
      // Force refresh to update cache after deletion
      await getProjects({}, filter.value, handle.value, currentPage.value, perPage.value, true);
    } catch (error) {
      showErrorToast(Swal, 'deleteError', { item: 'projects' }, error?.response?.data);
    } finally {
      showConfirmDeleteDialog.value = false;
      closeDeleteDialog();
    }
  };

  const archiveProject = async () => {
    const payload = {
      customFields: {
        ...selectedProject.value.customFields,
        status: 'archived',
      },
      archived: true,
    };
    try {
      await makeProjectService.updateProject(handle.value, selectedProject.value.key, payload);
      // Force refresh to update cache after archiving
      await getProjects({}, filter.value, handle.value, currentPage.value, perPage.value, true);
      showSuccessToast(Swal, 'archiveSuccess', { item: 'Project' });
    } catch (error) {
      showErrorToast(Swal, 'archiveError', { item: 'Project' }, error?.response?.data);
    } finally {
      showConfirmArchiveDialog.value = false;
      showConfirmDeleteDialog.value = false;
    }
  };

  const unarchiveProject = async () => {
    const payload = {
      customFields: {
        ...selectedProject.value.customFields,
        status: 'active',
      },
      archived: false,
    };
    try {
      await makeProjectService.updateProject(handle.value, selectedProject.value.key, payload);
      // Force refresh to update cache after unarchiving
      await getProjects({}, filter.value, handle.value, currentPage.value, perPage.value, true);
      showSuccessToast(Swal, 'unarchiveSuccess', { item: 'Project' });
    } catch (error) {
      showErrorToast(Swal, 'unarchiveError', { item: 'Project' }, error?.response?.data);
    } finally {
      showConfirmUnarchiveDialog.value = false;
    }
  };

  const closeArchiveDialog = () => {
    showConfirmArchiveDialog.value = false;
    selectedProject.value = null;
  };

  const confirmArchiveProject = (project) => {
    showConfirmArchiveDialog.value = true;
    selectedProject.value = project;
  };

  const closeUnarchiveDialog = () => {
    showConfirmUnarchiveDialog.value = false;
    selectedProject.value = null;
  };

  const confirmUnarchiveProject = (project) => {
    showConfirmUnarchiveDialog.value = true;
    selectedProject.value = project;
  };

  const editItem = (project) => {
    selectedProject.value = project;
    setProject(project);
    router.push({
      name: 'ProjectDetailView',
      params: {
        handle: route.params.handle,
        key: project.key,
      },
    });
  };

  const handleToggleStar = async (project) => {
    selectedProject.value = project;
    const payload = {
      customFields: {
        ...selectedProject.value.customFields,
        star: !selectedProject.value.customFields.star,
      },
    };

    try {
      await makeProjectService.updateProject(route.params.handle, selectedProject.value.key, payload);
      // Force refresh to update cache after toggling star
      await getProjects({}, filter.value, handle.value, currentPage.value, perPage.value, true);
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'projects' }, error?.response?.data);
    }
  };

  const useProject = (project) => {
    setProject(project);
    router.push({
      name: 'Cases',
      params: {
        handle: route.params.handle,
        key: project.key,
      },
    });
  };

  const handleRouteChange = (to) => {
    // Reset pagination when route changes
    currentPage.value = 1;
    getProjects({}, filter.value, to.params.handle, 1, perPage.value, true); // Force refresh
  };

  const createDemoProject = async ({ handle }) => {
    return await makeProjectService.createProject(handle, { createDemo: true });
  };

  const getProject = async ({ handle, projectKey }) => {
    return await makeProjectService.getProject(handle, projectKey);
  };

  watch(
    () => route.params,
    (newParams) => {
      if (newParams.handle !== handle.value) {
        handle.value = newParams.handle;
        // Reset pagination when handle changes
        currentPage.value = 1;
        getProjects({}, filter.value, newParams.handle, 1, perPage.value, true); // Force refresh
      }
    }
  );

  // Removed the watch for search since we now handle it with debounce in updateSearch

  return {
    handle,
    selectedProject,
    projects: projectsCache, // Return cached projects
    totalArchived: totalArchivedCache,
    totalActive: totalActiveCache,
    filter,
    isFilterApplied,
    search,
    table,
    options,
    membersState,
    headers,
    itemKey,
    rowClass,
    showConfirmDeleteDialog,
    showConfirmArchiveDialog,
    showConfirmUnarchiveDialog,
    tableLoadingState,
    skeletonLoaderState,

    // Pagination properties
    totalRows: totalRowsCache,
    currentPage,
    perPage,
    totalPages,

    // Computed properties
    currentAccount,
    writeProject,
    deleteProjectPermission,
    filteredHeaders,
    filteredMenuHeaders,
    filteredItems,
    totalActiveCount,
    totalArchivedCount,
    getActiveProjects,
    getActiveProjectCount,
    getArchivedProjects,
    getArchivedProjectCount,

    // Methods
    init,
    getProjects,
    onUpdatePagination,
    updateFilter,
    updateSearch,
    clearSearch,
    toggleTable,
    closeDeleteDialog,
    confirmDeleteProject,
    deleteProject,
    archiveProject,
    unarchiveProject,
    closeArchiveDialog,
    confirmArchiveProject,
    closeUnarchiveDialog,
    confirmUnarchiveProject,
    editItem,
    handleToggleStar,
    useProject,
    showSkeletonLoader,
    hideSkeletonLoader,
    handleRouteChange,
    createDemoProject,
    getProject,
    clearCache, // Expose cache clearing method
  };
};
