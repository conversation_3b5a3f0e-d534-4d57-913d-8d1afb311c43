import { ref, computed, onMounted, watch , nextTick} from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { useStore, $api } from '@/main';
import makeCasesService from '@/services/api/case';
import makeTemplateService from '@/services/api/template';
import { showErrorToast, showSuccessToast } from '@/composables/utils/toast';
import { useColorPreferences } from '@/composables/utils/colorPreferences';
import Swal from 'sweetalert2';
import { useProjectStatus } from '@/composables/utils/projectStatus';
import Vue from 'vue';
import DragIndicator from '@/components/Cases/DragIndicator.vue';
import makeTagService from '@/services/api/tag';
import { t } from '@/i18n'
import { selectedFolderUid } from '@/composables/modules/cases/index';
import { entityTypeNames } from '@/constants/templates';
import { useRelations } from '@/composables/utils/relations';
const { relationsLoading, fetchCaseRelations } = useRelations();

const isBlankTable = ref(false);
const isOpenAddToTestRunsDialog = ref(false);
const isOpenEditDialog = ref(false);
const isOpenExportDialog = ref(false);
const isOpenCreateTestRunDialog = ref(false);
const isOpenConfirmDialog = ref(false);
const headers = ref([]);
const isOpenConfirmBulkDeleteDialog = ref(false);
const isSelectedData = ref(false);
const isAddedTable = ref(false);
const openFilterDrawer = ref(true);
const toggleSelect = ref(false);
const testName = ref('');
const testTemplate = ref('');
export const tableSelectedCases = ref([]);
const menuOpen = ref(false);
const searchTerm = ref('');
const filters = ref({ priorities: [], tags: [] });
const actionBtnShow = ref(false);
const selectedData = ref([]);
const tableFilterData = ref(false);
const tags = ref([]);


export function useTestCasesList(props, { emit }) {
  const route = useRoute();
  const router = useRouter();
  const store = useStore();

  // Data properties

  const states = ref([{ text: 'High' }, { text: 'Medium' }, { text: 'Low' }]);

  const testTemplates = ref([]);
  const localCaseItems = ref([]);
  const itemToDelete = ref(null);
  const statuses = ref([]);
  const priorities = ref([]);
  const skeletonLoaderState = ref(false);

  // Services
  const caseService = makeCasesService($api);
  const templateService = makeTemplateService($api);
  const tagService = makeTagService($api);


  const { isProjectArchived } = useProjectStatus();

  // Computed properties
  const currentAccount = computed(() => store.state.user.currentAccount);
  const dynamicHeaders = computed(() => store.getters['headers/dynamicHeaders']);

  const activeRow = computed({
    get: () => props.selectedCase,
    set: (value) => {
      emit('update:selectedCase', value);
    },
  });

  const selectedRows = computed({
    get: () => (tableSelectedCases.value.length ? tableSelectedCases.value : props.initialSelected),
    set: (value) => {
      tableSelectedCases.value = value;
    },
  });

  const isSelected = computed(() => {
    return selectedRows.value.length > 0;
  });
  const selectedFolderUidStore = computed(() =>
    store.getters['testCases/selectedFolderUid']
  )

  const selectedItemsTitles = computed(() => {
    return props.selectedItems ? props.selectedItems.map((i) => i.text).join(', ') : '';
  });

  const filteredHeaders = computed(() => {
    return headers.value.filter((header) => header.checked);
  });

  const displayTableData = computed(() => {
    if (props.isImport) {
      return props.caseItems;
    }
    // Server-side filtering: return cases as they come from the API
    // Filtering is now handled by the backend, so we just show active cases
    return localCaseItems.value?.filter((item) => item.active !== false) || [];
  });

  const tableItems = computed(() => {
    // Helper function to apply client-side filtering
    const applyClientSideFiltering = (cases) => {
      let filteredCases = cases || [];
      
      // Apply search filter
      if (searchTerm.value && searchTerm.value.trim()) {
        const searchLower = searchTerm.value.toLowerCase().trim();
        filteredCases = filteredCases.filter(item => {
          return (
            item.name?.toLowerCase().includes(searchLower) ||
            item.id?.toString().toLowerCase().includes(searchLower) ||
            item.testCaseRef?.toString().toLowerCase().includes(searchLower) ||
            item.description?.toLowerCase().includes(searchLower)
          );
        });
      }
      
      // Apply priority filter
      if (filters.value.priorities && filters.value.priorities.length > 0) {
        filteredCases = filteredCases.filter(item => {
          return filters.value.priorities.includes(item.priority);
        });
      }
      
      // Apply tag filter
      if (filters.value.tagUids && filters.value.tagUids.length > 0) {
        filteredCases = filteredCases.filter(item => {
          if (!item.tags || item.tags.length === 0) return false;
          return filters.value.tagUids.some(tagUid => 
            item.tags.some(tag => tag.uid === tagUid)
          );
        });
      }
      
      return filteredCases;
    };

    if (tableFilterData.value) {
      // When on the "linked" tab, apply client-side filtering to selected cases
      return applyClientSideFiltering(tableSelectedCases.value);
    } else {
      // When on the "unlinked" tab, apply client-side filtering to display data
      return applyClientSideFiltering(displayTableData.value);
    }
  });

  const shouldHaveOverflow = computed(() => {
    // Enable overflow (horizontal scroll) if more than 4 columns
    return filteredHeaders.value && filteredHeaders.value.length > 4;
  });

  const tableContainerClass = computed(() => {
    return shouldHaveOverflow.value ? 'table-scroll-container' : 'table-no-scroll-container';
  });

  const tableClass = computed(() => {
    const baseClasses = 'table-fixed data-table-style';
    return shouldHaveOverflow.value ? `${baseClasses} table-min-width` : baseClasses;
  });

  // Methods
  function handleCloseConfirmBulkDeleteDialog() {
    isOpenConfirmBulkDeleteDialog.value = false;
  }

  function handleCloseConfirmDialog() {
    isOpenConfirmDialog.value = false;
    itemToDelete.value = null;
  }

  function handleConfirm() {
    emit('caseRemove', itemToDelete.value);
    isOpenConfirmDialog.value = false;
    itemToDelete.value = null;
  }

  function handleCloseAddToTestRunsDialog() {
    isOpenAddToTestRunsDialog.value = false;
  }

  function handleCloseCreateTestRunDialog(){
    isOpenCreateTestRunDialog.value = false;
  }

  function handleEditClick() {
    if (!isProjectArchived.value) {
      isOpenEditDialog.value = true;
    }
  }

  function handleCloseEditDialog() {
    isOpenEditDialog.value = false;
  }

  function handleExportClick() {
    isOpenExportDialog.value = true;
  }

  function handleCloseExportDialog() {
    isOpenExportDialog.value = false;
  }

  function getItemClass(item) {
    return `${activeRow.value && item.uid === activeRow.value.uid ? 'active-row' : ''} case-row uid-${
      item.testCaseRef
    }`;
  }

  function isLastItem(item) {
    return item.text == props.breadCrumbs[props.breadCrumbs.length - 1].text;
  }

  function onCancelAction() {
    selectedRows.value = [];
  }

  function applyFilters(newFilters) {
    filters.value = newFilters;
    // Emit filter change to parent component for server-side filtering
    emit('applyFilters', newFilters);
  }

  function applySearch(searchQuery) {
    searchTerm.value = searchQuery;
    // Emit search change to parent component for server-side search
    emit('applySearch', searchQuery);
  }

  function handleSelectTestCases() {
    emit('selectedCases', tableSelectedCases.value);
  }

  function handleClick(row) {
    activeRow.value = row;
    emit('expandDetail', row);
  }

  function addSelectedCases() {
    selectedData.value = selectedRows.value;
    isSelectedData.value = true;
    isAddedTable.value = true;
  }

  function removeSelectedCases() {
    isAddedTable.value = false;
  }



  // Quick create test case function
  async function quickCreate() {
    
    if (!isProjectArchived.value) {
      const selectedTemplate = testTemplates.value.find((template) => template.uid == testTemplate.value);
      const payload = {
        name: testName.value,
        parentId: Number(selectedFolderUidStore.value) || selectedFolderUid.value  || null,
        templateId: testTemplate.value,
        customFields: {
          tags: [],
          priority: priorities.value[priorities.value.length - 1].value,
          templateFields: selectedTemplate ? selectedTemplate.customFields.templateFields : null,
        },
      };
      emit('createCase', payload);
      testName.value = '';
    }
  }

  // Get Test case history function
  async function getCaseHistoryData() {
  let handle = currentAccount.value.handle;
    let folderId = route.params.folderUid;
    if (!folderId) return;

    try {
      const res = await caseService.getFolderCases(handle, route.params.key, folderId);
      if (res.status == 200) {
        const cases = res.data.cases;
        
        // Use the relations composable to fetch tag relations
        if (cases && cases.length > 0) {
          try {
            await fetchCaseRelations(caseService, handle, route.params.key, cases, ['tag'], 'testCaseRef');
          } catch (tagErr) {
            console.warn('Failed to fetch tag relations:', tagErr);
            // Continue without tags rather than failing completely
          }
        }
        
        localCaseItems.value = cases;
        emit('updateCasesHistoryData', cases);
      }
    } catch (err) {
      showErrorToast(Swal, 'genericError', { message: err });
    }
  }

  // Delete Test case from uid
  async function deleteTestCase(uid) {
    itemToDelete.value = uid;
    isOpenConfirmDialog.value = true;
  }

  async function handleBulkRemove() {
    if (selectedRows.value.length > 0 && !isProjectArchived.value) {
      isOpenConfirmBulkDeleteDialog.value = true;
    }
  }

  async function confirmBulkRemove() {
    const itemUIDs = selectedRows.value.map((row) => row.uid);
    const payload = {
      ids: itemUIDs,
    };
    emit('bulkCaseRemove', payload);

    isOpenConfirmBulkDeleteDialog.value = false;
  }
  async function handleAddToTestRuns(){
    isOpenAddToTestRunsDialog.value = true;
  }
  async function handleCreateTestRunDialogue(){
    isOpenCreateTestRunDialog.value = true;
  }

  

  async function updateSelectedCases(selectedFolder, selectedPriority, tagData) {
    try {
      const payload = selectedRows.value.map((row) => {
        const basePayload = {
          testCaseRef: row.testCaseRef,
          name: row.name,
          parentId: selectedFolder || row.parentUid || null,
          customFields: {
            ...row.customFields,
            priority: selectedPriority ?? row.customFields.priority,
          },
          priority: selectedPriority ?? row.priority,
          tagIds: [...tagData.selectedTags.map((tag) => tag.uid), ...tagData.withTag.map((tag) => tag.uid)],
          tagReplacements: [
            {
              existingTagUids: tagData.replaceTag.map((tag) => tag.uid),
              newTagUids: [],
            },
          ],
        };
        

        return basePayload;
      });

      const response = await caseService.updateBulkTestCases(route.params.handle, route.params.key, {
        cases: payload,
      });

      if (response.status === 207) {
        showErrorToast(Swal, 'partialError');
      } else if (response.status !== 200) {
        showErrorToast(Swal, 'updateError', { item: 'test cases' });
      } else {
        // Clear tag relations cache for updated cases to ensure fresh data
        const { clearEntityCache } = useRelations();
        const caseUids = selectedRows.value.map(row => row.testCaseRef);
        clearEntityCache('case', caseUids);
        
        // Refresh data after successful update
        await getCaseHistoryData();
        emit('updateSelectedCases');
        emit('reload-cases');

        isOpenEditDialog.value = false;
        selectedRows.value = [];
        showSuccessToast(Swal, response?.data?.message);
      }
    } catch (err) {
      showErrorToast(Swal, 'genericError', { message: err });
    }
  }

  async function updateTag(data, itemData) {
    const { tagIds, tagReplacements } = tagFormation(
      itemData.tags.map((tag) => tag.uid),
      data.map((tag) => tag.uid)
    );
    if(tagIds.length > 0 || tagReplacements.length > 0){
      // Immediately update the local item data for instant UI feedback
      itemData.tags = [...data];
      
      // Update the localCaseItems to reflect the change immediately
      const caseIndex = localCaseItems.value.findIndex(c => c.testCaseRef === itemData.testCaseRef);
      if (caseIndex !== -1) {
        localCaseItems.value[caseIndex].tags = [...data];
      }
      
      // Emit the updated data to parent component
      emit('updateCasesHistoryData', localCaseItems.value);
      
      try {
        await caseService.updateTestCase(route.params.handle, route.params.key, itemData.testCaseRef, {
          tagIds: tagIds,
          tagReplacements: tagReplacements,
        });    
        
        // Clear tag relations cache for this case to ensure fresh data
        const { clearEntityCache } = useRelations();
        clearEntityCache('case', [itemData.testCaseRef]);
        
        await getCaseHistoryData();
        showSuccessToast(Swal, 'updateSuccess', { item: t('tags') });
        // Emit an event to reload cases in parent components
        emit('reload-cases');
      } catch (error) {
        // If API call fails, revert the local change
        itemData.tags = itemData.tags.filter(tag => 
          !tagIds.includes(tag.uid) && !tagReplacements.some(rep => rep.newTagUids.includes(tag.uid))
        );
        
        // Revert the localCaseItems change
        if (caseIndex !== -1) {
          localCaseItems.value[caseIndex].tags = [...itemData.tags];
        }
        
        // Emit the reverted data
        emit('updateCasesHistoryData', localCaseItems.value);
        
        showErrorToast(Swal, 'updateError', { item: 'tags' });
        throw error;
      }
    }
  }

  function tagFormation(originalTags, newTags) {
    const tagIdsToRemove = originalTags.filter((tagId) => !newTags.includes(tagId));
    const tagIdsToAdd = newTags.filter((tagId) => !originalTags.includes(tagId));

    const tagReplacements = [];

    if (tagIdsToRemove.length > 0) {
      tagReplacements.push({
        existingTagUids: tagIdsToRemove,
        newTagUids: [],
      });
    }

    if (tagIdsToAdd.length > 0) {
      tagReplacements.push({
        existingTagUids: [],
        newTagUids: tagIdsToAdd,
      });
    }

    return {
      tagIds: tagIdsToAdd,
      tagReplacements,
    };
  }

  function handleRowEdit(uid, executionId) {
    const editObject = {
      name: 'EditTestCases',
      params: { uid: uid },
      query: { folderUid: route.params.folderUid },
    };
    if (props.fromRun) {
      editObject.query = {
        redirectTo: 'TestRunEdit',
        id: executionId,
        isExecution: true,
      };
    }
    router.push(editObject);
  }

  function initializeDraggable() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach(() => {
        const tableRows = document.querySelectorAll('#case-table tbody tr');
        tableRows.forEach((row) => applyDragFunctionality(row));
      });
    });

    // Start observing the table
    const tableSelector = '#case-table tbody';
    const checkTable = setInterval(() => {
      const table = document.querySelector(tableSelector);
      if (table) {
        clearInterval(checkTable);
        observer.observe(table, {
          childList: true,
          subtree: true,
        });

        // Apply to existing rows
        const tableRows = document.querySelectorAll('#case-table tbody tr');
        tableRows.forEach((row) => applyDragFunctionality(row));
      }
    }, 100);

    // Cleanup interval after 10 seconds if table is not found
    setTimeout(() => clearInterval(checkTable), 10000);
  }

  function applyDragFunctionality(row) {
    if (row.draggable === true && row.dataset.dragApplied === 'true') {
      return;
    }
    row.draggable = true;
    row.dataset.dragApplied = 'true';
    row.addEventListener('dragstart', (e) => {
      const regex = /uid-(\d+)/;
      const uid = regex.exec(row.className)[1];
      e.target.classList.add('dragging');
      window.isFileDragging = true;
      window.caseUid = uid;

      const draggedCaseIsSelected = selectedRows.value.some(selectedCase => 
        selectedCase.testCaseRef === uid || selectedCase.testCaseRef === parseInt(uid)
      );
      if (draggedCaseIsSelected && selectedRows.value.length > 1) {
        window.selectedCaseRefs = selectedRows.value.map(selectedCase => selectedCase.testCaseRef);
        window.isMultipleCasesDrag = true;
      } else {
        window.selectedCaseRefs = null;
        window.isMultipleCasesDrag = false;
      }

      const dragCount = draggedCaseIsSelected && selectedRows.value.length > 1 
        ? selectedRows.value.length 
        : 1;

      // Create and use custom drag image
      const dragImage = createDragImage(dragCount);
      document.body.appendChild(dragImage);

      // Position the drag image at the cursor location
      const offsetX = dragImage.offsetWidth / 2;
      const offsetY = dragImage.offsetHeight / 2;

      // Set the custom drag image
      e.dataTransfer.setDragImage(dragImage, offsetX, offsetY);


      // Remove the element after the drag operation starts
      setTimeout(() => {
        document.body.removeChild(dragImage);
      }, 0);
    });

    row.addEventListener('dragend', (e) => {  
      e.target.classList.remove('dragging');
      window.isMultipleCasesDrag = false;
      window.selectedCaseRefs = null;
    });
  }
  function addGlobalDragListeners() {
    document.addEventListener('dragover', (e) => {
      if (window.isFileDragging) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
      }
    });
    
    document.addEventListener('dragenter', (e) => {
      if (window.isFileDragging) {
        e.preventDefault();
      }
    });
  }
  // Create a custom drag image showing the count of test cases
  function createDragImage(count) {
    // Create a wrapper div to mount our Vue component
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.top = '-1000px'; // Position off-screen initially
    container.style.zIndex = '9999';
    container.style.pointerEvents = 'none';

    // Use Vue to create and mount our drag indicator component
    const ComponentClass = Vue.extend(DragIndicator);
    const instance = new ComponentClass({
      propsData: { count },
    });

    instance.$mount();
    container.appendChild(instance.$el);

    return container;
  }

  function handleBreadcrumbClick(item) {
    if (!isProjectArchived.value) {
      emit('folder-selected', item.uid);
    }
  }

  // Functions for the loading mixin
  function showSkeletonLoader() {
    skeletonLoaderState.value = true;
  }

  function hideSkeletonLoader() {
    skeletonLoaderState.value = false;
  }

  // Mixin methods for color preferences
  function getPriorityColor(value, prioritiesList) {
    const priority = prioritiesList.find((p) => p.id === value);
    return priority ? priority.color : '';
  }

  function getPriorityName(value, prioritiesList) {
    const priority = prioritiesList.find((p) => p.id === value);
    return priority ? priority.name : '';
  }

  // Fetch tags for TagEditor
  async function fetchTags() {
    try {
      const response = await tagService.getTags(route.params.handle, 'cases');
      if (response.status === 200) {
        tags.value = response.data.map((tag) => ({ uid: tag.uid, name: tag.name }));
      } else {
        showErrorToast(Swal, 'failedToFetchTags', { item: 'tags' });
      }
    } catch (error) {
      showErrorToast(Swal, 'errorFetchingTags', { item: 'tags' }, error?.response?.data);
    }
  }

  // Lifecycle hooks and watchers
  watch(
    () => props.caseItems,
    (newVal) => {
      localCaseItems.value = newVal;
      initializeDraggable();
    },
    { immediate: true }
  );
  watch(() => displayTableData.value, (newVal, oldVal) => {
   if (!oldVal || newVal.length !== oldVal.length) {
    nextTick(() => {
      initializeDraggable();
    });
   }
  });

  watch(() => props.initialSelected, (newVal) => {
    tableSelectedCases.value = newVal;
  });

  watch(() => props.tableFilter, (newVal) => {
    tableFilterData.value = newVal;
  });

  const clearFilters = () => {
    filters.value.priorities = []
    filters.value.tags = []
    filters.value.tagUids = []
    filters.value.tagObjects = []
    searchTerm.value = '';
    // Emit clear filters to parent component for server-side filtering
    emit('clearFilters');
  }

  function reloadCases() {
    getCaseHistoryData();
    selectedRows.value = [];
  }

  onMounted(async () => {
    if (!dynamicHeaders.value.case) {
      store.dispatch('headers/initializeHeaders', { type: 'case' });
    }
    headers.value = dynamicHeaders.value.case;

    if(route.name == 'Cases'){
      tableSelectedCases.value = [];
    }


    if(['TestRunEdit', 'TestRunCase', 'TestRunCaseEdit', 'TestRunCaseEditExecutions'].includes(route.name)){
      clearFilters();
      tableFilterData.value = false;
    }
    
    const { getPriorities, getStatuses } = useColorPreferences($api, Swal);

    priorities.value = getPriorities('testCase').filter((element) => !element.archived);
    statuses.value = getStatuses('testCase').filter((element) => !element.archived);

    try {
      showSkeletonLoader();
      
      const [testCaseResult, testResultResult] = await Promise.allSettled([
        templateService.getTemplates(route.params.handle, route.params.key, `per_page=9999&entityType=${entityTypeNames.testCase}&current_page=1`),
        templateService.getTemplates(route.params.handle, route.params.key, `per_page=9999&entityType=${entityTypeNames.testResult}&current_page=1`)
      ]);


      const allTemplates = [];

      if (testCaseResult.status === 'fulfilled') {
        allTemplates.push(...testCaseResult.value.data.templates);
      } else {
        console.error('Failed to fetch test case templates:', testCaseResult.reason);
        showErrorToast(Swal, 'fetchError', { item: 'test case templates' }, testCaseResult.reason?.response?.data);
      }

      if (testResultResult.status === 'fulfilled') {
        allTemplates.push(...testResultResult.value.data.templates);
      } else {
        console.error('Failed to fetch test result templates:', testResultResult.reason);
        showErrorToast(Swal, 'fetchError', { item: 'test result templates' }, testResultResult.reason?.response?.data);
      }

      testTemplates.value = allTemplates;


      // Find and set the default template if available
      const defaultTemplate = testTemplates.value.find(template => template.isDefault === true);
      if (defaultTemplate) {
        testTemplate.value = defaultTemplate.uid;
      } else if (testTemplates.value.length > 0) {
        // If no default template is found, use the first one
        testTemplate.value = testTemplates.value[0].uid;
      }

      if(route.name != "TestRunEdit"){
        await getCaseHistoryData();
      }
      initializeDraggable();
      await fetchTags();
    } catch (err) {
      showErrorToast(Swal, 'genericError', { message: err });
    } finally {
      hideSkeletonLoader();
    }
  });

  return {
    // Data refs
    isBlankTable,
    isOpenAddToTestRunsDialog,
    isOpenCreateTestRunDialog,
    isOpenEditDialog,
    isOpenExportDialog,
    isOpenConfirmDialog,
    headers,
    isOpenConfirmBulkDeleteDialog,
    isSelectedData,
    isAddedTable,
    openFilterDrawer,
    toggleSelect,
    testName,
    testTemplate,
    tableSelectedCases,
    menuOpen,
    searchTerm,
    filters,
    actionBtnShow,
    selectedData,
    states,
    testTemplates,
    localCaseItems,
    itemToDelete,
    statuses,
    priorities,
    skeletonLoaderState,
    tableFilterData,
    tableItems,
    // Relations loading state
    relationsLoading,
    // Computed properties
    currentAccount,
    dynamicHeaders,
    activeRow,
    selectedRows,
    isSelected,
    selectedItemsTitles,
    filteredHeaders,
    displayTableData,
    isProjectArchived,
    shouldHaveOverflow,
    tableContainerClass,
    tableClass,
    // Methods
    handleCloseConfirmBulkDeleteDialog,
    handleCloseConfirmDialog,
    handleConfirm,
    handleCloseAddToTestRunsDialog,
    handleEditClick,
    handleCloseEditDialog,
    handleExportClick,
    handleCloseExportDialog,
    getItemClass,
    isLastItem,
    onCancelAction,
    applyFilters,
    applySearch,
    clearFilters,
    handleSelectTestCases,
    handleClick,
    addSelectedCases,
    removeSelectedCases,
    quickCreate,
    getCaseHistoryData,
    deleteTestCase,
    handleBulkRemove,
    confirmBulkRemove,
    handleAddToTestRuns,
    updateSelectedCases,
    updateTag,
    tagFormation,
    handleRowEdit,
    initializeDraggable,
    applyDragFunctionality,
    addGlobalDragListeners,
    handleBreadcrumbClick,
    showSkeletonLoader,
    hideSkeletonLoader,
    getPriorityColor,
    getPriorityName,
    createDragImage,
    fetchTags,
    tags,
    reloadCases,
    handleCloseCreateTestRunDialog,
    handleCreateTestRunDialogue,
  };
}
