import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import makeCasesService from '@/services/api/case';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { usePermissions } from '@/composables/utils/permissions';
import { useLoading } from '@/composables/utils/loading';
import { useNetworkError } from '@/composables/utils/networkError';
import { useRelations } from '@/composables/utils/relations';
import { tableSelectedCases } from '@/composables/modules/cases/list';
import { useStore, $api } from '@/main';
import { useFolders } from '@/composables/modules/cases/folders';
import Swal from 'sweetalert2';

// Module-level state for caching cases across component instances
const casesCache = ref([]);
const totalCasesCache = ref(0);
const lastFetchedPage = ref(0);
const lastFetchedFolderUid = ref('');
const lastFetchedSearch = ref('');
const lastFetchedFilters = ref(null);
const lastFetchedSortBy = ref([]);
const lastFetchedSortDesc = ref([]);
const lastFetchedHandle = ref('');
const lastFetchedProjectKey = ref('');
const hasInitiallyLoaded = ref(false);

export const selectedFolderUid = ref();
const cases = ref([]);
const selectedCase = ref(null);
const preventWatch = ref(0);
export const folders = ref([]);

// Pagination state
const currentPage = ref(1);
const itemsPerPage = ref(10); // Default page size, can be changed from the table footer
const totalCases = computed(() => {
  // During initial load, return 0 to prevent "No matching results" from showing
  if (!hasInitiallyLoaded.value && casesCache.value.length === 0) {
    return 0;
  }
  return totalCasesCache.value;
});
const totalPages = computed(() => Math.ceil(totalCasesCache.value / itemsPerPage.value));

// Sorting state
const sortBy = ref([]);
const sortDesc = ref([]);

// Relations loading state
const relationsLoading = ref(false);

// Main table loading state for pagination changes
const casesLoading = ref(false);

// Search and filter state
const searchTerm = ref('');
const filters = ref({ priorities: [], tags: [], tagUids: [], tagObjects: [] });

// Debounced error handling for reference loading failures
let referenceErrorTimeout = null;
let hasShownReferenceError = ref(false);

const showDebouncedReferenceError = () => {
  if (hasShownReferenceError.value) return;
  
  hasShownReferenceError.value = true;
  showErrorToast(Swal, 'genericError', { 
    message: 'Some references couldn\'t be loaded. Please try refreshing the page.' 
  });
  
  // Reset the flag after 5 seconds to allow showing the error again if needed
  if (referenceErrorTimeout) {
    clearTimeout(referenceErrorTimeout);
  }
  referenceErrorTimeout = setTimeout(() => {
    hasShownReferenceError.value = false;
  }, 5000);
};

export const useTestCasesIndex = () => {
  const route = useRoute();
  const router = useRouter();
  const { authorityTo } = usePermissions();
  const { showSkeletonLoader, hideSkeletonLoader } = useLoading();
  const { redirectOnError } = useNetworkError();
  const store = useStore();
  const { relationLoadingStates, fetchCaseRelations, resetRelationLoadingStates } = useRelations();
  
  // Folders composable
  const folderComposable = useFolders();

  // Helper function to check if we need to fetch new data
  const needsDataFetch = (pageNumber, pageSize, folderUid, searchQuery, filterOptions, sortByValue, sortDescValue, handle, projectKey) => {
    // Always fetch if context changed
    if (folderUid !== lastFetchedFolderUid.value || 
        searchQuery !== lastFetchedSearch.value || 
        JSON.stringify(filterOptions) !== JSON.stringify(lastFetchedFilters.value) ||
        JSON.stringify(sortByValue) !== JSON.stringify(lastFetchedSortBy.value) ||
        JSON.stringify(sortDescValue) !== JSON.stringify(lastFetchedSortDesc.value) ||
        handle !== lastFetchedHandle.value ||
        projectKey !== lastFetchedProjectKey.value) {
      return true;
    }

    // Calculate required data range
    const requiredEndIndex = pageNumber * pageSize;
    const currentCachedCount = casesCache.value.length;
    
    // Need to fetch if we don't have enough cached data
    return requiredEndIndex > currentCachedCount && currentCachedCount < totalCasesCache.value;
  };

  // Helper function to clear cache
  const clearCache = () => {
    casesCache.value = [];
    totalCasesCache.value = 0;
    lastFetchedPage.value = 0;
    lastFetchedFolderUid.value = '';
    lastFetchedSearch.value = '';
    lastFetchedFilters.value = null;
    lastFetchedSortBy.value = [];
    lastFetchedSortDesc.value = [];
    lastFetchedHandle.value = '';
    lastFetchedProjectKey.value = '';
    hasInitiallyLoaded.value = false;
  };



  // Computed properties
  const writeEntity = computed(() => authorityTo('write_entity'));
  const deleteEntity = computed(() => authorityTo('delete_entity'));
  const readEntity = computed(() => authorityTo('read_entity'));
  const selectedFolderUidStore = computed(() =>
    store.getters['testCases/selectedFolderUid']
  )
  const initialSelectedFolder = computed(() => {
    if(route.params.caseUid) {
      return selectedCase.value?.parentUid;
    } else {
      return selectedFolderUidStore.value || folders.value[0]?.uid;
    }
  });

  // Methods
  async function onSelectFolder(folderUID = null) {
    try {
      if (folderUID) {
        if (folderUID !== selectedFolderUid.value) {
          selectedFolderUid.value = folderUID.uid ? folderUID.uid : folderUID;
          currentPage.value = 1; // Reset to first page on folder change
          await getCases(folderUID);
          setSelectFolderUidStore(folderUID);
          let newPath = route.path;
          preventWatch.value++;
          if (route.params.caseUid && preventWatch.value > 1) {
            newPath = newPath.replace(/\/cases\/[^/]+/, '/cases/');
          }
          await router.replace({ path: newPath });
        }
      } else {
        folderUID = selectedFolderUid.value;
      }
    } catch (error) {
      if (error?.name === 'NavigationDuplicated' || (error?.message && error.message.includes('NavigationDuplicated'))) {
        return; 
      }
      showErrorToast(Swal, 'fetchError', { item: 'cases' }, error?.response?.data);
    }
  }

  async function reloadCases() {
    await getCases(selectedFolderUid.value);
  }

  // Search and filter functions
  async function applySearch(searchQuery, fetchAll = false) {
    searchTerm.value = searchQuery;
    currentPage.value = 1; // Reset to first page on search
    clearCache();
    await getCases(selectedFolderUid.value, searchQuery, filters.value, false, fetchAll);
  }

  async function applyFilters(filterOptions, fetchAll = false) {
    filters.value = { ...filters.value, ...filterOptions };
    currentPage.value = 1; // Reset to first page on filter change
    clearCache();
    await getCases(selectedFolderUid.value, searchTerm.value, filters.value, false, fetchAll);
  }

  async function clearFilters() {
    filters.value = { priorities: [], tags: [], tagUids: [], tagObjects: [] };
    searchTerm.value = '';
    currentPage.value = 1;
    clearCache();
    await getCases(selectedFolderUid.value);
  }

  const setSelectFolderUidStore = uid => store.dispatch('testCases/selectFolder', uid);

  async function getCases(folderUID, searchQuery = '', filterOptions = {}, forceRefresh = false, fetchAll = false) {
    if (!folderUID) return;
    if (!readEntity.value) {
      // Handle unauthorized toast
      return;
    }
    
    const caseService = makeCasesService($api);
    const handle = route.params.handle;
    const projectKey = route.params.key;
    const currentFolderUid = folderUID?.uid ? folderUID?.uid : folderUID;
    selectedFolderUid.value = currentFolderUid;
    
    const currentSearch = searchQuery || searchTerm.value || '';
    const currentFilters = { ...filters.value, ...filterOptions };
    const currentSortBy = sortBy.value;
    const currentSortDesc = sortDesc.value;
    const pageNumber = currentPage.value;
    const pageSize = itemsPerPage.value;

    // Check if we need to fetch new data
    if (!forceRefresh && !needsDataFetch(pageNumber, pageSize, currentFolderUid, currentSearch, currentFilters, currentSortBy, currentSortDesc, handle, projectKey)) {
      // We have enough cached data, update the displayed cases
      const startIndex = (pageNumber - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      cases.value = casesCache.value.slice(startIndex, endIndex);
      return;
    }

    // If context changed, clear cache and start fresh
    const contextChanged = currentFolderUid !== lastFetchedFolderUid.value || 
                         currentSearch !== lastFetchedSearch.value || 
                         JSON.stringify(currentFilters) !== JSON.stringify(lastFetchedFilters.value) ||
                         JSON.stringify(currentSortBy) !== JSON.stringify(lastFetchedSortBy.value) ||
                         JSON.stringify(currentSortDesc) !== JSON.stringify(lastFetchedSortDesc.value) ||
                         handle !== lastFetchedHandle.value ||
                         projectKey !== lastFetchedProjectKey.value;

    if (contextChanged || forceRefresh) {
      clearCache();
    }

    // Calculate what data we need to fetch
    const startIndex = contextChanged ? 0 : casesCache.value.length;
    const limit = contextChanged ? pageNumber * pageSize : pageSize;
    const offset = startIndex;
    
    const params = {
      limit: limit,
      offset: offset,
    };

    // Add backend sorting parameters
    if (currentSortBy && currentSortBy.length > 0) {
      const frontendSortColumn = currentSortBy[0]; // Take first sort column
      const sortDirection = currentSortDesc[0] ? 'desc' : 'asc';
      
      // Map frontend column names to API field names (case sensitive)
      const sortColumnMapping = {
        'name': 'name', // Title column maps to name field
        'id': 'testCaseRef' // ID column maps to testCaseRef field
      };
      
      const apiSortColumn = sortColumnMapping[frontendSortColumn] || frontendSortColumn;
      
      params.orderBy = apiSortColumn;
      params.order = sortDirection;
    }

    // Add search term
    if (currentSearch) {
      params.searchKey = currentSearch;
    }

    // Add filters
    if (currentFilters.priorities && currentFilters.priorities.length > 0) {
      // For multiple priorities, we might need to make multiple calls or modify backend
      // For now, use the first priority (backend may need to support multiple)
      params.priority = currentFilters.priorities[0];
    }
    if (currentFilters.tagUids && currentFilters.tagUids.length > 0) {
      params.tagUids = currentFilters.tagUids;
    }
    if (currentFilters.tags && currentFilters.tags.length > 0) {
      // Convert tag names to tag UIDs if needed
      if (!params.tagUids) {
        params.tag = currentFilters.tags[0]; // Backend may support tag names
      }
    }
    
    casesLoading.value = true;
    try {
      const response = await caseService.getFolderCases(handle, projectKey, currentFolderUid, params, fetchAll);
      const casesData = response.data.items || response.data.cases || [];
      
      if (contextChanged || forceRefresh) {
        // Replace cache with new data
        casesCache.value = casesData;
      } else {
        // Append new data to cache
        casesCache.value.push(...casesData);
      }

      totalCasesCache.value = response.data.count || 0;

      // Update tracking variables
      lastFetchedPage.value = Math.ceil(casesCache.value.length / pageSize);
      lastFetchedFolderUid.value = currentFolderUid;
      lastFetchedSearch.value = currentSearch;
      lastFetchedFilters.value = currentFilters;
      lastFetchedSortBy.value = currentSortBy;
      lastFetchedSortDesc.value = currentSortDesc;
      lastFetchedHandle.value = handle;
      lastFetchedProjectKey.value = projectKey;
      hasInitiallyLoaded.value = true;

      // Set displayed cases for current page
      const displayStartIndex = (pageNumber - 1) * pageSize;
      const displayEndIndex = displayStartIndex + pageSize;
      cases.value = casesCache.value.slice(displayStartIndex, displayEndIndex);
      
      // Fetch tag relations for all cached cases if they exist - do this in background with progressive loading
      if (casesCache.value && casesCache.value.length > 0) {
        // Set relations loading to true
        relationsLoading.value = true;
        
        // Reset relation loading states before fetching
        resetRelationLoadingStates();
        
        fetchCaseRelations(
          caseService, 
          handle, 
          projectKey, 
          casesCache.value, 
          ['tag'], // Only load tags for cases
          'testCaseRef' // Use testCaseRef as the entity UID field
        ).then(() => {
          // Set relations loading to false when relations are loaded
          relationsLoading.value = false;
        }).catch(error => {
          console.warn('Failed to load case relations:', error);
          showDebouncedReferenceError();
          // Set relations loading to false even on error
          relationsLoading.value = false;
        });
      }
      
      // Optionally, you can expose response.data.pagination for the table
    } catch (error) {
      redirectOnError(error.response.status);
      showErrorToast(Swal, 'fetchError', { item: 'cases' }, error?.response?.data);
      casesCache.value = [];
      totalCasesCache.value = 0;
    } finally {
      casesLoading.value = false;
    }
  }

  // Method to update pagination and sorting (called from table footer and sorting events)
  function updatePagination(obj, fetchAll = false)  {
    if (obj && typeof obj === 'object') {
      const newPage = obj.page || currentPage.value;
      const newItemsPerPage = obj.itemsPerPage || itemsPerPage.value;
      const newSortBy = obj.sortBy || [];
      const newSortDesc = obj.sortDesc || [];
      
      // Check if anything actually changed
      const pageChanged = newPage !== currentPage.value;
      const itemsPerPageChanged = newItemsPerPage !== itemsPerPage.value;
      const sortByChanged = JSON.stringify(newSortBy) !== JSON.stringify(sortBy.value);
      const sortDescChanged = JSON.stringify(newSortDesc) !== JSON.stringify(sortDesc.value);
      
      // Update state
      currentPage.value = newPage;
      itemsPerPage.value = newItemsPerPage;
      sortBy.value = newSortBy;
      sortDesc.value = newSortDesc;
      
      // Only trigger API call if something actually changed
      if (pageChanged || itemsPerPageChanged || sortByChanged || sortDescChanged) {
        getCases(selectedFolderUid.value, searchTerm.value, filters.value, false, fetchAll);
      }
    }
  }

  function updateCasesHistoryData(data) {
    cases.value = data;
    getFolders();
  }

  async function getFolders() {
    const handle = route.params.handle;
    const projectKey = route.params.key;
    
    // Clean up old cached trees periodically
    cleanupOldFolderTreeCache();
    
    // Create a project-specific key
    const currentProjectKey = `${handle}-${projectKey}`;
    
    // Check if we have a cached folder tree for this project
    const cachedFolderTree = store.getters['testCases/getFolderTreeForProject'](currentProjectKey);
    
    // Check if cache is recent (less than 1 hour old)
    const lastUpdated = store.getters['testCases/getFolderTreeLastUpdated'](currentProjectKey);
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const isCacheValid = lastUpdated > oneHourAgo;
    
    // If we have a cached tree and it's still valid, use it
    if (cachedFolderTree && cachedFolderTree.length > 0 && isCacheValid) {
      folders.value = cachedFolderTree;
      return;
    }
    
    // Otherwise, fetch fresh folder data
    const folderData = await folderComposable.getFolders(handle, projectKey, { api: $api });
    folders.value = folderData;
    
    // Save to store for persistence
    store.dispatch('testCases/saveFolderTreeForProject', { 
      projectKey: currentProjectKey, 
      folderTree: folderData 
    });
  }

  async function onCaseRemove(uid) {
    const caseService = makeCasesService($api);
    const handle = route.params.handle;
    const projectKey = route.params.key;
    
    try {
      showSkeletonLoader();
      await caseService.deleteCase(handle, projectKey, uid);
      showSuccessToast(Swal, 'deleteSuccess', { item: 'Test Case' });
      cases.value = cases.value.filter(item => item.uid !== uid);
    } catch (error) {
      redirectOnError(error.response.status);
      showErrorToast(Swal, 'deleteError', { item: 'case' }, error?.response?.data);
    } finally {
      hideSkeletonLoader();
    }
  }

  async function onBulkRemove(payload) {
    const caseService = makeCasesService($api);
    const handle = route.params.handle;
    const projectKey = route.params.key;
    
    try {
      showSkeletonLoader();
      await caseService.deleteCases(handle, projectKey, payload);
      cases.value = cases.value.filter(item => !payload.ids.includes(item.uid));
      showSuccessToast(Swal, 'deleteSuccess', { item: 'cases' });
    } catch (error) {
      redirectOnError(error.response.status);
      showErrorToast(Swal, 'deleteError', { item: 'cases' }, error?.response?.data);
    } finally {
      hideSkeletonLoader();
    }
  }

  async function getCase() {
    const findCaseUid = route.params.caseUid;
    const caseService = makeCasesService($api);
    try {
      const res = await caseService.getCase(route.params.handle, route.params.key, findCaseUid);
      if (res.status == 200) {
        const caseData = res.data;
        
        // Fetch tag relations for this case using progressive loading
        if (caseData && caseData.testCaseRef) {
          // Reset relation loading states before fetching
          resetRelationLoadingStates();
          
          fetchCaseRelations(
            caseService, 
            route.params.handle, 
            route.params.key, 
            [caseData], 
            ['tag'], // Only load tags for cases
            'testCaseRef' // Use testCaseRef as the entity UID field
          ).catch(error => {
            console.warn('Failed to load case relations:', error);
            showDebouncedReferenceError();
          });
        }
        
        selectedCase.value = caseData;
      } else {
        showErrorToast(window.$swal, 'fetchError', { item: 'Case' });
      }
    } catch (error) {
      showErrorToast(window.$swal, 'fetchError', { item: 'Case' }, error?.response?.data);
    }
  }

  async function init() {
    // On initial load, no folder is selected. The automatic selection is
    // propagated up after it is made by the child folder tree element.
    tableSelectedCases.value = [];
    try {
      await getFolders();
      if (route.params.caseUid) {
        await getCase();
      } 
      await getCases(initialSelectedFolder.value);
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'cases' }, error?.response?.data);
    }
  }

  function updateCases(newCases) {
    cases.value = newCases;
  }

  // Function to save expanded folder tree to store
  function saveExpandedFolderTree(updatedFolders = null) {
    const foldersToSave = updatedFolders || folders.value;
    if (foldersToSave && foldersToSave.length > 0) {
      const handle = route.params.handle;
      const projectKey = route.params.key;
      const currentProjectKey = `${handle}-${projectKey}`;
      
      store.dispatch('testCases/saveFolderTreeForProject', { 
        projectKey: currentProjectKey, 
        folderTree: foldersToSave 
      });
      
      // Also update the local folders ref if an updated tree was provided
      if (updatedFolders) {
        folders.value = updatedFolders;
      }
    }
  }

  // Function to clean up old cached folder trees (older than 1 hour)
  function cleanupOldFolderTreeCache() {
    const folderTreeCache = store.getters['testCases/folderTreeCache'];
    const oneHourAgo = Date.now() - (60 * 60 * 1000); // 1 hour in milliseconds
    
    Object.keys(folderTreeCache).forEach(projectKey => {
      const lastUpdated = store.getters['testCases/getFolderTreeLastUpdated'](projectKey);
      if (lastUpdated < oneHourAgo) {
        store.dispatch('testCases/clearFolderTreeCache', projectKey);
      }
    });
  }

  // Function to force refresh folders (bypassing cache)
  async function refreshFolders() {
    const handle = route.params.handle;
    const projectKey = route.params.key;
    const currentProjectKey = `${handle}-${projectKey}`;
    
    // Clear the cached tree for this project
    store.dispatch('testCases/clearFolderTreeCache', currentProjectKey);
    
    // Force fresh fetch
    const folderData = await folderComposable.getFolders(handle, projectKey, { api: $api });
    folders.value = folderData;
    
    // Save new tree to cache
    store.dispatch('testCases/saveFolderTreeForProject', { 
      projectKey: currentProjectKey, 
      folderTree: folderData 
    });
  }

  return {
    cases, // This already shows the current page slice from the cache
    folders,
    selectedFolderUid,
    writeEntity,
    deleteEntity,
    readEntity,
    initialSelectedFolder,
    onSelectFolder,
    reloadCases,
    getCases,
    updateCasesHistoryData,
    getFolders,
    onCaseRemove,
    onBulkRemove,
    init,
    updateCases,
    selectedCase,
    selectedFolderUidStore,
    setSelectFolderUidStore,
    // Pagination
    currentPage,
    itemsPerPage,
    updatePagination,
    totalCases, // This now uses the computed property that returns cached total
    totalPages, // This uses totalCasesCache
    relationsLoading,
    relationLoadingStates,
    casesLoading,
    // Sorting
    sortBy,
    sortDesc,
    // Search and filters
    searchTerm,
    filters,
    applySearch,
    applyFilters,
    clearFilters,
    clearCache, // Expose cache clearing method
    saveExpandedFolderTree, // Expose folder tree saving
    refreshFolders, // Expose force refresh
    cleanupOldFolderTreeCache, // Expose cache cleanup
  };
};

export { useTestCasesExport } from './export'; 