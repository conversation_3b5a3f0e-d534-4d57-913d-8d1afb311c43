import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router/composables';
import Papa from 'papaparse/papaparse.js';
import * as XLSX from 'xlsx';
import { XMLParser } from 'fast-xml-parser';
import makeCasesService from '@/services/api/case';
import makeTemplatesService from '@/services/api/template';
import {  showErrorToast } from '@/composables/utils/toast';
import { t } from '@/i18n'
import { $api } from '@/main';
import Swal from 'sweetalert2';
// Import the worker file using Vite's worker import syntax
import TestCaseWorker from '@/workers/testCaseWorker.js?worker';




  const step = ref(1);
  const csvFile = ref(null);
  const csvFileData = ref();
  const selectedFolder = ref(null);
  const folders = ref([]);
  const mappedRows = ref();
  const createBtnLoading = ref(false);
  const folderCasesMap = ref({});
  const csvSeparator = ref(';');
  const firstRow = ref(1);
  const tagsSeparator = ref(' ');
  const rowFormat = ref('single');
  const templates = ref([]);
  const templateFields = ref({});  
  const selectedTemplateDropdown = ref(''); 

  const persistedMappings = ref(null);

export const casesToImport = ref([]);
  const parseLoading = ref(false);

  const worker = ref(null);
  

  const processProgress = ref(0);
  const processedItems = ref(0);
  const totalItems = ref(0);
  
  // Track the current file type
  const currentFileType = ref('csv');
  

  const submissionInProgress = ref(false);
  const submissionProgress = ref(0);
  const totalSubmissionItems = ref(0);
  const submissionFolderName = ref('');
  
export const useCsvImport = () => {

  const router = useRouter();
  const route = useRoute();

  const templateService = makeTemplatesService($api); 

  const convertXmlToCsvFormat = (xmlObj) => {

    const result = {
      headers: [],
      data: []
    };
    

    let testCases = [];
    

    const findTestCases = (obj, path = '') => {
      if (testCases.length > 0) return;
      
      if (path === 'suite' && obj.sections?.section?.cases?.case) {
        const cases = obj.sections.section.cases.case;
        if (Array.isArray(cases) && cases.length > 0) {
          testCases = cases;
          return;
        }
      }
      
      if (Array.isArray(obj)) {
        if (obj.length > 0 && isLikelyTestCase(obj[0])) {
          testCases = obj;
          return;
        }
        
        obj.forEach((item, index) => {
          if (typeof item === 'object' && item !== null) {
            findTestCases(item, `${path}[${index}]`);
          }
        });
        return;
      }
      
      if (typeof obj === 'object' && obj !== null) {
        Object.keys(obj).forEach(key => {
          const value = obj[key];
          const newPath = path ? `${path}.${key}` : key;
          
          if (['testcase', 'test', 'case'].includes(key.toLowerCase()) && Array.isArray(value)) {
            if (value.length > 0 && isLikelyTestCase(value[0])) {
              testCases = value;
              return;
            }
          }
          
          if (typeof value === 'object' && value !== null) {
            findTestCases(value, newPath);
          }
        });
      }
    };
    
    const isLikelyTestCase = (obj) => {
      if (!obj || typeof obj !== 'object') return false;
      
      const hasTitle = obj.name || obj.title || obj.testName;
      const hasTestProperties = obj.description || obj.steps || obj.type || obj.priority || 
                              obj.id || obj.custom || obj.expected || obj.expectedResult;
      
      return hasTitle || hasTestProperties;
    };
    

    if (xmlObj.testsuites && xmlObj.testsuites.testsuite) {

      const suites = Array.isArray(xmlObj.testsuites.testsuite) 
        ? xmlObj.testsuites.testsuite 
        : [xmlObj.testsuites.testsuite];
        
      suites.forEach(suite => {
        if (suite.testcase) {
          const cases = Array.isArray(suite.testcase) ? suite.testcase : [suite.testcase];
          testCases.push(...cases);
        }
      });
    } else if (xmlObj.testsuite && xmlObj.testsuite.testcase) {

      testCases = Array.isArray(xmlObj.testsuite.testcase) 
        ? xmlObj.testsuite.testcase 
        : [xmlObj.testsuite.testcase];
    } else if (xmlObj.testcases && xmlObj.testcases.testcase) {

      testCases = Array.isArray(xmlObj.testcases.testcase) 
        ? xmlObj.testcases.testcase 
        : [xmlObj.testcases.testcase];
    } else if (xmlObj.tests && xmlObj.tests.test) {

      testCases = Array.isArray(xmlObj.tests.test) 
        ? xmlObj.tests.test 
        : [xmlObj.tests.test];
    } else if (xmlObj.suite && xmlObj.suite.sections?.section?.cases?.case) {

      testCases = Array.isArray(xmlObj.suite.sections.section.cases.case) 
        ? xmlObj.suite.sections.section.cases.case 
        : [xmlObj.suite.sections.section.cases.case];
    } else {

      findTestCases(xmlObj);
    }
    

    if (testCases.length === 0) {
      return result;
    }
    

    const allHeaders = new Set();
    
    // Process each test case to collect all possible headers
    testCases.forEach(testCase => {
      // Process regular fields
      Object.keys(testCase).forEach(key => {
        // Skip attributes for now, we'll handle them separately
        if (!key.startsWith('_')) {
          allHeaders.add(key);
          
          // Special handling for custom fields
          if (key === 'custom' && typeof testCase[key] === 'object') {
            Object.keys(testCase[key]).forEach(customKey => {
              allHeaders.add(`custom_${customKey}`);
            });
          }
        }
      });
      
      // Add attributes as regular fields with prefix
      Object.keys(testCase).forEach(key => {
        if (key.startsWith('_')) {
          allHeaders.add(key.substring(1)); // Remove the underscore
        }
      });
    });
    
    // Convert Set to Array for headers
    result.headers = Array.from(allHeaders);
    
    // Common mappings for standardized field names
    const fieldMappings = {
      'name': ['name', 'title', 'testname', 'testcasename'],
      'description': ['description', 'desc', 'summary', 'custom_preconds', 'preconds'],
      'priority': ['priority', 'severity', 'importance'],
      'status': ['status', 'state'],
      'tags': ['tags', 'tag', 'labels', 'label'],
      'externalId': ['id', 'externalid', 'external_id', 'testid']
    };
    
    // Add standard field headers if they don't exist yet
    Object.keys(fieldMappings).forEach(standardField => {
      if (!result.headers.includes(standardField)) {
        result.headers.push(standardField);
      }
    });
    
    // Special handling for steps
    if (!result.headers.includes('_steps')) {
      result.headers.push('_steps');
    }
    
    // Process each test case into our standardized format
    testCases.forEach((testCase, index) => {
      const record = {};
      
      // Initialize record with empty values for all headers
      result.headers.forEach(header => {
        record[header] = '';
      });
      
      // Process each field in the test case
      Object.keys(testCase).forEach(key => {
        let value = testCase[key];
        let normalizedKey = key;
        
        // Handle attribute keys (remove underscore prefix)
        if (key.startsWith('_')) {
          normalizedKey = key.substring(1);
        }
        
        // Special handling for custom fields
        if (key === 'custom' && typeof value === 'object') {
          Object.keys(value).forEach(customKey => {
            // Extract the value - sometimes it's an object with id/value, sometimes direct
            let customValue = value[customKey];
            if (typeof customValue === 'object' && customValue !== null && 'value' in customValue) {
              customValue = customValue.value;
            }
            
            // Map custom field to appropriate standard field if possible
            let mappedKey = `custom_${customKey}`;
            
            // Check for steps in custom fields
            if (customKey.toLowerCase() === 'steps') {
              record['_steps'] = processSteps(customValue, testCase.custom?.expected || '');
            } 
            // Check for expected results in custom fields
            else if (customKey.toLowerCase() === 'expected' || customKey.toLowerCase() === 'expectedresult') {
              // We'll handle this together with steps
            } 
            // Check for preconditions in custom fields - map to description
            else if (customKey.toLowerCase() === 'preconds' || customKey.toLowerCase() === 'preconditions') {
              record['description'] = customValue;
            } else {
              record[mappedKey] = customValue;
            }
          });
          return; // Skip further processing of the 'custom' key
        }
        
        // Apply field mappings to standardize field names
        for (const [standardField, possibleNames] of Object.entries(fieldMappings)) {
          if (possibleNames.includes(normalizedKey.toLowerCase())) {
            normalizedKey = standardField;
            break;
          }
        }
        
        // Special handling for complex types
        if (Array.isArray(value)) {
          if (normalizedKey.toLowerCase() === 'steps' || normalizedKey.toLowerCase() === 'step') {
            // Format steps as expected by the application
            record['_steps'] = processArraySteps(value);
          } else if (normalizedKey.toLowerCase() === 'tags' || normalizedKey.toLowerCase() === 'tag' ||
                     normalizedKey.toLowerCase() === 'labels' || normalizedKey.toLowerCase() === 'label') {
            // Join tags with the current separator
            record['tags'] = value.map(tag => {
              return typeof tag === 'object' ? (tag.value || tag.name || tag.text || '') : tag;
            }).join(tagsSeparator.value);
          } else {
            // For other arrays, just join with commas
            record[normalizedKey] = value.map(item => {
              return typeof item === 'object' ? JSON.stringify(item) : item;
            }).join(', ');
          }
        } else if (typeof value === 'object' && value !== null) {
          // Handle nested objects
          record[normalizedKey] = JSON.stringify(value);
        } else {
          // For title field, use it as name
          if (normalizedKey.toLowerCase() === 'title') {
            record['name'] = value;
          }
          
          // Simple scalar values
          record[normalizedKey] = value;
        }
      });
      
      // If no name/title found, provide a default one
      if (!record.name) {
        record.name = `Test Case ${index + 1}`;
      }
      
      // If no external ID, generate one
      if (!record.externalId) {
        record.externalId = `TC-${index + 1}`;
      }
      
      result.data.push(record);
    });
    
    return result;
  };

  // Helper function to process steps with expected results
  const processSteps = (stepsText, expectedText) => {
    if (!stepsText) return '';
    
    // Try to detect if steps are numbered
    const lines = stepsText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    const formattedSteps = [];
    
    // If the steps are in a format like "1. Step", "2. Step", etc.
    if (lines.some(line => /^\d+\.\s+/.test(line))) {
      let currentStep = '';
      let stepNumber = 1;
      
      lines.forEach(line => {
        const match = line.match(/^(\d+)\.\s+(.*)/);
        if (match) {
          // If we had a previous step, save it
          if (currentStep) {
            formattedSteps.push(`Step Description: ${currentStep}\nExpected Result: \nTitle: Step ${stepNumber - 1}`);
          }
          currentStep = match[2];
          stepNumber = parseInt(match[1]) + 1;
        } else {
          // Continue the current step
          currentStep += ' ' + line;
        }
      });
      
      // Add the last step
      if (currentStep) {
        formattedSteps.push(`Step Description: ${currentStep}\nExpected Result: ${expectedText || ''}\nTitle: Step ${stepNumber - 1}`);
      }
    } 
    // If steps are in a format like "A. Step", "B. Step", etc.
    else if (lines.some(line => /^[A-Z]\.\s+/.test(line))) {
      let currentStep = '';
      let stepIndex = 0;
      
      lines.forEach(line => {
        const match = line.match(/^([A-Z])\.\s+(.*)/);
        if (match) {
          // If we had a previous step, save it
          if (currentStep) {
            formattedSteps.push(`Step Description: ${currentStep}\nExpected Result: \nTitle: Step ${stepIndex}`);
          }
          currentStep = match[2];
          stepIndex++;
        } else {
          // Continue the current step
          currentStep += ' ' + line;
        }
      });
      
      // Add the last step
      if (currentStep) {
        formattedSteps.push(`Step Description: ${currentStep}\nExpected Result: ${expectedText || ''}\nTitle: Step ${stepIndex}`);
      }
    } 
    // If no clear step structure, treat as a single step
    else {
      formattedSteps.push(`Step Description: ${stepsText}\nExpected Result: ${expectedText || ''}\nTitle: Step 1`);
    }
    
    return formattedSteps.join('\n\n');
  };
  
  // Helper function to process array-based steps
  const processArraySteps = (steps) => {
    return steps.map((step, index) => {
      let description = '';
      let expectedResult = '';
      let title = `Step ${index + 1}`;
      
      // Extract step data based on possible structures
      if (typeof step === 'string') {
        description = step;
      } else if (typeof step === 'object') {
        description = step.description || step.action || step.content || '';
        expectedResult = step.expectedresult || step.expected || step.result || '';
        title = step.title || step.name || title;
      }
      
      return `Step Description: ${description}\nExpected Result: ${expectedResult}\nTitle: ${title}`;
    }).join('\n\n');
  };

  // CSV handling
  const handleFileChange = (event) => {
    const file = event[0];

    if (!file) {
      // Reset file type when no file is selected
      currentFileType.value = null;
      return;
    }

    // Check if file is valid type
    const isCSV = file.name && file.name.toLowerCase().endsWith('.csv');
    const isExcel = file.name && (
      file.name.toLowerCase().endsWith('.xls') || 
      file.name.toLowerCase().endsWith('.xlsx')
    );
    const isXML = file.name && file.name.toLowerCase().endsWith('.xml');

    if (!isCSV && !isExcel && !isXML) {
      showErrorToast(Swal, t('selectValidFile'));
      return;
    }
    
    // Set the current file type
    if (isCSV) {
      currentFileType.value = 'csv';
    } else if (isExcel) {
      currentFileType.value = 'excel';
    } else if (isXML) {
      currentFileType.value = 'xml';
    }
    
    if (isCSV) {
      // Handle CSV files
      const reader = new FileReader();
      reader.readAsText(file);
      reader.onload = () => {
        Papa.parse(file, {
          header: true,
          // delimiter: csvSeparator.value, 
          skipEmptyLines: true,
          complete: (results) => {
            const { data, meta } = results;

            csvFile.value = {
              header: meta.fields,
              body: data
            };
          }
        });
      };
      reader.onerror = () => {
        showErrorToast(Swal, t('errorReadingFile'));
      };
    } else if (isXML) {
      // Handle XML files
      const reader = new FileReader();
      reader.readAsText(file);
      reader.onload = (e) => {
        try {
          const xmlContent = e.target.result;
          // Parse XML with options to convert attributes to properties and preserve arrays
          const parser = new XMLParser({
            ignoreAttributes: false,
            attributeNamePrefix: "_",
            isArray: (name) => {
              // Consider common elements that should be arrays even when single
              return ['testcase', 'test', 'step', 'tag', 'row', 'item'].includes(name.toLowerCase());
            }
          });
          const xmlObj = parser.parse(xmlContent);
          
          // Convert XML structure to CSV-compatible format
          const { headers, data } = convertXmlToCsvFormat(xmlObj);
          
          if (data.length === 0) {
            showErrorToast(Swal, t('emptyXmlFile') || 'Empty XML file or no test cases found');
            return;
          }
          
          csvFile.value = {
            header: headers,
            body: data
          };
        } catch (error) {
          console.error('XML parsing error:', error);
          showErrorToast(Swal, t('errorConvertingXml') || 'Error converting XML file');
        }
      };
      reader.onerror = () => {
        showErrorToast(Swal, t('errorReadingFile'));
      };
    } else {
      // Handle Excel files
      const reader = new FileReader();
      reader.readAsArrayBuffer(file);
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // Process all sheets
          const allSheetData = [];
          workbook.SheetNames.forEach(sheetName => {
            const worksheet = workbook.Sheets[sheetName];
            
            // Convert to CSV format
            const csvData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            
            if (csvData.length > 0) {
              // Extract headers (first row)
              const headers = csvData[0];
              
              // Extract data (remaining rows)
              const records = [];
              for (let i = 1; i < csvData.length; i++) {
                const row = csvData[i];
                const record = {};
                
                // Map each cell to corresponding header
                for (let j = 0; j < headers.length; j++) {
                  if (headers[j]) { // Only process cells with valid headers
                    record[headers[j]] = j < row.length ? row[j] : '';
                  }
                }
                
                records.push(record);
              }
              
              allSheetData.push(...records);
            }
          });
          
          if (allSheetData.length === 0) {
            showErrorToast(Swal, t('emptyExcelFile'));
            return;
          }
          
          // Use headers from the first non-empty sheet
          const firstNonEmptySheet = workbook.SheetNames.find(sheetName => {
            const worksheet = workbook.Sheets[sheetName];
            const csvData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            return csvData.length > 0;
          });
          
          const headers = XLSX.utils.sheet_to_json(workbook.Sheets[firstNonEmptySheet], { header: 1 })[0];
          
          csvFile.value = {
            header: headers,
            body: allSheetData
          };
        } catch (error) {
          console.error('Excel parsing error:', error);
          showErrorToast(Swal, t('errorConvertingExcel'));
        }
      };
      reader.onerror = () => {
        showErrorToast(Swal, t('errorReadingFile'));
      };
    }
  };



  // Navigation
  const goBack = () => {
    if (step.value === 1) {
      router.go(-1);
    } else {
      step.value--;
    }
  };

  const goToNextStep = () => {
    step.value++;
  };

  const handleNextOne = (selectedData) => {
    csvFileData.value = selectedData.csvFile;
    
    // Update CSV configuration only if provided (CSV files only)
    if (selectedData.csvSeparator !== undefined) {
      csvSeparator.value = selectedData.csvSeparator;
    }
    if (selectedData.firstRow !== undefined) {
      firstRow.value = selectedData.firstRow;
    }
    if (selectedData.tagsSeparator !== undefined) {
      tagsSeparator.value = selectedData.tagsSeparator;
    }
    if (selectedData.rowFormat !== undefined) {
      rowFormat.value = selectedData.rowFormat;
    }
    
    // Filter out rows with empty title fields
    if (csvFileData.value && csvFileData.value.body && csvFileData.value.body.length > 0) {
      csvFileData.value.body = csvFileData.value.body.filter(row => {
        // Check if any field in the row is non-null and its string representation has non-zero length after trimming
        return Object.values(row).some(value => value != null && String(value).trim().length > 0);
      });
    }
    
    goToNextStep();
  };

  const handleNextTwo = (mappingRowsData) => {
    mappedRows.value = mappingRowsData;
    persistedMappings.value = mappingRowsData;
    
    // Filter out rows with empty title fields
    if (csvFileData.value && csvFileData.value.body && csvFileData.value.body.length > 0) {
      // Find the title field mapping
      const titleMapping = mappingRowsData.mappings.find(mapping => mapping.biddingValue === 'name');
      
      if (titleMapping && titleMapping.csvHeader) {
        csvFileData.value.body = csvFileData.value.body.filter(row => {
          const titleValue = row[titleMapping.csvHeader];
          return titleValue != null && String(titleValue).trim().length > 0;
        });
      }
    }
    
    goToNextStep();
  };

  // Data processing
  const getKeyValue = (item, key, reference) => {
    const referenceKey = reference.mappings.find(ref => ref.biddingValue === key)?.csvHeader;
    return item[referenceKey];
  };

  // Process tags using the selected separator
  const processTags = (tagString) => {
    if (!tagString) return [];
    return tagString.split(tagsSeparator.value).map(tag => tag.trim()).filter(tag => tag);
  };

  const getStepsValue = (item, key, reference) => {
    const referenceKey = reference.mappings.find(ref => ref.biddingValue === key)?.csvHeader;
    const stepsString = item[referenceKey];
    
    if (!stepsString) {
      return [];
    }

    const stepObjects = [];
    
    // Handle multi-row test cases differently if selected
    if (rowFormat.value === 'multi') {
      // Implementation for multi-row test cases would go here
      // This would need more specific requirements about the multi-row format
    } else {
      // Default single row implementation
    const stepParts = stepsString.split(/\n{2,}/); // Split on multiple newlines

    stepParts.forEach(part => {
      const [descriptionLine, expectedResultLine, titleLine] = part.split("\n");
      
      const step = {
        description: descriptionLine ? descriptionLine.replace(/^Step Description:\s*/, '') : '-',
        expectedResult: expectedResultLine ? expectedResultLine.replace(/^Expected Result:\s*/, '') : '-',
        title: titleLine ? titleLine.replace(/^Title:\s*/, '') : '-'
      };

      stepObjects.push(step);
    });
    }
    
    return stepObjects.length ? stepObjects : [];
  };

  // Add template fetching function
  const fetchTemplates = async (handle, key) => {
    try {
      const response = await templateService.getTemplates(
        handle,
        key,
        'per_page=9999'
      );
      templates.value = response.data.templates || [];

      // Process template fields - updated to handle the proper structure
      templates.value.forEach(template => {
        if (template.customFields?.templateFields?.length) {
          const fields = template.customFields.templateFields.map(field => ({
            id: field.id || field.name, // Use name as ID if no ID exists
            name: field.name,
            icon: getFieldIcon(field.dataType || 'text')
          }));
          templateFields.value[template.uid] = fields;
        } else if (template.fields?.length) {
          // Fallback for old structure if it exists
          const fields = template.fields.map(field => ({
            id: field.field_id || field.id || field.name, // Use name as ID if no ID exists
            name: field.name || field.label,
            icon: getFieldIcon(field.type)
          }));
          templateFields.value[template.uid] = fields;
        } else {
          templateFields.value[template.uid] = [];
        }
      });
      
      return templates.value;
    } catch (error) {
      showErrorToast(Swal, 'fetchError', { item: 'Templates' }, error?.response?.data);
      return [];
    }
  };

  // Helper to get field icon based on field type
  const getFieldIcon = (fieldType) => {
    const iconMap = {
      'text': 'mdi-format-text',
      'textarea': 'mdi-text-box',
      'number': 'mdi-numeric',
      'date': 'mdi-calendar',
      'checkbox': 'mdi-checkbox-marked',
      'dropdown': 'mdi-menu-down',
      'steps': 'mdi-format-list-numbered',
      'expected_result': 'mdi-check-circle-outline',
      'multi': 'mdi-text-box-multiple',
      'single': 'mdi-text-box-outline'
    };
    return iconMap[fieldType] || 'mdi-text-box';
  };

  // Add the function to delete a CSV field 
  const deleteCsvField = async (field) => {
    if (!csvFileData.value) return;

    // Remove the field from the header
    const headerIndex = csvFileData.value.header.indexOf(field);
    if (headerIndex !== -1) {
      csvFileData.value.header.splice(headerIndex, 1);
    }

    // Remove the field from all data objects in the body
    if (csvFileData.value.body && csvFileData.value.body.length) {
      csvFileData.value.body = csvFileData.value.body.map(item => {
        const newItem = { ...item };
        delete newItem[field];
        return newItem;
      });
    }

    // Check if there are any mappings in persistedMappings that need to be updated
    if (persistedMappings.value) {
      // Remove standard mappings for this field
      if (persistedMappings.value.mappings) {
        persistedMappings.value.mappings = persistedMappings.value.mappings.filter(
          mapping => mapping.csvHeader !== field
        );
      }

      // Remove template field mappings for this field
      if (persistedMappings.value.templateFields) {
        Object.keys(persistedMappings.value.templateFields).forEach(fieldId => {
          if (persistedMappings.value.templateFields[fieldId].csvHeader === field) {
            delete persistedMappings.value.templateFields[fieldId];
          }
        });
      }
    }

    // Return the updated headers for components to sync with
    return csvFileData.value.header;
  };

  // Function to prepare test cases for CaseList component
  const prepareTestCases = () => {
    if (!csvFileData.value || !csvFileData.value.body || csvFileData.value.body.length === 0) {
      casesToImport.value = [];
      return Promise.resolve([]);
    }
    
    parseLoading.value = true;
    processProgress.value = 0;
    processedItems.value = 0;
    totalItems.value = csvFileData.value.body.length;
    
    // Get metadata about the selected template
    let selectedTemplateName = 'Default Template';
    let selectedTemplateId = selectedTemplateDropdown.value;
    
    if (selectedTemplateId) {
      const template = templates.value.find(t => t.uid === selectedTemplateId);
      if (template) {
        selectedTemplateName = template.name;
      }
    }
    
    // Create a new worker or reuse existing
    if (!worker.value) {
      worker.value = new TestCaseWorker();
    }
    
    // Return a promise that resolves when the worker completes
    return new Promise((resolve, reject) => {
      
      // Set up the event listener for when the worker completes
      worker.value.onmessage = function(e) {
        const { type, data, progress, processed, total, error } = e.data;
        
        if (type === 'progress') {
          // Update progress information
          processProgress.value = progress;
          processedItems.value = processed;
          totalItems.value = total;
        } else if (type === 'complete') {
          // Processing is complete
          casesToImport.value = data;
          parseLoading.value = false;
          processProgress.value = 100;
          resolve(data);
        } else if (type === 'error') {
        
          // Display a user-friendly error message
          showErrorToast(
            Swal, 
            'csvImportWorkerError', 
            { context: error.context?.context || 'data processing' }, 
            { message: error.message }
          );
          
          if (!data || (Array.isArray(data) && data.length === 0)) {
            parseLoading.value = false;
            reject(new Error(error.message || 'Error processing CSV data'));
          }
        }
      };
      
      worker.value.onerror = function(e) {
        console.error('Worker fatal error:', e);
        parseLoading.value = false;
        showErrorToast(Swal, 'csvImportWorkerFatalError', {}, { message: e.message });
        reject(new Error(e.message || 'Worker terminated unexpectedly'));
      };
      
      // Post the data to the worker
      worker.value.postMessage({
        csvFileData: csvFileData.value,
        mappedRows: mappedRows.value,
        route: { params: route.params },
        selectedTemplateId,
        selectedTemplateName,
        selectedFolder: selectedFolder.value,
        tagsSeparator: tagsSeparator.value
      });
    });
  };

  const goToStepFour = async () => {
    try {
      // Wait for the worker to complete processing
      await prepareTestCases();
      
      // Check if all cases have folder assignments
      const allCasesAssigned = casesToImport.value.length > 0 && 
        casesToImport.value.every(testCase => testCase._hasFolderAssignment);
      
      if (allCasesAssigned) {
        // All cases have folder assignments, upload directly and redirect
        const routeParams = {
          params: {
            handle: route.params.handle,
            key: route.params.key
          }
        };
        
        submissionFolderName.value = t('assignedFolders');
        
        // Start submission process
        submissionInProgress.value = true;
        
        try {
          // Upload all pre-assigned cases
          await handleSubmit(routeParams, casesToImport.value, null);
          
          // After successful upload, redirect to repository
          gotoRepository();
        } catch (error) {
          console.error('Error uploading pre-assigned cases:', error);
          showErrorToast(Swal, 'processingError', { item: 'test cases' }, error?.response?.data);
        } finally {
          submissionInProgress.value = false;
        }
      } else {
        // Some cases need manual assignment, proceed to step 4
        step.value = 4;
      }
    } catch (error) {
      parseLoading.value = false;
      showErrorToast(Swal, 'processingError', { item: 'spreadsheet data' });
    }
  };

  const gotoRepository = () => {
    router.push({ name: 'Cases' });
    resetAllFields();
  }

  // Clean up worker when component is unmounted
  const cleanupWorker = () => {
    if (worker.value) {
      worker.value.terminate();
      worker.value = null;
    }
  };

  const resetAllFields = () => {
    // Reset all ref values to their defaults
    step.value = 1;
    csvFile.value = null;
    csvFileData.value = undefined;
    selectedFolder.value = null;
    folders.value = [];
    mappedRows.value = undefined;
    createBtnLoading.value = false;
    folderCasesMap.value = {};
    
    // Reset CSV configuration
    csvSeparator.value = ';';
    firstRow.value = 1;
    tagsSeparator.value = ' ';
    rowFormat.value = 'single';
    
    // Reset template-related state
    templates.value = [];
    templateFields.value = {};
    selectedTemplateDropdown.value = '';
    persistedMappings.value = null;
    
    // Reset import and progress tracking
    casesToImport.value = [];
    parseLoading.value = false;
    processProgress.value = 0;
    processedItems.value = 0;
    totalItems.value = 0;
    
    // Reset file type
    currentFileType.value = null;
    
    // Reset submission progress
    submissionInProgress.value = false;
    submissionProgress.value = 0;
    totalSubmissionItems.value = 0;
    submissionFolderName.value = '';
    
    // Clean up worker if it exists
    cleanupWorker();
  };
  
  // Function to upload only pre-assigned test cases
  const uploadPreAssignedCases = async (route) => {
    // Filter cases that have folder assignments
    const casesWithFolderAssignments = casesToImport.value.filter(testCase => testCase._hasFolderAssignment);
    
    if (!casesWithFolderAssignments.length) {
      return { success: false, message: 'No pre-assigned test cases to upload' };
    }
    
    try {
      // Submit the pre-assigned cases without a parent folder (each case has its own folder assignment)
      await handleSubmit(route, casesWithFolderAssignments, null);
      
      // After successful upload, remove these cases from the casesToImport
      casesToImport.value = casesToImport.value.filter(testCase => !testCase._hasFolderAssignment);
      
      return { 
        success: true, 
        count: casesWithFolderAssignments.length,
        message: `Successfully uploaded ${casesWithFolderAssignments.length} pre-assigned test case(s)` 
      };
    } catch (error) {
      console.error('Error uploading pre-assigned cases:', error);
      return { 
        success: false, 
        message: error.message || 'Error uploading pre-assigned test cases',
        error
      };
    }
  };

  return {
    // State
    step,
    goToStepFour,
    csvFile,
    csvFileData,
    selectedFolder,
    folders,
    mappedRows,
    createBtnLoading,
    // CSV configuration
    csvSeparator,
    firstRow,
    tagsSeparator,
    rowFormat,
    // Add templates to the returned object
    templates,
    templateFields,
    selectedTemplateDropdown,
    // Add persistedMappings to the returned object
    persistedMappings,
    // Add casesToImport to the returned object
    casesToImport,
    // Add progress tracking
    processProgress,
    processedItems,
    totalItems,
    // Add submission progress
    submissionInProgress,
    submissionProgress,
    totalSubmissionItems,
    submissionFolderName,
    // Add file type tracking
    currentFileType,
    
    // Methods
    handleFileChange,
    gotoRepository,
    // Add template fetching function to exposed methods
    fetchTemplates,
    goBack,
    handleNextOne,
    handleNextTwo,
    handleSubmit,
    getKeyValue,
    getStepsValue,
    processTags,
    prepareTestCases,
    getFieldIcon,
    parseLoading,
    cleanupWorker,
    folderCasesMap,
    // Add the new deleteCsvField method
    deleteCsvField,
    resetAllFields,
    // Add the new uploadPreAssignedCases function
    uploadPreAssignedCases,
  };
} 

export const handleSubmit = async (route, result, parentId, folders) => {
    // Create hashMap to track cases per folder


    folderCasesMap.value[parentId] = (folderCasesMap.value[parentId] || 0) + result.length;
    folderCasesMap.value = {...folderCasesMap.value}

    const projectHandle = route.params.handle;
    const projectKey = route.params.key;
    const caseService = makeCasesService($api); 
    const batchSize = 1000;
    let successCount = 0;
    let resultHolder = [...result];
    
    if (parentId) {
        let folderName = 'selected folder';
        if (folders && folders.length > 0) {
          const folderInfo = folders.find(folder => folder.uid === parentId);
            if (folderInfo) {
                folderName = folderInfo.name;
            }
        }
        submissionFolderName.value = folderName;
    } else {
        // Check if we have cases with pre-assigned folders
        const hasPreAssignedFolders = result.some(item => item._hasFolderAssignment && item.parentName);
        if (hasPreAssignedFolders) {
            submissionFolderName.value = t('assignedFolders');
        } else {
            submissionFolderName.value = t('rootFolder');
        }
    }
    
    // Set submission progress tracking
    submissionInProgress.value = true;
    submissionProgress.value = 0;
    totalSubmissionItems.value = resultHolder.length;
    createBtnLoading.value = true;

    while (resultHolder.length > 0) {
        const currentBatch = resultHolder.slice(0, batchSize).map(item => {
          if (item._hasFolderAssignment && item.parentName) {
            const cleanItem = { ...item };
            delete cleanItem._hasFolderAssignment;
            return cleanItem;
          } else {
            return { ...item, parentId };
          }
        });
        
        try {
            await caseService.createTestCases(projectHandle, projectKey, currentBatch);
             successCount = successCount + currentBatch.length;
            
            // Remove the successfully imported cases from casesToImport by their IDs
            const successfulIds = currentBatch.map(item => item.id);
            casesToImport.value = casesToImport.value.filter(item => !successfulIds.includes(item.id));
            resultHolder = resultHolder.filter(item => !successfulIds.includes(item.id));
            
            // Update progress
            submissionProgress.value = Math.min(100, Math.round(((totalSubmissionItems.value - resultHolder.length) / totalSubmissionItems.value) * 100));
        } catch (error) {
            console.error('Error creating test cases batch:', error);
            console.error('Batch size:', currentBatch.length);
            console.error('Batch contains pre-assigned folders:', currentBatch.some(item => item.parentName));
            // Keep the failed cases in the array to retry
        }
    }
    
  
        // showSuccessToast(Swal, 'createSuccess', { item: 'Test case' });

  setTimeout(() =>
  {
      createBtnLoading.value = false;
    submissionInProgress.value = false;
  }, 500)
      

};