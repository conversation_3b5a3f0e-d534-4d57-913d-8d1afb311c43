import { ref, computed, watch } from 'vue';
import { saveAs } from "file-saver";
import <PERSON> from "papaparse/papaparse.js";
import * as XLSX from "xlsx";
import { useRoute } from 'vue-router/composables';
import { useColorPreferences } from '@/composables/utils/colorPreferences';
import Swal from 'sweetalert2';
import {  $api } from '@/main';

export function useTestCasesExport(props, { emit }) {
  const { getPriorities } = useColorPreferences($api, Swal);
  const route = useRoute();
  const priorities = ref([]);
  priorities.value = getPriorities('testCase').filter((element) => !element.archived);

  function getPriorityName(value, prioritiesList) {
    const priority = prioritiesList.find((p) => p.id === value);
    return priority ? priority.name : '';
  }

  const selectedFields = ref([]);
  const selectedItem = ref('csv');
  const exportTypes = ref([
    { title: "CSV", value: "csv" },
    { title: "Excel", value: "excel" },
  ]);
  const isOpenStatus = ref(props.isOpen);
  const includeCustomFields = ref(false);
  const fields = ref([
    { title: "ID", value: "uid", isSelected: false },
    { title: "Title", value: "title", isSelected: false },
    { title: "Priority", value: "priority", isSelected: false },
    { title: "Steps", value: "steps", isSelected: false },
  ]);
  

  const selectAllFields = computed(() => {
    return fields.value.every(field => field.isSelected);
  });
  
  const selectSomeFields = computed(() => {
    const selectedCount = fields.value.filter(field => field.isSelected).length;
    return selectedCount > 0 && selectedCount < fields.value.length;
  });
  

  watch(() => props.isOpen, (newVal) => {
    isOpenStatus.value = newVal;
  });
  
  watch(fields, () => {
    updateSelectedFields();
  }, { deep: true });
  

  function clickOutside() {
    emit("closeDialog");
  }
  
  function toggle() {
    const allSelected = selectAllFields.value;
    fields.value.forEach(field => {
      field.isSelected = !allSelected;
    });
    updateSelectedFields();
  }
  
  function updateSelectedFields() {
    selectedFields.value = fields.value
      .filter(field => field.isSelected)
      .map(field => field.value);
  }
  
  function exportData() {
    const data = prepareData();
    if (selectedItem.value === "csv") {
      exportToCSV(data);
    } else if (selectedItem.value === "excel") {
      exportToExcel(data);
    }
  }
  
  function prepareData() {
    return props.selectedRows;
  }
  
  function exportToCSV(data) {
    const headers = getHeaders();
    const csvData = data.map((row) => {
      const newRow = {};
      headers.forEach((header) => {
        switch (header) {
          case "priority":
            newRow[header] = getPriorityName(row?.priority, priorities.value) || 'N/A';
            break;
          case "title":
            newRow[header] = row.name || 'N/A';
            break;
          case "uid":
            newRow[header] = `${route.params.key}-${row.testCaseRef}`;
            break;
          case "steps":
            if (Array.isArray(row.steps) && row.steps.length > 0) {
              newRow[header] = row.steps.map((step, index) => {
                const stepDesc = step.description || 'No description';
                const stepResult = step.expectedResult || 'No expected result';
                return `${index + 1}. Description: ${stepDesc} | Expected Result: ${stepResult}`;
              }).join('\n');
            } else {
              newRow[header] = 'N/A';
            }
            break;
          default:
            newRow[header] = row[header] || "N/A";
        }
      });

      if (includeCustomFields.value && row.customFields?.templateFields) {
        Object.values(row.customFields.templateFields).forEach(customField => {
          if (customField && typeof customField === 'object' && customField.name && customField.value !== undefined) {
            const key = customField.name;
            let value = customField.value;

            if (value === null) {
              value = 'N/A';
            } else if (typeof value === 'object') {
              value = JSON.stringify(value);
            } else if (typeof value === 'boolean') {
              value = value ? 'true' : 'false';
            }

            newRow[key] = value;
          }
        });
      }

      return newRow;
    });


    const csv = Papa.unparse(csvData);
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "test_cases.csv";
    link.style.display = "none";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url); 
  }
  
  function exportToExcel(data) {
    const headers = getHeaders();
    const excelData = data.map((row) => {
      const newRow = {};
      headers.forEach((header) => {
        switch (header) {
          case "priority":
            newRow[header] = getPriorityName(row?.priority, priorities.value) || 'N/A';
            break;
          case "title":
            newRow[header] = row.name || 'N/A';
            break;
          case "uid":
            newRow[header] = `${route.params.key}-${row.testCaseRef}`;
            break;
          case "steps":
            if (Array.isArray(row.steps) && row.steps.length > 0) {
              newRow[header] = row.steps.map((step, index) => {
                const stepDesc = step.description || 'No description';
                const stepResult = step.expectedResult || 'No expected result';
                return `${index + 1}. Description: ${stepDesc} | Expected Result: ${stepResult}`;
              }).join('\n');
            } else {
              newRow[header] = 'N/A';
            }
            break;
          default:
            newRow[header] = row[header] || "N/A";
        }
      });


      if (includeCustomFields.value && row.customFields?.templateFields) {
        Object.values(row.customFields.templateFields).forEach(customField => {
          if (customField && typeof customField === 'object' && customField.name && customField.value !== undefined) {
            const key = customField.name;
            let value = customField.value;

            if (value === null) {
              value = 'N/A';
            } else if (typeof value === 'object') {
              value = JSON.stringify(value);
            } else if (typeof value === 'boolean') {
              value = value ? 'true' : 'false';
            }

            newRow[key] = value;
          }
        });
      }

      return newRow;
    });


    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Test Cases");
    const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
    const blob = new Blob([excelBuffer], {
      type:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
    });
    saveAs(blob, "test_cases.xlsx");
  }
  
  function getHeaders() {
    return selectedFields.value.map((item) => {
      if (typeof item === "string") {
        return item;
      }
      return item.value;
    });
  }
  
  function getFieldValue(header) {
    const field = fields.value.find((f) => f.title === header);
    return field ? field.value : "";
  }


  return {
    selectedFields,
    selectedItem,
    exportTypes,
    isOpenStatus,
    fields,
    selectAllFields,
    selectSomeFields,
    clickOutside,
    toggle,
    exportData,
    includeCustomFields,
    getFieldValue,
  };
} 