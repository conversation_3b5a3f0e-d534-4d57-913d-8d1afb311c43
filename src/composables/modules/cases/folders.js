import { ref } from 'vue';
import makeFoldersService from '@/services/api/folder';
import { showErrorToast } from '@/composables/utils/toast';
import { t } from '@/i18n';
import Swal from 'sweetalert2';


const folders = ref([]);
  
export const useFolders = () => {

  const isLoading = ref(false);



  /**
   * Recursively builds folder tree by fetching children for each folder
   * @param {Array} folders - Array of folders to process
   * @param {string} handle - Organization/user handle
   * @param {string} projectKey - Project key
   * @param {Object} folderService - Folder service instance
   * @param {number} maxDepth - Maximum depth to build (default: 1)
   * @param {number} currentDepth - Current depth level (default: 0)
   */
  const buildFolderTree = async (folders, handle, projectKey, folderService, maxDepth = 1, currentDepth = 0) => {
    // Stop recursion if we've reached the maximum depth
    if (currentDepth >= maxDepth) {
      return;
    }

    for (const folder of folders) {
      if (folder.children && folder.children.length > 0) {
        // For each child folder, get its children recursively
        for (const child of folder.children) {
          try {
            const childResponse = await folderService.getFolder(handle, projectKey, child.uid, true);
            child.children = childResponse.data.children || [];
            
            // Recursively build children of this child only if we haven't reached max depth
            if (child.children.length > 0 && currentDepth + 1 < maxDepth) {
              await buildFolderTree([child], handle, projectKey, folderService, maxDepth, currentDepth + 1);
            }
          } catch (error) {
            showErrorToast(Swal, t('test_folder.failed_to_fetch_children'), { folderUid: child.uid }, error?.response?.data);
            child.children = [];
          }
        }
      }
    }
  };

  /**
   * Fetches project folders and builds complete tree structure
   * @param {string} handle - Organization/user handle
   * @param {string} projectKey - Project key
   * @param {Object} options - Additional options
   * @param {string} options.testRunId - Test run ID for execution folders (legacy approach)
   * @param {string} options.runId - Test run ID for run folders (new optimized approach)
   * @param {Object} options.api - API instance
   * @param {number} options.maxDepth - Maximum depth to build (default: 1 for performance)
   * @returns {Promise<Array>} Complete folder tree
   */
  const getFolders = async (handle, projectKey, options = {}) => {
    const { testRunId, runId, api, sortBy, sortOrder, orderBy, ...apiParams } = options;
    const folderService = makeFoldersService(api);
    
    try {
      isLoading.value = true;
      
      let response;
      let rootFolders;
      
             if (runId) {
         // Use the new optimized endpoint for run folders (from testRunFolders table)
         // This endpoint returns only folders that belong to the specific test run
         response = await folderService.getRunFolders(handle, projectKey, runId);
         const runFolders = response.data.folders || [];
         
         // Build the complete folder hierarchy from the flat list
         // Since these are run-specific folders, we already have all the data
         const buildFolderHierarchy = (folders) => {
           const folderMap = new Map();
           const rootFolders = [];
           
           // First pass: create folder map
           folders.forEach(folder => {
             folderMap.set(folder.uid, { ...folder, children: [] });
           });
           
           // Second pass: build hierarchy
           folders.forEach(folder => {
             if (folder.parentUid && folderMap.has(folder.parentUid)) {
               // Add to parent's children
               folderMap.get(folder.parentUid).children.push(folderMap.get(folder.uid));
             } else {
               // Root folder (no parent)
               rootFolders.push(folderMap.get(folder.uid));
             }
           });
           
           return rootFolders;
         };
         
         folders.value = buildFolderHierarchy(runFolders);
         return folders.value;
       } else if (testRunId) {
        // Legacy approach for backwards compatibility
        response = await folderService.getProjectFoldersByTestRun(handle, projectKey, testRunId);
        rootFolders = response.data.folders;
      } else {
        // Prepare sorting parameters for API call
        const folderParams = { ...apiParams };
        if (orderBy) {
          folderParams.orderBy = orderBy;
        } else if (sortBy) {
          folderParams.sortBy = sortBy;
          if (sortOrder) {
            folderParams.sortOrder = sortOrder;
          }
        }
        
        response = await folderService.getProjectFolders(handle, projectKey, {...folderParams, returnType: 'root'});
        rootFolders = response.data.folders;
      }
      
      if (rootFolders && rootFolders.length > 0) {
        folders.value = rootFolders.map(folder => ({
          ...folder,
          ...(folder.customFields && folder.customFields.isParent ? { children: [] } : {}) // Only add children if isParent

        }));
        
        return folders.value ;
      } else {
        folders.value = [];
        return [];
      }
    } catch (error) {
      showErrorToast(Swal, t("test_folder.refresh_failed"), {}, error?.response?.data);
      folders.value = [];
      return [];
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Flattens hierarchical folder tree into a flat array of all folders
   * @param {Array} folderData - Folder tree data
   * @returns {Array} Array of all folders with simplified structure (name, uid)
   */
  const flattenFolderTree = (folderData) => {
    let nodes = [];
    
    const traverse = (folders) => {
      for (let folder of folders) {
        let parent = {
          name: folder.name,
          uid: folder.uid,
        };
        
        if (!folder.children || folder.children.length < 1) {
          nodes.push(parent);
        } else {
          nodes.push(parent);
          traverse(folder.children);
        }
      }
    };
    
    if (folderData && Array.isArray(folderData)) {
      traverse(folderData);
    }
    
    return nodes;
  };

  /**
   * Loads children for a specific folder dynamically (for Vuetify treeview load-children)
   * @param {Object} item - The folder item being expanded
   * @param {string} handle - Organization/user handle
   * @param {string} projectKey - Project key
   * @param {Object} api - API instance
   * @returns {Promise<void>} Pushes children into item.children array
   */
  const loadFolderChildren = async (item, handle, projectKey, api) => {
    const folderService = makeFoldersService(api);
    
    try {
      const response = await folderService.getFolder(handle, projectKey, item.uid, true);
      const children = response.data.children || [];
      
      // Prepare children with empty children arrays for further dynamic loading
      const preparedChildren = children.map(child => ({
        ...child,
        ...(child.customFields && child.customFields.isParent ? { children: [] } : {}) // Only add children if isParent
      }));
      
      // Push children into the item's children array (Vuetify pattern)
      item.children.push(...preparedChildren);
    } catch (error) {
      showErrorToast(Swal, t('test_folder.failed_to_fetch_children'), { folderUid: item.uid }, error?.response?.data);
    }
  };

  /**
   * Preserves tree state during refresh by maintaining expanded folders and their children
   * @param {Array} oldTree - Current tree with potentially loaded children
   * @param {Array} newTree - Fresh tree from server
   * @returns {Array} New tree with preserved expanded state
   */
  const preserveTreeState = (oldTree, newTree) => {
    if (!oldTree || !newTree) return newTree;
    
    const preserveNode = (oldNode, newNode) => {
      if (!oldNode || !newNode || oldNode.uid !== newNode.uid) return newNode;
      
      // Preserve children if they were dynamically loaded
      if (oldNode.children && oldNode.children.length > 0) {
        newNode.children = oldNode.children.map(oldChild => {
          const newChild = newTree.find(n => n.uid === oldChild.uid);
          return newChild ? preserveNode(oldChild, newChild) : oldChild;
        });
      }
      
      return newNode;
    };
    
    return newTree.map(newNode => {
      const oldNode = oldTree.find(n => n.uid === newNode.uid);
      return oldNode ? preserveNode(oldNode, newNode) : newNode;
    });
  };

  /**
   * Refreshes folder tree while preserving expanded state
   * @param {string} handle - Organization/user handle
   * @param {string} projectKey - Project key
   * @param {Object} options - Additional options
   * @param {Array} options.currentTree - Current tree state to preserve
   */
  const refreshFoldersWithState = async (handle, projectKey, options = {}) => {
    const { currentTree } = options;
    const freshTree = await getFolders(handle, projectKey, options);
    
    if (currentTree && currentTree.length > 0) {
      return preserveTreeState(currentTree, freshTree);
    }
    
    return freshTree;
  };

  /**
   * Refreshes folder tree (legacy function for backward compatibility)
   * @param {string} handle - Organization/user handle
   * @param {string} projectKey - Project key
   * @param {Object} options - Additional options
   */
  const refreshFolders = async (handle, projectKey, options = {}) => {
    return await getFolders(handle, projectKey, options);
  };

  return {
    folders,
    isLoading,
    getFolders,
    buildFolderTree,
    flattenFolderTree,
    refreshFoldersWithState,
    refreshFolders,
    loadFolderChildren,
  };
}; 
