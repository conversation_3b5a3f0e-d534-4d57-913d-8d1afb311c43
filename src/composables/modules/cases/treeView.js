import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router/composables';
import { useStore, $api } from '@/main';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import { calculateFractionalOrder, checkForRebalance, isDescendant, getRebalanceWindow, rebalanceOrder } from '@/composables/utils/orderingFunctions';
import makeFoldersService from '@/services/api/folder';
import makeCasesService from '@/services/api/case';
import uid from 'tiny-uid';
import cloneDeep from 'lodash/cloneDeep';
import { useProjectStatus } from '@/composables/utils/projectStatus';
import { t } from '@/i18n'
import Swal from 'sweetalert2';
import { tableSelectedCases } from '@/composables/modules/cases/list';
import { handleSubmit, casesToImport } from '@/composables/modules/cases/csvImport';
import { useFolders } from '@/composables/modules/cases/folders';
import { debounce } from 'lodash';
import Vue from 'vue';

  // Data properties
  const folderDeleteDialog = ref(false);
  const folderDeleteName = ref('');
  const folderDeleteItem = ref(null);
  const openedFolders = ref([]);
  const tree = ref([]);
  const editingFolder = ref(null);
  const selectedItem = ref(null);
  const createdFoldersCache = ref({});
  const currentEditableItem = ref(null);

  

  const selectedItemsArray = ref([]);
  const rootFolderUID = ref('');
  const currentFolderUID = ref('');
  const isFolderLoadingState = ref(false);
  const originalFolderName = ref(null);
  const isScrolled = ref(false);
  const showMenu = ref(false);
  const menuX = ref(0);
  const menuY = ref(0);
  const selectedContextItem = ref(null);
  const dragFromFolder = ref(null);
  const dragToFolder = ref(null);
const skeletonLoaderState = ref(false);
  
export const useTreeView = (props, { emit }) => {
  const route = useRoute();
  const store = useStore();
  const { loadFolderChildren } = useFolders();


  
    // Computed properties
    
    const { isProjectArchived } = useProjectStatus();

  const open = computed({
    get: () => {
      return openedFolders.value.length
        ? openedFolders.value
        : getParentFolderKeys(props.items, { uid: parseInt(props.selectedFolderUid) });
    },
    set: (newValue) => {
      openedFolders.value = newValue;
    }
  });

  const getFolderByUid = computed(() => {
    const folderUids = []
    const traverse = (folders) => {
      if (!folders || !Array.isArray(folders)) return;
      folders.forEach((folder) => {
        folderUids.push(folder.uid);
        traverse(folder.children);
      });
    };
    traverse(props.items);
    return folderUids;
  });
  
  const isOpenedFoldersNotEmpty = computed(() => {
    return openedFolders.value.length > 0;
  });
  
  const isCollapsed = computed(() => {
    // TODO - passing this as a number instead of a boolean is a huge hack
    //        to get around:
    // Property or method "on"/"attrs" is not defined on the instance...
    return props.collapsed ? true : false;
  });
  
  const selectedProjectByKey = computed(() => {
    return route.params.key;
  });
  
  const selectedOrganization = computed(() => {
    return store.state.currentAccount;
  });
  
  const rootFolder = computed(() => {
    return props.items.find((folder) => folder.parentUid === null);
  });
  
  const sortedFolders = computed(() => {
    return sortFoldersByUpdatedAt(props.items);
  });
  
  const editedItems = computed(() => {
    if (props.executionFolders?.length && props.items?.length) {
      const filterFolders = (folders, executionFolders) => {
        return folders.map(folder => {
          // Create a deep copy to avoid mutating original data
          const folderCopy = cloneDeep(folder);
          
          const processFolder = (folder) => {
            if (folder.children && Array.isArray(folder.children)) {
              folder.children = folder.children.map(child => processFolder(child)).filter(Boolean);
            }

            if (executionFolders.includes(folder.uid) || (folder.children && folder.children.length > 0)) {
              return folder;
            }

            return null;
          };
          
          return processFolder(folderCopy);
        }).filter(Boolean);
      };
      
      const filteredFolders = filterFolders(props.items, props.executionFolders);
      return addDepth(filteredFolders);
    }

    return addDepth(sortedFolders.value);
  });
  
  const isEditedItemsHasChildren = computed(() => {
    return editedItems.value.some((item) => item?.children?.length > 0);
  });
  
 
  
  const unauthorizedToast = computed(() => {
    // This would come from a mixin, but we'll handle it here
    return () => showErrorToast(Swal, 'unauthorized');
  });
  
  // Methods
  const doesFolderExist = computed(() => {
    return getFolderByUid.value.includes(Number(props.selectedFolderUid));
  });

  watch(getFolderByUid, (newVal) => {
    if (route.name === 'Cases') {
      if (newVal.length > 0 && !doesFolderExist.value) {
        selectedItem.value = rootFolder.value.uid;
      }
    }
  });

  function getFolderByEvent(event) {
    const folders = editedItems.value;
    const folderUid = event.target?.childNodes[event?.target?.childNodes?.length - 1]?.childNodes[1]?.childNodes[0]?.id;

    const searchFolder = (items) => {
      for (let item of items) {
        if (item.uid === Number(folderUid)) {
          return item;
        }

        // If item has children, search recursively
        if (item.children && item.children.length > 0) {
          const found = searchFolder(item.children);
          if (found) {
            return found;
          }
        }
      }
      return null;
    };
    return searchFolder(folders);
  }
  
  function onScroll() {
    isScrolled.value = window.pageYOffset > 180;
  }
  
  function flattenFolders(folders) {
    const result = [];

    function traverse(folders) {
      if (!Array.isArray(folders) || folders.length === 0) {
        return;
      }

      folders.forEach((folder) => {
        result.push(folder.uid);
        folder.children = Array.isArray(folder.children) ? folder.children : [];

        if (folder.children.length > 0) {
          traverse(folder.children);
        }
      });
    }

    traverse(folders);
    return result;
  }

  function flattenFoldersStructure(folders) {
    const result = [];

    function traverse(folders) {
      if (!Array.isArray(folders) || folders.length === 0) {
        return;
      }

      folders.forEach((folder) => {
        result.push(folder);
        folder.children = Array.isArray(folder.children) ? folder.children : [];

        if (folder.children.length > 0) {
          traverse(folder.children);
        }
      });
    }

    traverse(folders);
    return result;
  }


  
  // Folders are now sorted on backend, so just preserve structure without sorting
  function sortFoldersByUpdatedAt(folders) {
    return folders.map((folder) => {
      const folderCopy = cloneDeep(folder);

      if (folderCopy.children && folderCopy.children.length > 0) {
        folderCopy.children = sortFoldersByUpdatedAt(folderCopy.children);
        // Removed client-side sorting - folders are sorted by backend
      }

      return folderCopy;
    });
  }
  
  function addDepth(folders, depth = 0) {
    return folders.map((folder) => {
      folder.depth = depth;
      if (folder.children && Array.isArray(folder.children)) {
        folder.children = addDepth(folder.children, depth + 1);
      }
      return folder;
    });
  }
  
  function getDepth(folders, uid) {
    const findDepth = (folders, uid, depth = 0) => {
      for (const folder of folders) {
        if (folder.uid === uid) {
          return depth;
        }
        if (folder.children && folder.children.length > 0) {
          const childDepth = findDepth(folder.children, uid, depth + 1);
          if (childDepth !== -1) {
            return childDepth;
          }
        }
      }
      return -1;
    };

    return findDepth(folders, uid);
  }
  
  function updateSelected(uid) {
    selectedItem.value = props.items.find((item) => item.uid === uid);
  }
  
  function makeEditable(selecteditem) {
    if (!props.writeEntity) {
      unauthorizedToast.value();
      return;
    }
    if (isProjectArchived.value) return;
    originalFolderName.value = selecteditem.name;
    if (currentEditableItem.value && currentEditableItem.value !== selecteditem) {
      currentEditableItem.value.editable = false;
    }
    selecteditem.editable = true;
    editingFolder.value = selecteditem;
  }
  

  
  function handleCloseDialog() {
    folderDeleteDialog.value = false;
    folderDeleteItem.value = null;
  }
  
  function handleOpenDialog(item) {
    folderDeleteName.value = item.name;
    folderDeleteDialog.value = true;
    folderDeleteItem.value = item;
  }
  
  function setActive(activeItems) {
    if (!activeItems.length) {
      selectedItem.value = null;
      return;
    }

    let currentItem = activeItems.length ? activeItems[0] : null;
    selectedItem.value = currentItem;
    currentFolderUID.value = currentItem;
    // Emit event to parent so cases are fetched for the selected folder
    emit('folder-selected', currentItem);
  }
  
  async function addTestInternalFolder() {
    if (!isProjectArchived.value) {
      // Check depth of selected folder
      const MAX_DEPTH = 5; // Max depth of 5 allows for 6 levels (0-5)

      if (route.params.folderUid) {
        const currentDepth = getDepth(editedItems.value, route.params.folderUid);
        if (currentDepth >= MAX_DEPTH) {
          showErrorToast(Swal, t('error.maxFolderDepth', { max: MAX_DEPTH + 1 }));
          return;
        }
      }
      const selectedFolder = findFolderByUid(props.items, currentFolderUID.value);

      editingFolder.value = {
        tempUid: uid(),
        name: `${t('newFolder')}_${uid(3)}`,
        count: 0,
        parent: selectedItem.value || null,
        position: (selectedFolder?.children?.length * 10) ? (selectedFolder?.children?.length * 10) : 0,
      };

      isFolderLoadingState.value = true;
      await saveNewFolder();
      isFolderLoadingState.value = false;
    }
  }

  const addTestFolder = debounce(addTestInternalFolder, 500, {
    leading: true,
    trailing: false,
  });
  
  async function saveNewFolder() {
    const folderService = makeFoldersService($api);
    const newFolder = {
      parentId: route.params.folderUid || currentFolderUID.value || rootFolder.value.uid,
      position: editingFolder.value.position,
      name: editingFolder.value.name,
    };
    try {
      const response = await folderService.createFolder(route.params.handle, route.params.key, newFolder);
      const createdFolder = response.data;
      
      // Prepare the new folder for dynamic loading
      const preparedFolder = {
        ...createdFolder,
      };
      
      // Add folder locally to preserve expanded state
      if (newFolder.parentId) {
        // Find parent folder and add to its children
        const parentFolder = findFolderByUid(props.items, newFolder.parentId);
        if (parentFolder) {
          if (!openedFolders.value.includes(parentFolder.uid)) {
            Vue.set(parentFolder, 'children', []);
            openedFolders.value.push(parentFolder.uid);
          }

          
          // Ensure children array exists
          if (!Array.isArray(parentFolder.children)) {
            parentFolder.children = [];
          }
          
          // Check if folder already exists before adding
          const folderAlreadyExists = parentFolder.children.some(child => child.uid === createdFolder.uid);
          if (!folderAlreadyExists) {
            parentFolder.children.push(preparedFolder);
            // Force reactivity update
            Vue.set(parentFolder, 'children', [...parentFolder.children]);
          }
        }
      } else {
        // Add to root level
        const folderAlreadyExists = props.items.some(item => item.uid === createdFolder.uid);
        if (!folderAlreadyExists) {
          props.items.push(preparedFolder);
        }
      }
      
      createdFoldersCache.value[editingFolder.value.tempUid] = createdFolder;
      editingFolder.value = null;
      showSuccessToast(Swal, 'createSuccess', { item: 'Folder' });
    } catch (error) {
      showErrorToast(Swal, 'createError', { item: 'Folder' }, error?.response?.data);
    }
  }
  
  async function updateFolder(item) {

    const folderService = makeFoldersService($api);
    let newFolder = {
      ...item,
    };
    if (!newFolder.name || folderDeleteDialog.value) {
      return;
    }

    try {
      const response = await folderService.updateFolder(
        route.params.handle,
        route.params.key,
        item.uid,
        newFolder
      );
      
      // Update the folder locally to preserve children and expanded state
      const updatedFolder = response.data;
      Object.assign(item, {
        ...updatedFolder,
        children: item.children || [], // Preserve existing children
      });
      
      selectedItem.value = item;
      editingFolder.value = null;
      showSuccessToast(Swal, 'updateSuccess', { item: 'Folder' });
    } catch (error) {
      item.name = originalFolderName.value;
      if (error.status === 409) {
        showErrorToast(Swal, 'duplicateNameError', { item: 'Folder' }, error?.response?.data);
      } else {
        showErrorToast(Swal, 'updateError', { item: 'Folder' }, error?.response?.data);
      }
    }
    item.editable = false;
    currentEditableItem.value = null;
  }
  
  
  
  async function deleteFolder() {
    const folderService = makeFoldersService($api);
    try {
      await folderService.deleteFolder(route.params.handle, route.params.key, folderDeleteItem.value.uid);
      
      // Remove folder locally to preserve expanded state
      const folderToDelete = folderDeleteItem.value;
      const deletedFolderUid = folderToDelete.uid;
      
      // Check if we're deleting the currently selected folder
      const wasSelectedFolder = selectedItem.value?.uid === deletedFolderUid || 
                              selectedItem.value === deletedFolderUid ||
                              selectedItemsArray.value.includes(deletedFolderUid);
      
      // Find and remove from parent's children array
      const parentFolder = findParent(props.items, folderToDelete.uid);
      if (parentFolder && Array.isArray(parentFolder.children)) {
        const index = parentFolder.children.findIndex(child => child.uid === folderToDelete.uid);
        if (index !== -1) {
          parentFolder.children.splice(index, 1);
          Vue.set(parentFolder, 'children', (parentFolder.children.length > 0) ? [...parentFolder.children] : null);
        }
      } else {
        // Remove from root level
        const index = props.items.findIndex(item => item.uid === folderToDelete.uid);
        if (index !== -1) {
          props.items.splice(index, 1);
        }
      }
      
      // If we deleted the selected folder, select a new folder
      if (wasSelectedFolder) {
        // Try to select the parent folder if it exists, otherwise select the root folder
        const newSelectedFolder = parentFolder || rootFolder.value || props.items[0];
        if (newSelectedFolder) {
          selectedItem.value = newSelectedFolder.uid;
          selectedItemsArray.value = [newSelectedFolder.uid];
          emit('folder-selected', newSelectedFolder.uid);
        }
      }
      
      handleCloseDialog();
      // Don't emit folder-delete to avoid tree resets - just handle folder selection change
      showSuccessToast(Swal, 'deleteSuccess', { item: 'Folder' });
    } catch (error) {
      showErrorToast(Swal, 'deleteError', { item: 'Folder' }, error?.response?.data);
    }
  }
  
  function findParent(folders, childUid) {
    for (let folder of folders) {
      if (folder.children && folder.children.length > 0) {
        for (let child of folder.children) {
          if (child.uid == childUid) {
            return folder;
          }
        }

        const foundParent = findParent(folder.children, childUid);
        if (foundParent) {
          return foundParent;
        }
      }
    }

    return null;
  }
  
  function findFolderByUid(folders, uid) {
    for (const folder of folders) {
      if (folder.uid === uid) {
        return folder;
      }
      if (folder.children) {
        const foundInChildren = findFolderByUid(folder.children, uid);
        if (foundInChildren) {
          return foundInChildren;
        }
      }
    }
    return null;
  }
  
  function getParentFolderKeys(allFolders, targetFolder) {
    const parentFolderKeys = [];

    const findParent = (folders, target) => {
      for (const folder of folders) {
        if (!folder.children || !folder.children.length) continue;

        if (folder.uid == target.uid) return true;

        if (folder.children.some((child) => child.uid === target.uid)) {
          parentFolderKeys.unshift(folder.uid);
          return true;
        }

        if (findParent(folder.children, target)) {
          parentFolderKeys.unshift(folder.uid);
          return true;
        }
      }
      return false;
    };

    if (findParent(allFolders, targetFolder)) parentFolderKeys.push(targetFolder.uid);

    return parentFolderKeys;
  }
  
  function hasChildren(item) {
    return item && item.children && item.children.length > 0;
  }
  
  function countAllDescendants(item) {
    if (!item.children || item.children.length === 0) {
      return 0;
    }
    let totalCount = item.children.length;
    for (const child of item.children) {
      totalCount += countAllDescendants(child);
    }
    
    return totalCount;
  }
  
  function findSelectedItem() {
    selectedItemsArray.value.push(props.selectedFolderUid);
  }
  
  function showContextMenu(event, item) {
    event.preventDefault();
    menuX.value = event.clientX;
    menuY.value = event.clientY;
    selectedContextItem.value = item;
    showMenu.value = true;
  }
  
  function handleMenuEdit() {
    if (selectedContextItem.value) {
      makeEditable(selectedContextItem.value);
    }
    showMenu.value = false;
  }
  
  function handleMenuDelete() {
    if (selectedContextItem.value) {
      handleOpenDialog(selectedContextItem.value);
    }
    showMenu.value = false;
  }
  
  function initializeDraggable() {
    let treeObserver;
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1) {
            const treeNodes = node.classList.contains('v-treeview-node__root')
              ? [node]
              : node.querySelectorAll('.v-treeview-node__root');

            treeNodes.forEach((treeNode) => {
              applyDragFunctionality(treeNode);
            });
          }
        });
      });
    });

    const checkTreeNodes = setInterval(() => {
      const treeNodes = document.querySelectorAll('.v-treeview-node__root');

      if (treeNodes && treeNodes.length > 0) {
        clearInterval(checkTreeNodes);

        treeNodes[0].classList.add('first-node');

        treeNodes.forEach((node) =>{
          applyDragFunctionality(node);
        });

        const treeviewContainer = document.querySelector('.v-treeview');
        if (treeviewContainer) {
          observer.observe(treeviewContainer, {
            childList: true,
            subtree: true,
          });
        }
      }
    }, 100);

    setTimeout(() => {
      clearInterval(checkTreeNodes);
    }, 10000);

    treeObserver = observer;
    return treeObserver;
  }

  async function handleFolderOrderChange(from, {leftFolder = null, rightFolder = null}) {
    const folderService = makeFoldersService($api);
    let newFolder = {
      ...from,
    };
    let updatedFolder = null;
    if (!newFolder.name || folderDeleteDialog.value) {
      return;
    }

    try {
      // Check if the folder is being moved to its own child if so return
      if (
        !window.isFileDragging &&
        (isDescendant(from, leftFolder) || isDescendant(from, rightFolder))
      ) {
        showErrorToast(Swal, this.$t("couldnotMoveParentToItsChild"), {});
        return;
      }
      if (leftFolder?.uid && rightFolder?.uid) {
        newFolder.position = calculateFractionalOrder(leftFolder.position, rightFolder.position);
        newFolder.parentUid = leftFolder.parentUid;
        newFolder.parentId = leftFolder.parentUid;

        // If the folder order does not need rebalancing, we can just return the new folder
        if (!checkForRebalance(leftFolder, rightFolder, 3)) {
          updatedFolder = [newFolder];
        }
        const parentFolder = findParent(props.items, leftFolder.uid);
        const {start, mid, end} = getRebalanceWindow({children:parentFolder.children, center:leftFolder, newFolder:newFolder, windowSize:10});
        const rebalancedFolders = rebalanceOrder(start, mid, end, 4);
        updatedFolder = rebalancedFolders.map(folder => {
          return {
            ...folder,
            parentId: leftFolder.parentUid,
            parentUid: leftFolder.parentUid,
          };
        })

      } else if (!leftFolder?.uid && rightFolder?.uid) {
        newFolder.position = rightFolder?.position - 10;
        newFolder.parentId = rightFolder?.parentUid;
        newFolder.parentUid = rightFolder?.parentUid;
        updatedFolder = [newFolder];
      } else if (leftFolder?.uid && !rightFolder?.uid) {
        newFolder.position = leftFolder?.position + 10;
        newFolder.parentId = leftFolder?.parentUid;
        newFolder.parentUid = leftFolder?.parentUid;
        updatedFolder = [newFolder];
      }
      const response = await folderService.updateFolders(
        route.params.handle,
        route.params.key,
        updatedFolder
      );

      // Update folder tree locally to preserve expanded state
      if (!window.isFileDragging && response.status === 200) {
        // Moving a folder - remove from old parent and add to new parent
        const fromFolder = findFolderByUid(props.items, from.uid);
        if (fromFolder) {
          // Remove from old parent
          const oldParent = findParent(props.items, from.uid);
          if (oldParent && Array.isArray(oldParent.children)) {
            const oldIndex = oldParent.children.findIndex(
              (child) => child.uid === from.uid
            );
            if (oldIndex !== -1) {
              oldParent.children.splice(oldIndex, 1);
              Vue.set(oldParent, "children", [...oldParent.children]);
            }
          } else {
            // Remove from root level
            const oldIndex = props.items.findIndex(
              (item) => item.uid === from.uid
            );
            if (oldIndex !== -1) {
              props.items.splice(oldIndex, 1);
            }
          }

          // Add to new parent
          let newParent;
          let insertIndex = 0;

          if(leftFolder?.uid) {
            newParent = findParent(props.items, leftFolder.uid);
            insertIndex = newParent.children.findIndex(child => child.uid === leftFolder.uid) + 1;
          } else if(rightFolder?.uid) {
            newParent = findParent(props.items, rightFolder.uid);
            insertIndex = newParent.children.findIndex(child => child.uid === rightFolder.uid);
          } else {
            newParent = props.items;
            insertIndex = newParent.children.length;
          }
          // 4. Insert the folder at the new position
          newParent.children.splice(insertIndex, 0, newFolder);

          // 5. Force reactivity
          Vue.set(newParent, "children", [...newParent.children]);
        }
        showSuccessToast(Swal, 'updateSuccess', { item: `Folder Order` });
      }
    } catch (error) {
      from.name = originalFolderName.value;
      if (error.status === 409) {
        showErrorToast(
          Swal,
          "duplicateNameError",
          { item: "Folder" },
          error?.response?.data?.message
        );
      } else {
        showErrorToast(
          Swal,
          "updateError",
          { item: "Folder" },
          error?.response?.data
        );
      }
    }
  }

  
  async function handleFolderMove() {
    const from = window.isFileDragging ? window.caseUid : dragFromFolder.value;
    const to = dragToFolder.value;
    if (!from || !to) return;

    if (from.uid === to.uid) return;

    const isChild = (parent, child) => {
      if (!parent.children) return false;
      return parent.children.some((item) => item.uid === child.uid || isChild(item, child));
    };
    if (!window.isFileDragging && isChild(from, to)) return;

    try {
      const folderService = makeFoldersService($api);
      const caseService = makeCasesService($api);


      if (props.isImport) {
        // If no cases are selected but a file is being dragged, move just that single case
        if (tableSelectedCases.value.length === 0 && window.isFileDragging && window.caseUid) {
            const fullCaseObject = casesToImport.value.find(item => Number(item.testCaseRef) === Number(window.caseUid));
            if (fullCaseObject) {
              handleSubmit(route, [fullCaseObject], to.uid, flattenFoldersStructure(editedItems.value));
            }

        } else {
          // Otherwise, proceed with selected cases as before
          handleSubmit(route, tableSelectedCases.value, to.uid, flattenFoldersStructure(editedItems.value));
        }
        return;
      }
      if (window.isFileDragging) {
        // Check if we're moving multiple selected cases
        if (window.isMultipleCasesDrag && 
            window.selectedCaseRefs && 
            Array.isArray(window.selectedCaseRefs) && 
            window.selectedCaseRefs.length > 1 &&
            tableSelectedCases.value.length > 0) {
          // Prepare bulk update payload - only updating parentId for folder move
          const bulkUpdatePayload = tableSelectedCases.value
            .filter(selectedCase => window.selectedCaseRefs.includes(selectedCase.testCaseRef))
            .map(caseItem => ({
              testCaseRef: caseItem.testCaseRef,
              name: caseItem.name,
              parentId: to.uid,
              customFields: caseItem.customFields || {},
              priority: caseItem.priority,
              tagIds: caseItem.tags ? caseItem.tags.map(tag => tag.uid) : [],
              tagReplacements: []
            }));
          
          if (bulkUpdatePayload.length === 0) {
            showErrorToast(Swal, 'updateError', { item: 'test cases' });
            return;
          }
          
          const response = await caseService.updateBulkTestCases(
            route.params.handle,
            route.params.key,
            {
              cases: bulkUpdatePayload
            }
          );
          
          if (response.status === 207) {
            showErrorToast(Swal, 'partialError');
          } else if (response.status !== 200) {
            showErrorToast(Swal, 'updateError', { item: 'test cases' });
          } else {
            const caseCount = window.selectedCaseRefs ? window.selectedCaseRefs.length : bulkUpdatePayload.length;
            showSuccessToast(Swal, 'updateSuccess', { item: `${caseCount} test cases` });
            tableSelectedCases.value = [];
          }
        } else {
          await caseService.updateTestCase(
            route.params.handle,
            route.params.key,
            window.caseUid,
            {
              parentId: to.uid,
            }
          );
          showSuccessToast(Swal, 'updateSuccess', { item: 'Test case' });
        }
        
        // Clean up drag state
        window.isFileDragging = false;
        window.caseUid = null;
        window.isMultipleCasesDrag = false;
        window.selectedCaseRefs = null;
      } else {
        const response = await folderService.updateFolder(
          route.params.handle,
          route.params.key,
          from.uid,
          {
            parentId: to.uid,
          }
        );

        if (selectedItem.value && selectedItem.value.uid === from.uid) {
          selectedItem.value = response.data;
        }
        
        // Update folder tree locally to preserve expanded state
        if (!window.isFileDragging) {
          // Moving a folder - remove from old parent and add to new parent
          const fromFolder = findFolderByUid(props.items, from.uid);
          if (fromFolder) {
            // Remove from old parent
            const oldParent = findParent(props.items, from.uid);
            if (oldParent && Array.isArray(oldParent.children)) {
              const oldIndex = oldParent.children.findIndex(child => child.uid === from.uid);
              if (oldIndex !== -1) {
                oldParent.children.splice(oldIndex, 1);
                Vue.set(oldParent, 'children', [...oldParent.children]);
              }
            } else {
              // Remove from root level
              const oldIndex = props.items.findIndex(item => item.uid === from.uid);
              if (oldIndex !== -1) {
                props.items.splice(oldIndex, 1);
              }
            }
            
            // Add to new parent
            const newParent = findFolderByUid(props.items, to.uid);
            if (newParent && Array.isArray(newParent.children)) {
              // Update the folder's parentId to match the server response
              const updatedFolder = {
                ...fromFolder,
                parentUid: to.uid, // Update parent reference
              };
              newParent.children.push(updatedFolder);
              Vue.set(newParent, 'children', [...newParent.children]);
            }
          }
        }
        
        showSuccessToast(Swal, 'updateSuccess', { item: 'Folder' });
      }
      emit('reload-cases');

      dragFromFolder.value = null;
      dragToFolder.value = null;

    } catch (err) {
      showErrorToast(Swal, 'updateError', { item: 'Folder' }, err?.response?.data);
    }
  }
  
  function applyDragFunctionality(node) {
    // Make the root node and all its children draggable
    const makeNodeDraggable = (element) => {
      if (element.classList.contains('first-node')) {
        element.draggable = false;
        return;
      }
      
      element.draggable = true;
      
      // Add drag event listeners
      element.addEventListener('dragstart', (e) => {
        // Find the closest root node
        const rootNode = e.target.closest('.v-treeview-node__root');
        if (rootNode) {
          rootNode.classList.add('dragging');
          dragFromFolder.value = getFolderByEvent({ target: rootNode });
        }
        e.stopPropagation(); // Prevent event bubbling
      });

      element.addEventListener('dragend', (e) => {
        const rootNode = e.target.closest('.v-treeview-node__root');
        if (rootNode) {
          rootNode.classList.remove('dragging');
        }
        e.stopPropagation();
      });

      element.addEventListener('dragover', (e) => {
        const rect = e.target.getBoundingClientRect();
        const offsetY = e.clientY - rect.top;
        const percent = (offsetY / rect.height) * 100;
        const rootNode = e.target.closest('.v-treeview-node__root');
        const draggedOverFolderUid = getFolderByEvent({target: rootNode})?.uid;
        const draggedFolderUid = dragFromFolder.value?.uid;
        const parentFolder = findParent(props.items, draggedOverFolderUid);
        const isFirstFolder = parentFolder && parentFolder.children && parentFolder.children[0]?.uid === draggedOverFolderUid;

        e.preventDefault();
          if ( draggedFolderUid === draggedOverFolderUid) {
            return;
          }
        if (rootNode) {
          rootNode.classList.remove('drag-over', 'drag-over-bottom', 'drag-over-top');
          if (isFirstFolder && percent < 20) {
            rootNode.classList.add('drag-over-top');
          } else if (percent > 70) {
            rootNode.classList.add('drag-over-bottom');
            rootNode.classList.remove('drag-over');
            rootNode.classList.remove('drag-over-top');
          } else if (percent < 70 && percent > 20) {
            rootNode.classList.remove('drag-over-bottom');
            rootNode.classList.remove('drag-over-top');
            rootNode.classList.add('drag-over');
          }
        }
        e.stopPropagation();
      });

      element.addEventListener('dragleave', (e) => {
        const rootNode = e.target.closest('.v-treeview-node__root');
        if (rootNode) {
          rootNode.classList.remove('drag-over');
          rootNode.classList.remove('drag-over-bottom');
          rootNode.classList.remove('drag-over-top');
        }
        e.stopPropagation();
      });

      element.addEventListener('drop', (e) => {
        e.preventDefault();
        const rootNode = e.target.closest('.v-treeview-node__root');
        const rect = e.target.getBoundingClientRect();
        const offsetY = e.clientY - rect.top;
        const percent = (offsetY / rect.height) * 100;
        if (rootNode) {
          dragToFolder.value = getFolderByEvent({ target: rootNode });
          const beforeFolderParent = findParent(props.items, dragToFolder.value?.uid);
          const draggedOverIndexInParent = beforeFolderParent?.children?.findIndex(child => child.uid === dragToFolder.value?.uid);
          const beforeFolder = beforeFolderParent?.children?.[draggedOverIndexInParent + 1];
          const fromEntity = window.isFileDragging ? window.caseUid : dragFromFolder.value;
          const parentFolder = findParent(props.items, dragToFolder.value?.uid);
          const parentLength = parentFolder?.children?.length || 0;
          const isFirstFolder = parentFolder.children[0]?.uid === dragToFolder.value?.uid;
          const isLastFolder = parentFolder.children[parentLength - 1]?.uid === dragToFolder.value?.uid;
          if (isFirstFolder && percent < 20) {
            handleFolderOrderChange(fromEntity, {rightFolder :dragToFolder.value});
          } else if (isLastFolder && percent > 70) {
            handleFolderOrderChange(fromEntity, {leftFolder: dragToFolder.value});
          }else if(!isLastFolder && percent > 70){
            handleFolderOrderChange(fromEntity, {leftFolder:dragToFolder.value, rightFolder:beforeFolder});
          } else if (percent < 70 && percent > 20) {
            if (fromEntity && dragToFolder.value) {
              handleFolderMove();
            }
          }
          rootNode.classList.remove('drag-over');
          rootNode.classList.remove('drag-over-bottom');
          rootNode.classList.remove('drag-over-top');
        }
        e.stopPropagation();
      });
    };

    // Apply drag functionality to the root node and all its descendants
    makeNodeDraggable(node);
    node.querySelectorAll('*').forEach(makeNodeDraggable);
  }
  
  /**
   * Dynamic loading function for Vuetify treeview load-children
   * @param {Object} item - The folder item being expanded
   * @returns {Promise<void>} Loads children into item.children array
   */
  const loadTreeChildren = async (item) => {
    try {
      await loadFolderChildren(item, route.params.handle, route.params.key, $api);
      // After loading children, save the updated folder tree to store
      emit('save-expanded-tree', props.items);
    } catch (error) {
      showErrorToast(Swal, t('test_folder.failed_to_fetch_children'), { folderUid: item.uid }, error?.response?.data);
    }
  };
  
  // Functions for the loading mixin
  function showSkeletonLoader() {
    skeletonLoaderState.value = true;
  }
  
  function hideSkeletonLoader() {
    skeletonLoaderState.value = false;
  }
  

  
  // Setup lifecycle hooks in the composable
  let treeObserver = null;
  
  onMounted(() => {
    props.items.map((item) => {
      item.editable = false;
    });
    
    // Auto-expand the first folder on initial page load if not already opened
    // Use Vue.nextTick to ensure Vuetify treeview is ready before setting expansion state
    if (props.items.length > 0 && !openedFolders.value.includes(props.items[0].uid)) {
      Vue.nextTick(() => {
        openedFolders.value = [props.items[0].uid];
      });
    }
    
    window.addEventListener('scroll', onScroll);
    treeObserver = initializeDraggable();
  });
  
  onBeforeUnmount(() => {
    window.removeEventListener('scroll', onScroll);
    if (treeObserver) {
      treeObserver.disconnect();
    }
  });
  
  // Setup watchers in the composable
  watch(() => editingFolder.value, () => {
    if (selectedItem.value) {
      openedFolders.value.push(selectedItem.value.uid);
    }
  }, { immediate: true });
  
  watch(() => selectedItem.value, (newVal) => {
    if (newVal) {
      if (selectedItem.value) {
        emit('folder-selected', selectedItem.value?.uid ? selectedItem.value?.uid : selectedItem.value);
        updateSelected(selectedItem.value?.uid);
      } else if (selectedItemsArray.value && selectedItemsArray.value.tempUid) {
        // In the edge case that we deselect and reselect a newly created
        // folder before we've refreshed the folder list from the backend,
        // the real values for that folder live in this cache.
        emit('folder-selected', createdFoldersCache.value[selectedItemsArray.value.tempUid]);
        updateSelected(createdFoldersCache.value[selectedItemsArray.value.tempUid].uid);
      }
    }
  }, { immediate: true });
  
  watch(() => props.selectedFolderUid, (newVal) => {
    if (newVal) {
      findSelectedItem();
    }
  }, { immediate: true });

  // Auto-expand first folder when items are loaded
  watch(() => props.items, (newItems, oldItems) => {
    // Only run if items actually changed (not on initial load)
    if (newItems && newItems.length > 0 && oldItems && oldItems.length === 0) {
      const firstFolderUid = newItems[0].uid;
      if (!openedFolders.value.includes(firstFolderUid)) {
        // Use Vue.nextTick to ensure the treeview is rendered before setting the open state
        Vue.nextTick(() => {
          openedFolders.value = [firstFolderUid];
        });
      }
    }
  });

  return {
    // Data properties
    folderDeleteDialog,
    folderDeleteName,
    folderDeleteItem,
    openedFolders,
    tree,
    editingFolder,
    selectedItem,
    createdFoldersCache,
    currentEditableItem,
    selectedItemsArray,
    rootFolderUID,
    isFolderLoadingState,
    originalFolderName,
    isScrolled,
    showMenu,
    menuX,
    menuY,
    selectedContextItem,
    dragFromFolder,
    dragToFolder,
    skeletonLoaderState,
    
    // Computed properties
    open,
    isOpenedFoldersNotEmpty,
    isCollapsed,
    selectedProjectByKey,
    selectedOrganization,
    rootFolder,
    sortedFolders,
    editedItems,
    isEditedItemsHasChildren,
    isProjectArchived,
    unauthorizedToast,
    
    // Methods
    getFolderByEvent,
    onScroll,
    flattenFolders,
    sortFoldersByUpdatedAt,
    addDepth,
    getDepth,
    updateSelected,
    makeEditable,
    handleCloseDialog,
    handleOpenDialog,
    setActive,
    addTestFolder,
    saveNewFolder,
    updateFolder,
    deleteFolder,
    findParent,
    findFolderByUid,
    getParentFolderKeys,
    hasChildren,
    countAllDescendants,
    findSelectedItem,
    showContextMenu,
    handleMenuEdit,
    handleMenuDelete,
    initializeDraggable,
    handleFolderMove,
    applyDragFunctionality,
    showSkeletonLoader,
    hideSkeletonLoader,
    loadTreeChildren,
  };
}; 