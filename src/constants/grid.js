import i18n from '@/i18n';

export const caseStatusMap = {
  Draft: { color: 'primary', text: i18n.t('statuses.draft'), icon: 'mdi-file-outline' },
  Active: { color: 'success', text: i18n.t('statuses.active'), icon: 'mdi-clipboard-check-outline' },
  'In Review': { color: 'warning', text: i18n.t('statuses.inReview'), icon: 'mdi-progress-alert' },
  Rejected: { color: 'error', text: i18n.t('statuses.rejected'), icon: 'mdi-sim-off-outline' },
  Closed: { color: 'info', text: i18n.t('statuses.closed'), icon: 'mdi-close-circle-outline' },
};

export const runStateMap = {
  New: { color: 'primary', text: i18n.t('statuses.new'), icon: 'mdi-moon-new' },
  'In Progress': { color: 'success', text: i18n.t('statuses.inProgress'), icon: 'mdi-progress-alert' },
  Complete: { color: 'warning', text: i18n.t('statuses.complete'), icon: 'mdi-clipboard-check-outline' },
};

export const milestoneStatusMap = {
  Open: {},
  Closed: {},
};

export const priorities = {
  High: { color: 'green', value: 'High', text: i18n.t('priorities.high'), icon: 'mdi-arrow-up' },
  Medium: { color: 'blue', value: 'Medium', text: i18n.t('priorities.medium'), icon: 'mdi-checkbox-blank-circle' },
  Low: { color: 'red', value: 'Low', text: i18n.t('priorities.low'), icon: 'mdi-arrow-down' },
};

export const inviteStatuses = {
  processed: { color: 'warning', value: 'processed', text: i18n.t('inviteStatuses.processed'), icon: 'mdi-checkbox-blank-circle' },
  dropped: { color: 'red', value: 'dropped', text: i18n.t('inviteStatuses.dropped'), icon: 'mdi-progress-alert' },
  delivered: { color: 'green', value: 'delivered', text: i18n.t('inviteStatuses.delivered'), icon: 'mdi-checkbox-blank-circle' },
  deferred: { color: 'grey', value: 'deferred', text: i18n.t('inviteStatuses.deferred'), icon: 'mdi-close-circle-outline' },
  bounce: { color: 'red', value: 'bounce', text: i18n.t('inviteStatuses.bounce'), icon: 'mdi-close-circle-outline' },
};

export const resultStatusMap = {
  passed: { color: 'success', value: 'passed', text: i18n.t('statuses.passed'), icon: 'mdi-check-circle-outline' },
  failed: { color: 'error', value: 'failed', text: i18n.t('statuses.failed'), icon: 'mdi-close-circle-outline' },
  pending: { color: 'warning', value: 'pending', text: i18n.t('statuses.pending'), icon: 'mdi-progress-alert' },
  inconclusive: { color: 'info', value: 'inconclusive', text: i18n.t('statuses.inconclusive'), icon: 'mdi-close-circle-outline' },
  skipped: { color: 'grey', value: 'skipped', text: i18n.t('statuses.skipped'), icon: 'mdi-skip-next' },
};

export const testsTableHeader = [
  { text: i18n.t('name'), value: 'name' },
  { text: i18n.t('priority'), value: 'priority' },
  { text: i18n.t('status'), value: 'status' },
  { text: i18n.t('actions'), value: 'actions' },
];

export const TagsHeader = [
  {
    text: i18n.t('name'),
    align: 'start',
    sortable: true,
    value: 'name',
    class: 'elevation-0 rounded-l-lg',
    checked: true,
    width: '35%',
  },
  {
    text: i18n.t('description'),
    value: 'description',
    sortable: true,
    checked: true,
    width: '25%',
  },
  {
    text: i18n.t('tagPage.type'),
    value: 'entityTypes',
    sortable: true,
    checked: true,
    width: '25%',
  },
  {
    text: i18n.t('tagPage.count'),
    value: 'count',
    sortable: true,
    checked: true,
    width: '10%',
  },
  {
    text: '',
    value: 'uid',
    sortable: false,
    class: 'rounded-r-lg',
    checked: true,
    width: '5%',
  },
];

export const JiraDefectHeader = [
  {
    text: i18n.t('id'),
    align: 'start',
    sortable: true,
    value: 'id',
    checked: true,
    width: '10%',
  },
  {
    text: i18n.t('defect.issueType'),
    value: 'issueType',
    align: 'start',
    sortable: true,
    checked: true,
    width: '10%',
  },
  {
    text: i18n.t('defect.execution'),
    value: 'linkedExecutions',
    align: 'start',
    sortable: true,
    checked: true,
    width: '20%',
  },
  {
    text: i18n.t('defect.summary'),
    align: 'start',
    sortable: true,
    value: 'name',
    checked: true,
    width: '35%',
  },
  {
    text: i18n.t('status'),
    value: 'status',
    align: 'start',
    sortable: true,
    checked: true,
    width: '10%',
  },
  {
    text: i18n.t('priority'),
    value: 'priority',
    align: 'start',
    sortable: true,
    checked: true,
    width: '10%',
  },
  {
    text: i18n.t('lastUpdate'),
    value: 'updatedAt',
    align: 'start',
    sortable: true,
    checked: false,
    width: '10%',
  },
  {
    text: '',
    value: 'uid',
    sortable: false,
    class: 'rounded-r-lg',
    checked: true,
    width: '5%',
  },
];

export const GithubDefectHeader = [
  {
    text: i18n.t('id'),
    align: 'start',
    sortable: true,
    value: 'id',
    checked: true,
    width: '5%',
  },
  {
    text: i18n.t('defect.title'),
    align: 'start',
    sortable: true,
    value: 'name',
    checked: true,
    width: '25%',
  },
  {
    text: i18n.t('defect.execution'),
    value: 'linkedExecutions',
    align: 'start',
    sortable: true,
    checked: true,
    width: '15%',
  },
  {
    text: i18n.t('defect.repository'),
    value: 'repository',
    align: 'start',
    sortable: true,
    checked: true,
    width: '15%',
  },
  {
    text: i18n.t('labels'),
    value: 'labels',
    align: 'start',
    sortable: true,
    checked: true,
    width: '25%',
  },
  {
    text: i18n.t('lastUpdate'),
    value: 'updatedAt',
    align: 'start',
    sortable: true,
    checked: true,
    width: '10%',
  },
  {
    text: '',
    value: 'uid',
    sortable: false,
    class: 'rounded-r-lg',
    checked: true,
    width: '5%',
  },
];

export const ProjectsViewHeader = [
  {
    text: i18n.t('name'),
    align: 'start',
    sortable: true,
    value: 'name',
    class: 'elevation-0 rounded-l-lg',
    checked: true,
  },
  {
    text: i18n.t('projects.key'),
    align: 'start',
    sortable: true,
    value: 'key',
    class: 'elevation-0 rounded-l-lg',
  },
  {
    text: i18n.t('testRuns'),
    value: 'test',
    sortable: true,
    checked: true,
  },
  {
    text: i18n.t('testCases'),
    value: 'cases',
    sortable: true,
  },
  {
    text: i18n.t('defects'),
    value: 'defects',
    sortable: true,
  },
  {
    text: i18n.t('creationDate'),
    value: 'createdAt',
    sortable: true,
    class: 'rounded-r-lg',
    checked: true,
  },
  {
    text: i18n.t('users'),
    value: 'users',
    sortable: true,
    class: 'rounded-r-lg',
    checked: true,
  },
];

export const ProjectEditHeader = [
  {
    text: i18n.t('user'),
    align: 'start',
    sortable: false,
    value: 'name',
    class: 'rounded-l-lg gray-ish--text',
    checked: true,
  },
  {
    text: i18n.t('role'),
    value: 'role',
    sortable: true,
    class: 'gray-ish--text',
    checked: true,
  },
  {
    text: i18n.t('lastActivity'),
    value: 'lastActivity',
    sortable: true,
    class: 'gray-ish--text',
    checked: true,
  },
  {
    text: i18n.t('tag'),
    value: 'tag',
    sortable: true,
    class: 'gray-ish--text',
    checked: true,
  },
  {
    text: i18n.t('email'),
    value: 'email',
    sortable: true,
    class: 'rounded-r-lg gray-ish--text',
    checked: true,
  },
];

export const UsersViewHeader = [
  {
    text: i18n.t('name'),
    align: 'start',
    sortable: true,
    value: 'name',
    class: 'elevation-0 rounded-l-lg',
    checked: true,
    width: '20%',
  },
  {
    text: i18n.t('role'),
    value: 'role',
    sortable: true,
    checked: true,
    width: '15%',
  },
  {
    text: i18n.t('inviteSent'),
    value: 'createdAt',
    sortable: true,
    checked: true,
    width: '10%',
  },
  {
    text: i18n.t('tags'),
    value: 'tag',
    sortable: true,
    checked: true,
    width: '15%',
  },
  {
    text: i18n.t('email'),
    value: 'email',
    sortable: true,
    checked: true,
    width: '10%',
  },
  {
    text: i18n.t('lastActivity'),
    value: 'lastActivity',
    sortable: true,
    checked: true,
    width: '10%',
  },
  {
    text: i18n.t('status'),
    value: 'status',
    sortable: true,
    checked: true,
    width: '10%',
  },
  {
    text: i18n.t('integrations.projects'),
    value: 'project',
    sortable: true,
    checked: true,
    width: '25%',
  },
  {
    text: '',
    value: 'deleteIcon',
    sortable: false,
    class: 'rounded-r-lg',
    checked: true,
    width: '5%',
  },
];

export const RoleMemberHeader = [
  {
    text: i18n.t('user'),
    align: 'start',
    sortable: true,
    value: 'user',
    class: 'elevation-0 rounded-l-lg',
    checked: true,
  },
  {
    text: i18n.t('role'),
    value: 'role',
    sortable: true,
    checked: true,
  },
  {
    text: i18n.t('project'),
    value: 'project',
    sortable: true,
    checked: true,
  },
  {
    text: i18n.t('tags'),
    value: 'tags',
    sortable: true,
    checked: true,
  },
  {
    text: i18n.t('email'),
    value: 'email',
    sortable: true,
    checked: true,
  },
];

export const CustomTemplatesHeader = [
  {
    text: i18n.t('name'),
    align: 'start',
    sortable: true,
    value: 'name',
    class: 'elevation-0 rounded-l-lg',
    checked: true,
    width: '40%',
  },
  {
    text: i18n.t('creationDate'),
    value: 'createdAt',
    sortable: true,
    checked: true,
    width: '20%',
  },
  {
    text: i18n.t('templatesPage.creator'),
    value: 'createdBy',
    sortable: true,
    checked: true,
    width: '20%',
  },
  {
    text: '',
    value: 'isDefault',
    sortable: false,
    checked: true,
    width: '10%',
  },
  {
    text: '',
    value: 'uid',
    sortable: false,
    checked: true,
    class: 'rounded-r-lg',
    width: '10%',
  },
];

export const CustomFieldsHeader = [
  {
    text: i18n.t('name'),
    align: 'start',
    sortable: true,
    value: 'name',
    class: 'elevation-0 rounded-l-lg',
    checked: true,
    width: '30%',
  },
  {
    text: i18n.t('usedIn'),
    value: 'entityType',
    sortable: true,
    checked: true,
    width: '15%',
  },
  {
    text: i18n.t('customFieldPage.dataType'),
    value: 'type',
    sortable: true,
    checked: true,
    width: '15%',
  },
  {
    text: i18n.t('creationDate'),
    value: 'createdAt',
    sortable: true,
    checked: true,
    width: '15%',
  },
  {
    text: i18n.t('customFieldPage.dataSource'),
    value: 'source',
    sortable: true,
    checked: true,
    width: '15%',
  },
  {
    text: '',
    value: 'uid',
    sortable: false,
    checked: true,
    class: 'rounded-r-lg no-table-label',
    width: '10%',
  },
];

export const PersonalizationHeader = [
  {
    text: i18n.t('dataColors.color'),
    sortable: false,
    value: 'color',
    width: '30%',
    checked: true,
    align: 'start',
  },
  {
    text: i18n.t('Test Cases'),
    sortable: false,
    value: 'testCase',
    width: '14%',
    checked: true,
    align: 'center',
  },
  {
    text: i18n.t('Test Executions'),
    sortable: false,
    value: 'testExecution',
    width: '14%',
    checked: true,
    align: 'center',
  },
  {
    text: i18n.t('Test Runs'),
    sortable: false,
    value: 'testRun',
    width: '14%',
    checked: true,
    align: 'center',
  },
  {
    text: i18n.t('Test Plans'),
    sortable: false,
    value: 'testPlan',
    width: '14%',
    checked: true,
    align: 'center',
  },
  {
    text: i18n.t('Milestones'),
    sortable: false,
    value: 'milestone',
    width: '14%',
    checked: true,
    align: 'center',
  },
];

export const TestCaseTableHeader = [
  { text: i18n.t('id'), value: 'id', sortable: false, checked: true, isSelected: true, width: '15%' },
  { text: i18n.t('title'), value: 'name', sortable: true, checked: true, isSelected: true, width: '50%' },
  { text: i18n.t('priority'), value: 'priority', sortable: false, checked: true, isSelected: true, width: '15%' },
  { text: i18n.t('tags'), value: 'tags', sortable: false, checked: true, width: '15%' },
  { text: '', value: 'actions', sortable: false, checked: true, width: '5%' },
];

export const ExecutionTableHeader = [
  { text: i18n.t('id'), value: 'id', sortable: true, checked: true, isSelected: true, width: 'auto', minWidth: '15%' },
  { text: i18n.t('name'), value: 'name', sortable: true, checked: true, isSelected: true, width: '32%' },
  { text: i18n.t('assignedTo'), value: 'assignees', sortable: true, checked: true, isSelected: true, width: '18%' },
  { text: i18n.t('duedate'), value: 'dueAt', sortable: true, checked: true, isSelected: true, width: '10%' },
  { text: i18n.t('priority'), value: 'priority', sortable: true, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('status'), value: 'status', sortable: true, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('actions'), value: 'actions', sortable: false, checked: true, width: '5%' },
];

export const WorkspaceTableHeader = [
  { text: i18n.t('id'), value: 'uid', sortable: true, checked: true, isSelected: true, width: 'auto', minWidth: '11%' },
  { text: i18n.t('name'), value: 'name', sortable: true, checked: true, isSelected: true, width: '15%' },
  { text: i18n.t('priority'), value: 'priority', sortable: true, checked: true, isSelected: true, width: '8%' },
  { text: i18n.t('status'), value: 'status', sortable: true, checked: true, isSelected: true, width: '8%' },
  { text: i18n.t('assignedTo'), value: 'assignedTo', sortable: true, checked: true, isSelected: true, width: '8%' },
  { text: i18n.t('assignDate'), value: 'lastAssignedAt', sortable: true, checked: true, isSelected: true, width: '8%' },
  { text: i18n.t('duedate'), value: 'dueAt', sortable: true, checked: true, isSelected: true, width: '8%' },
  { text: i18n.t('project'), value: 'project', sortable: false, checked: true, isSelected: true, width: '8%' },
  { text: i18n.t('milestone.title'), value: 'milestone', sortable: false, checked: true, isSelected: true, width: '8%' },
  { text: i18n.t('testPlan'), value: 'testplan', sortable: false, checked: true, isSelected: true, width: '8%' },
  { text: i18n.t('testRun'), value: 'testrun', sortable: false, checked: true, isSelected: true, width: '8%' },
  { text: i18n.t('tags'), value: 'tags', sortable: false, checked: true, isSelected: true, width: '5%' },
  { text: '', value: 'actions', sortable: false, checked: true, isSelected: true, width: '5%', class: 'action-column' },
];

export const CaseInRunTableHeader = [
  { text: i18n.t('id'), value: 'id', sortable: true, isSelected: true, checked: true },
  { text: i18n.t('name'), value: 'name', sortable: true, isSelected: true, checked: true },
  { text: i18n.t('assignedTo'), value: 'assigned', sortable: true, isSelected: true, checked: true },
  { text: i18n.t('priority'), value: 'priority', sortable: true, isSelected: true, checked: true },
  { text: i18n.t('status'), value: 'status', sortable: true, isSelected: true, checked: true },
  { text: '', value: 'actions', sortable: false, isSelected: true, checked: true },
];

export const TestRunTableHeader = [
  { id: 1, text: i18n.t('name'), value: 'name', sortable: true, isSelected: true, checked: true, width: 'auto', minWidth: '40%' },
  { id: 2, text: i18n.t('priority'), value: 'priority', sortable: false, isSelected: true, checked: true, width: '10%' },
  // { id: 3, text: i18n.t('status'), value: 'status', sortable: false, isSelected: true, checked: true, width: '10%' },
  // { id: 4, text: i18n.t('configurations.title'), value: 'config', sortable: false, isSelected: true, checked: true, width: '12%' },
  { id: 5, text: i18n.t('testCases'), value: 'testcases', sortable: false, isSelected: true, checked: true, width: '20%' },
  // { id: 6, text: i18n.t('milestone.title'), value: 'milestone', sortable: false, isSelected: true, checked: true, width: '9%' },
  // { id: 7, text: i18n.t('creationDate'), value: 'creationdate', sortable: true, isSelected: true, checked: true, width: '10%' },
  // { id: 8, text: i18n.t('duedate'), value: 'duedate', sortable: true, isSelected: true, checked: true, width: '10%' },
  // { id: 9, text: i18n.t('tags'), value: 'tags', sortable: false, isSelected: true, checked: true, width: '7%' },
  { id: 10, text: i18n.t('progress'), value: 'progress', sortable: false, isSelected: true, checked: true, width: '25%' },
  { id: 11, text: i18n.t('actions'), value: 'actions', isSelected: true, checked: true, sortable: false, width: '5%' },
];

export const TestRunFailedSyncsTableHeader = [
  { id: 1, text: i18n.t('integrations.integration'), value: 'integration', sortable: true, isSelected: true, checked: true, width: '15%' },
  { id: 2, text: i18n.t('name'), value: 'name', sortable: true, isSelected: true, checked: true, width: '25%' },
  { id: 3, text: i18n.t('date'), value: 'date', sortable: true, isSelected: true, checked: true, width: '10%' },
  { id: 4, text: i18n.t('milestone.title'), value: 'milestone', sortable: false, isSelected: true, checked: true, width: '10%' },
  { id: 5, text: i18n.t('testPlan'), value: 'testPlan', sortable: false, isSelected: true, checked: true, width: '15%' },
  { id: 6, text: i18n.t('testRun'), value: 'testRun', sortable: false, isSelected: true, checked: true, width: '20%' },
  { id: 7, text: i18n.t('actions'), value: 'actions', isSelected: true, checked: true, sortable: false, width: '5%' }
];

export const AuditLogHeader = [
  { text: i18n.t('auditLog.date'), align: 'start', sortable: true, value: 'date', class: 'elevation-0 rounded-l-lg', checked: true, width: '15%' },
  { text: i18n.t('auditLog.projectName'), align: 'start', sortable: true, value: 'projectName', checked: true, width: '15%' },
  { text: i18n.t('auditLog.entityId'), align: 'start', sortable: true, value: 'entityId', checked: true, width: '10%' },
  { text: i18n.t('auditLog.entityName'), align: 'start', sortable: true, value: 'entityName', checked: true, width: '15%' },
  { text: i18n.t('auditLog.action'), align: 'start', sortable: true, value: 'action', checked: true, width: '10%' },
  { text: i18n.t('auditLog.actor'), align: 'start', sortable: true, value: 'actor', checked: true, width: '15%' },
  { text: '', align: 'start', value: 'actions', sortable: false, class: 'rounded-r-lg', checked: true, width: '7%' },
];

export const testConfigurationOptions = [
  { text: i18n.t('testConfigurations.chrome'), value: 'chrome' },
  { text: i18n.t('testConfigurations.firefox'), value: 'firefox' },
  { text: i18n.t('testConfigurations.safari'), value: 'safari' },
  { text: i18n.t('testConfigurations.edge'), value: 'edge' },
  { text: i18n.t('testConfigurations.ie'), value: 'ie' },
  { text: i18n.t('testConfigurations.android'), value: 'android' },
  { text: i18n.t('testConfigurations.ios'), value: 'ios' },
  { text: i18n.t('testConfigurations.linux'), value: 'linux' },
  { text: i18n.t('testConfigurations.macos'), value: 'macos' },
  { text: i18n.t('testConfigurations.windows'), value: 'windows' },
  { text: i18n.t('testConfigurations.headless'), value: 'headless' },
  { text: i18n.t('testConfigurations.mobile'), value: 'mobile' },
  { text: i18n.t('testConfigurations.desktop'), value: 'desktop' },
  { text: i18n.t('testConfigurations.tablet'), value: 'tablet' },
  { text: i18n.t('testConfigurations.landscape'), value: 'landscape' },
  { text: i18n.t('testConfigurations.portrait'), value: 'portrait' },
  { text: i18n.t('testConfigurations.darkMode'), value: 'darkmode' },
  { text: i18n.t('testConfigurations.lightMode'), value: 'lightmode' },
  { text: i18n.t('testConfigurations.highContrast'), value: 'highcontrast' },
  { text: i18n.t('testConfigurations.lowContrast'), value: 'lowcontrast' },
  { text: i18n.t('testConfigurations.noJavaScript'), value: 'nojs' },
  { text: i18n.t('testConfigurations.noCSS'), value: 'nocs' },
  { text: i18n.t('testConfigurations.noImages'), value: 'noimages' },
  { text: i18n.t('testConfigurations.noAudio'), value: 'noaudio' },
  { text: i18n.t('testConfigurations.noVideo'), value: 'novideo' },
  { text: i18n.t('testConfigurations.noCookies'), value: 'nocookies' },
  { text: i18n.t('testConfigurations.noFlash'), value: 'noflash' },
  { text: i18n.t('testConfigurations.noPopups'), value: 'nopopups' },
  { text: i18n.t('testConfigurations.noReferrer'), value: 'noreferrer' },
  { text: i18n.t('testConfigurations.noCache'), value: 'nocache' },
  { text: i18n.t('testConfigurations.noStorage'), value: 'nostorage' },
  { text: i18n.t('testConfigurations.noIndexedDB'), value: 'noindexeddb' },
  { text: i18n.t('testConfigurations.noServiceWorker'), value: 'noserviceworker' },
  { text: i18n.t('testConfigurations.noGPU'), value: 'nogpu' },
  { text: i18n.t('testConfigurations.noWebGL'), value: 'nowebgl' },
  { text: i18n.t('testConfigurations.noWebRTC'), value: 'nowebrtc' },
  { text: i18n.t('testConfigurations.noWebSockets'), value: 'nowebsockets' },
  { text: i18n.t('testConfigurations.noGeolocation'), value: 'nogeolocation' },
  { text: i18n.t('testConfigurations.noMicrophone'), value: 'nomicrophone' },
  { text: i18n.t('testConfigurations.noCamera'), value: 'nocamera' },
  { text: i18n.t('testConfigurations.noMIDI'), value: 'nomidi' },
  { text: i18n.t('testConfigurations.noNotifications'), value: 'nonotifications' },
  { text: i18n.t('testConfigurations.noBattery'), value: 'nobattery' },
];

export const MilestoneTableHeader = [
  { text: i18n.t('name'), value: 'name', sortable: true, isSelected: true, checked: true, width: '15%' },
  { text: i18n.t('status'), value: 'status', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('testPlans'), value: 'testplans', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('testRuns'), value: 'testruns', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('testCases'), value: 'testcases', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('milestone.startdate'), value: 'startdate', sortable: true, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('duedate'), value: 'due_at', sortable: true, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('tags'), value: 'tags', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('progress'), value: 'progress', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('actions'), value: 'actions', isSelected: true, checked: true, sortable: false, width: '5%' },
];
export const MilestoneTestRunTableHeader = [
  { text: i18n.t('name'), value: 'name', sortable: true, isSelected: true, checked: true },
  { text: i18n.t('priority'), value: 'priority', sortable: false, isSelected: true, checked: true },
  { text: i18n.t('status'), value: 'status', sortable: false, isSelected: true, checked: true },
  { text: i18n.t('configurations.title'), value: 'config', sortable: false, isSelected: true, checked: true },
  { text: i18n.t('testCases'), value: 'testcases', sortable: false, isSelected: true, checked: true },
  { text: i18n.t('creationDate'), value: 'creationdate', sortable: true, isSelected: true, checked: true },
  { text: i18n.t('tags'), value: 'tags', sortable: false, isSelected: true, checked: true },
  { text: i18n.t('progress'), value: 'progress', sortable: false, isSelected: true, checked: true },
];

export const MilestoneTestPlanTableHeader = [
  { text: i18n.t('name'), value: 'name', sortable: true, isSelected: true, checked: true, width: '15%' },
  { text: i18n.t('priority'), value: 'priority', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('status'), value: 'status', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('configurations'), value: 'configurations', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('plans.list.testRuns'), value: 'testruns', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('creationDate'), value: 'creationdate', sortable: true, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('tags'), value: 'tags', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('progress'), value: 'progress', sortable: false, isSelected: true, checked: true, width: '20%' },
  { text: '', value: 'actions', isSelected: true, checked: true, sortable: false, width: '5%' },
];

export const SharedStepTableHeader = [
  { text: i18n.t('title'), value: 'name', isSelected: true, checked: true, width: 'auto', minWidth: '50%' },
  { text: i18n.t('sharedStepPage.numberOfSteps'), value: 'steps_number', isSelected: true, checked: true, width: '20%' },
  { text: i18n.t('sharedStepPage.referencedBy'), value: 'referencedBy', isSelected: true, checked: true, width: '20%' },
  { text: '', value: 'actions', isSelected: true, sortable: false, checked: true, width: '10%' },
];

export const DefaultFilter = {
  selectedRoles: [],
  testRuns: [0, 9999],
  testCases: [0, 9999],
  users: [0, 100],
  dateRange: {
    start: '',
    end: '',
  },
  dueDateRange: {
    start: '',
    end: '',
  },
  progress: [0, 100],
};
export const TestPlansTestRunsTableHeaderAll = [
  { text: i18n.t('name'), value: 'name', sortable: true, isSelected: true, checked: true, width: 'auto', minWidth: '25%' },
  { text: i18n.t('priority'), value: 'priority', sortable: false, isSelected: true, checked: true, width: '10%' },
  {
    text: i18n.t('plans.create.testRuns.tableHeader.testCases'),
    value: 'testcases',
    sortable: false,
    isSelected: true,
    checked: true,
    width: '10%',
  },
  { text: i18n.t('creationDate'), value: 'creationdate', sortable: true, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('tags'), value: 'tags', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('progress'), value: 'progress', sortable: false, isSelected: true, checked: true, width: '15%' },
  { text: i18n.t('configurations.title'), value: 'configurations', sortable: false, isSelected: true, checked: true, width: '15%' },
  { text: i18n.t('actions'), value: 'actions', isSelected: true, checked: true, sortable: false, width: '5%' },
];
export const TestPlansTestRunsTableHeaderSelected = [
  { text: i18n.t('name'), value: 'name', sortable: true, isSelected: true, checked: true },
  { text: i18n.t('priority'), value: 'priority', sortable: false, isSelected: true, checked: true },
  { text: i18n.t('plans.create.testRuns.tableHeader.testCases'), value: 'testcases', sortable: false, isSelected: true, checked: true },
  { text: i18n.t('creationDate'), value: 'creationdate', sortable: true, isSelected: true, checked: true },
  { text: i18n.t('tags'), value: 'tags', sortable: false, isSelected: true, checked: true },
  { text: i18n.t('progress'), value: 'progress', sortable: false, isSelected: true, checked: true },
  { text: i18n.t('configurations.title'), value: 'configuration', sortable: false, isSelected: true, checked: true },
];

// { text: 'Milestones', value: 'milestones', isSelected: true, checked: true, width: '10%' },
export const TestPlansTableHeader = [
  { text: i18n.t('name'), value: 'name', sortable: true, isSelected: true, checked: true, width: 'auto', minWidth: '5%', },
  { text: i18n.t('priority'), value: 'priority', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('status'), value: 'status', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('milestones'), value: 'milestones', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('plans.list.testRuns'), value: 'testruns', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('creationDate'), value: 'creationdate', sortable: true, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('tags'), value: 'tags', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('progress'), value: 'progress', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: '', value: 'actions', isSelected: true, checked: true, sortable: false, width: '5%' },
];

export const TestPlansRerunTableHeader = [
  { text: i18n.t('name'), value: 'name', sortable: true, isSelected: true, checked: true, width: 'auto', minWidth: '20%' },
  { text: i18n.t('priority'), value: 'priority', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('status'), value: 'status', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('plans.list.testCases'), value: 'testcases', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('creationDate'), value: 'creationdate', sortable: true, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('tags'), value: 'tags', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: i18n.t('progress'), value: 'progress', sortable: false, isSelected: true, checked: true, width: '15%' },
  { text: i18n.t('configurations.title'), value: 'configurations', sortable: false, isSelected: true, checked: true, width: '10%' },
  { text: '', value: 'actions', isSelected: true, checked: true, sortable: false, width: '5%' },
];

export const IntegrationsViewHeader = [
  {
    text: i18n.t('integrations.name'),
    value: 'name',
    align: 'start',
    sortable: true,
    checked: true,
    class: 'elevation-0 rounded-l-lg',
    width: '25%',
  },
  {
    text: i18n.t('projectsMenu'),
    value: 'projects',
    align: 'center',
    sortable: true,
    checked: true,
    width: '25%',
  },
  {
    text: i18n.t('integrations.syncedAt'),
    value: 'syncedAt',
    align: 'center',
    sortable: true,
    checked: true,
    width: '25%',
  },
  {
    text: '',

    value: 'actions',
    align: 'end',
    sortable: false,
    isSelected: true,
    checked: true,
    class: 'rounded-r-lg',
    width: '25%',
  },
];

export const ConfigurationsHeader = [
  {
    text: i18n.t('name'),
    align: 'start',
    value: 'name',
    sortable: true,
    class: 'header_text',
    checked: true,
    width: '30%',
  },
  {
    text: i18n.t('description'),
    align: 'start',
    value: 'description',
    sortable: false,
    class: 'header_text',
    checked: true,
    width: '40%',
  },
  {
    text: i18n.t('configurations.title'),
    align: 'start',
    value: 'options',
    sortable: false,
    class: 'header_text',
    checked: true,
    width: '20%',
  },
  {
    text: '',
    align: 'end',
    value: 'actions',
    sortable: false,
    class: 'header_text',
    width: '10%',
    checked: true,
  },
];
