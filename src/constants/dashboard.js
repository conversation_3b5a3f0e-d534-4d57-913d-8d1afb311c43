export const relationsMappingNames = {
  'testExecution': 'Test Executions',
  'defect': 'Defects',
  'testRun': 'Test Runs',
  'milestone': 'Milestones',
  'testPlan': 'Test Plans'
}

export const charts = [
  {
    type: 'donut',
    name: 'Donut chart',
    value: 'donutChart'
  },
  {
    type: 'line',
    name: 'Line chart',
    value: 'lineChart'
  },
  {
    type: 'bar',
    name: 'Bar chart',
    value: 'barChart'
  },
  {
    type: 'radar',
    name: 'Radar chart',
    value: 'radarChart'
  },
  {
    type: 'heatmap',
    name: 'Heatmap chart',
    value: 'heatmapChart'
  },
  // {
  //   type: 'boxPlot',
  //   name: 'Boxplot chart',
  //   value: 'boxplotChart'
  // }
]

export const  defaultNonAxis = {
  name: 'XXX',        
  status:{ 
      "Status#1":{
          "color": "#42A5F5",
          "value": 42,
      },
      "Status#2":{
          "color": "#66BB6A",
          "value": 44,
      },
      "Status#3":{
          "color": "#FFA726",
          "value": 32,
      },
      "Status#4":{
          "color": "#EF5350",
          "value": 80,
      },
  },
  split: 'status'
}



export const defaultLine = [{
  color:  '#0C2FF3',
  name: "XXX",
  data: [
    {
      "x": "XX1",
      "y": 5
    },
    {
      "x": "XX2",
      "y": 8
    },
    {
      "x": "XX3",
      "y": 1
    },
    {
      "x": "XX4",
      "y": 6
    },
    {
      "x": "XX5",
      "y": 12
    },
    {
      "x": "XX6",
      "y": 4
    },
    {
      "x": "XX7",
      "y": 8
    },
  ]
}]

export const defaultRerun = [{
  color:  '#0C2FF3',
  name: "XXX",
  data: [
    {
      "x": "Cycle 1",
      "y": 7
    },
    {
      "x": "Cycle 2",
      "y": 2
    },
    {
      "x": "Cycle 3",
      "y": 4
    },
    {
      "x": "Cycle 4",
      "y": 3
    },
    {
      "x": "Cycle 5",
      "y": 7
    },
  ]
}]

export const  defaultBarRerun =[{
  color:  '#FFA726',
  name: "XXX",
  data: [
    {
      "x": "Run 1",
      "y": 5
    },
    {
      "x": "Run 2",
      "y": 8
    },
    {
      "x": "Run 3",
      "y": 1
    },
    {
      "x": "Run 4",
      "y": 6
    },
    {
      "x": "Run 5",
      "y": 12
    },
  ]
}]

export const defaultPlanBreakdown =[{
  color:  '#FFA726',
  name: "XXX",
  data: [
    {
      "x": "Test Plan 1",
      "y": 2
    },
    {
      "x": "Test Plan 2",
      "y": 6
    },
    {
      "x": "Test Plan 3",
      "y": 4
    },
    {
      "x": "Test Plan 4",
      "y": 8
    },
    {
      "x": "Test Plan 5",
      "y": 3
    },
  ]
}]

export const  defaultLines = [{
  color: '#EF5350',
  name: "#Status 1",
  data: [
    {
      "x": "XX1",
      "y": 5
    },
    {
      "x": "XX2",
      "y": 8
    },
    {
      "x": "XX3",
      "y": 1
    },
    {
      "x": "XX4",
      "y": 6
    },
    {
      "x": "XX5",
      "y": 12
    },
    {
      "x": "XX6",
      "y": 4
    },
    {
      "x": "XX7",
      "y": 8
    },
  ]
},
{
  color: '#FFA726',
  name: "#Status 2",
  data: [
    {
      "x": "XX1",
      "y": 9
    },
    {
      "x": "XX2",
      "y": 7
    },
    {
      "x": "XX3",
      "y": 2
    },
    {
      "x": "XX4",
      "y": 12
    },
    {
      "x": "XX5",
      "y": 3
    },
    {
      "x": "XX6",
      "y": 6
    },
    {
      "x": "XX7",
      "y": 1
    },
  ]
},
{
  color: '#66BB6A',
  name: "#Status 3",
  data: [
    {
      "x": "XX1",
      "y": 2
    },
    {
      "x": "XX2",
      "y": 6
    },
    {
      "x": "XX3",
      "y": 4
    },
    {
      "x": "XX4",
      "y": 9
    },
    {
      "x": "XX5",
      "y": 6
    },
    {
      "x": "XX6",
      "y": 3
    },
    {
      "x": "XX7",
      "y": 4
    },
  ]
}]

export const entitiesType = [
  {
    entityType: 'testCase',
    value: 'XXXX',
    name: 'Test Cases',
    disabled: false,
  },
  {
    entityType: 'testRun',
    value: 'XXXX',
    name: 'Test Runs',
    disabled: false,
  },
  {
    entityType: 'testExecution',
    value: 'XXXX',
    name: 'Test Executions',
    disabled: false
  },
  {
    entityType: 'milestone',
    value: 'XXXX',
    name: 'Milestones',
    disabled: false
  },
  {
    entityType: 'testPlan',
    value: 'XXXX',
    name: 'Test plans',
    disabled: false,
  },
  {
    entityType: 'defect',
    value: 'XXXX',
    name: 'Defects',
    disabled: false,
  },
]