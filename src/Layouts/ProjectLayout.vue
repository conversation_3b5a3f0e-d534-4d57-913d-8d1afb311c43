<template>
  <v-container fluid>
    <Appbar />
    <error404 v-if="show404" />
    <div
      v-else
      class="d-flex"
    >
      <div
        class="pr-0 pb-0"
        :style="{ width: isProjectMenuCollapsed ? '4vw' : '12vw' }"
        :class="{ 'mw-leftmenu': !isProjectMenuCollapsed }"
      >
        <project-left-menu :menu-items="menuItems" />
      </div>

      <div
        :style="{ width: isProjectMenuCollapsed ? '96vw' : '88vw' }"
        class="pb-0"
      >
        <router-view />
      </div>
    </div>
    <span id="versionSpan">
      {{ versionString }}
    </span>
    <div v-if="isTextAssistOpen">
      <AiAssist />
    </div>
  </v-container>
</template>

<script>
import { mapGetters } from 'vuex';
import { createNamespacedHelpers } from 'vuex';
import DefectIcon from '@/assets/svg/left-menu/defect.svg';
import MilestoneIcon from '@/assets/svg/left-menu/milestone.svg';
import TestPlansIcon from '@/assets/svg/left-menu/test-plans.svg';
import TestRunsIcon from '@/assets/svg/left-menu/test-runs.svg';
import TestCasesIcon from '@/assets/svg/left-menu/test-cases.svg';
import TemplatesIcon from '@/assets/svg/left-menu/templates.svg';
import DashboardIcon from '@/assets/svg/left-menu/dashboard.svg';
import CustomFieldsIcon from '@/assets/svg/left-menu/custom-fields.svg';
import SharedStepsIcon from '@/assets/svg/left-menu/shared-steps.svg';
import error404 from "@/components/base/error404.vue";
import ConfigurationsIcon from '@/assets/svg/admin-left-menu/config.svg';
import Appbar from "@/components/Layout/Appbar/Index.vue"
import ProjectLeftMenu from '@/components/Project/ProjectLeftMenu';
import UsersIcon from '@/assets/svg/admin-left-menu/users.svg';
import AiAssist from '@/components/base/AiAssist.vue';
import data from '@/assets/svg/admin-left-menu/data.svg'
const { mapState } = createNamespacedHelpers('user');

export default {
  name: 'ProjectLayout',

  components: {
    Appbar,
    ProjectLeftMenu,
    AiAssist,
    error404
  },
  computed: {
    ...mapGetters(['isProjectMenuCollapsed', 'isTextAssistOpen']),
    ...mapGetters("error404", ["show404"]),
    ...mapState(['currentAccount']),
    _readDefect(){
      return this.authorityTo('read_defect')
    },
    _readDashboard(){
      return this.authorityTo('read_dashboard')
    },
    _readRole(){
      return this.authorityTo('read_role')
    },
    _readMember(){
      return this.authorityTo('read_member')
    },
    versionString() {
      if (
        import.meta.env.VITE_APP_VERSION &&
        import.meta.env.VITE_APP_STRIPE_PUBLIC_KEY.indexOf('live') < 0
      ) {
        return `FRONTEND VERSION: ${import.meta.env.VITE_APP_VERSION}`;
      }
      return '';
    },
    menuItems() {
      const handle = this.$route.params.handle
      const projectKey = this.$route.params.key
      const isOrgScope = this.currentAccount?.type === 'org';
      return [
        {
          group: 'General',
          list: [
            ...(this._readDashboard ? [{ id: 1, title: 'Dashboard', icon: DashboardIcon, className: 'stroke', to: { name: 'ProjectDashboard', params: { handle: handle, key: projectKey } }, isActive: false }] : []),
            { id: 2, title: 'Milestones', icon: MilestoneIcon, className: 'stroke', to: { name: 'Milestones', params: { handle: handle, key: projectKey } }, isActive: false },
            { id: 3, title: 'Test plans', icon: TestPlansIcon, className: 'stroke', to: { name: 'TestPlans', params: { handle: handle, key: projectKey } }, isActive: false },
            { id: 4, title: 'Test runs', icon: TestRunsIcon, className: 'stroke', to: { name: 'Runs', params: { handle: handle, key: projectKey } }, isActive: false },
            { id: 5, title: 'Test cases', icon: TestCasesIcon, className: 'stroke', to: { name: 'Cases', params: { handle: handle, key: projectKey } }, isActive: false },
            ...(this._readDefect ? [{ id: 6, title: 'Defects', icon: DefectIcon, className: 'stroke', to: { name: 'Defects', params: { handle: handle, key: projectKey } }, isActive: false }] : []),
          ]
        },
        {
          group: 'Settings',
          list: [
          ...(projectKey && isOrgScope && this._readMember ? [{id: 7, title: 'Users', icon: UsersIcon, className: 'stroke', to: { name: 'ProjectUsers', params: { handle: handle, key: projectKey } }, isActive: false}] : []),
            { id: 8, title: 'Templates', icon: TemplatesIcon, className: 'stroke', to: { name: 'Templates', params: { handle: handle, key: projectKey } }, isActive: false },
            { id: 9, title: 'Custom fields', icon: CustomFieldsIcon, className: 'stroke', to: { name: 'CustomFields', params: { handle: handle, key: projectKey } }, isActive: false },
            { id: 10, title: 'Shared steps', icon: SharedStepsIcon, className: 'fill', to: { name: 'SharedSteps', params: { handle: handle, key: projectKey } }, isActive: false },
            { id: 11, title: 'Configurations', icon: ConfigurationsIcon, className: 'stroke', to: { name: 'Configurations', params: { handle: handle, key: projectKey } }, isActive: false },
            { id: 12, title: 'Data', icon: data, className: 'stroke', to: { name: 'Data', params: { handle: handle, key: projectKey } }, isActive: false },
          ]
        }
      ];
    }
  },
}
</script>

<style scoped>
#versionSpan {
  position: absolute;
  bottom: 0px;
  left: 0px;
  padding: 0 1em;
  color: white;
  background: black
}

.app-bar-project {
  margin-left: 12px;
  margin-right: 12px;
}

</style>