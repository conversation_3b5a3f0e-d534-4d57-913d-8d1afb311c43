import Vue from 'vue';
import Vuex from 'vuex';
import createPersistedState from 'vuex-persistedstate';
import makeUser from './modules/user';
import makeProject from './modules/project';
import makeAttachment from './modules/attachment';
import makeHeader from './modules/tableHeaders';
import makeError404 from './modules/error404';
import makeIntegration from './modules/integration';
import makeSkeletons from './modules/skeletons';
import makeEncryption from './modules/publicKey';
import makeTestCasesStore from './modules/testCases';
import makeWorkSpaceStore from './modules/workspace';
import makeStorage from './modules/storage';
import makeAuditLog from '../services/api/auditLog';
import makeDefectStore from './modules/defects';
import makeVersions from './modules/versions';
Vue.use(Vuex);

export default function makeStore(api) {
  return new Vuex.Store({
    state: {
      isTextAssistOpen: false,
      textAssistField: {},
      textAssistCallback: () => {},
      isProjectMenuCollapsed: false,
      isMenuCollapsed: false,
      isTestMenuCollapsed: false,
      isLoading: false,
      loadingText: '',
      filter: 'open',
    },
    mutations: {
      toggleMenu(state) {
        state.isMenuCollapsed = !state.isMenuCollapsed;
      },
      toggleProjectMenu(state) {
        state.isProjectMenuCollapsed = !state.isProjectMenuCollapsed;
      },
      toggleTestMenu(state) {
        state.isTestMenuCollapsed = !state.isTestMenuCollapsed;
      },
      toggleTextAssist(state) {
        state.isTextAssistOpen = !state.isTextAssistOpen;
      },
      setTextAssistData(state, { field, callback }) {
        state.textAssistField = field;
        state.textAssistCallback = callback;
      },
      setFilter(state, newFilter) {
        state.filter = newFilter;
      }
    },
    getters: {
      isMenuCollapsed: (state) => state.isMenuCollapsed,
      isProjectMenuCollapsed: (state) => state.isProjectMenuCollapsed,
      isTestMenuCollapsed: (state) => state.isTestMenuCollapsed,
      isTextAssistOpen: (state) => state.isTextAssistOpen,
      textAssistField: (state) => state.textAssistField,
      textAssistCallback: (state) => state.textAssistCallback,
      isLoading: () => false, //state.isLoading,
      loadingText: (state) => state.loadingText,
      activeMilestoneFilter: (state) => state.filter,
    },
    modules: {
      user: makeUser(api),
      headers: makeHeader(),
      encryption: makeEncryption(api),
      attachment: makeAttachment(api),
      project: makeProject(api),
      error404: makeError404(),
      integration: makeIntegration(),
      skeletons: makeSkeletons(),
      testCases: makeTestCasesStore(),
      workspace: makeWorkSpaceStore(),
      storage: makeStorage(api),
      auditLog: makeAuditLog(api),
      defects: makeDefectStore(),
      versions: makeVersions(),
    },
    plugins: [
      createPersistedState({
        paths: [
          'user',
          'headers',
          'org',
          'integration',
          'skeletons',
          'testCases',
          'workspace',
          'defects',
          'versions',
        ],
      }),
    ],
  });
}
