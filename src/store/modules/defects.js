export default function makeDefectStore() {
  return {
    namespaced: true,
    
    state: {
      projectIntegrationPreferences: {}, // Store per-project preferences: { handle_projectKey: integration }
      createDefectIntegrationPreferences: {}, // Store per-project integration UID preferences: { handle_projectKey: { service, integrationUid } }
      lastSelectedIntegration: {}, // Store last manually selected integration: { handle_projectKey: { service, integrationUid } }
      lastSelectedRepoProject: {}, // Store last selected repo/project for each integration: { handle_projectKey_integrationUid: { repoProjectUid, repoProjectName, resourceId } }
    },
    
    mutations: {
      SET_PROJECT_INTEGRATION_PREFERENCE(state, { handle, projectKey, integration }) {
        const key = `${handle}_${projectKey}`;
        state.projectIntegrationPreferences[key] = integration;
      },
      SET_CREATE_DEFECT_INTEGRATION_PREFERENCE(state, { handle, projectKey, service, integrationUid }) {
        const key = `${handle}_${projectKey}`;
        state.createDefectIntegrationPreferences[key] = { service, integrationUid };
      },
      SET_LAST_SELECTED_INTEGRATION(state, { handle, projectKey, service, integrationUid }) {
        const key = `${handle}_${projectKey}_${service}`;
        state.lastSelectedIntegration[key] = { service, integrationUid };
      },
      SET_LAST_SELECTED_REPO_PROJECT(state, { handle, projectKey, integrationUid, repoProjectUid, repoProjectName, resourceId }) {
        const key = `${handle}_${projectKey}_${integrationUid}`;
        state.lastSelectedRepoProject[key] = { repoProjectUid, repoProjectName, resourceId };
      },
    },
    
    actions: {
      setProjectIntegrationPreference({ commit }, { handle, projectKey, integration }) {
        commit('SET_PROJECT_INTEGRATION_PREFERENCE', { handle, projectKey, integration });
      },
      setCreateDefectIntegrationPreference({ commit }, { handle, projectKey, service, integrationUid }) {
        commit('SET_CREATE_DEFECT_INTEGRATION_PREFERENCE', { handle, projectKey, service, integrationUid });
      },
      setLastSelectedIntegration({ commit }, { handle, projectKey, service, integrationUid }) {
        commit('SET_LAST_SELECTED_INTEGRATION', { handle, projectKey, service, integrationUid });
      },
      setLastSelectedRepoProject({ commit }, { handle, projectKey, integrationUid, repoProjectUid, repoProjectName, resourceId }) {
        commit('SET_LAST_SELECTED_REPO_PROJECT', { handle, projectKey, integrationUid, repoProjectUid, repoProjectName, resourceId });
      },
    },
    
    getters: {
      getProjectIntegrationPreference: (state) => (handle, projectKey) => {
        const key = `${handle}_${projectKey}`;
        return state.projectIntegrationPreferences[key] || null;
      },
      getCreateDefectIntegrationPreference: (state) => (handle, projectKey) => {
        const key = `${handle}_${projectKey}`;
        return state.createDefectIntegrationPreferences[key] || null;
      },
      getLastSelectedIntegration: (state) => (handle, projectKey, service) => {
        const key = `${handle}_${projectKey}_${service}`;
        return state.lastSelectedIntegration[key] || null;
      },
      getLastSelectedRepoProject: (state) => (handle, projectKey, integrationUid) => {
        const key = `${handle}_${projectKey}_${integrationUid}`;
        return state.lastSelectedRepoProject[key] || null;
      },
    },
  };
} 