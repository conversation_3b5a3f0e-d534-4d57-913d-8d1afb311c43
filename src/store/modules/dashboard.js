import makeDashboardService from "../../services/api/dashboards"

export const getters = {
  getDashboardData: (state) => ({handle, projectKey, view}) => {
    const isUserWorkspace = handle === state.user.handle;
    const orgIndex = state.orgs.findIndex(element => element.handle === handle);
    if (isUserWorkspace) {
      if(projectKey)
        if(view){
          return state.user.projectDashboards?.[projectKey][view]
        }else{
          return state.user.projectDashboards?.[projectKey]
        }
      else
        return state.user.userDashboard
    }
    if(projectKey)
      return view ? state.orgs[orgIndex]?.projectDashboards?.[projectKey]?.[view] : state.orgs[orgIndex]?.projectDashboards?.[projectKey];
    else
      return state.orgs[orgIndex]?.orgDashboard
  },
  entityName: () => (entityType) => {
    const entities = {
      testPlan: 'Test Plans',
      testCase: 'Test Cases',
      defect: 'Defects',
      testRun: 'Test Runs',
      testExecution: 'Test Executions',
      milestone: 'Milestones'
    }

    return entities[entityType]
  },
  period: (_, getters) => ({handle, projectKey, view}) => {
    const dashboard = getters.getDashboardData({projectKey,handle, view});
    return dashboard?.period
  },
  getFilters: (_, getters) => ({handle, projectKey, view}) => {
    const dashboard = getters.getDashboardData({projectKey,handle, view});
    return dashboard?.filters ?? {
      projects: [],
      testPlans: [],
      testMilestones: [],
      tags: []
    }
  },
  lastUpdatedAt: (_, getters) => ({handle, projectKey, view}) =>{
    const dashboard = getters.getDashboardData({projectKey,handle, view});
    return dashboard?.lastUpdatedAt;
  },
  storedCharts: (_, getters) => ({projectKey, handle, view}) => {
    const dashboard = getters.getDashboardData({projectKey, handle, view});
    return dashboard?.charts;
  },
  dashboardUid: (_, getters) => ({projectKey, handle, view}) => {
    const dashboard = getters.getDashboardData({projectKey,handle, view});
    return dashboard?.dashboardUid;
  }
}

export const mutations = {
  setDashboardData( state, { projectKey, handle, data, view } ){
    const isUserWorkspace = handle == state.user.handle;
    const orgIndex = state.orgs.findIndex(element => element.handle == handle);
    if(isUserWorkspace){
      if(!state.user.userDashboard) state.user.userDashboard = {};
      if(!state.user.projectDashboards) state.user.projectDashboards = {};
      if(projectKey){
        if(!state.user.projectDashboards[projectKey])
          state.user.projectDashboards[projectKey] = {}
        if(projectKey && view){
          state.user.projectDashboards[projectKey][view] = {
            ...state.user.projectDashboards[projectKey][view],
            ...data
          }
        }else{
          state.user.projectDashboards[projectKey] = {
            ...state.user.projectDashboards[projectKey],
            ...data
          }
        }
      }
      else{
        state.user.userDashboard = {
          ...state.user.userDashboard,
          ...data
        }
      }
    }else{
      if(!state.orgs[orgIndex].orgDashboard) state.orgs[orgIndex].orgDashboard = {}
      if(!state.orgs[orgIndex].projectDashboards) state.orgs[orgIndex].projectDashboards = {}
      if(projectKey){
        if(!state.orgs[orgIndex].projectDashboards[projectKey])
          state.orgs[orgIndex].projectDashboards[projectKey] = {}
        if(projectKey && view){
          state.orgs[orgIndex].projectDashboards[projectKey][view] = {
            ...state.orgs[orgIndex].projectDashboards[projectKey][view],
            ...data
          }
        }else{
          state.orgs[orgIndex].projectDashboards[projectKey] = {
            ...state.orgs[orgIndex].projectDashboards[projectKey],
            ...data
          }
        }
      }
      else{
        state.orgs[orgIndex].orgDashboard = {
          ...state.orgs[orgIndex].orgDashboard,
          ...data
        }
      }
    }
  },
  saveChart(state, { chartID, modifiedValues, projectKey, handle, view}) {
    const isUserWorkspace = handle == state.user.handle;
    const orgIndex = state.orgs.findIndex(element => element.handle == handle);
    if(isUserWorkspace){
      if(projectKey){
        if(view){
          const chartIndex = state.user.projectDashboards[projectKey][view].charts.findIndex(element => element.id == chartID);
          if(chartID >= 0){
            const newChart = {
              ...state.user.projectDashboards[projectKey][view].charts[chartIndex],
              ...modifiedValues
            }
            state.user.projectDashboards[projectKey][view].charts.splice(chartIndex, 1, newChart)
          }
        }else{
          const chartIndex = state.user.projectDashboards[projectKey].charts.findIndex(element => element.id == chartID);
          if(chartID >= 0){
            const newChart = {
              ...state.user.projectDashboards[projectKey].charts[chartIndex],
              ...modifiedValues
            }
            state.user.projectDashboards[projectKey].charts.splice(chartIndex, 1, newChart)
          }
        }
      }else{
        const chartIndex = state.user.userDashboard.charts.findIndex(element => element.id == chartID);
        const newChart = {
          ...state.user.userDashboard.charts[chartIndex],
          ...modifiedValues
        }
        state.user.userDashboard.charts.splice(chartIndex, 1, newChart)
      }
    }else{
      if(projectKey){
        if(view){
          const chartIndex = state.orgs[orgIndex].projectDashboards[projectKey][view].charts.findIndex(element => element.id == chartID);
          const newChart = {
            ...state.orgs[orgIndex].projectDashboards[projectKey][view].charts[chartIndex],
            ...modifiedValues
          }
          state.orgs[orgIndex].projectDashboards[projectKey][view].charts.splice(chartIndex, 1, newChart)
        }else{
          const chartIndex = state.orgs[orgIndex].projectDashboards[projectKey].charts.findIndex(element => element.id == chartID);
          const newChart = {
            ...state.orgs[orgIndex].projectDashboards[projectKey].charts[chartIndex],
            ...modifiedValues
          }
          state.orgs[orgIndex].projectDashboards[projectKey].charts.splice(chartIndex, 1, newChart)
        }
      }else{
        const chartIndex = state.orgs[orgIndex].orgDashboard.charts.findIndex(element => element.id == chartID);
        const newChart = {
          ...state.orgs[orgIndex].orgDashboard.charts[chartIndex],
          ...modifiedValues
        }
        state.orgs[orgIndex].orgDashboard.charts.splice(chartIndex, 1, newChart)
      }
    }
  },
}

export const actions = (api) => {
  const dashboardService = makeDashboardService(api);
  return {
    setDashboard({commit}, { charts, lastUpdatedAt, handle, projectKey, dashboardUid, view}){
      const dashboardData = {
        charts,
        lastUpdatedAt,
        dashboardUid
      };

      commit('setDashboardData', { projectKey, handle, data: dashboardData, view })
    },
    storePeriod({commit}, { text, value, projectKey, handle, view}){
      const period = {
        text,
        value
      }
      commit('setDashboardData', { projectKey, handle, data: { period }, view})
    },
    storeFilters({commit}, { projectKey, handle, filters, view}){
      commit('setDashboardData', { projectKey, handle, data: { filters }, view})
    },
    storeCharts({commit}, { projectKey, handle, charts, view}){
      commit('setDashboardData', { projectKey, handle, data: { charts }, view})
    },
    async pullChart({commit}, {handle, projectKey, queries, view, dashboardID, chartID}){
      const params = {
        ...queries
      }

      await dashboardService.getMissingDashboardChart(handle, params, projectKey, dashboardID, chartID).then(response => {
        commit('saveChart', { chartID, projectKey, handle, view, modifiedValues: { entityData: response.data} })
      }).catch(() => {
        commit('saveChart', { chartID, projectKey, handle, view, modifiedValues: { retryTimes: 3} })
      })
    },
    forceLoadChart({commit, getters}, {chartID, projectKey, handle, view}){
      const charts = getters.getDashboardData({handle, projectKey, view}).charts;
      const chartIndex = charts.findIndex(element => element.id == chartID);
      commit('saveChart', { chartID, projectKey, handle, view, modifiedValues: { key: charts[chartIndex].key+=1} })
    }
  }
}