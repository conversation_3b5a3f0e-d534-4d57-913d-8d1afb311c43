import credentialService from "@/services/local/credential";
import makeUserService from '@/services/api/user';
import makeOrgService from '@/services/api/org';
import makeProjectsService from '@/services/api/project';
import { getters as dashboardGetters, actions as dashboardActions, mutations as dashboardMutations } from './dashboard'
import { mutations as preferencesMutations, getters as preferencesGetters, actions as preferencesActions} from './preferences'
// CTODO - Shouldn't be a factory external api access should not be in state files
const state = {
  user: credentialService.getUser(),
  orgs: credentialService.getOrgs(),
  currentAccount: null,
  signupOrgDetails: null,
  dismissedToasts: []
};

const getters = {
  userName(state) {
    return state.user ? `${state.user.first_name} ${state.user.last_name}` : "";
  },
  isAuthenticated(state) {
    return state?.user;
  },
  dismissedToasts(state){
    return state?.dismissedToasts || []
  },
  isToastDismissed: (state) => (toastId) => {
    return state.dismissedToasts?.includes(toastId);
  },
  currentAccount(state) {
    return state?.currentAccount;
  },
  user(state) {
    return state?.user;
  },
  accounts(state) {
    return state.accounts;
  },
  orgs(state) {
    return state.orgs;
  },
  getUserPreferences(state) {
    return state?.user.preferences || {};
  },
  getSignupOrgDetails(state) {
    return state.signupOrgDetails;
  },
  getUserStatusColorsByEntityType: (_,getters) => (entityType) => {
    const preferencesData = getters.getUserPreferences;
    return preferencesData?.statusColors?.filter((status) => status.entityType === entityType) || [];
  },
  getUserPriorityColorsByEntityType: (_, getters) => (entityType) => {
    const preferencesData = getters.getUserPreferences;
    return preferencesData?.priorityColors?.filter((priority) => priority.entityType === entityType) || [];
  },
  /**
   * returns true if the current user is org admin
   * @param {String} handle
   * @return {Boolean} isAdmin
   */
  isOrgAdmin: (state) => (handle) => {
    let org = state.orgs.filter((org) => org.handle == handle)[0];
    return org && org.roleName !== "member";
  },
  /**
   * get org with org handle
   * @param {String} handle
   * @return {Object} org
   */
  getOrg: (state) => (handle) => {
    return state.orgs.filter((org) => org.handle == handle)[0];
  },
  currentPermissions: (state) => (handle, projectKey) => {
    if (!handle) {
      console.log('Handle required for authz.');
    }
    const orgIndex = state.orgs.findIndex((org) => org.handle === handle);
    if(orgIndex == -1)
      return []
      
    const isWorkspacePermissions = state.user.orgs?.map(item => item.handle).includes(handle);
    return projectKey ? (isWorkspacePermissions ?  state.user?.projectAuthz?.[projectKey]?.permissions : state.orgs[orgIndex].projectAuthz[projectKey].permissions) : (isWorkspacePermissions ? state.user.orgAuthzPermissions : state.orgs[orgIndex].orgAuthzPermissions);
  },
  ...dashboardGetters,
  ...preferencesGetters
};

const mutations = {
  setProjectAuthz(state, { permissions, handle, projectKey }) {
    const orgIndex = state.orgs.findIndex((org) => org.handle === handle);
    if (!state.orgs[orgIndex].projectAuthz) state.orgs[orgIndex].projectAuthz = {};
    state.orgs[orgIndex].projectAuthz[projectKey] = {
      permissions,
      updatedAt: Date.now(),
    };
  },
  _setOrgAuthz(state, { permissions, handle }) {
    const orgIndex = state.orgs.findIndex((org) => org.handle === handle);
    if (orgIndex === -1) return false;
    state.orgs[orgIndex].orgAuthzPermissions = permissions;
    state.orgs[orgIndex].orgAuthzUpdatedAt = Date.now();
    return true;
  },

  setUser(state, user) {
    state.user = user;
  },
  updateUser(state, user) {
    state.user = { ...state.user, ...user };
  },
  setOrgs(state, orgs) {
    state.orgs = orgs;
  },
  setSignupOrgDetails(state, orgDetails) {
    state.signupOrgDetails = orgDetails;
  },
  updateOrg(state, org){
    const findIndex = state.orgs.findIndex(element => element.uid == org.uid)
    state.orgs[findIndex] = org;
  },
  setCurrentAccount(state, currentAccount) {
    state.currentAccount = currentAccount;
  },
  emptyState(state) {
    state.user = null;
    state.currentAccount = null;
    state.orgs = [];
  },
  setAccounts(state, accounts) {
    state.accounts = accounts;
  },
  setUserPreferences(state, preferences) {
    state.user.preferences = preferences;
  },
  setDismissedToasts(state, dismissedToast) {
    const dismissedToasts = state?.dismissedToasts || [];
    if(Array.isArray(dismissedToasts) && !dismissedToasts.includes(dismissedToast))
      state.dismissedToasts.push(dismissedToast)
  },
  ...dashboardMutations,
  ...preferencesMutations
};
const makeActions = (api) => {
  return {
    initSettings({ state, dispatch }) {
      const currentAccount = state.currentAccount;
      
      // Verify current account access
      if (currentAccount && state.orgs) {
        const hasAccess = state.orgs.some(org => org.uid === currentAccount.uid);
        if (!hasAccess) {
          // User no longer has access to current account, reset to first available org
          if (state.orgs.length > 0) {
            const defaultAccount = {
              isOrg: true,
              ...state.orgs[0],
            };
            state.currentAccount = defaultAccount;
          } else {
            state.currentAccount = null;
          }
        } else {
          state.currentAccount = currentAccount;
        }
      }
      
      // Set default account if none exists
      if (!state.currentAccount && state.orgs && state.orgs.length > 0) {
        const defaultAccount = {
          isOrg: true,
          ...state.orgs[0],
        };
        state.currentAccount = defaultAccount;
      }
      
      if (state.currentAccount) {
        dispatch("project/get", state.currentAccount.uid, { root: true });
      }
    },
    async updateProjectAuthz({ commit }, { handle, projectKey }) {
      try {
        const projectsService = makeProjectsService(api);
        const response = await projectsService.getAuthz(handle, projectKey);
        const projectAuthz = response?.data?.[handle]?.projects?.[projectKey];

        if(projectAuthz) {
        commit('setProjectAuthz', {
          permissions: projectAuthz,
          handle,
          projectKey,
        });}
      } catch (error) {
        console.error('Error updating project authz:', error);
      }
    },
    async setOrgAuthz({ commit, dispatch, state }, { permissions, handle }) {

      commit('_setOrgAuthz', { permissions, handle });
      if (state.orgs.find(org => org.handle === handle)) return;

      const userService = makeUserService(api);

      try {
        const response = await userService.getOrgs(handle);
        await dispatch('setOrgs', response.data?.orgs || []);
        commit('_setOrgAuthz', { permissions, handle });
        if (state.orgs.find(org => org.handle === handle)) return;
        commit('error404/SET_SHOW_404', true, { root: true });
      } catch (error) {
        console.error('Error setting org authz:', error);
      }
    },
    async updateOrgAuthz({ dispatch }, handle) {
      const orgService = makeOrgService(api);
      try {
        const response = await orgService.getAuthz(handle);
          if (response.data?.[handle]?.permissions) {
            await dispatch('setOrgAuthz', {
              permissions: response.data[handle].permissions,
              handle,
            });
          }
      } catch (error) {
        console.error('Error updating org authz:', error);
      }
    },
    /**
     * set currently selected account
     * @param {Object} currentAccount
     */
    setCurrentAccount({ commit }, currentAccount) {
      commit("setCurrentAccount", currentAccount);
    },
    setOrgs({ commit }, newOrgs) {
      credentialService.setOrgs(newOrgs);
      commit("setOrgs", newOrgs);
    },
    /**
     * set user on org and cahce
     * @param {Object} newUser
     */
    setUser({ commit }, newUser) {
      const { preferences, ...data } = newUser;
      const fieldsToIgnore = ['secret', 'phoneNumber', 'recoveryCodes'];

      if(preferences) {
        fieldsToIgnore.forEach(field => {
          if (field in preferences) {
            delete preferences[field];
          }
        });

      }

      credentialService.setUser({
        ...data,
        preferences: preferences || {},
      });
      commit("setUser", {
        ...data,
        preferences: preferences || {},
      });
    },
    setUserPreferences({ commit }, preferences) { 
      commit("setUserPreferences", preferences);
    },
    /**
     * persists a user session for subsequent auth
     * @param {*} param0
     * @param {*} accounts
     */
    initSession({ dispatch }, { user, currentAccount, orgs }) {
      dispatch("setUser", user);
      dispatch("setCurrentAccount", currentAccount);
      dispatch("setOrgs", orgs || []);
    },
    /**
     * set dismissed toasts
     * @param {*} param0
     */
    setDismissedToasts({ commit }, dismissedToast) {
      commit("setDismissedToasts", dismissedToast);
    },
    ...dashboardActions(api),
    ...preferencesActions(api)
  }
};

export default function makeUser(api) {
  return {
    namespaced: true,
    state,
    getters,
    mutations,
    actions: makeActions(api),
  };
}
