const MAX_BACKEND_REDIRECTS_PER_INTERVAL = 1;
const MAX_FRONTEND_RELOADS_PER_INTERVAL = 1;
const TRACKING_WINDOW_MS = 10 * 60 * 1000; 

const state = {
  frontendVersion: null,
  backendVersion: null,
  backendRedirectAttempts: [], 
  frontendReloadAttempts: [],
  frontendMismatchHandled: null, 
};

const cleanupOldAttempts = (state) => {
  const now = Date.now();
  
  state.backendRedirectAttempts = state.backendRedirectAttempts.filter(
    timestamp => now - timestamp < TRACKING_WINDOW_MS
  );
  state.frontendReloadAttempts = state.frontendReloadAttempts.filter(
    timestamp => now - timestamp < TRACKING_WINDOW_MS
  );
  
  if (state.frontendMismatchHandled && 
      (now - state.frontendMismatchHandled.timestamp) > TRACKING_WINDOW_MS) {
    state.frontendMismatchHandled = null;
  }
};

const mutations = {
  setFrontendVersion(state, version) {
    state.frontendVersion = version;
  },
  setBackendVersion(state, version) {
    state.backendVersion = version;
  },
  setVersions(state, { frontendVersion, backendVersion }) {
    if (frontendVersion !== undefined) state.frontendVersion = frontendVersion;
    if (backendVersion !== undefined) state.backendVersion = backendVersion;
  },
  addBackendRedirectAttempt(state) {
    const now = Date.now();
    state.backendRedirectAttempts.push(now);
    cleanupOldAttempts(state);
  },
  addFrontendReloadAttempt(state) {
    const now = Date.now();
    state.frontendReloadAttempts.push(now);
    cleanupOldAttempts(state);
  },
  setFrontendMismatchHandled(state, version) {
    state.frontendMismatchHandled = {
      version,
      timestamp: Date.now()
    };
  },
  clearFrontendMismatchHandled(state) {
    state.frontendMismatchHandled = null;
  },
  cleanupOldAttempts(state) {
    cleanupOldAttempts(state);
  },
};

const getters = {
  frontendVersion: (state) => state.frontendVersion,
  backendVersion: (state) => state.backendVersion,
  hasVersions: (state) => !!(state.frontendVersion || state.backendVersion),
  
  backendRedirectCount: (state) => {
    const now = Date.now();
    return state.backendRedirectAttempts.filter(
      timestamp => now - timestamp < TRACKING_WINDOW_MS
    ).length;
  },
  
  frontendReloadCount: (state) => {
    const now = Date.now();
    return state.frontendReloadAttempts.filter(
      timestamp => now - timestamp < TRACKING_WINDOW_MS
    ).length;
  },
  
  canBackendRedirect: (state, getters) => {
    return getters.backendRedirectCount < MAX_BACKEND_REDIRECTS_PER_INTERVAL;
  },
  
  canFrontendReload: (state, getters) => {
    return getters.frontendReloadCount < MAX_FRONTEND_RELOADS_PER_INTERVAL;
  },
  
  isFrontendMismatchHandled: (state) => (version) => {
    if (!state.frontendMismatchHandled) return false;
    
    const now = Date.now();
    const isWithinWindow = (now - state.frontendMismatchHandled.timestamp) < TRACKING_WINDOW_MS;
    const isSameVersion = state.frontendMismatchHandled.version === version;
    
    return isWithinWindow && isSameVersion;
  },
};

const actions = {
  setVersions({ commit }, versions) {
    commit('setVersions', versions);
  },
  trackBackendRedirect({ commit }) {
    commit('addBackendRedirectAttempt');
  },
  trackFrontendReload({ commit }) {
    commit('addFrontendReloadAttempt');
  },
  markFrontendMismatchHandled({ commit }, version) {
    commit('setFrontendMismatchHandled', version);
  },
  cleanupTracking({ commit }) {
    commit('cleanupOldAttempts');
  },
  resetAllTracking({ commit }) {
    commit('clearFrontendMismatchHandled');
  },
};

export default function makeVersions() {
  return {
    namespaced: true,
    state,
    mutations,
    getters,
    actions,
  };
}
