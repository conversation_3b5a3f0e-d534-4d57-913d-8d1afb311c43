import makeHandleService from "../../services/api/handle";
import { showErrorToast } from '@/composables/utils/toast';
import Swal from 'sweetalert2';
import Vue from 'vue';

export const getters = {
  getOrgPreferences: (state) => (handle) => {
    const orgIndex = state.orgs.findIndex((org) => org.handle == handle);
    const org = state.orgs[orgIndex]?.preferences || {};
    return org || {};
  },
  getOrgStatusColorsByEntityType: (state) => (handle, entityType) => {
    const orgIndex = state.orgs.findIndex((org) => org.handle == handle);
    const org = state.orgs[orgIndex].preferences || {};
    const preferencesData = org || {};
    return preferencesData?.statusColors?.filter((status) => status.entityType === entityType) || [];
  },
  getOrgPriorityColorsByEntityType: (state) => (handle, entityType) => {
    const orgIndex = state.orgs.findIndex((org) => org.handle == handle); 
    const org = state.orgs[orgIndex]?.preferences || {};
    const preferencesData = org || {};
    return preferencesData?.priorityColors?.filter((priority) => priority.entityType === entityType) || [];
  },
}
export const mutations = {
  setOrgPreferences(state, {preferences, handle}){
    const findIndex = state.orgs.findIndex((org) => org.handle == handle);
    if(findIndex >= 0)
      Vue.set(state.orgs[findIndex], 'preferences', preferences);

  },
  setUserPreferences(state, preferences) {
    state.user.preferences = preferences;
  },
}
export const actions = (api) => {
  const handleService = makeHandleService(api)
  return{
    async getHandlePreferences({ dispatch}, {handle, accountType}){
      await handleService.getPreferences(handle).then((preferences) => {
      const preferencesData = preferences.data.preferences || preferences.data;
      const timestamp = new Date().getTime();
      dispatch('setPreferences', { preferences: { ...preferencesData, timestamp }, type: accountType, handle })
      }).catch((err) => {
        console.log(err)
        showErrorToast(Swal, 'fetchError', { item: 'preferences' }, err?.response?.data)
      });
    },
    /**
     * 
     * @param {String} handle
     * @param {String} type 
     * @param {Object} preferences 
     */
    setPreferences({commit}, {handle, type, preferences}){
      if(type == 'org')
        commit('setOrgPreferences', {handle, preferences})
      else if(type == 'user')
        commit('setUserPreferences', preferences)
    },
  }
}