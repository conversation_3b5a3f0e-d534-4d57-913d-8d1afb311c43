import makeStorageService from '@/services/api/storage';
import { showSuccessToast, showErrorToast } from '@/utils/toast';

const state = {
  storages: [],
  storage: {},
  file: {},
  task: {},
  regions: [],
};

const mutations = {
  SET_STORAGES(state, storages) {
    state.storages = storages;
  },
  ADD_STORAGE(state, storage) {
    state.storages.unshift(storage);
  },
  SET_SELECTED_STORAGE(state, storage) {
    state.storage = storage;
  },
  SET_STORAGE_file(state, file) {
    state.file = file;
  },
  SET_STORAGE_TASK(state, task) {
    state.task = task
  },
  SET_REGIONS(state, regions) {
    state.regions = regions;
  },
};

const makeActions = (api) => {
  const storageService = makeStorageService(api);

  const handleResponse = (commit, mutation, response, toastOptions) => {
    commit(mutation, response.data.profiles);
    if (toastOptions) {
      showSuccessToast(toastOptions.swal, toastOptions.messageKey, { item: toastOptions.item });
    }
  };

  return {
    async get({ commit }, handle) {
      try {
        const response = await storageService.getStorages(handle);
        if (response?.data?.profiles) {
          commit('SET_STORAGES', response.data.profiles);
        }
      } catch (err) {
        console.error('Error fetching storages:', err);
      }
    },

    async getAttachmentSize({ commit }, { swal, handle }) {
      try {
        const response = await storageService.fetchAttachmentSize(handle);
        if(response?.data){
          commit('SET_STORAGE_file', response.data);
        }
      } catch (err) {
        showErrorToast(swal, err.response.data.message);
      }
    },

    async create({ commit }, { swal, handle, payload }) {
      try {
        const response = await storageService.createStorage(handle, payload);
        handleResponse(commit, 'ADD_STORAGE', response, { swal, messageKey: 'createSuccess', item: 'Storage' });
        return response.data;
      } catch(err){
        showErrorToast(swal, err.response.data.message);
      }
    },

    async fetchAndStoreTask({ commit }, { handle, id }) {
      try {
        const response = await storageService.getScheduledTask(handle, id);
        if(response?.data){
          commit('SET_STORAGE_TASK', response.data);
        }
        return response.data;
      } catch(err){
        showErrorToast(this.$swal, err.response.data.message);
      }
    },

    async fetchAndStoreTaskRun({ commit }, { handle, id}) {
      try {
        const response = await storageService.getScheduledTaskRun(handle, id);
        if( response?.data?.task){
          commit('SET_STORAGE_TASK', response.data.task);
        }
      } catch(err) {
        console.error('Error fetching task run:', err);
      }
    },

    async update({ commit }, { swal, handle, payload }) {
      try {
        const response = await storageService.updateStorage(handle, payload, payload.uid);
        handleResponse(commit, 'SET_STORAGES', response, { swal, messageKey: 'updateSuccess', item: 'Storage' });
        return response.data;
      } catch (err) {
        showErrorToast(swal, err.response.data.message);
      }
    },
    async fetchRegions({swal, commit }) {
      try {
        const response = await storageService.getRegions();
        if (response?.data) commit('SET_REGIONS', response.data);
      } catch (err) {
        showErrorToast(swal, err.response.data.message);
      }
    },
    async setSelectedStorage({ commit, state }, data) {
      const found = state.storages?.find(item => item.provider === data.provider);
      commit('SET_SELECTED_STORAGE', found || data);
    },
  };
};

const getters = {
  hasStorages: (state) => state.storages && state.storages.length > 0,
  defaultStorage: (state) => state.storages?.find(storage => storage?.isDefault),
  hasDefaultStorage: (state, getters) => !!getters.defaultStorage,
};

export default function makeStorage(api) {
  return {
    namespaced: true,
    state,
    mutations,
    getters,
    actions: makeActions(api),
  };
}