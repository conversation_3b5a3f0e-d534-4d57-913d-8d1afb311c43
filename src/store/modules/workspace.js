const state = {
  workspaceFilter: {
    assignToList: [],
    recentProjects: []
  },
};

const mutations = {
  SET_ASSIGN_TO_LIST(state, assignToList) {
    state.workspaceFilter.assignToList = assignToList;
  },
  ADD_ASSIGN_TO(state, uid) {
    if (!state.workspaceFilter.assignToList.includes(uid)) {
      state.workspaceFilter.assignToList.push(uid);
    }
  },
  REMOVE_ASSIGN_TO(state, uid) {
    state.workspaceFilter.assignToList = state.workspaceFilter.assignToList.filter(userId => userId !== uid);
  },
  CLEAR_ASSIGN_TO(state) {
    state.workspaceFilter.assignToList = [];
  },
  ADD_RECENT_PROJECT(state, project) {
    if (!state.workspaceFilter.recentProjects.some(p => p.id === project.id)) {
      state.workspaceFilter.recentProjects.push(project);
    } else {
      // If the project already exists, remove it and add it to the end
      state.workspaceFilter.recentProjects = state.workspaceFilter.recentProjects.filter(p => p.id !== project.id);
      state.workspaceFilter.recentProjects.push(project);
    }
  },
};

const makeActions = () => ({
  setAssignToList({ commit }, assignToList) {
    commit('SET_ASSIGN_TO_LIST', assignToList);
  },
  addAssignTo({ commit }, uid) {
    commit('ADD_ASSIGN_TO', uid);
  },
  removeAssignTo({ commit }, uid) {
    commit('REMOVE_ASSIGN_TO', uid);
  },
  clearAssignTo({ commit }) {
    commit('CLEAR_ASSIGN_TO', []);
  },
  addRecentProject({ commit }, project) {
    commit('ADD_RECENT_PROJECT', project);
  },
});

const getters = {
  assignToList: (state) => state.workspaceFilter.assignToList,
  recentProjects: (state) => state.workspaceFilter.recentProjects,
};

export default function makeWorkSpaceStore() {
  return {
    namespaced: true,
    state,
    mutations,
    actions: makeActions(),
    getters
  };
}