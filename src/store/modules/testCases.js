export default function makeTestCasesStore() {
  return {
    namespaced: true,
    state: {
      selectedFolderUid: null,
      isTreeViewCollapsed: false,
      expandedFolderIds: [],
      appliedFilters: {},
      searchTerm: '',
      isDetailViewCollapsed: false,
      lastCaseId: null,
      folderTreeCache: {} // Store folder trees per project: { "handle-key": { tree: [...], lastUpdated: timestamp } }
    },
    mutations: {
      SET_SELECTED_FOLDER(state, folderUid) {
        state.selectedFolderUid = folderUid;
      },
      SET_TREE_VIEW_COLLAPSED(state, isCollapsed) {
        state.isTreeViewCollapsed = isCollapsed;
      },
      SET_EXPANDED_FOLDERS(state, expandedIds) {
        state.expandedFolderIds = expandedIds;
      },
      SET_APPLIED_FILTERS(state, filters) {
        state.appliedFilters = filters;
      },
      SET_SEARCH_TERM(state, term) {
        state.searchTerm = term;
      },
      SET_DETAIL_VIEW_COLLAPSED(state, isCollapsed) {
        state.isDetailViewCollapsed = isCollapsed;
      },
      SET_LAST_CASE_ID(state, caseId) {
        state.lastCaseId = caseId;
      },
      SET_FOLDER_TREE_CACHE(state, { projectKey, folderTree }) {
        state.folderTreeCache[projectKey] = {
          tree: folderTree,
          lastUpdated: Date.now()
        };
      },
      CLEAR_FOLDER_TREE_CACHE(state, projectKey) {
        if (projectKey) {
          delete state.folderTreeCache[projectKey];
        } else {
          state.folderTreeCache = {};
        }
      }
    },
    actions: {
      selectFolder({ commit }, folderUid) {
        commit('SET_SELECTED_FOLDER', folderUid);
      },
      toggleTreeView({ commit, state }) {
        commit('SET_TREE_VIEW_COLLAPSED', !state.isTreeViewCollapsed);
      },
      updateExpandedFolders({ commit }, expandedIds) {
        commit('SET_EXPANDED_FOLDERS', expandedIds);
      },
      applyFilters({ commit }, filters) {
        commit('SET_APPLIED_FILTERS', filters);
      },
      setSearchTerm({ commit }, term) {
        commit('SET_SEARCH_TERM', term);
      },
      toggleDetailView({ commit, state }, isCollapsed) {
        const newState = isCollapsed !== undefined ? isCollapsed : !state.isDetailViewCollapsed;
        commit('SET_DETAIL_VIEW_COLLAPSED', newState);
      },
      setLastCaseId({ commit }, caseId) {
        commit('SET_LAST_CASE_ID', caseId);
      },
      saveFolderTreeForProject({ commit }, { projectKey, folderTree }) {
        commit('SET_FOLDER_TREE_CACHE', { projectKey, folderTree });
      },
      clearFolderTreeCache({ commit }, projectKey = null) {
        commit('CLEAR_FOLDER_TREE_CACHE', projectKey);
      }
    },
    getters: {
      selectedFolderUid: state => state.selectedFolderUid,
      isTreeViewCollapsed: state => state.isTreeViewCollapsed,
      expandedFolderIds: state => state.expandedFolderIds,
      appliedFilters: state => state.appliedFilters,
      searchTerm: state => state.searchTerm,
      isDetailViewCollapsed: state => state.isDetailViewCollapsed,
      lastCaseId: state => state.lastCaseId,
      folderTreeCache: state => state.folderTreeCache,
      getFolderTreeForProject: (state) => (projectKey) => {
        return state.folderTreeCache[projectKey]?.tree || [];
      },
      getFolderTreeLastUpdated: (state) => (projectKey) => {
        return state.folderTreeCache[projectKey]?.lastUpdated || 0;
      }
    }
  };
} 