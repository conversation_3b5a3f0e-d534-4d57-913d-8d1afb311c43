
const routes = [
  {
    path: '/:handle/admin',
    component: () => import('@/Layouts/DefaultLayout.vue'),
    name: 'admin',
    redirect: () => {
      return { name: 'Tags' }
    },
    children: [ // This weird structure is to allow nesting of Layouts
      {
        path: '',
        component: () => import('@/Layouts/AdminLayout.vue'),
        children: [
          {
            path: '',
            redirect: 'users',
          },
          {
            path: 'users',
            name: 'UsersView',
            component: () => import('@/views/Admin/Users/<USER>'),
            meta: {
              requiredAuthz: ['read_member'],
            },
          },
          {
            path: 'tags',
            name: 'Tags',
            component: () => import('@/views/Admin/Tags/Index'),
          },
        ],
      },
    ],
  },
];

export default routes.map(route => {
  const meta = {
    ...route.meta,
    requiresAuth: true,
    authzScope: 'org',
  };
  return { ...route, meta };
});
