import ProjectLayout from '@/Layouts/ProjectLayout.vue';

const routes = [
  {
    path: '/:handle/:key/settings',
    component: ProjectLayout,
    children: [
      {
            path: 'templates',
            name: 'Templates',
            component: () => import('@/views/Admin/CustomTemplates/Index'),
          },
          {
            path: 'custom-fields',
            name: 'CustomFields',
            component: () => import('@/views/Admin/CustomFields/Index'),
          },
          {
            path: 'shared-steps',
            name: 'SharedSteps',
            component: () => import('@/views/Admin/SharedSteps/Index'),
      },
                {
            path: 'configurations',
            name: 'Configurations',
            component: () => import('@/views/Admin/Configurations/Index'),
          },
           {
            path: 'data',
            name: 'Data',
            component: () => import('@/views/Admin/DataManagement/DataManagement'),
          },
    ],
  },
];

export default routes.map(route => {
  const meta = {
    ...route.meta,
    requiresAuth: true,
    authzScope: 'project',
  }
  return { ...route, meta }
});
