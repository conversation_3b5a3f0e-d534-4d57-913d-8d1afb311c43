import DefaultLayout from '@/Layouts/DefaultLayout.vue';

const routes = [
  {
    path: '/:handle/workspace',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'Workspace',
        component: () => import('@/views/Workspace/Index.vue'),
        props: true,
      },
    ],
  },
];

export default routes.map((route) => {
  const meta = {
    ...route.meta,
    requiresAuth: true,
    authzScope: 'org',
    // This route doesn't require requiredAuthz in orgScope, 
    // since it should be accessible if the user has at least read_activity permission in any of their projects.
  };
  return { ...route, meta };
});
