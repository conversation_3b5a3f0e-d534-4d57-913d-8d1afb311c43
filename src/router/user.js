import DefaultLayout from '@/Layouts/DefaultLayout.vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ProjectLayout from '@/Layouts/ProjectLayout.vue';

const routes = [
  {
    path: '/:handle/admin',
    component: DefaultLayout,
    children: [
      {
        path: '',
        component: AdminLayout,
        children: [
          {
            path: '',
            redirect: 'users',
          },
          {
            path: 'users',
            name: 'UsersView',
            component: () => import('@/views/Admin/Users/<USER>'),
            meta: {
              authzScope: 'org'
            },
          },
        ],
      },
    ],
  },
  {
    path: '/:handle/:key/users',
    component: ProjectLayout,
    children: [
      {
        path: '',
        name: 'ProjectUsers',
        component: () => import('@/views/Admin/Users/<USER>'),
        meta: {
          authzScope: 'project',
        }
      }
    ]
  }
];

export default routes.map(route => {
  const meta = {
    ...route.meta,
    requiresAuth: true,
    requiredAuthz: ['read_member'],
  };
  return { ...route, meta };
});
