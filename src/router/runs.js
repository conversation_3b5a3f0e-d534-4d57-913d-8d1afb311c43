import ProjectLayout from '@/Layouts/ProjectLayout.vue';
import DefaultLayout from '@/Layouts/DefaultLayout.vue';

const routes = [
  {
    path: '/:handle/:key/runs',
    component: ProjectLayout,
    meta: {
      requiredAuthz: ['read_entity'],
    },
    children: [
      {
        path: '',
        name: 'Runs',
        component: () => import('@/views/Tests/Runs/Index'),
        props: true,
      },
    ],
  },
  {
    path: '/:handle/:key/runs/create',
    component: DefaultLayout,
    meta: {
      requiredAuthz: ['write_entity'],
    },
    children: [
      {
        path: '',
        name: 'TestRunCreate',
        component: () => import('@/views/Tests/Runs/Create/Index'),
      },
      {
        path: 'cases',
        name: 'RunAddCase',
        component: () => import('@/views/Tests/Runs/Create/AddCases'),
        props: true,
      },
      {
        path: 'duplicate',
        name: 'TestRunDuplicate',  
      component: () => import('@/views/Tests/Runs/Duplicate/Index'),
      }
    ]
  },
  {
    path: '/:handle/:key/runs/:id',
    component: ProjectLayout,
    children: [
      {
        path: 'folders/:folderUid/executions/:executionUid',
        name: 'TestRunCaseEditExecutions',
        component: () => import('@/views/Tests/Runs/View'),
        props: true,
        meta: {
          requiredAuthz: ['read_activity'],
        },
      },
      {
        path: 'folders/:folderUid?',
        name: 'TestRunCaseEdit',
        component: () => import('@/views/Tests/Runs/View'),
        props: true,
        meta: {
          requiredAuthz: ['read_activity'],
        },
      },
      {
        path: 'edit/:folderUid?',
        name: 'TestRunEdit',
        component: () => import('@/views/Tests/Runs/Edit'),
        props: true,
        meta: {
          requiredAuthz: ['read_activity'],
        },
      },
      {
        path: 'failedsyncs',
        name: 'TestRunCaseFailedSync',
        component: () => import('@/views/Tests/Runs/RunCaseFailedSyncsView'),
        props: true,
        meta: {
          requiredAuthz: ['read_activity'],
        },
      },
      {
        path: '',
        name: 'TestRunCase',
        component: () => import('@/views/Tests/Runs/View'),
        props: true,
        meta: {
          requiredAuthz: ['read_activity'],
        },
      },
    ]
  },
];

export default routes.map((route) => {
  const meta = {
    ...route.meta,
    requiresAuth: true,
    authzScope: 'project',
    requiredAuthz: ['read_entity'],
  }
  return { ...route, meta };
});
