import AdminLayout from '@/Layouts/AdminLayout.vue';
import DefaultLayout from '@/Layouts/DefaultLayout.vue';
import ProjectLayout from '@/Layouts/ProjectLayout.vue';
const routes = [
  {
    path: '/:handle/admin',
    component: DefaultLayout,
    children: [
      {
        path: 'roles',
        component: AdminLayout,
        children: [
          {
            path: '',
            name: 'Roles',
            component: () => import('@/views/Admin/Roles/Index'),
          }, 
          {
            path: ':id/edit',
            name: 'AdminRoleEdit',
            component: () => import('@/views/Admin/Roles/RolesEditView'),
            props: true,
          },
          {
            path: 'create',
            name: 'AdminRoleCreate',
            component: () => import('@/views/Admin/Roles/RolesCreateView'),
          },
          {
            path: ':id/member',
            name: 'AdminRolesMember',
            component: () => import('@/views/Admin/Roles/RolesMemberView'),
            props: true,
            meta: {
              requiredAuthz: ['read_member'],
            },
          },
        ]
      }
    ]
  },  
  {
    path: '/:handle/:key/roles',
    component: ProjectLayout,
    children: [
      {
        path: '',
        name: 'ProjectRoles',
        component: () => import('@/views/Admin/Roles/Index'),
        meta: {
          requiredAuthz: ['read_role'],
          authzScope: 'project',
        },
      },
      {
        path: ':id/edit',
        name: 'ProjectRoleEdit',
        component: () => import('@/views/Admin/Roles/RolesEditView'),
        props: true,
        meta: {
          requiredAuthz: ['write_role'],   
          authzScope: 'project', 
        },
      },
      {
        path: 'create',
        name: 'ProjectRoleCreate',
        component: () => import('@/views/Admin/Roles/RolesCreateView'),
        meta: {
          requiredAuthz: ['write_role'],
          authzScope: 'project',
        },
      },
      {
        path: ':id/member',
        name: 'ProjectRolesMember',
        component: () => import('@/views/Admin/Roles/RolesMemberView'),
        props: true,
        meta: {
          requiredAuthz: ['read_member'],
          authzScope: 'project',
        },
      },
    ]
  },
];

export default routes.map((route) => {
  const meta = {
    requiresAuth: true,
    authzScope: 'org',
    ...route.meta,
  };
  return { ...route, meta };
});
