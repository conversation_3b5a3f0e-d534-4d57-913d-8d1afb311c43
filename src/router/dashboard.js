import DefaultLayout from '@/Layouts/DefaultLayout.vue';
import ProjectLayout from '@/Layouts/ProjectLayout.vue';

const routes = [
  {
    path: '/:handle/:key/dashboard/:id?',
    component: ProjectLayout,
    meta:{
      authzScope: 'project',
    },
    children: [
      {
        path: '',
        name: 'ProjectDashboard',
        component: () => import('@/views/Dashboard'),
        props: true,
      },
    ],
  },
  {
    path: '/:handle/dashboard/:id?',
    component: DefaultLayout,
    meta:{
      authzScope: 'org',
    },
    children: [
      {
        path: '',
        name: 'OrgDashboard',
        component: () => import('@/views/Dashboard'),
        props: true,
      },
    ],
  },

];

export default routes.map((route) => {
  const meta = {
    ...route.meta,
    requiresAuth: true,
    authzScope: 'org',
    // This route doesn't require requiredAuthz in orgScope, 
    // since it should be accessible if the user has at least read_activity permission in any of their projects.
  };
  return { ...route, meta };
});
