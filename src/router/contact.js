import DefaultLayout from '@/Layouts/DefaultLayout'
const routes = [
  {
    path: '/:handle/contact',
    component: DefaultLayout,
    children: [
      {
        path: '', 
        name: 'contact',
        component: () => import('@/views/Contact/Index.vue'),     
      }
    ]
  },
]

export default routes.map(route => {
    const meta = {
      ...route.meta,
      requiresAuth: true,
      authzScope: 'org',
    }
    return { ...route, meta }
});
  