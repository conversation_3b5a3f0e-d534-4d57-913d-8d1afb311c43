import { createNamespacedHelpers  } from 'vuex';

import { showErrorToast } from '@/utils/toast';

const { mapActions: mapUserActions, mapGetters: mapUserGetters, mapState: mapUserState, mapMutations: mapUserMutations } = createNamespacedHelpers("user");


export default {
  data(){
    return {
      priorityEntityType: null,
    }
  },

  computed: {
    ...mapUserGetters(['getUserStatusColorsByEntityType', 'getUserPriorityColorsByEntityType', 'getOrgPriorityColorsByEntityType', 'getOrgStatusColorsByEntityType','getUserPreferences', 'getOrgPreferences']),
    ...mapUserState(["currentAccount"]),
    isOrg() {
      return this.currentAccount?.type == "org" || this.currentAccount?.isOrg;
    },
    handle() {
      return this.$route.params.handle || this.currentAccount.handle;
    },
  },
  methods: {
    ...mapUserMutations(["setOrgPreferences"]),
    ...mapUserActions(["setUserPreferences", "setPreferences", 'getHandlePreferences']),
    getDefaultStatus(statuses){
      return statuses.find(status => status.isDefault === true);
    },
    getDefaultPriority(priorities){
      return priorities.find(priority => priority.isDefault === true)
    },
    getPriorities(entityType){
      return this.isOrg ? this.getOrgPriorityColorsByEntityType(this.handle, entityType) : this.getUserPriorityColorsByEntityType(entityType)
    },
    getStatuses(entityType) {
      return this.isOrg ? this.getOrgStatusColorsByEntityType(this.handle, entityType) : this.getUserStatusColorsByEntityType(entityType)
    },
    generateExecutionsProgress(completedFreq){
      if (!completedFreq) return;
      this.excutionStatuses = this.isOrg ? this.getOrgStatusColorsByEntityType(this.handle, 'testExecution') : this.getUserStatusColorsByEntityType('testExecution')
      const status = this.excutionStatuses;
      const result = {};

      for(const item of status){
        const { id, name } = item;
        result[name.toLowerCase()] = {
          id,
          name,
          count: completedFreq[id] || 0,
        };
      }
      return result;
    },
      getObjectCount(frequency) {
      let count = 0;
      for (let key in frequency) {
        if (Object.hasOwn(frequency, key)) {
          count += frequency[key];
        }
      }
      return count;
    },
    isStatusesHasDefault(entityType){
      const statuses = this.isOrg ? this.getOrgStatusColorsByEntityType(this.handle, entityType) : this.getUserStatusColorsByEntityType(entityType);
      return statuses.some(status => status.isDefault === true);
    },
    getEntityCompletedStatuses(entityType){
      const statuses = this.isOrg ? this.getOrgStatusColorsByEntityType(this.handle, entityType) : this.getUserStatusColorsByEntityType(entityType)
      return statuses.filter(element => element.isCompleted).map(item => item.id)
    },
    getEntityTodoStatuses(entityType) {
      const statuses = this.isOrg
        ? this.getOrgStatusColorsByEntityType(this.handle, entityType)
        : this.getUserStatusColorsByEntityType(entityType);
      return statuses.filter(element => !element.isCompleted).map(item => item.id);
    },
    isPrioritiesHasDefault(entityType){
      const priorities = this.isOrg ? this.getOrgPriorityColorsByEntityType(this.handle, entityType) : this.getUserPriorityColorsByEntityType(entityType);
      return priorities.some(priority => priority.isDefault === true);
    },
    getPriorityColor(priorityId, priorities) {
      return priorities.find((priority) => priority.id == priorityId)?.color || '#0c111d';
    },
    getStatusColor(statusId, statuses) {
      return statuses.find((status) => status.id == statusId)?.color || '#0c111d';
    },
    getStatusColorByName(statusName, statuses) {
      const name = statusName?.toLowerCase()?.replace(/\s+/g, '');
      return statuses.find((status) => status.name?.toLowerCase()?.replace(/\s+/g, '') == name)?.color || '#0c111d';
    },
    getPriorityColorByName(priorityName, priorities) {
      const name = priorityName?.toLowerCase()?.replace(/\s+/g, '');
      return priorities.find((priority) => priority.name?.toLowerCase()?.replace(/\s+/g, '') == name)?.color || '#0c111d';
    },
    getStatusName(statusId, statuses) {
      return statuses.find((status) => status.id == statusId)?.name || "";
    },
    getStatusDetails(statusId, statuses) {
      return statuses.find((status) => status.id == statusId);
    },
    getPriorityName(priorityId, priorities) {
      return priorities.find((priority) => priority.id == priorityId)?.name || "";
    },
    generateRandomHexColor() {
      return `#${Math.floor(Math.random() * ********).toString(16).padStart(6, "0")}`;
    },
    updatePreferences(handleObj){
      const handle = handleObj?.handle || this.handle;
      try{
        const preferences = handleObj?.type == 'org' ? this.getOrgPreferences(handle) : this.getUserPreferences
        if(!preferences.priorityColors || !preferences.statusColors || !preferences.timestamp || (preferences.timestamp && preferences.timestamp < Date.now() - ( 5 * 60 * 1000)))
          this.getHandlePreferences({handle, accountType: handleObj.type})
        else
          this.setPreferences({handle, type: handleObj.type, preferences })
      }catch (error){
        showErrorToast(this.$swal, 'fetchError', { item: 'preferences' }, error?.response?.data);
      }
    }
  }
};