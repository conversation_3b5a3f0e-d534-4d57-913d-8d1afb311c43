<template>
  <div>
    <CustomizeHeader />
    <v-row
      v-if="isOrgProjectView"
      no-gutters
    >
      <v-col
        cols="12"
        class="my-2"
      >
        <div class="chart-builder">
          <ChartBuilder @buildChart="addChart" />
        </div>
      </v-col>
      <v-col
        cols="6"
        class="pr-1"
      >
        <div class="customize_charts __single_charts mt-2">
          <div class="customize_charts_header">
            <h4>{{ $t('dashboard.signleValueChart') }}</h4>
          </div>     
          <div class="customize_charts_body mt-2">
            <div
              v-for="(item, index) in entitiesType"
              :key="index"
              class="customize_chart_single_chart"
            >
              <CounterChart 
                class="mb-4"
                :data="item"
                value="XXXX"
              />
              <v-btn 
                class="text-capitalize __main-btn"
                :class="{'danger': counterCharts.includes(item.entityType)}"
                block
                large
                depressed
                :disabled="item.disabled || !isOrgProjectView || isAddingChart"
                @click="counterCharts.includes(item.entityType) ? $emit('removeCounterChart', item.entityType) : debounceAddChart('counterChart', item.entityType, 3, 4, {groupBy: [{field: 'uid'}]})"
              >
                {{ counterCharts.includes(item.entityType) ? $t('remove') : $t('add') }}
              </v-btn>
            </div>
          </div>
        </div>
      </v-col>
      <v-col
        cols="6"
        class="pl-1"
      >
        <div
          class="customize_charts __chart_section mt-2 white"
          style="height: 98.5%"
        >
          <div class="customize_charts_header">
            <h4>{{ $t('dashboard.donutChart') }}</h4>
          </div>     
          <div class="customize_charts_body mt-2">
            <div class="chart-info">
              <p>Split</p>
              <v-tooltip
                bottom
                max-width="500"
              >
                <template #activator="{ on, attrs }">
                  <v-icon
                    color="#667085"
                    dark
                    v-bind="attrs"
                    v-on="on"
                  >
                    mdi-help-circle-outline 
                  </v-icon>
                </template>
                <span>{{ $t('dashboard.donutToolTip') }}</span>
              </v-tooltip>
            </div>
            <div>
              <v-select
                v-if="isDetailView || isOrgProjectView"
                v-model="donutChart"
                type="text"
                dense
                single-line
                height="38px"
                :items="filteredEntities"
                item-text="name"
                item-value="entityType"
                clear-icon="body-2"
                append-icon="mdi-chevron-down"
                class="rounded-lg field-theme custom-prepend"
                background-color="#F9F9FB"
                :placeholder="$t('dashboard.selecteEntity')"
                :menu-props="{ offsetY: true }"
              />
            </div>  
            <DonutChart
              :data="{}"
              :default-data="defaultNonAxis"
            />
            <div class="chart-action text-right">
              <v-btn 
                class="text-capitalize __main-btn"
                large
                depressed
                :disabled="isAddingChart"
                @click="debounceAddChart('donutChart', donutChart, 4, 10)"
              >
                {{ $t('add') }}
              </v-btn>
            </div>
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col
        cols="12"
        class="my-3"
      >
        <div class="customize_charts_header">
          <h4>{{ $t('dashboard.lineChart') }}</h4>
        </div>
      </v-col>
      <v-col
        :cols="isRunDetailView ? 12 : 6"
        class="pr-1"
      >
        <div class="wrap_chart pa-4 white">
          <div class="chart-info d-flex">
            <h4 class="font-weight-medium mr-1">
              {{ $t('dashboard.activityOverTime') }}
            </h4>
            <v-tooltip
              bottom
              max-width="500"
            >
              <template #activator="{ on, attrs }">
                <v-icon
                  color="#667085"
                  dark
                  v-bind="attrs"
                  v-on="on"
                >
                  mdi-help-circle-outline 
                </v-icon>
              </template>
              <span>{{ $t('dashboard.activityOverTimeTooltip') }}</span>
            </v-tooltip>
          </div>
          <v-select
            v-if="isDetailView || isOrgProjectView"
            v-model="lineChart"
            type="text"
            dense
            single-line
            height="38px"
            :items="filteredEntities"
            item-text="name"
            item-value="entityType"
            clear-icon="body-2"
            append-icon="mdi-chevron-down"
            class="rounded-lg field-theme custom-prepend my-4"
            background-color="#F9F9FB"
            :placeholder="$t('dashboard.selecteEntity')"
            :menu-props="{ offsetY: true }"
          />
          <LineChart
            :data="{}"
            :default-data="defaultLine"
          />
          <div class="chart-action py-2 pr-5 text-right">
            <v-btn 
              class="text-capitalize __main-btn"
              large
              depressed
              :disabled="isAddingChart"
              @click="debounceAddChart('lineChart', lineChart, 12, 10)"
            >
              {{ $t('add') }}
            </v-btn>
          </div>
        </div>
      </v-col>
      <v-col
        v-if="!isRunDetailView"
        cols="6"
        class="pl-1"
      >
        <div
          class="wrap_chart pa-4 white d-flex flex-column"
          style="height: 100%"
        >
          <div class="chart-info d-flex mb-4">
            <h4 class="font-weight-medium mr-1">
              {{ isPlanView ? 'Test Plans ' + $t('dashboard.breakdown') : $t('dashboard.rerunWithCycle') }}
            </h4>
            <v-tooltip
              bottom
              max-width="500"
            >
              <template #activator="{ on, attrs }">
                <v-icon
                  color="#667085"
                  dark
                  v-bind="attrs"
                  v-on="on"
                >
                  mdi-help-circle-outline 
                </v-icon>
              </template>
              <span>{{ isPlanView ? 'Testplan ' + $t('dashboard.breakdown') : $t('dashboard.rerunWithCycleToolTip') }}</span>
            </v-tooltip>
          </div>
          <LineChart
            :data="{}"
            :default-data="isPlanView ? defaultPlanBreakdown : defaultRerun"
          />
          <div class="chart-action py-2 pr-5 text-right">
            <v-btn 
              class="text-capitalize __main-btn"
              large
              depressed
              :disabled="isAddingChart"
              @click="debounceAddChart('lineChart', isPlanView ? 'testPlan' : 'testRun', 12, 10, {relatedTo: [isPlanView ? {entityType: 'testRun'} : {entityType: 'defect'}]})"
            >
              {{ $t('add') }}
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col
        cols="12"
        class="p-0 my-3"
      >
        <div class="wrap_chart pa-4 white">
          <div class="chart-info d-flex">
            <h4 class="font-weight-medium mr-1">
              {{ $t('dashboard.averageTimeInStatus') }}
            </h4>
            <v-tooltip
              bottom
              max-width="500"
            >
              <template #activator="{ on, attrs }">
                <v-icon
                  color="#667085"
                  dark
                  v-bind="attrs"
                  v-on="on"
                >
                  mdi-help-circle-outline 
                </v-icon>
              </template>
              <span>{{ $t('dashboard.averageTimeInStatusToolTip') }}</span>
            </v-tooltip>
          </div>
          <v-select
            v-if="isDetailView || isOrgProjectView"
            v-model="multiLineChart"
            type="text"
            dense
            single-line
            height="38px"
            :items="filteredEntities"
            item-text="name"
            item-value="entityType"
            clear-icon="body-2"
            append-icon="mdi-chevron-down"
            class="rounded-lg field-theme custom-prepend my-4"
            background-color="#F9F9FB"
            :placeholder="$t('dashboard.selecteEntity')"
            :menu-props="{ offsetY: true }"
          />
          <LineChart
            :data="{}"
            :default-data="defaultLines"
          />
          <div class="chart-action py-2 pr-5 text-right">
            <v-btn 
              class="text-capitalize __main-btn"
              large
              depressed
              :disabled="isAddingChart"
              @click="debounceAddChart('lineChart', multiLineChart, 12, 10, {isMultiSeries: true})"
            >
              {{ $t('add') }}
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col
        cols="12"
        class="mb-3"
      >
        <div class="customize_charts_header">
          <h4>{{ $t('dashboard.barChart') }}</h4>
        </div>
      </v-col>
      <v-col
        :cols="isRunDetailView ? 12 : 6"
        class="pr-1"
      >
        <div class="wrap_chart pa-4 white">
          <div class="chart-info d-flex">
            <h4 class="font-weight-medium mr-1">
              {{ $t('dashboard.barChartBreakdown') }}
            </h4>
            <v-tooltip
              bottom
              max-width="500"
            >
              <template #activator="{ on, attrs }">
                <v-icon
                  color="#667085"
                  dark
                  v-bind="attrs"
                  v-on="on"
                >
                  mdi-help-circle-outline 
                </v-icon>
              </template>
              <span>{{ $t('dashboard.barChartToolTip') }}</span>
            </v-tooltip>
          </div>
          <v-select
            v-if="isDetailView || isOrgProjectView"
            v-model="barChart"
            type="text"
            dense
            single-line
            height="38px"
            :items="filteredEntities"
            item-text="name"
            item-value="entityType"
            clear-icon="body-2"
            append-icon="mdi-chevron-down"
            class="rounded-lg field-theme custom-prepend my-4"
            background-color="#F9F9FB"
            :placeholder="$t('dashboard.selecteEntity')"
            :menu-props="{ offsetY: true }"
          />
          <BarChart
            :data="{}"
            :default-data="defaultLines"
          />
          <div class="chart-action py-4 pr-5 text-right">
            <v-btn 
              class="text-capitalize __main-btn"
              large
              depressed
              :disabled="isAddingChart"
              @click="debounceAddChart('barChart', barChart, 12, 10, {isMultiSeries: true})"
            >
              {{ $t('add') }}
            </v-btn>
          </div>
        </div>
      </v-col>
      <v-col
        v-if="!isRunDetailView"
        cols="6"
        class="pl-1"
      >
        <div
          class="wrap_chart pa-4 white d-flex flex-column justify-space-between"
          style="height:100%"
        >
          <div class="chart-info d-flex">
            <h4 class="font-weight-medium d-flex mr-1">
              {{ isPlanView ? 'Test Plans ' + $t('dashboard.breakdown') : $t('numberOfDefects') }}
            </h4>
            <v-tooltip
              bottom
              max-width="500"
            >
              <template #activator="{ on, attrs }">
                <v-icon
                  color="#667085"
                  dark
                  v-bind="attrs"
                  v-on="on"
                >
                  mdi-help-circle-outline 
                </v-icon>
              </template>
              <span>{{ $t('dashboard.numberOfDefectsToolTip') }}</span>
            </v-tooltip>
          </div>
          <div class="wrap_bar_chart">
            <BarChart
              :data="{}"
              :default-data="isPlanView ? defaultPlanBreakdown : defaultBarRerun"
            />
          </div>
          <div class="chart-action py-4 pr-5 text-right">
            <v-btn 
              class="text-capitalize __main-btn"
              large
              depressed
              :disabled="isAddingChart"
              @click="debounceAddChart('barChart', isPlanView ? 'testPlan' : 'testRun', 12, 10, {relatedTo: [isPlanView ? {entityType: 'testRun'} : {entityType: 'defect'}]})"
            >
              {{ $t('add') }}
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col
        cols="12"
        class="my-3"
      >
        <div class="customize_charts_header">
          <h4>{{ $t('dashboard.otherCharts') }}</h4>
        </div>
      </v-col>
      <v-col
        cols="6"
        class="pr-1"
      >
        <div class="wrap_chart pa-4 white">
          <div class="chart-info d-flex">
            <h4 class="font-weight-medium mr-1">
              {{ $t('dashboard.radarChart') }}
            </h4>
            <v-tooltip
              bottom
              max-width="500"
            >
              <template #activator="{ on, attrs }">
                <v-icon
                  color="#667085"
                  dark
                  v-bind="attrs"
                  v-on="on"
                >
                  mdi-help-circle-outline 
                </v-icon>
              </template>
              <span>{{ $t('dashboard.barChartToolTip') }}</span>
            </v-tooltip>
          </div>
          <v-select
            v-if="isDetailView || isOrgProjectView"
            v-model="selectRadarChart"
            type="text"
            dense
            single-line
            height="38px"
            :items="filteredEntities"
            item-text="name"
            item-value="entityType"
            clear-icon="body-2"
            append-icon="mdi-chevron-down"
            class="rounded-lg field-theme custom-prepend my-4"
            background-color="#F9F9FB"
            :placeholder="$t('dashboard.selecteEntity')"
            :menu-props="{ offsetY: true }"
          />
          <RadarChart
            :data="{}"
            :default-data="defaultNonAxis"
          />
          <div class="chart-action py-4 pr-5 text-right">
            <v-btn 
              class="text-capitalize __main-btn"
              large
              depressed
              :disabled="isAddingChart"
              @click="debounceAddChart('radarChart', selectRadarChart, 4, 10, {isMultiSeries: true})"
            >
              {{ $t('add') }}
            </v-btn>
          </div>
        </div>
      </v-col>
      <v-col
        cols="6"
        class="pl-1"
      >
        <div class="wrap_chart pa-4 white">
          <div class="chart-info d-flex">
            <h4 class="font-weight-medium mr-1">
              {{ $t('dashboard.StatusPriorityDistribution') }}
            </h4>
            <v-tooltip
              bottom
              max-width="500"
            >
              <template #activator="{ on, attrs }">
                <v-icon
                  color="#667085"
                  dark
                  v-bind="attrs"
                  v-on="on"
                >
                  mdi-help-circle-outline 
                </v-icon>
              </template>
              <span>{{ $t('dashboard.donutToolTip') }}</span>
            </v-tooltip>
          </div>
          <v-select
            v-if="isDetailView || isOrgProjectView"
            v-model="barStatusPriority"
            type="text"
            dense
            single-line
            height="38px"
            :items="filteredEntities"
            item-text="name"
            item-value="entityType"
            clear-icon="body-2"
            append-icon="mdi-chevron-down"
            class="rounded-lg field-theme custom-prepend my-4"
            background-color="#F9F9FB"
            :placeholder="$t('dashboard.selecteEntity')"
            :menu-props="{ offsetY: true }"
          />
          <StatusPriorityBarChart
            :height="300"
            :data="{}"
            :default-data="defaultNonAxis"
          />
          <div class="chart-action py-4 pr-5 text-right">
            <v-btn 
              class="text-capitalize __main-btn"
              large
              depressed
              :disabled="isAddingChart"
              @click="debounceAddChart('StatusPriorityBarChart', barStatusPriority, 4, 11)"
            >
              {{ $t('add') }}
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>
  </div>
</template>
<script>

import CustomizeHeader from '@/components/Dashboard/CustomizeChart/CustomizeHeader.vue'
import ChartBuilder from '@/components/Dashboard/CustomizeChart/ChartBuilder.vue'
import CounterChart from '@/components/Dashboard/Charts/CounterChart'
import LineChart from '@/components/Dashboard/Charts/LineChart'
import DonutChart from '@/components/Dashboard/Charts/DonutChart'
import StatusPriorityBarChart from '@/components/Dashboard/Charts/StatusPriorityBarChart'
import BarChart from '@/components/Dashboard/Charts/BarChart'
import RadarChart from '@/components/Dashboard/Charts/RadarChart'
import { debounce } from 'lodash'

import { showErrorToast } from '@/utils/toast';

import { defaultNonAxis, defaultLine, defaultRerun, defaultBarRerun, defaultLines, entitiesType, defaultPlanBreakdown } from '@/constants/dashboard'
export default {
  components: { 
    CustomizeHeader,
    CounterChart,
    DonutChart,
    LineChart,
    BarChart,
    RadarChart,
    StatusPriorityBarChart,
    ChartBuilder
   },
  props: {
    charts: Array,
    isAddingChart: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      donutChart: null,
      selectedXYEntity: null,
      selectRadarChart: null,
      lineChart: null,
      multiLineChart: null,
      barChart: null,
      barStatusPriority: null,
      selectedChart: null,
      selectedEntity: null,
      defaultNonAxis,
      defaultLine,
      defaultRerun,
      defaultBarRerun,
      defaultLines,
      entitiesType,
      defaultPlanBreakdown,
    };
  },
  computed:{
    isOrgProjectView(){
      return ['OrgDashboard', 'ProjectDashboard'].includes(this.$route.name)
    },
    isDetailView(){
      return [
        'TestRunCase',
        'TestPlanRerun',
        'MilestoneView',
      ].includes(this.$route.name)
    },
    isRunDetailView(){
      return this.$route.name == 'TestRunCase'
    },
    isPlanView(){
      return this.$route.name == 'TestPlans'
    },
    counterCharts(){
      return this.charts.filter(element => element.type == 'counterChart').map(item => item.entityType);
    },
    filteredEntities(){
      return this.entitiesType.filter(item => {
        if(this.$route.name === 'TestRunCase')
          return ['defect', 'testExecution'].includes(item.entityType)
        if(this.$route.name === 'TestPlanRerun')
          return ['testRun', 'testExecution', 'defect'].includes(item.entityType)
        if(this.$route.name === 'MilestoneView')
          return ['testPlan','testRun', 'testExecution', 'defect'].includes(item.entityType)
        return true
      })
    }
  },
  methods: {
    debounceAddChart: debounce(function(chartType, entityType, w, h, ...vals) {
      this.addChart(chartType, entityType, w, h, ...vals);
    }.bind(this), 300),
    addChart(chartType, entityType, w, h, ...vals){
      if(this.$route.name == 'Runs')
        entityType = 'testRun'
      if(this.$route.name == 'TestPlans')
        entityType = 'testPlan'
        
      if(!entityType)
        return showErrorToast(this.$swal, this.$t('dashboard.selectEntityToAddChart'))

      // Default
      let groupBy = [];

      if(!vals?.relatedTo){
        if(entityType == 'testCase')
          groupBy = [{field: 'priority'}]
        else if(entityType == 'milestone')
          groupBy = [{field: 'status'}]
        else if(entityType == 'defect')
          groupBy = [{field: 'labels'}]
        else
          groupBy = [{field: 'status'}, {field: 'priority'}]
      }

      const payload = {
        chartType,
        entityType,
        w,
        h,
        groupBy // can be overridden if needed (vals include groupBy)
      }

      vals.forEach(val => {
        Object.entries(val).forEach(([key, value]) => {
          payload[key] = value
        });
      })
      this.$emit('addChart', payload)
    }
  },
  created(){
    this.debounceAddChart = debounce(function(chartType, entityType, w, h, ...vals) {
      this.addChart(chartType, entityType, w, h, ...vals);
    }.bind(this), 300, { leading: true, trailing: false });
  }
};
</script>
<style lang="scss">
.customize_charts_header{
  padding: 14px 24px;
  background-color: #FFF;
  border-radius: 8px;
}
.customize_charts_header h4, .chart-builder h4{
  font-weight: 600;
  font-size: 20px;
}
.customize_charts.__single_charts .customize_charts_body{
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  height: 100%;
}
.customize_charts.__single_charts .customize_charts_body .customize_chart_single_chart{
  background-color: #FFF;
  border-radius: 8px;
  padding: 24px;
  min-width: calc(50% - 4px)
}
.customize_charts.__chart_section .customize_charts_body{
  background-color: #FFF;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.customize_charts.__chart_section .customize_charts_body .chart-info{
  display: flex;
  gap: 8px; 
}
.customize_charts.__chart_section .customize_charts_body .chart-info p{
  margin-bottom: 0px;
  color: #0C111D;
}
.customize_charts .chart-action{
  background-color: #FFF;
}
.customize_charts .chart-action .v-btn{
  min-width: 130px !important;
}
</style>
