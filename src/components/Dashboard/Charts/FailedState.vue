<template>
  <div class="wrap-failed-chart d-flex align-center justify-center">
    <div class="failed-chart d-flex flex-column align-center">
      <div class="chart-svg">
        <!-- SVG Required here -->
      </div>
      <div class="chart-text">
        <p>{{ $t('failedToLoadChart') }}</p>
      </div>
    </div>
  </div>
</template>
<script>
export default{
  components: {
  }
}
</script>
<style scoped>
.chart-text p{
  color: #667085;
}
.wrap-failed-chart{
  height: 100%;
}
</style>