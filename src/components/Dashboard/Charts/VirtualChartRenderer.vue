<template>
  <div style="width: 100%">
    <!-- Remove <PERSON>. Use the <component :is="type"> approach instead. -->
    <PriorityStatusFilter
      v-if="type !== 'boxPlot'"
      :groups="groups"
      :group-by="groupBy"
      @updateGroups="updateGroups"
    />
    <ChartViewFilter
      v-if="showToggleGroups"
      :key="groupBy"
      :groups="existsGroups"
      :group-by="groupBy"
      @updateGroupBy="updateGroupBy"
      @updateMultiSeries="updateMultiSeries"
    />
    <vue-apex-charts
      :key="type"
      class="d-flex justify-center"
      height="250"
      width="550"
      :options="options"
      :series="series"
    />
  </div>
</template>
<script>
import VueApexCharts from 'vue-apexcharts'
import { createNamespacedHelpers } from 'vuex';
import PriorityStatusFilter from '@/components/Dashboard/Filters/PriorityStatusFilter'
import ChartViewFilter from '@/components/Dashboard/Filters/ChartViewFilter'
const { mapGetters: mapDashboardGetters } = createNamespacedHelpers('user')

export default{
components:{
  VueApexCharts,
  PriorityStatusFilter,
  ChartViewFilter
},
props:{
  data:{
    type: Object,
    required: true,
    default: () => {
      return {}
    }
  },
  name: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    required: true
  },
  isXYChart: {
    type: Boolean
  }
},
data(){
  return{
    groups: {},
    groupBy: null,
    isMultiSeries: null,
    boxPlotSeries: [ // boxplot dummy data
      {
        type: 'boxPlot',
        data: [
          {
            x: 'Test Case 1',
            y: [1, 3, 5, 7, 10]
          },
          {
            x: 'Test Case 2',
            y: [2, 4, 6, 8, 9]
          },
          {
            x: 'Test Case 3',
            y: [1, 2, 5, 6, 8]
          },
          {
            x: 'Test Case 4',
            y: [3, 4, 6, 7, 9]
          },
          {
            x: 'Test Case 5',
            y: [2, 3, 6, 7, 8]
          },
          {
            x: 'Test Case 6',
            y: [4, 5, 6, 7, 8]
          },
          {
            x: 'Test Case 7',
            y: [1, 2, 4, 6, 9]
          }
        ]
      }
    ]
  }
},
methods: {
  updateGroups(groups){
    this.groups = groups
  },
  updateGroupBy(groupBy){
    this.groupBy = groupBy
  },
  updateMultiSeries(multiSeries){
    this.isMultiSeries = multiSeries
  },
  XYSeries(data, groupBy){
    const selectedNames = this.groups[this.groupBy].reduce((acc, curr) => {
      if(curr.selected)
        acc.push(curr.name)

      return acc
    },[]);
      
    const aggregatedData = {
      name: this.entityName(this.name),
      data: [],
      color: '#0C2FFC'
    };
    const multiAggregatedData = {};

    Object.entries(data).forEach(([date, details]) => {
      
      const group = details[groupBy];
      let totalValue = 0;
      if(this.isMultiSeries){
        Object.values(group).forEach(({ value, name, color }) => {
          if (!multiAggregatedData[name]) {
            multiAggregatedData[name] = {
              name,
              color: color,
              data: []
            };
          }

          if (selectedNames?.includes(name)) {
            multiAggregatedData[name].data.push({ x: date, y: value });
          }
        });
      }else{

        Object.values(group).forEach(({ value, name }) => {
          if(selectedNames?.includes(name))
            totalValue += value;
        });

        aggregatedData.data.push({ x: date, y: totalValue });
      }

    });

    if (this.isMultiSeries)
      return Object.values(multiAggregatedData);
    else 
      return [aggregatedData];
  }
},
computed:{
    ...mapDashboardGetters(['entityName']),
    seriesLength(){
      return Object.keys(this.series[0]?.data ?? [])?.length ?? 0;
    },
    options(){
      return {
        chart:{
          type: this.type,
        },
        responsive: [
          {
            breakpoint: 1780,
            options:{
              legend: {
                position: 'bottom',
                itemMargin: {
                  horizontal: 10,
                  vertical: 0,
                },
                floating: false,
              }
            }
          },
        ],
        legend: {
          floating: false,
          show: true,
          position: 'left',
          horizontalAlign: 'center',
          fontWeight: 400,
          height: 250,
          fontSize: '16px',
          itemMargin: {
            horizontal: 24,
            vertical: 0,
          },
        },
        plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: 0.25 * Math.log(this.seriesLength + 1) * 30 + "%",
        },
        boxPlot: {
          colors: {
            upper: '#66BB6A',
            lower: '#EF5350'
          }
        },
        pie: {
          expandOnClick: false,
            donut: {
              size: "85%",
                labels: {
                    show: true,
                    total: {
                        show: true,
                        label: this.entityName(this.name),
                        fontSize: '22px',

                    }
                }
            },
            radar: {
              polygons: {
                strokeColor: '#000',
                fill: {
                    colors: !this.isXYChart ? this.colors : undefined
                }
              }
            }
          }
        },
        labels: !this.isXYChart ? this.labels : [],
        colors: !this.isXYChart ? this.colors : [],
        dataLabels: {
          formatter: (val, { seriesIndex, w }) => w.config.series[seriesIndex],
          enabled: false,
        },
        grid: {
          show: true,
          borderColor: '#90A4AE',
          strokeDashArray: 5,
          position: 'back',
          row: {
            colors: ['#FFF'],
            opacity: 0.5
          },
        },
      }
    },
    entityData(){
      const selectedNames = this.groups[this.groupBy].reduce((acc, curr) => {
        if(curr.selected)
          acc.push(curr.name)

        return acc
      },[]);
    
      return this.data[this.groupBy].filter(element => selectedNames.includes(element.name))
    },
    labels(){
      if(this.type == 'boxPlot') return [];
      const entityLabels = this.entityData.map(item => item.name);
      return entityLabels;
    },
    series(){
      let typeSeries = null;
      if(this.type == 'boxPlot')
        return this.boxPlotSeries
      if(['line', 'bar', 'heatmap'].includes(this.type))
        typeSeries = this.XYSeries(this.data, this.groupBy)
      if(this.type == 'donut')
        typeSeries = this.entityData.map(item => item.value)
      if(this.type == 'radar')
        typeSeries = [
          {
            data: this.entityData.map(item => item.value),
            name: this.entityName(this.name)
          }
        ]
      return typeSeries
    },
    colors(){
      if(this.type == 'boxPlot') return [];
      const entityColors = this.entityData.map(item => item.color)
      return entityColors
    },
    existsGroups() {
      return Object.keys(this.groups);
    },
    showToggleGroups(){
      return this.existsGroups.length > 1
    }
  },
  created(){
    let data = null;
    if(this.isXYChart){
      const firstKey = Object.keys(this.data)[0];
      data = this.data[firstKey];
    }else{
      data = this.data
    }

    this.groups = Object.entries(data).reduce((acc, [key, value]) => {
      acc[key] = value.map(item => {
        return {
          name: item.name,
          selected: true
        }
      })
      return acc
    }, {})
    this.groupBy = Object.keys(data)[0];
  }
}
</script>