<template>
  <div
    style="width: 100%; height: 100%"
    class="apex_line-chart"
  >
    <template v-if="!isChartLoad">
      <div
        v-if="data.id"
        class="d-flex justify-space-between align-center mb-5"
      >
        <div class="chart-filters d-flex align-center flex-wrap">
          <h3>{{ $t('dashboard.activityOverTime') }}</h3>
          <EntitySelector
            :entity="data.entityType"
            :chart-id="data.id"
            :disable-select="isAxisRelationshipValid"
            :editable="editable"
            @updateEntity="updateEntity"
          />
          <ChartViewFilter
            v-if="!isAxisRelationshipValid"
            :key="groupBy"
            :groups="existsGroups"
            :group-by="groupBy"
            :default-time="data.groupByTime"
            :is-x-y-chart="true"
            :is-multi-series="isMultiSeries"
            @updateMultiSeries="changeMultiSeries"
            @changeTimeGrouping="changeTimeGrouping"
            @updateGroupBy="updateGroupBy"
          />
          <PriorityStatusFilter
            v-if="!isAxisRelationshipValid"
            :groups="groups"
            :group-by="groupBy"
            @updateGroups="updateGroups"
          />
        </div>
        <div class="chart-settings">
          <v-menu
            left
            offset-y
            content-class="chart-settings-menu"
          >
            <template #activator="{ on }">
              <v-btn
                icon
                v-on="on"
              >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list
              dense
              class="text-left"
            >
              <v-list-item
                @click="$emit('deleteChart', data.id)"
              >
                <DeleteIcon />
                <v-list-item-content class="ml-2 error--text">
                  {{ $t('delete') }}
                </v-list-item-content>
              </v-list-item>
              <v-list-item
                @click="$emit('expandChart', data.id)"
              >
                <ExpandIcon />
                <v-list-item-content class="ml-2">
                  {{ $t('expand') }}
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
      <vue-apex-charts
        v-if="!isChartEmpty"
        type="line"
        height="280"
        :options="chartOptions"
        :series="series"
      />
      <FailedState v-else-if="loadChartFailed" />
      <EmptyState v-else />
    </template>
    <div
      v-else
      class="dashboard__charts-card"
    >
      <div
        class="d-flex flex-column justify-space-between align-center"
        style="min-height: 325px"
      >
        <div class="d-flex justify-space-between align-center w-full">
          <div class="d-flex gap-2">
            <v-skeleton-loader
              v-for="t in 3"
              :key="t"
              height="40"
              width="100"
              type="chart"
            />
          </div>
          <v-skeleton-loader
            type="button"
            height="40"
            width="40"
          />
        </div>
        <v-skeleton-loader
          type="chart"
          height="240"
          width="100%"
        />
      </div>
    </div>
  </div>
</template>
<script>
import VueApexCharts from 'vue-apexcharts'
import ExpandIcon from '@/assets/svg/expand.svg';
import ChartViewFilter from '@/components/Dashboard/Filters/ChartViewFilter'
import PriorityStatusFilter from '@/components/Dashboard/Filters/PriorityStatusFilter'
import DeleteIcon from '@/assets/svg/delete.svg';
import { createNamespacedHelpers } from 'vuex';
import { groupByWeek, groupByMonth } from '@/utils/timeGroupingUtils';
import { relationsMappingNames } from '@/constants/dashboard.js'
import FailedState from '@/components/Dashboard/Charts/FailedState.vue';
const { mapGetters: mapDashboardGetters } = createNamespacedHelpers('user')


export default{
  components:{
    VueApexCharts,
    ChartViewFilter,
    PriorityStatusFilter,
    DeleteIcon,
    ExpandIcon,
    FailedState
  },
  props:{
    data:{
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    },
    defaultData:{
      type: Array,
    },
    view: {
      type: String,
      required: false
    },
    editable: {
      type: Boolean,
      default: true
    },
    chartLoad: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
      timeGrouping: null,
      isMultiSeries: null,
      relationsMappingNames,
      groups: [],
      groupBy: null
    }
  },
  computed:{
    ...mapDashboardGetters(['entityName']),
    existsGroups() {
      return Object.keys(this.groups);
    },
    isChartLoad(){
      return this.data?.load === true || this.chartLoad === true
    },
    isChartEmpty(){
      const sum = this.series?.reduce((acc, curr) => {
        curr.data?.forEach(e => {
          acc += (this.isAxisRelationshipValid ? e : e?.y) ?? 0
        })

        return acc
      }, 0) || 0

      return sum == 0 ? true : false;
    },
    loadChartFailed(){
      return this.data?.retryTimes >= 3;
    },
    chartData(){
      if(this.loadChartFailed)
        return {};
    
      if(this.isAxisRelationshipValid){
        const data = {
          series: [],
          xAxis: null
        };

        for (const curr of this.data.relatedTo) {
          
          const reData = this.data.entityData?.[curr.entityType];

          if (!reData && this.data.retryTimes < 3) {
            this.$emit('pullChart', { chartID: this.data.id, });
            return {}; 
          }
          data.series.push({
            name: relationsMappingNames[curr.entityType],
            data: Object.values(reData),
            color: '#0c2ff3'
          });

          if (!data.xAxis) {
            data.xAxis = Object.keys(reData);
          }

        }
        return data;
      }
      const entityData = this.data.entityData;

      if(!entityData && !this.isChartLoad && this.data.retryTimes < 3){
        this.$emit('pullChart', {chartID: this.data.id })
        return {} 
      }
        
      if(this.data.groupByTime == 'weekly'){
        return groupByWeek(entityData, this.existsGroups)
      }

      if(this.data.groupByTime == 'monthly'){
        return groupByMonth(entityData, this.existsGroups)
      }

      return entityData
    },
    isAxisRelationshipValid(){
      return Array.isArray(this.data.relatedTo);
    },
    series(){
      if(this.isAxisRelationshipValid)
        return this.chartData.series;

      const selectedNames = (this.groups[this.groupBy] ?? []).reduce((acc, curr) => {
        if(curr.selected)
          acc.push(curr.name)

        return acc
      },[]);

      const aggregateData = (entityData, keyType) => {
        const aggregatedData = {
          name: this.entityName(this.data.entityType),
          data: [],
          color: '#0C2FFC'
        };

        const multiAggregatedData = {};

      Object.entries(entityData ?? []).forEach(([date, details]) => {
        
        const group = details[keyType];
        let totalValue = 0;
        if(this.data.isMultiSeries){
          Object.entries(group ?? []).forEach(([name, { value, color }]) => {
            if (!multiAggregatedData[name]) {
              multiAggregatedData[name] = {
                name,
                color: color,
                data: []
              };
            }
            if (selectedNames?.includes(name)) {
              multiAggregatedData[name].data.push({ x: date, y: value });
            }
          });
        }else{
          Object.entries(group ?? []).forEach(([name, {value}]) => {
            if(selectedNames?.includes(name))
              totalValue += value;
          });
  
          aggregatedData.data.push({ x: date, y: totalValue });
        }

      });

      if (this.data.isMultiSeries)
        return Object.values(multiAggregatedData);
      else 
        return [aggregatedData];
    };

      return this.defaultData ?? aggregateData(this.chartData, this.groupBy);
    },
    chartOptions(){
      return{
        chart: {
          type: 'line',
          zoom: {
            enabled: false
          },
          
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth',
          width: 3,
        },
        markers: {
          size: 6,
        },
        tooltip: {
          enabled: true,
          shared: false,
        },
        xaxis:{
          ...(this.isAxisRelationshipValid ? {
            categories: this.chartData.xAxis
          } : {})
        },
        grid: {
          show: true,
          borderColor: '#90A4AE',
          strokeDashArray: 5,
          position: 'back',
          row: {
            colors: ['#FFF'],
            opacity: 0.5
          },
        },
      }
    }  
  },
  created(){
    const data = this.data?.entityData ?? [];
    const dateKey = Object.keys(data)[0];
    this.isMultiSeries = this.data.isMultiSeries
    this.groups = Object.keys(data[dateKey] ?? []).reduce((acc, curr) => {
      if(!acc[curr])
        acc[curr] = Object.keys(data[dateKey][curr]).map(item => {
          return {
            name: item,
            selected: true
          }
        })
      
      return acc
    }, {})
    
    this.groupBy = Object.keys(this.groups)[0]
  },
  methods:{
    updateEntity(entity){
      this.$emit('updateEntity', entity, this.data.id)
    },
    changeTimeGrouping(val){
      this.$emit('updateTimeGrouping', val, this.data.id)
      this.timeGrouping = val;
    },
    changeMultiSeries(val){
      this.$emit('updateMultiSeries', val, this.data.id)
    },
    updateGroupBy(groupBy){
      this.groupBy = groupBy
    },
    updateGroups(groups){
      this.groups = groups
    },
  }
}
</script>
<style scoped>
.apex_line-chart .apexcharts-canvas,.apex_line-chart .apexcharts-canvas .apexcharts-svg{
  width: 100% !important;
}
</style>
<style scoped>
.apex_line-chart .chart-filters{
  gap: 8px;
}
</style>
<style scoped>
.expand-content{
  color: #344054 !important;
}
</style>