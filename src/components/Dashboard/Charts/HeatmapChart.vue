<template>
  <div
    style="width: 100%; height: 100%"
    class="apex_bar-chart"
  >
    <template v-if="!isChartLoad">
      <div
        v-if="data.id"
        class="d-flex justify-space-between align-center mb-5"
      >
        <div class="chart-filters d-flex align-center flex-wrap">
          <h3>{{ $t('dashboard.breakdown') }}</h3>
          <EntitySelector
            :entity="data.entityType"
            :disable-select="false"
            :chart-id="data.id"
            :editable="editable"
            @updateEntity="updateEntity"
          />
          <ChartViewFilter
            :key="groupBy"
            :groups="existsGroups"
            :group-by="groupBy"
            :default-time="data.groupByTime"
            :is-x-y-chart="true"
            :show-multi-series="false"
            @changeTimeGrouping="changeTimeGrouping"
            @updateGroupBy="updateGroupBy"
          />
        </div>
        <div class="chart-settings">
          <v-menu
            left
            offset-y
            content-class="chart-settings-menu"
          >
            <template #activator="{ on }">
              <v-btn
                icon
                v-on="on"
              >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list
              dense
              class="text-left"
            >
              <v-list-item
                @click="$emit('deleteChart', data.id)"
              >
                <DeleteIcon />
                <v-list-item-content class="ml-2 error--text">
                  {{ $t('delete') }}
                </v-list-item-content>
              </v-list-item>
              <v-list-item
                @click="$emit('expandChart', data.id)"
              >
                <ExpandIcon />
                <v-list-item-content class="ml-2">
                  {{ $t('expand') }}
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
      <vue-apex-charts
        v-if="!isChartEmpty"
        height="280"
        :options="chartOptions"
        :series="series"
      />
      <FailedState v-else-if="loadChartFailed" />
      <EmptyState v-else />
    </template>
    <div
      v-else
      class="dashboard__charts-card"
    >
      <div
        class="d-flex flex-column justify-space-between align-center"
        style="min-height: 325px"
      >
        <div class="d-flex justify-space-between align-center w-full">
          <div class="d-flex gap-2">
            <v-skeleton-loader
              v-for="t in 3"
              :key="t"
              height="40"
              width="100"
              type="chart"
            />
          </div>
          <v-skeleton-loader
            type="button"
            height="40"
            width="40"
          />
        </div>
        <v-skeleton-loader
          type="chart"
          height="240"
          width="100%"
        />
      </div>
    </div>
  </div>
</template>
<script>
import VueApexCharts from 'vue-apexcharts'
import EntitySelector from '@/components/Dashboard/Filters/EntitySelector'
import ChartViewFilter from '@/components/Dashboard/Filters/ChartViewFilter'
import ExpandIcon from '@/assets/svg/expand.svg';
import DeleteIcon from '@/assets/svg/delete.svg';
import { createNamespacedHelpers } from 'vuex';
import { groupByWeek, groupByMonth } from '@/utils/timeGroupingUtils';
import { relationsMappingNames } from '@/constants/dashboard.js'
import FailedState from '@/components/Dashboard/Charts/FailedState.vue';

const { mapGetters: mapDashboardGetters } = createNamespacedHelpers('user')


export default{
  components:{
    VueApexCharts,
    EntitySelector,
    DeleteIcon,
    ExpandIcon,
    ChartViewFilter,
    FailedState
  },
  props:{
    data:{
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    },
    defaultData:{
      type: Array,
    },
    view: {
      type: String,
      required: false
    },
    editable: {
      type: Boolean,
      default: true
    },
    chartLoad: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
      timeGrouping: null,
      isMultiSeries: true,
      relationsMappingNames,
      groups: [],
      groupBy: null,
    }
  },
  computed:{
    ...mapDashboardGetters(['entityName']),
    seriesLength(){
      return Object.keys(this.series[0]?.data ?? [])?.length ?? 0;
    },
    isChartEmpty(){
      const sum = this.series?.reduce((acc, curr) => {
        curr.data?.forEach(e => {
          acc += e.y ?? 0
        })

        return acc
      }, 0)

      return sum == 0 ? true : false;
    },
    existsGroups() {
      return Object.keys(this.groups);
    },
    loadChartFailed(){
      return this.data?.retryTimes >= 3;
    },
    isChartLoad(){
      return this.data?.load === true || this.chartLoad === true 
    },
    chartData(){
      if(this.loadChartFailed)
        return {};

      const entityData = this.data.entityData;

      if(!entityData && !this.isChartLoad && this.data.retryTimes < 3){
        this.$emit('pullChart', {chartID: this.data.id })
        return {} 
      }
        
      if(this.data.groupByTime == 'weekly'){
        return groupByWeek(entityData, this.existsGroups)
      }

      if(this.data.groupByTime == 'monthly'){
        return groupByMonth(entityData, this.existsGroups)
      }

      return entityData
    },
    generateColorRanges() {
      const ranges = Object.entries(this.groups[this.groupBy]).map(([statusName, { key, color }]) => {
        return {
          from: key + 0.1 - 1,
          to: key + 0.1,
          color,
          name: statusName
        };
      });

      return ranges;
    },
    series(){
      const transformedData = JSON.parse(JSON.stringify(this.chartData));

      Object.entries(transformedData).forEach(([, groupObj]) => {
        Object.entries(groupObj).forEach(([groupKey, groups]) => {
          Object.entries(groups).forEach(([grKey, grData]) => {
            const key = this.groups?.[groupKey]?.[grKey]?.key ?? 0;
            grData.value = grData.value >= 1 ? key : 0;
          });
        });
      });
      
      const aggregateData = (entityData, keyType) => {
      const multiAggregatedData = {};

      Object.entries(entityData ?? []).forEach(([date, details]) => {
        
        const group = details[keyType];

        Object.entries(group ?? []).forEach(([name, { value, color }]) => {
          if (!multiAggregatedData[name]) {
            multiAggregatedData[name] = {
              name,
              color: color,
              data: []
            };
          }
          multiAggregatedData[name].data.push({ x: date, y: value });
        });
      });
        return Object.values(multiAggregatedData);
    };

      return this.defaultData ?? aggregateData(transformedData, this.groupBy);
    },
    chartOptions(){
      return{
        chart: {
          type: 'heatmap',
          stacked: true,
          zoom: {
            enabled: false
          },
        },
        dataLabels: {
          enabled: false
        },
        plotOptions: {
          heatmap: {
            radius: 2,
            enableShades: false,
            shadeIntensity: 0.5,
            reverseNegativeShade: false,
            distributed: false,
            useFillColorAsStroke: false,
            colorScale: {
              ranges: [
                {
                  from: 0,
                  to: 1,
                  color: '#EEE',
                  name: 'Empty',
                },
                ...this.generateColorRanges
              ],
            }
          }
        },
        grid: {
          show: true,
          borderColor: '#90A4AE',
          strokeDashArray: 5,
          position: 'back',
          row: {
            colors: ['#FFF'],
            opacity: 0.5
          },
        },
      }
    }  
  },
  created() {
    const data = this.data?.entityData ?? {};
    const dateKey = Object.keys(data)[0];
    const dateEntry = data[dateKey];

    this.groups = {};

    Object.keys(dateEntry ?? {}).forEach((groupKey) => {
      const statuses = dateEntry[groupKey];

      let index = 2;
      this.groups[groupKey] = {};

      Object.keys(statuses).forEach((statusKey) => {
        this.groups[groupKey][statusKey] = {
          key: index++,
          color: statuses[statusKey].color
        };
      });
    });
    
    this.groupBy = Object.keys(this.groups)[0];
  },
  methods:{
    updateEntity(entity){
      this.$emit('updateEntity', entity, this.data.id)
    },
    changeTimeGrouping(val){
      this.$emit('updateTimeGrouping', val, this.data.id)
      this.timeGrouping = val;
    },
    updateGroupBy(groupBy){
      this.groupBy = groupBy
    },
  }
}
</script>
<style scoped>
.apex_bar-chart .apexcharts-canvas,.apex_bar-chart .apexcharts-canvas .apexcharts-svg{
  width: 100% !important;
}
</style>
<style scoped>
.apex_bar-chart .chart-filters{
  gap: 8px;
}
</style>
<style scoped>
.expand-content{
  color: #344054 !important;
}

</style>