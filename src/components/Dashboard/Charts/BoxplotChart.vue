<template>
  <div
    style="width: 100%; height: 100%"
    class="apex_line-chart"
  >
    <template v-if="!isChartLoad">
      <div
        class="d-flex justify-space-between align-center mb-5"
      >
        <div class="chart-filters d-flex align-center flex-wrap">
          <h3>{{ $t('dashboard.activityOverTime') }}</h3>
        </div>
        <div class="chart-settings">
          <v-menu
            left
            offset-y
            content-class="chart-settings-menu"
          >
            <template #activator="{ on }">
              <v-btn
                icon
                v-on="on"
              >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list
              dense
              class="text-left"
            >
              <v-list-item
                @click="$emit('deleteChart', data.id)"
              >
                <DeleteIcon />
                <v-list-item-content class="ml-2 error--text">
                  {{ $t('delete') }}
                </v-list-item-content>
              </v-list-item>
              <v-list-item
                @click="$emit('expandChart', data.id)"
              >
                <ExpandIcon />
                <v-list-item-content class="ml-2">
                  {{ $t('expand') }}
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
      <vue-apex-charts
        v-if="!isChartEmpty"
        height="280"
        :options="chartOptions"
        :series="series"
      />
      <FailedState v-else-if="loadChartFailed" />
      <EmptyState v-else />
    </template>
    <div
      v-else
      class="dashboard__charts-card"
    >
      <div
        class="d-flex flex-column justify-space-between align-center"
        style="min-height: 325px"
      >
        <div class="d-flex justify-space-between align-center w-full">
          <div class="d-flex gap-2">
            <v-skeleton-loader
              v-for="t in 3"
              :key="t"
              height="40"
              width="100"
              type="chart"
            />
          </div>
          <v-skeleton-loader
            type="button"
            height="40"
            width="40"
          />
        </div>
        <v-skeleton-loader
          type="chart"
          height="240"
          width="100%"
        />
      </div>
    </div>
  </div>
</template>
<script>
import VueApexCharts from 'vue-apexcharts'
import ExpandIcon from '@/assets/svg/expand.svg';
import DeleteIcon from '@/assets/svg/delete.svg';
import FailedState from '@/components/Dashboard/Charts/FailedState.vue';
import { createNamespacedHelpers } from 'vuex';

const { mapGetters: mapDashboardGetters } = createNamespacedHelpers('user')


export default{
  components:{
    VueApexCharts,
    DeleteIcon,
    ExpandIcon,
    FailedState,
  },
  props:{
    data:{
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    },
    defaultData:{
      type: Array,
    },
    view: {
      type: String,
      required: false
    },
    chartLoad: {
      type: Boolean,
      default: false
    }
  },
  computed:{
    ...mapDashboardGetters(['entityName']),
    existsGroups() {
      return Object.keys(this.groups);
    },
    isChartLoad(){
      return this.data?.load === true || this.chartLoad === true
    },
    loadChartFailed(){
      return this.data?.retryTimes >= 3;
    },
    isChartEmpty(){
      const sum = this.series?.reduce((acc, curr) => {
        curr.data?.forEach(e => {
          acc += e.y ?? 0
        })

        return acc
      }, 0) || 0

      return sum == 0 ? true : false;
    },
    chartData(){
      if(this.loadChartFailed){
        return []
      }

      const entityData = this.data.entityData?.['testExecution']; // TODO  No hard coding, should be updated to handle other cases 

      if(!entityData && !this.isChartLoad && this.data.retryTimes < 3){
        this.$emit('pullChart', {chartID: this.data.id })
        return []
      }

      return entityData
    },
    seriesLength(){
      return Object.keys(this.series[0]?.data ?? [])?.length ?? 0;
    },
    series(){

      const getMean = arr => arr.reduce((sum, val) => sum + val, 0) / arr.length; // Mean of the three scores
      
      const data = this.chartData?.map(entry => {
        const x = `${entry.name} - ${entry.uid}`;

        let y = [0];

        if (entry.testExecution && entry.testExecution.length > 0) { // TODO No hard coding, should be updated to handle other cases 
          const executionAverages = entry.testExecution.map(exec => {
            const scores = Object.values(exec);
            return getMean(scores);
          });

          y = executionAverages
        }

        return { x, y };
      });
      return [
        {
          type: 'boxPlot',
          data
        }
      ]
    },
    chartOptions(){
      return{
        chart: {
          type: 'boxPlot',
          zoom: {
            enabled: false
          },
          
        },
        dataLabels: {
          enabled: false
        },
        tooltip: {
          enabled: true,
          shared: false,
        },
        xaxis:{
          labels: {
            show: true,
            rotate: -45,
            rotateAlways: false,
            hideOverlappingLabels: true,
            showDuplicates: false,
            trim: false,
            minHeight: undefined,
            maxHeight: 120,
            style: {
              colors: [],
              fontSize: '10',
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 400,
            },
          },
        },
        plotOptions: {
          boxPlot: {
            colors: {
              upper: '#66BB6A',
              lower: '#EF5350'
            }
          },
          bar: {
            horizontal: false,
            columnWidth: 0.25 * Math.log(this.seriesLength + 1) * 30 + "%",
          },
        },
        grid: {
          show: true,
          borderColor: '#90A4AE',
          strokeDashArray: 5,
          position: 'back',
          row: {
            colors: ['#FFF'],
            opacity: 0.5
          },
        },
      }
    }  
  },
}
</script>
<style scoped>
.apex_line-chart .apexcharts-canvas,.apex_line-chart .apexcharts-canvas .apexcharts-svg{
  width: 100% !important;
}
</style>
<style scoped>
.apex_line-chart .chart-filters{
  gap: 8px;
}
</style>
<style scoped>
.expand-content{
  color: #344054 !important;
}
</style>