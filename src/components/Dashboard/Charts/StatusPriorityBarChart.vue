<template>
  <div style="width: 100%">
    <template v-if="!isChartLoad">    
      <div
        v-if="!defaultData?.name && data.id"
        class="d-flex justify-space-between align-center mb-5"
      >
        <div class="apex_chart-filters d-flex align-center flex-wrap">
          <h3>{{ $t('groupBy') }}</h3>
          <EntitySelector
            :entity="data.entityType"
            :chart-id="data.id"
            :editable="editable"
            @updateEntity="updateEntity"
          />
          <ChartViewFilter
            v-if="showToggleGroups"
            :key="groupBy"
            :groups="existsGroups"
            :group-by="groupBy"
            :is-x-y-chart="false"
            @updateGroupBy="updateGroupBy"
          />
          <PriorityStatusFilter
            :groups="groups"
            :group-by="groupBy"
            @updateGroups="updateGroups"
          />
        </div>
        <div class="chart-settings">
          <v-menu
            left
            offset-y
            content-class="chart-settings-menu"
          >
            <template #activator="{ on }">
              <v-btn
                icon
                v-on="on"
              >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list
              dense
              class="text-left"
            >
              <v-list-item
                @click="$emit('deleteChart', data.id)"
              >
                <DeleteIcon />
                <v-list-item-content class="ml-2 error--text">
                  {{ $t('delete') }}
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
      <vue-apex-charts
        v-if="!isChartEmpty"
        class="d-flex justify-center"
        :options="options"
        :height="height"
        :series="series"
      />
      <FailedState v-else-if="loadChartFailed" />
      <EmptyState v-else />
    </template>
    <template v-else>
      <div class="dashboard__charts-card">
        <div
          class="d-flex flex-column justify-start align-center gap-6"
          style="min-height: 360px"
        >
          <div class="d-flex justify-space-between align-center w-full">
            <div class="d-flex gap-2">
              <v-skeleton-loader
                v-for="t in 3"
                :key="t"
                height="40"
                width="100"
                type="chart"
              />
              <v-skeleton-loader
                type="button"
                height="40"
                width="40"
              />
            </div>
            <v-skeleton-loader
              type="button"
              height="40"
              width="40"
            />
          </div>
          <div class="d-flex align-center w-full">
            <div class="d-flex flex-column w-25">
              <v-skeleton-loader
                v-for="z in 5"
                :key="z"
                class="w-full mb-2"
                type="text"
                height="16"
              />
            </div>
            <div class="d-flex align-end justify-end w-75">
              <v-skeleton-loader
                width="100%"
                max-width="240"
                height="240"
                type="avatar"
              />
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import PriorityStatusFilter from '@/components/Dashboard/Filters/PriorityStatusFilter'
import EntitySelector from '@/components/Dashboard/Filters/EntitySelector'
import ChartViewFilter from '@/components/Dashboard/Filters/ChartViewFilter'
import DeleteIcon from '@/assets/svg/delete.svg';
import VueApexCharts from 'vue-apexcharts'
import FailedState from '@/components/Dashboard/Charts/FailedState.vue';
import { createNamespacedHelpers } from 'vuex';
const { mapGetters: mapDashboardGetters, mapActions: mapDashboardActions } = createNamespacedHelpers('user')
export default{
components:{
  VueApexCharts,
  PriorityStatusFilter,
  EntitySelector,
  ChartViewFilter,
  DeleteIcon,
  FailedState
},
props:{
  data:{
    type: Object,
    required: true,
    default: () => {
      return {}
    }
  },
  defaultData: {
    type: Object,
    default: () => {
      return {}
    }
  },
  height: {
    type: Number,
    default: 300
  },
  view: {
    type: String,
    required: false
  },
  editable: {
    type: Boolean,
  },
  chartLoad: {
    type: Boolean,
    default: false
  }
},
data(){
  return {
    groups: [],
    groupBy: null
  }
},
computed:{
  ...mapDashboardGetters(['entityName']),
  options(){
    return {
      chart:{
        type: 'bar',
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        dashArray: 0
      },
      legend: {
    show: true,               // Show legend
    position: 'bottom',        // Position at the bottom
    floating: false,           // Prevent floating/rotation
    fontSize: '12px',          // Set a smaller font size for the legend
    labels: {
      useSeriesColors: true,   // Optional, uses series colors for the legend labels
      formatter: function(val) {
        return val;            // Optional: adjust how legend labels are displayed
      }
    },
    itemMargin: {
      horizontal: 10,          // Space between legend items
      vertical: 5              // Optional: adjust vertical spacing
    },
  },
  plotOptions: {
      bar: {
        distributed: true,
        columnWidth: 0.45 * Math.log(this.series.length + 1) * 30 + "%",
        barHeight: '50%',
      },
    },
    colors: this.colors,
    xaxis: {
      labels: {
        show: false,
      },
    categories: this.labels ?? [],
    },
  }
  },
  isChartEmpty(){
    const sum = this.series[0].data?.reduce((acc, curr) => {
      acc += curr

      return acc
    },0)
    return this.series[0].data?.length == 0 || sum == 0 || !sum? true : false;
  },
  loadChartFailed(){
    return this.data?.retryTimes >= 3;
  },
  chartData(){
    if(this.loadChartFailed)
      return {};

    if(this.isChartLoad)
      return;
      
    if(!this.isMockup && !this.data.entityData && !this.isChartLoad && this.data.retryTimes < 3)
        this.$emit('pullChart', { chartID: this.data.id });

    const selectedNames = (this.groups[this.groupBy] ?? []).reduce((acc, curr) => {
        if(curr.selected)
          acc.push(curr.name)

        return acc
      },[]);

    return this.defaultData?.status ?? Object.keys(this.data.entityData?.[this.groupBy] ?? [])
    .reduce((acc, curr) => {
      if(selectedNames.includes(curr) && !acc[curr])
        acc[curr] = this.data.entityData[this.groupBy][curr]

      return acc
    }, {});
   },
  showToggleGroups(){
    return this.existsGroups.length > 1
  },
  isChartLoad(){
    return this.data?.load === true || this.chartLoad === true   
  },
  colors(){
    return Object.values(this.chartData ?? []).map(item => item.color)
  },
  labels(){
    return Object.keys(this.chartData ?? {});
  },
  isMockup(){
    return this.defaultData && this.defaultData.status
  },
  existsGroups() {
    return Object.keys(this.groups);
  },
  series(){
    const series = Object.values(this.chartData ?? []).map(item => item.value)

      return [
        {
          name: '',
          data: series?.length ? series : [0]
        }
      ]
    },
  },
  methods:{
    ...mapDashboardActions(['pullChart']),
    updateEntity(entity){
      this.$emit('updateEntity', entity, this.data.id)
    },
    updateGroupBy(groupBy){
      this.groupBy = groupBy
    },
    updateGroups(groups){
      this.groups = groups
    },
  },
  created(){
    const data = this.data?.entityData ?? [];
    
    this.groups = Object.keys(data).reduce((acc, curr) => {
      if(!acc[curr])
        acc[curr] = Object.keys(data[curr]).map(item => {
          return {
            name: item,
            selected: true
          }
        })
      
      return acc
    }, {})

    this.groupBy = Object.keys(this.groups)[0];
  }
}
</script>