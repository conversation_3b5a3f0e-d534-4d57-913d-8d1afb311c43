<template>
  <div style="width: 100%">
    <template v-if="!isChartLoad">
      <div
        v-if="!defaultData?.name && data.id"
        class="d-flex justify-space-between align-center mb-5"
      >
        <div class="apex_chart-filters d-flex align-center flex-wrap">
          <h3>{{ $t('groupBy') }}</h3>
          <EntitySelector
            :entity="data.entityType"
            :chart-id="data.id"
            :editable="editable"
            @updateEntity="updateEntity"
          />
          <ChartViewFilter
            v-if="showToggleGroups"
            :key="groupBy"
            :groups="existsGroups"
            :group-by="groupBy"
            :is-x-y-chart="false"
            @updateGroupBy="updateGroupBy"
          />
          <PriorityStatusFilter
            :groups="groups"
            :group-by="groupBy"
            @updateGroups="updateGroups"
          />
        </div>
        <div class="chart-settings">
          <v-menu
            left
            offset-y
            content-class="chart-settings-menu"
          >
            <template #activator="{ on }">
              <v-btn
                icon
                v-on="on"
              >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list
              dense
              class="text-left"
            >
              <v-list-item
                @click="$emit('deleteChart', data.id)"
              >
                <DeleteIcon />
                <v-list-item-content class="ml-2 error--text">
                  {{ $t('delete') }}
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
      <vue-apex-charts
        v-if="!isChartEmpty"
        class="d-flex justify-center"
        height="300"
        width="550"
        :options="options"
        :series="series"
      />
      <FailedState v-else-if="loadChartFailed" />
      <EmptyState v-else />
    </template>
    <template v-else>
      <div class="dashboard__charts-card">
        <div
          class="d-flex flex-column justify-start align-center gap-6"
          style="min-height: 360px"
        >
          <div class="d-flex justify-space-between align-center w-full">
            <div class="d-flex gap-2">
              <v-skeleton-loader
                v-for="t in 3"
                :key="t"
                height="40"
                width="100"
                type="chart"
              />
              <v-skeleton-loader
                type="button"
                height="40"
                width="40"
              />
            </div>
            <v-skeleton-loader
              type="button"
              height="40"
              width="40"
            />
          </div>
          <div class="d-flex align-center w-full">
            <div class="d-flex flex-column w-25">
              <v-skeleton-loader
                v-for="z in 5"
                :key="z"
                class="w-full mb-2"
                type="text"
                height="16"
              />
            </div>
            <div class="d-flex align-end justify-end w-75">
              <v-skeleton-loader
                width="100%"
                max-width="240"
                height="240"
                type="avatar"
              />
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import ChartViewFilter from '@/components/Dashboard/Filters/ChartViewFilter'
import PriorityStatusFilter from '@/components/Dashboard/Filters/PriorityStatusFilter'
import EntitySelector from '@/components/Dashboard/Filters/EntitySelector'
import EmptyState from '@/components/Dashboard/Charts/EmptyState'
import DeleteIcon from '@/assets/svg/delete.svg';
import VueApexCharts from 'vue-apexcharts'
import FailedState from '@/components/Dashboard/Charts/FailedState.vue';
import { createNamespacedHelpers } from 'vuex';

const { mapGetters: mapDashboardGetters } = createNamespacedHelpers('user')
export default{
components:{
  VueApexCharts,
  PriorityStatusFilter,
  ChartViewFilter,
  EntitySelector,
  DeleteIcon,
  EmptyState,
  FailedState
},
props:{
  data:{
    type: Object,
    required: true,
    default: () => {
      return {}
    }
  },
  defaultData:{
    type: Object,
  },
  view: {
    type: String,
    required: false
  },
  editable: {
    type: Boolean,
    default: true
  },
  chartLoad: {
    type: Boolean,
    default: false
  }
},
data(){
  return {
    groups: [],
    groupBy: null
  }
},
computed:{
  ...mapDashboardGetters(['entityName']),
  isChartLoad(){
    return this.data?.load === true || this.chartLoad === true
  },
  isMockup(){
    return this.defaultData && this.defaultData.status
  },
  loadChartFailed(){
    return this.data?.retryTimes >= 3;
  },
  name(){
    return this.entityName(this.data.entityType)
  },
  existsGroups() {
    return Object.keys(this.groups);
  },
  isChartEmpty(){
    const sum = this.series[0].data?.reduce((acc, curr) => {
      acc += curr

      return acc
    },0)
    return this.series[0]?.data?.length == 0 || sum == 0 || !sum ? true : false;
  },
  showToggleGroups(){
    return this.existsGroups.length > 1
  },
  options(){
    return {
      chart:{
        type: 'radar',
      },
      dataLabels: {
        enabled: true
      },
      stroke: {
    show: true,
    width: 2,
    dashArray: 0
  },
  legend: {
              position: 'left',
              itemMargin: {
                horizontal: 10,
                vertical: 0,
              },
              floating: false,
            },
      responsive: [
        {
          breakpoint: 1780,
          options:{
            legend: {
              position: 'bottom',
              itemMargin: {
                horizontal: 10,
                vertical: 0,
              },
              floating: false,
            }
          }
        },
      ],
      plotOptions: {
        radar:{
          polygons: {
            strokeWidth: 2,
          },
        }
      },
      xaxis: {
        categories: this.labels ?? [],
          labels:{
            show: true,
            style: {
              fontSize: "14px",
              colors: this.colors ?? [0],
            }
          }
        },
    }
  },
  chartData(){
    if(this.loadChartFailed)
      return {};

    if(this.isChartLoad)
      return;

    if(!this.isMockup && !this.data?.entityData && !this.isChartLoad && this.data.retryTimes < 3){
      this.$emit('pullChart', { chartID: this.data.id });
    }

    const selectedNames = (this.groups[this.groupBy] ?? []).reduce((acc, curr) => {
      if(curr.selected)
        acc.push(curr.name)

      return acc
    },[]);

    return this.defaultData?.status ?? Object.keys(this.data?.entityData?.[this.groupBy] ?? [])
    .reduce((acc, curr) => {
      if(selectedNames.includes(curr) && !acc[curr])
        acc[curr] = this.data.entityData[this.groupBy][curr]

      return acc
    }, {});
  },
  series(){
    const series = Object.values(this.chartData).map(item => item.value)

      return series?.length ? [{data: series, name: this.entityName(this.data.entityType)}] : [0]

    },
  labels(){
    return Object.keys(this.chartData);
  },
  colors(){
    return Object.values(this.chartData).map(item => item.color)
  },
  },
  created(){
    const data = this.data?.entityData ?? [];

    this.groups = Object.keys(data).reduce((acc, curr) => {
      if(!acc[curr])
        acc[curr] = Object.keys(data[curr]).map(item => {
          return {
            name: item,
            selected: true
          }
        })

      return acc
    }, {})

    this.groupBy = Object.keys(this.groups)[0];
  },
  methods:{
    updateEntity(entity){
      this.$emit('updateEntity', entity, this.data.id)
    },
    updateGroupBy(groupBy){
      this.groupBy = groupBy
    },
    updateGroups(groups){
      this.groups = groups
    },
  }
}
</script>