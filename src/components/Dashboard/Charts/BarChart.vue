<template>
  <div
    style="width: 100%; height: 100%"
    class="apex_bar-chart"
  >
    <template v-if="!isChartLoad">
      <div
        v-if="data.id"
        class="d-flex justify-space-between align-center mb-5"
      >
        <div class="chart-filters d-flex align-center flex-wrap">
          <h3>{{ $t('dashboard.breakdown') }}</h3>
          <PriorityStatusFilter
            v-if="!isAxisRelationshipValid"
            :groups="groups"
            :group-by="groupBy"
            @updateGroups="updateGroups"
          />
          <EntitySelector
            :entity="data.entityType"
            :disable-select="isAxisRelationshipValid"
            :chart-id="data.id"
            :editable="editable"
            @updateEntity="updateEntity"
          />
          <ChartViewFilter
            v-if="!isAxisRelationshipValid"
            :key="groupBy"
            :groups="existsGroups"
            :group-by="groupBy"
            :default-time="data.groupByTime"
            :is-x-y-chart="true"
            @updateMultiSeries="changeMultiSeries"
            @changeTimeGrouping="changeTimeGrouping"
            @updateGroupBy="updateGroupBy"
          />
        </div>
        <div class="chart-settings">
          <v-menu
            left
            offset-y
            content-class="chart-settings-menu"
          >
            <template #activator="{ on }">
              <v-btn
                icon
                v-on="on"
              >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list
              dense
              class="text-left"
            >
              <v-list-item
                @click="$emit('deleteChart', data.id)"
              >
                <DeleteIcon />
                <v-list-item-content class="ml-2 error--text">
                  {{ $t('delete') }}
                </v-list-item-content>
              </v-list-item>
              <v-list-item
                @click="$emit('expandChart', data.id)"
              >
                <ExpandIcon />
                <v-list-item-content class="ml-2">
                  {{ $t('expand') }}
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
      <vue-apex-charts
        v-if="!isChartEmpty"
        type="bar"
        height="280"
        :options="chartOptions"
        :series="series"
      />
      <FailedState v-else-if="loadChartFailed" />
      <EmptyState v-else />
    </template>
    <div
      v-else
      class="dashboard__charts-card"
    >
      <div
        class="d-flex flex-column justify-space-between align-center"
        style="min-height: 325px"
      >
        <div class="d-flex justify-space-between align-center w-full">
          <div class="d-flex gap-2">
            <v-skeleton-loader
              v-for="t in 3"
              :key="t"
              height="40"
              width="100"
              type="chart"
            />
          </div>
          <v-skeleton-loader
            type="button"
            height="40"
            width="40"
          />
        </div>
        <v-skeleton-loader
          type="chart"
          height="240"
          width="100%"
        />
      </div>
    </div>
  </div>
</template>
<script>
import VueApexCharts from 'vue-apexcharts'
import EntitySelector from '@/components/Dashboard/Filters/EntitySelector'
import ChartViewFilter from '@/components/Dashboard/Filters/ChartViewFilter'
import PriorityStatusFilter from '@/components/Dashboard/Filters/PriorityStatusFilter'
import ExpandIcon from '@/assets/svg/expand.svg';
import DeleteIcon from '@/assets/svg/delete.svg';
import { createNamespacedHelpers } from 'vuex';
import { groupByWeek, groupByMonth } from '@/utils/timeGroupingUtils';
import { relationsMappingNames } from '@/constants/dashboard.js'
import FailedState from '@/components/Dashboard/Charts/FailedState.vue';


const { mapGetters: mapDashboardGetters } = createNamespacedHelpers('user')


export default{
  components:{
    VueApexCharts,
    EntitySelector,
    DeleteIcon,
    ExpandIcon,
    ChartViewFilter,
    PriorityStatusFilter,
    FailedState
  },
  props:{
    data:{
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    },
    defaultData:{
      type: Array,
    },
    view: {
      type: String,
      required: false
    },
    editable: {
      type: Boolean,
      default: true
    },
    chartLoad: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
      timeGrouping: null,
      isMultiSeries: true,
      relationsMappingNames,
      groups: [],
      groupBy: null
    }
  },
  computed:{
    ...mapDashboardGetters(['entityName']),
    seriesLength(){
      if(Array.isArray(this.series))
        return Object.keys(this.series[0]?.data)?.length;
      return 0
    },
    isAxisRelationshipValid(){
      return Array.isArray(this.data.relatedTo);
    },
    loadChartFailed(){
      return this.data?.retryTimes >= 3;
    },
    isChartEmpty(){
      const sum = this.series?.reduce((acc, curr) => {
        curr.data?.forEach(e => {
          acc += (this.isAxisRelationshipValid ? e : e?.y) ?? 0
        })

        return acc
      }, 0)

      return sum == 0 ? true : false;
    },
    existsGroups() {
      return Object.keys(this.groups);
    },
    isChartLoad(){
      return this.data?.load === true || this.chartLoad === true
    },
    chartData(){
      if(this.loadChartFailed){
        return {}
      }

      if(this.isAxisRelationshipValid){
        const data = {
          series: [],
          xAxis: null
        };

        for (const curr of this.data.relatedTo) {
          
          const reData = this.data.entityData?.[curr.entityType];

          if (!reData && this.data.retryTimes < 3) {
            this.$emit('pullChart', { chartID: this.data.id });
            return {}; 
          }
          data.series.push({
            name: relationsMappingNames[curr.entityType],
            data: Object.values(reData),
            color: '#0c2ff3'
          });

          if (!data.xAxis) {
            data.xAxis = Object.keys(reData);
          }

        }
        return data;
      }
      const entityData = this.data.entityData;

      if(!entityData && !this.isChartLoad){
        this.$emit('pullChart', {chartID: this.data.id })
        return {} 
      }
        
      if(this.data.groupByTime == 'weekly'){
        return groupByWeek(entityData, this.existsGroups)
      }

      if(this.data.groupByTime == 'monthly'){
        return groupByMonth(entityData, this.existsGroups)
      }

      return entityData
    },
    series(){
      if(this.isAxisRelationshipValid)
        return this.chartData.series;

      const selectedNames = (this.groups[this.groupBy] ?? []).reduce((acc, curr) => {
        if(curr.selected)
          acc.push(curr.name)

        return acc
      },[]);

      const aggregateData = (entityData, keyType) => {
        const aggregatedData = {
          name: this.entityName(this.data.entityType),
          data: [],
          color: '#0C2FFC'
        };

        const multiAggregatedData = {};

      Object.entries(entityData ?? []).forEach(([date, details]) => {
        
        const group = details[keyType];
        let totalValue = 0;
        if(this.data.isMultiSeries){
          Object.entries(group ?? []).forEach(([name, { value, color }]) => {
            if (!multiAggregatedData[name]) {
              multiAggregatedData[name] = {
                name,
                color: color,
                data: []
              };
            }
            if (selectedNames?.includes(name)) {
              multiAggregatedData[name].data.push({ x: date, y: value });
            }
          });
        }else{
          Object.entries(group ?? []).forEach(([name, {value}]) => {
            if(selectedNames?.includes(name))
              totalValue += value;
          });
  
          aggregatedData.data.push({ x: date, y: totalValue });
        }

      });

      if (this.data.isMultiSeries)
        return Object.values(multiAggregatedData);
      else 
        return [aggregatedData];
    };

      return this.defaultData ?? aggregateData(this.chartData, this.groupBy);
    },
    chartOptions(){
      return{
        chart: {
          type: 'bar',
          stacked: true,
          zoom: {
            enabled: false
          },
          
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth',
        },
        markers: {
          size: 6,
        },
        tooltip: {
          enabled: true,
          shared: false,
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: 0.25 * Math.log(this.seriesLength + 1) * 30 + "%",
          },
        },
        xaxis:{
          ...(this.isAxisRelationshipValid ? {
            categories: this.chartData.xAxis
          } : {})
        },
        grid: {
          show: true,
          borderColor: '#90A4AE',
          strokeDashArray: 5,
          position: 'back',
          row: {
            colors: ['#FFF'],
            opacity: 0.5
          },
        },
      }
    }  
  },
  created(){
    const data = this.data?.entityData ?? [];
    const dateKey = Object.keys(data)[0];

    this.groups = Object.keys(data[dateKey] ?? []).reduce((acc, curr) => {
      if(!acc[curr])
        acc[curr] = Object.keys(data[dateKey][curr]).map(item => {
          return {
            name: item,
            selected: true
          }
        })
      
      return acc
    }, {})

    this.groupBy = Object.keys(this.groups)[0]
  },
  methods:{
    updateEntity(entity){
      this.$emit('updateEntity', entity, this.data.id)
    },
    changeTimeGrouping(val){
      this.$emit('updateTimeGrouping', val, this.data.id)
      this.timeGrouping = val;
    },
    changeCategoryGrouping(val){
      this.$emit('updateGroupBy', val, this.data.id)
    },
    changeMultiSeries(val){
      this.$emit('updateMultiSeries', val, this.data.id)
    },
    updateGroupBy(groupBy){
      this.groupBy = groupBy
    },
    updateGroups(groups){
      this.groups = groups
    },
  }
}
</script>
<style scoped>
.apex_bar-chart .apexcharts-canvas,.apex_bar-chart .apexcharts-canvas .apexcharts-svg{
  width: 100% !important;
}
</style>
<style scoped>
.apex_bar-chart .chart-filters{
  gap: 8px;
}
</style>
<style scoped>
.expand-content{
  color: #344054 !important;
}

</style>