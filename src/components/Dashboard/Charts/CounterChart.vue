<template>
  <div class="dashboard__analytics-card d-flex py-5">
    <template v-if="!isChartLoad">
      <div class="analytics-card-icon">
        <div class="v-icon-wraper align-center">
          <component
            :is="types[data.entityType].icon"
            :stroke-width="2"
          />
        </div>
      </div>
      <div class="analytics-card-text">
        <h3>{{ value ?? countsValue }}</h3>
        <p>{{ types[data.entityType].name }}</p>
      </div>
    </template>
    <div
      v-else
      class="d-flex align-center justify-center w-50"
    >
      <v-skeleton-loader
        height="64"
        min-width="64"
        type="avatar"
      />
      <div class="d-flex flex-column align-start justify-center ml-3 w-full">
        <v-skeleton-loader
          class="mb-2 w-full"
          type="text"
          height="24"
        />
        <v-skeleton-loader
          class="w-full"
          type="text"
          height="16"
        />
      </div>
    </div>
  </div>
</template>
<script>

import DefectIcon from '@/assets/svg/left-menu/defect.svg';
import MilestoneIcon from '@/assets/svg/left-menu/milestone.svg';
import TestPlansIcon from '@/assets/svg/left-menu/test-plans.svg';
import TestRunsIcon from '@/assets/svg/left-menu/test-runs.svg';
import TestCasesIcon from '@/assets/svg/left-menu/test-cases.svg';
import TestProgramsIcon from '@/assets/svg/left-menu/test-programs.svg'


export default{
  components:{
    DefectIcon,
    MilestoneIcon,
    TestPlansIcon,
    TestRunsIcon,
    TestCasesIcon,
    TestProgramsIcon
  },
  props:{
    data: {
      required: true,
      type: Object,
    },
    value: {
      type: String
    },
    view: {
      type: String,
      required: false
    },
    chartLoad: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
      types: {
        "testCase": {
          icon: 'TestCasesIcon',
          name: 'Test cases'
        },
        "testExecution": {
          icon: 'TestCasesIcon',
          name: 'Test Executions'
        },
        "testRun": {
          icon: 'TestRunsIcon',
          name: 'Test runs'
        },
        "defect": {
          icon: 'DefectIcon',
          name: 'Defects'
        },
        'milestone': {
          icon: 'MilestoneIcon',
          name: 'Milestones'
        },
        'testPlan': {
          icon: 'TestPlansIcon',
          name: 'Test plans',
        },
        'testProgram': {
          icon: 'TestProgramsIcon',
          name: 'Test programs'
        }
      }
    }
  },
  computed:{
    isChartLoad(){
      return this.data?.load === true || this.chartLoad === true
    },
    loadChartFailed(){
      return this.data?.retryTimes >= 3;
    },
    countsValue(){
      if(this.loadChartFailed)
        return 0;

      if(this.isChartLoad)
        return;

      if(!this.data.entityData)
        this.$emit('pullChart', { chartID: this.data.id });

      return this.data.entityData?.uid?.value ?? 0
    }
  },
}
</script>
