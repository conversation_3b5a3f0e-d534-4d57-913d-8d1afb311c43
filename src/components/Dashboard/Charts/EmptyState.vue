<template>
  <div class="wrap-empty-chart d-flex align-center justify-center">
    <div class="empty-chart d-flex flex-column align-center">
      <div class="chart-svg">
        <EmptyChart />
      </div>
      <div class="chart-text">
        <p>{{ $t('noDataAvaliable') }}</p>
      </div>
    </div>
  </div>
</template>
<script>
import EmptyChart from '@/assets/svg/empty-chart.svg';
export default{
  components: {
    EmptyChart
  }
}
</script>
<style scoped>
.chart-text p{
  color: #667085;
}
.wrap-empty-chart{
  height: 100%;
}
</style>