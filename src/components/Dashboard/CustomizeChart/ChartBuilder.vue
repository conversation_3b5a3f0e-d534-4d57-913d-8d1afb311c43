<template>
  <v-expansion-panels
    flat
    class="chart-builder-expansion"
  >
    <v-expansion-panel>
      <v-expansion-panel-header>
        <h4>{{ $t('dashboard.chartBuilder') }}</h4>
      </v-expansion-panel-header>
      <v-expansion-panel-content>
        <v-row no-gutters>
          <v-col :cols="showChart ? 6 : 12">
            <div class="chart-info d-flex mb-10">
              <p class=" mr-1 mb-0">
                {{ $t('dashboard.customizeYourInsights') }}
              </p>
              <v-tooltip
                bottom
                max-width="500"
              >
                <template #activator="{ on, attrs }">
                  <v-icon
                    color="#667085"
                    dark
                    v-bind="attrs"
                    v-on="on"
                  >
                    mdi-help-circle-outline 
                  </v-icon>
                </template>
                <span>{{ $t('dashboard.chartBuilderTooltip') }}</span>
              </v-tooltip>
            </div>
            <v-label
              class="text-left fs-14px text-theme-label font-weight-medium mb-2"
              style="width: 57%; display: flex; justify-content: start"
            >
              {{ $t('dashboard.chartType') }} <strong
                class="red--text text--lighten-1"
              >*</strong>
            </v-label>
            <v-select
              v-model="selectedChart"
              type="text"
              dense
              single-line
              height="38px"
              :items="charts"
              item-text="name"
              item-value="type"
              clear-icon="body-2"
              append-icon="mdi-chevron-down"
              class="rounded-lg field-theme custom-prepend"
              background-color="#F9F9FB"
              :placeholder="$t('dashboard.chartType')"
              :menu-props="{ offsetY: true }"
              @change="updateGroupSelections"
            />
            <v-label
              class="text-left fs-14px text-theme-label font-weight-medium mb-2"
              style="width: 57%; display: flex; justify-content: start"
            >
              {{ $t('dashboard.entityType') }} <strong
                class="red--text text--lighten-1"
              >*</strong>
            </v-label>
            <v-select
              v-model="selectedEntity"
              type="text"
              dense
              single-line
              height="38px"
              :items="filterEntitiesType"
              item-text="name"
              item-value="entityType"
              clear-icon="body-2"
              append-icon="mdi-chevron-down"
              class="rounded-lg field-theme custom-prepend"
              background-color="#F9F9FB"
              :placeholder="$t('dashboard.entityType')"
              :menu-props="{ offsetY: true }"
              @change="updateGroupSelections"
            />
            <template v-if="isXYChart">
              <v-label
                class="text-left fs-14px text-theme-label font-weight-medium mb-2"
                style="width: 57%; display: flex; justify-content: start"
              >
                {{ $t('dashboard.xAxis') }} <strong
                  class="red--text text--lighten-1"
                >*</strong>
              </v-label>
              <v-select
                v-model="selectedxAxis"
                type="text"
                dense
                single-line
                height="38px"
                :items="xAxisOptions"
                item-text="name"
                item-value="value"
                clear-icon="body-2"
                append-icon="mdi-chevron-down"
                class="rounded-lg field-theme custom-prepend"
                background-color="#F9F9FB"
                :placeholder="$t('dashboard.entityType')"
                :menu-props="{ offsetY: true }"
                @change="updateGroupSelections"
              />
            </template>
            <template v-if="selectedEntity && selectedChart !== 'boxPlot'">
              <v-label
                class="text-left fs-14px text-theme-label font-weight-medium mb-2"
                style="width: 57%; display: flex; justify-content: start"
              >
                {{ $t('groupBy') }} <strong
                  class="red--text text--lighten-1"
                >*</strong>
              </v-label>
              <v-autocomplete
                v-model="selectedGroupBy"
                :items="groupByOptions"
                multiple
                item-text="name"
                item-value="value"
                :placeholder="$t('chooseTags')"
                small-chips
                deletable-chips
                dense
                background-color="#F9F9FB"
                class="rounded-lg field-theme custom-prepend mh-38px"
                hide-details
                @change="groupByUpdated"
              >
                <template #item="{ item, on, attrs }">
                  <v-list-item
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-list-item-action class="mr-2">
                      <v-checkbox
                        v-model="selectedGroupBy" 
                        :value="item.value" 
                        class="field-theme"
                        :ripple="false"
                        off-icon="icon-checkbox-off"
                        on-icon="icon-checkbox-on" 
                        @click.stop
                      />
                    </v-list-item-action>
                    <v-list-item-content>
                      <v-list-item-title>{{ item.name }}</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-autocomplete>
            </template>
          </v-col>
          <v-col
            v-if="showChart"
            cols="6"
          >
            <VirtualChartRenderer
              :key="Object.keys(filterData).length + chartKey + dummyLength"
              :name="selectedEntity"
              :type="selectedChart"
              :data="filterData"
              :is-x-y-chart="isXYChart"
            />
          </v-col>
        </v-row>
        <!-- Temporarily remove advanced filters. -->
        <v-expansion-panels
          v-if="showChart && (!selectedxAxis || selectedxAxis == 'time') && selectedChart !== 'boxPlot' && false" 
          flat
          class="advanced-filters-expansion"
        >
          <v-expansion-panel>
            <v-expansion-panel-header class="pa-0">
              <v-label
                class="text-left fs-14px text-theme-label font-weight-medium mb-2"
                style="width: 57%; display: flex; justify-content: start"
              >
                {{ $t('dashboard.advancedFilters') }} <strong
                  class="red--text text--lighten-1"
                />
              </v-label>
            </v-expansion-panel-header>
            <v-expansion-panel-content class="pa-0">
              <div class="select-group-filter">
                <div class="select-group-input">
                  <v-label
                    class="text-left fs-14px text-theme-label font-weight-medium mb-2"
                    style="width: 57%; display: flex; justify-content: start"
                  >
                    {{ $t('dashboard.filterField') }} <strong
                      class="red--text text--lighten-1"
                    >*</strong>
                  </v-label>
                  <v-select
                    v-model="selectedGroupByFilter"
                    type="text"
                    dense
                    single-line
                    height="38px"
                    :items="selectedGroupBy"
                    item-text="name"
                    item-value="value"
                    clear-icon="body-2"
                    append-icon="mdi-chevron-down"
                    class="rounded-lg field-theme custom-prepend"
                    background-color="#F9F9FB"
                    :placeholder="$t('dashboard.filterField')"
                    :menu-props="{ offsetY: true }"
                    :hide-details="true"
                  />
                  <v-btn
                    v-if="selectedGroupByFilter"
                    color="primary"
                    class="white--text text-capitalize btn-theme"
                    depressed
                    height="30"
                    elevation="0"
                    @click="setFilter"
                  >
                    {{ $t('addFilter') }}
                    <v-icon>
                      mdi-plus
                    </v-icon>
                  </v-btn>
                </div>
              </div>
              <div
                v-for="(groupFilter, index) in filters"
                :key="index + filtersKey"
                class="groupBy-filter"
              >
                <div
                  v-for="(filter, indx) in groupFilter"
                  :key="indx"
                  class="tp-filter my-3"
                >
                  <div class="custom-filter my-4">
                    <template v-if="isNameColorRequired">
                      <v-label
                        class="text-left fs-14px text-theme-label font-weight-medium mb-2"
                        style="width: 57%; display: flex; justify-content: start"
                      >
                        {{ $t('dashboard.filterName') }} <strong
                          class="red--text text--lighten-1"
                        >*</strong>
                      </v-label>
                      <v-text-field
                        v-model="filter.name"
                        type="text"
                        dense
                        single-line
                        height="38px"
                        clear-icon="body-2"
                        :hide-details="true"
                        class="rounded-lg field-theme custom-prepend mb-3"
                        background-color="#F9F9FB"
                        :placeholder="$t('dashboard.filterName')"
                        :menu-props="{ offsetY: true }"
                      />
                    </template>
                    <template v-if="isNameColorRequired">
                      <v-label
                        class="text-left fs-14px text-theme-label font-weight-medium mb-2"
                        style="width: 57%; display: flex; justify-content: start"
                      >
                        {{ $t('dashboard.color') }} <strong
                          class="red--text text--lighten-1"
                        >*</strong>
                      </v-label>
                      <div class="custom-container mb-3">
                        <ColorItem
                          v-model="filter.color"
                          default-color="#000"
                          :disabled="true"
                          @input="() => filtersKey++"
                        />
                      </div>
                    </template>
                    <div
                      v-for="(con, idx) in filter.conditions"
                      :key="idx + filtersKey"
                      class="custom-filter-conditions my-3"
                    >
                      <v-label
                        class="text-left fs-14px text-theme-label font-weight-medium mb-2"
                        style="width: 57%; display: flex; justify-content: start"
                      >
                        {{ $t('dashboard.filterCondition') }} <strong
                          class="red--text text--lighten-1"
                        >*</strong>
                      </v-label>
                      <v-select
                        v-model="con.operator"
                        type="text"
                        dense
                        single-line
                        height="38px"
                        :items="operatorOptions(index)"
                        :hide-details="true"
                        item-text="name"
                        item-value="value"
                        clear-icon="body-2"
                        append-icon="mdi-chevron-down"
                        class="rounded-lg field-theme custom-prepend mb-3"
                        background-color="#F9F9FB"
                        :placeholder="$t('dashboard.filterCondition')"
                        :menu-props="{ offsetY: true }"
                      />
                      <template>  
                        <v-label
                          class="text-left fs-14px text-theme-label font-weight-medium mb-2"
                          style="width: 57%; display: flex; justify-content: start"
                        >
                          {{ $t('dashboard.filterValue') }} <strong
                            class="red--text text--lighten-1"
                          >*</strong>
                        </v-label>
                        <v-combobox
                          v-model="con.value"
                          type="text"
                          dense
                          single-line
                          :hide-details="true"
                          small-chips
                          :items="valueOptions(con.operator, index)"
                          item-text="name"
                          item-value="value"
                          height="38px"
                          clear-icon="body-2"
                          class="rounded-lg field-theme custom-prepend"
                          background-color="#F9F9FB"
                          :placeholder="$t('dashboard.filterValue')"
                          :menu-props="{ offsetY: true }"
                        />
                      </template>
                      <template v-if="con.operator == 'between'">
                        <v-label
                          class="text-left fs-14px text-theme-label font-weight-medium mb-2"
                          style="width: 57%; display: flex; justify-content: start"
                        >
                          {{ $t('dashboard.filterValue') }} <strong
                            class="red--text text--lighten-1"
                          >*</strong>
                        </v-label>
                        <v-combobox
                          v-model="con.value2"
                          type="text"
                          dense
                          single-line
                          :hide-details="true"
                          small-chips
                          :items="valueOptions(con.operator, index)"
                          item-text="name"
                          item-value="value"
                          height="38px"
                          clear-icon="body-2"
                          class="rounded-lg field-theme custom-prepend"
                          background-color="#F9F9FB"
                          :placeholder="$t('dashboard.filterValue')"
                          :menu-props="{ offsetY: true }"
                        />
                      </template>
                    </div>
                  </div>
                  <div class="custom-filter-actions  d-flex justify-space-between">
                    <div
                      class="add-actions d-flex"
                      style="gap: 8px"
                    >
                      <v-btn
                        color="primary"
                        class="white--text text-capitalize btn-theme"
                        depressed
                        height="30"
                        elevation="0"
                        @click="addCondition(indx)"
                      >
                        {{ $t('addCondition') }}
                        <v-icon>
                          mdi-plus
                        </v-icon>
                      </v-btn>
                    </div>
                  </div>
                </div>
                <v-btn
                  height="30"
                  color="danger"
                  class="text-capitalize rounded-lg mr-3 white--text btn-theme"
                  depressed
                  icon
                  @click="deleteFilter(index)"
                >
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </div>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
        <template v-if="selectedChart === 'boxPlot' && selectedEntity">
          <v-btn
            color="primary"
            class="white--text text-capitalize btn-theme my-4"
            depressed
            height="40"
            elevation="0"
            @click="addField"
          >
            {{ $t('addField') }}
            <v-icon>
              mdi-plus
            </v-icon>
          </v-btn>
          <div
            v-for="(field, index) in relatedFields"
            :key="index"
            class="mb-2"
          >
            <v-text-field
              v-model="field.name"
              type="text"
              dense
              single-line
              height="38px"
              clear-icon="body-2"
              :hide-details="true"
              class="rounded-lg field-theme custom-prepend mb-3"
              background-color="#F9F9FB"
              :placeholder="$t('dashboard.fieldName')"
              :menu-props="{ offsetY: true }"
            />
            <v-btn
              icon
              color="red"
              @click="removeField(index)"
            >
              <v-icon>mdi-delete</v-icon>
            </v-btn>
          </div>
        </template>
        <v-btn
          v-if="showAddChart"
          color="primary"
          class="white--text text-capitalize btn-theme my-4"
          depressed
          height="40"
          elevation="0"
          @click="addChart()"
        >
          {{ $t('addChart') }}
          <v-icon>
            mdi-plus
          </v-icon>
        </v-btn>
      </v-expansion-panel-content>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script>
import { charts, entitiesType } from '@/constants/dashboard'
import VirtualChartRenderer from '@/components/Dashboard/Charts/VirtualChartRenderer'
import colorPreferencesMixin from '@/mixins/colorPreferences';
import ColorItem from '@/components/Settings/DataColors/ColorItem.vue';
import { showErrorToast } from '@/utils/toast';
import dayjs from 'dayjs';
export default{
  components: {
    VirtualChartRenderer,
    ColorItem
  },
  mixins: [colorPreferencesMixin],
  data(){
    return {
      selectedEntity: null,
      selectedChart: null,
      selectedCustomField: null,
      selectedxAxis: null,
      selectedGroupBy: [],
      charts,
      entitiesType,
      relatedFields:[],
      groupByOptions: [],
      filters: {},
      filtersKey: 0,
      chartKey: 0,
      selectedGroupByFilter: null
    }
  },
  computed:{
    showAddChart(){
      return (this.selectedChart && this.selectedGroupBy.length) || (this.selectedChart == 'boxPlot' && this.selectedEntity && this.relatedFields.length)
    },
    filterEntitiesType(){
      return this.selectedChart == 'boxPlot' ? this.entitiesType.filter(e => e.entityType == 'testCase') : this.entitiesType.filter(e => e.entityType !== 'defect');
    },
    operators(){
      const op = [
        {name: 'Equal', value: '='},
        {name: 'Not Equal', value: '!='}
      ];
      if(this.selectedFilter == 'priority')
        op.push({name: "Completed priority", value:"isPriorityCompleted"}) // Start implement custom function to validate those types
      if(this.selectedFilter == 'status')
        op.push({name: "Completed status", value:"isStatusCompleted"})
      if(this.selectedGroupByFilter == 'progress') // We should indicate other customFields that should be filtered by number
        op.push(...[{name: 'Between', value: 'between'},{name: 'Less than', value: '<'}, {name: 'Greater than', value: '<'}])
      
      return op
    },
    generateFilterName(){
      return this.selectedGroupByFilter + Object.keys(this.filters[this.selectedGroupByFilter] ?? {}).length
    },
    xAxisOptions(){
      const options = [{
        name: 'Over time',
        value: 'time'
      }];

      if(this.selectedChart !== 'heatmap'){
        const entity = entitiesType.find(e => e.entityType == this.selectedEntity)
        if(entity && !['testCase', 'testExecution'].includes(entity.entityType)){ // Entities explicitly omitted from X-axis display
          options.push({
            name: entity.name,
            value: entity.entityType
          })
        }
      }

      return options
    },
    isNameColorRequired(){
      return !(this.isXYChart && this.selectedxAxis != 'time')
    },
    showChart(){
      return (this.selectedChart && this.selectedEntity && this.selectedGroupBy.length) || (this.selectedChart == 'boxPlot' && this.selectedEntity)
    },
    dummyLength(){
      let counts = 0;
      Object.values(this.filterData).forEach(e => counts += e.length);
      return counts
    },
    filterData() {
    const inputData = this.dummyData;
    
    const isDateGrouped = Object.values(inputData)[0] && 
                        typeof Object.values(inputData)[0] === 'object' &&
                        !Array.isArray(Object.values(inputData)[0]) &&
                        !Object.keys(Object.values(inputData)[0]).every(key => !isNaN(key));

  if (isDateGrouped) {
    return Object.keys(inputData).reduce((acc, date) => {
      const dateData = inputData[date];
      acc[date] = {};

      Object.keys(dateData).forEach(group => {
        const filters = this.filters[group];
        let groupData = dateData[group];

        if (groupData && typeof groupData === 'object' && !Array.isArray(groupData)) {
          groupData = Object.values(groupData);
        }

        if (filters?.added) {
          acc[date][group] = this.filterAndAggregateData(groupData, filters);
        } else {
          acc[date][group] = groupData || [];
        }
      });

      return acc;
    }, {});
  } else {
    return Object.keys(inputData).reduce((acc, group) => {
      const filters = this.filters[group];
      let groupData = inputData[group];

      if (groupData && typeof groupData === 'object' && !Array.isArray(groupData)) {
        groupData = Object.values(groupData);
      }

      if (filters?.added) {
        acc[group] = this.filterAndAggregateData(groupData, filters);
      } else {
        acc[group] = groupData || [];
      }

      return acc;
    }, {});
  }
},
    isXYChart(){
      return['line', 'bar', 'heatmap'].includes(this.selectedChart)
    },
    dummyData() {
      let data = {};
      this.selectedGroupBy.forEach(group => {
      if (this.isXYChart) {
        const entityName = this.selectedEntity === 'testExecution' ? 'testCase' : this.selectedEntity;
        if(this.selectedxAxis == 'time'){
          const numberOfDays = 7;
          
          for (let daysAgo = 0; daysAgo < numberOfDays; daysAgo++) {
            const dateKey = dayjs().subtract(daysAgo, 'day').format('MM/DD');
            
            if (!data[dateKey]) {
              data[dateKey] = {};
            }
            
            if (!data[dateKey][group]) {
              data[dateKey][group] = [];
            }
            
            
            const dummyData = this.generateData(group.toLowerCase(), entityName);
            
            data[dateKey][group].push(...dummyData);
          }
        }else{
          const entityType = this.entitiesType.find(e => e.entityType === this.selectedEntity);
          if (entityType) {
            const numberOfEntities = 5;
            
            for (let i = 1; i <= numberOfEntities; i++) {
              const entityKey = `${entityType.name} ${i}`;
              
              if (!data[entityKey]) data[entityKey] = {};
              if (!data[entityKey][group.value]) data[entityKey][group.value] = [];
              
              const dummyData = this.generateData(group.toLowerCase(), entityName);
              data[entityKey][group.value].push(...dummyData);
            }
          }
        }
      } else {
        const entityName = this.selectedEntity === 'testExecution' ? 'testCase' : this.selectedEntity;
        data[group] = this.generateData(group.toLowerCase(), entityName);
      }
  });
  
  return data;
},
  },
  methods:{
    updateGroupSelections(){
      this.selectedGroupBy = []
      this.initializeGroupByOptions();
    },
    filterAndAggregateData(data, filters) {
      // Temporarily pause showing filter results
      if (!Array.isArray(data)) {
        data = Object.values(data || {});
      }

      return data.reduce((filteredAcc, element) => {
        const filterResult = this.applyFilters(filters, element);
        if (filterResult.match) {
          const existing = filteredAcc.find(item => item.name === filterResult.name);
          if (existing) {
            existing.value += element.value;
          } else {
            filteredAcc.push({
              ...element,
              color: filterResult.color || element.color,
              name: filterResult.name || element.name,
              value: element.value,
            });
          }
        }
        return filteredAcc;
      }, []);
    },
    addField() {
      this.relatedFields.push({ name: '' });
    },
    removeField(index) {
      this.relatedFields.splice(index, 1);
    },
    applyFilters(nfilters, record) {
      if(!nfilters)
        return {match: true}

      const filters = nfilters.conditions;
      if (!Array.isArray(filters) || filters.length === 0 || !nfilters.added) return {match: true};

      let result = { match: false, color: null, name: null };

      const { color, name } = nfilters;
       
      for (const filter of filters) {
        if (!filter) continue;

        const { operator, value } = filter;
        let isMatch = false;
        const pNumber = Number.parseInt(record.value);

        const recordLower = String(record.name).toLowerCase();
        const valueLower = String(value).toLowerCase();
        const isCompleted = record.isCompleted;
        switch (operator) {
          case 'completedStatus':
            isMatch = isCompleted == true;
            break;
          case '=':
            isMatch = recordLower == valueLower;
            break;
          case '!=':
            isMatch = recordLower != valueLower;
            break;
          case '<':
            isMatch = pNumber < value;
            break;
          case '>':
            isMatch = pNumber > value;
            break;
          case '<=':
            isMatch = pNumber <= value;
            break;
          case '>=':
            isMatch = pNumber >= value;
            break;
          default:
            isMatch = false;
        }

          if (isMatch) {
            result.match = true;
            result.color = color || result.color;
            result.name = name || result.name;
            break;
          }
        }

      return result.match ? result : false;
    },
    generateRandomHexColor() {
      return `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, "0")}`;
    },
    generateData(group, entityName){
      if(group == 'status')
        return this.getStatuses(entityName).map(item => {
            return {
              ...item,
              value: Math.floor(Math.random() * (18 - 3 + 1)) + 3
            }
          })
      else if(group == 'priority')
        return this.getPriorities(entityName).map(item => {
              return {
                ...item,
                value: Math.floor(Math.random() * (18 - 3 + 1)) + 3
              }
            })
      else {
        const randomLength = Math.floor(Math.random() * (5 - 3 + 1)) + 3;
         return Array.from({ length: randomLength }).map((_, index) => {
            return {
              name: `${group} ${index + 1}`,
              value: Math.floor(Math.random() * (18 - 3 + 1)) + 3,
              color: this.generateRandomHexColor(),
            }
          });
      }
    },
    deleteFilter(filter){
      if(this.filters[filter]){
        const newFilters = { ...this.filters };
        delete newFilters[filter];
        this.filters = newFilters; 
      }
    },
    valueOptions(operator, field){
      if(operator == '=' || operator == '!='){
        if(field == 'status')
          return [{name: 'Completed status', value: 'completedStatus'}, {name: 'Failed status', value: 'failedStatus'}]
        if(field == 'priority')
          return [{name: 'Completed priority', value: 'completedPriority'}]
      }
    },
    operatorOptions(field){
      const op = [
        {name: 'Equal', value: '='},
        {name: 'Not Equal', value: '!='}
      ];
      if(field == 'progress')
        op.push(...[{name: 'Between', value: 'between'},{name: 'Less than', value: '<'}, {name: 'Greater than', value: '<'}])

      return op;
    },
    setFilter(){
      if(!this.filters[this.selectedGroupByFilter]){
        this.filters = {
          ...this.filters,
          [this.selectedGroupByFilter] : [{
            ...(this.isNameColorRequired ? {color: '#000'} : {}),
            ...(this.isNameColorRequired ? {name: this.generateFilterName} : {}),
            conditions: [{
              operator: null,
              value: null,
              value2: null, // Required in case of "between" operator
            }],
            added: false    
          }]
        }
      }else{
        this.filters[this.selectedGroupByFilter].push({
            ...(this.isNameColorRequired ? {color: '#000'} : {}),
            ...(this.isNameColorRequired ? {name: this.generateFilterName} : {}),
            conditions: [{
              operator: null,
              value: null,
              value2: null, // Required in case of "between" operator
            }],
            added: false    
          })
      }
    },
    addCondition(index){
      this.filters[this.selectedGroupByFilter][index].conditions.push({
        operator: null,
        value: null,
        value2: null,
      })
      this.filtersKey++;
    },
    groupByUpdated(){
      Object.keys(this.filters).forEach(element => {
        if(!this.selectedGroupBy.map(item => item.value).includes(element))
          delete this.filters[element]
      })
    },
    initializeGroupByOptions() {
      const options = [];
      
      if (!this.isXYChart || (this.isXYChart && this.selectedxAxis === 'time')) {
        // Handle standard group-by options
        if (this.selectedEntity === 'milestone') {
          options.push(
            { name: 'Status', value: 'status' },
            { name: 'Progress', value: 'progress' }
          );
        } else if (this.selectedEntity === 'testCase') {
          options.push(
            { name: 'Priority', value: 'priority' }
          );
        } else {
          options.push(
            { name: 'Status', value: 'status' },
            { name: 'Priority', value: 'priority' }
          );
        }
      } else {
        
        // Handle entity-type specific options
        const entityMappings = {
          milestone: ['testPlan', 'testRun'],
          testRun: ['testExecution', 'defect'],
          testPlan: ['testRun']
        };

        const validTypes = entityMappings[this.selectedEntity] || [];
        options.push(...this.entitiesType.filter(e => validTypes.includes(e.entityType)).map(item => {
          return {
            name: item.name,
            value: item.entityType
          }
        }));
      }

      this.groupByOptions = options;
    },
    addChart(){
      let w = 0;
      let h = 0;

      if(['donut', 'radar'].includes(this.selectedChart)){
        w = 4;
        h = 10
      }else if(['line', 'bar', 'heatmap', 'boxPlot'].includes(this.selectedChart)){
        w = 12;
        h = 10;
      }
      const chartType = this.charts.find(element => element.type == this.selectedChart).value;

      const chartConfig = this.isXYChart && this.selectedxAxis !== 'time' ? {
        relatedTo: this.selectedGroupBy.map( g => {
          return {
            entityType: g
          }
        })
      } : {
      groupBy: this.selectedChart == 'boxPlot' ? [ {field: 'testCaseRef'} ] : this.selectedGroupBy.map(curr => ({
        field: curr,
        ...(this.filters[curr] && { filters: this.filters[curr] })
      }))
      }

      if (this.selectedChart === 'boxPlot') {
        for (const field of this.relatedFields) {
          if (!field.name) {
            showErrorToast(this.$swal, this.$t('dashboard.noFieldError'));
            return;
          }
        }
      }

      if(this.selectedChart == 'boxPlot')
        chartConfig['relatedTo'] = [{
          entityType: 'testExecution',
          fields: this.relatedFields.map(field => ({
            name: field.name, // TODO Filters should add here later, when need to implement filters for Y axis
          }))
        }]

      this.$emit('buildChart', chartType, this.selectedEntity, w, h, chartConfig);
      
    }
  }
}
</script>
<style>
.chart-builder-expansion .v-select__selections input{
  min-height: 40px !important;  
}
.advanced-filters-expansion .v-expansion-panel-content .v-expansion-panel-content__wrap{
  padding: 0px !important;
}
.advanced-filters-expansion .v-expansion-panel-header{
  min-height: 40px !important;
}
.advanced-filters-expansion .custom-container{
  padding: 8px;
  border-radius: 8px;
  background-color: #F9F9FB;
}
</style>