<template>
  <div
    class="dashboard__header"
    :class="[{['d-flex flex-row justify-space-between']: !isOrgProjectView}]"
  >
    <DeleteDashboardDialog 
      v-model="showDeleteDialog"
      @delete="deleteDashboard"
    />
    <EditDashboardDialog
      :key="showEditDialog"
      v-model="showEditDialog"
      :dashboard="currentDashboard"
      @edit="editDashboard"
    />
    <div
      class="d-flex align-center justify-space-between"
      :class="[{['mb-6'] : isOrgProjectView, ['order-1'] : !isOrgProjectView}]"
    >
      <div
        v-if="isOrgProjectView"
        class="dashboard__header-title"
      >
        <h2 v-if="!skeletonLoaderState">
          {{ selectedProject }}
        </h2>
        <v-skeleton-loader
          v-else
          height="36"
          width="140"
          type="heading"
        />
      </div>
      <div class="dashboard__header-actions">
        <template v-if="isOrgProjectView">
          <v-tooltip
            v-if="!skeletonLoaderState"
            bottom
          >
            <template #activator="{ on }">
              <v-btn
                class="rounded-lg text-none flex-grow-1"
                height="40"
                min-width="40"
                elevation="0"
                v-on="on"
                @click="copyLink"
              >
                <v-icon>mdi-link</v-icon>
              </v-btn>
            </template>
            {{ $t('common.copy') }}
          </v-tooltip>
          <v-skeleton-loader
            v-else
            class="rounded-lg"
            height="40"
            width="60"
            type="button"
          />
          <v-tooltip
            v-if="!skeletonLoaderState"
            bottom
          >
            <template #activator="{ on }">
              <v-btn       
                v-if="!skeletonLoaderState"              
                class="rounded-lg text-none flex-grow-1"
                height="40"
                min-width="40"
                elevation="0"
                v-on="on"
                @click="setDefault()"
              >
                <v-icon :color="currentDashboard?.systemDefault ? '#0c2ff3' : '#667085'">
                  mdi-star-check-outline
                </v-icon>
              </v-btn>
            </template>
            {{ $t('dashboard.setDefault') }}
          </v-tooltip>
          <v-skeleton-loader
            v-else
            class="rounded-lg"
            height="40"
            width="60"
            type="button"
          />
        </template>
        <v-menu
          v-if="!skeletonLoaderState && isOrgProjectView"
          left
          :close-on-content-click="false"
          :nudge-bottom="4"
          offset-y
          content-class="dashboard-menu-list"
        >
          <template #activator="{ on }">
            <v-btn
              id="select-dashboard-views"
              class="rounded-lg text-none"
              height="40"
              elevation="0"
              color="#F9F9FB"
              v-on="on"
            >
              <span class="mr-1 text-left">{{ dashboards[selectedDashboard]?.name ?? $t('dashboard.defaultView') }}</span>
              <v-icon>
                mdi-chevron-down
              </v-icon>
            </v-btn>
          </template>
          <v-list
            class="d-flex flex-column justify-space-between rounded-xl"
            style="width:278px"
          >
            <v-list-item class="mb-2 px-2">
              <v-text-field             
                v-model="searchDashboard"
                :placeholder="$t('search')"
                class="round-8 field-theme"
                background-color="#F9F9FB"
                dense
                hide-details
                height="38px"
                required
              >
                <template #prepend-inner>
                  <SearchIcon />
                </template>
              </v-text-field>
            </v-list-item>
            <v-list-item-group>
              <v-list-item
                v-for="(item, index) in filterDashboards"
                :key="index"
                style="min-height: 44px;"
                class="custom-menu-item justify-space-between px-2"
              >
                <template v-if="item">
                  <v-list-item-content @click="selectedDashboard = index">
                    <div
                      class="dashboard-name d-flex flex-grow-1"
                      style="max-width: 100%; overflow: hidden;"
                    >
                      <p
                        style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; width: 100%;"
                        class="mb-0"
                      >
                        {{ item.name }}
                      </p>
                    </div>
                  </v-list-item-content>
                  <v-list-item-action
                    v-if="currentDashboard?.uid == item.uid"
                    class="dahboard-actions d-flex flex-row"
                    style="gap:5px"
                  >
                    <v-btn
                      icon
                      x-small
                      @click="setEditable()"
                    >
                      <UnLockIcon v-if="item.editable" />
                      <LockIcon v-else />
                    </v-btn>
                    <v-tooltip 
                      bottom
                      :disabled="_writeDashboard" 
                    >
                      <template #activator="{ on, attrs }">
                        <div 
                          v-bind="attrs" 
                          v-on="on"
                        >
                          <v-btn
                            icon
                            x-small
                            :disabled="!_writeDashboard"
                            @click="confirmEdit()"
                          >
                            <EditIcon />
                          </v-btn>
                        </div>
                      </template>
                      <span>
                        {{ $t("dashboard.noPermissionToDo", { action: $t("edit").toLowerCase() }) }}
                      </span>
                    </v-tooltip>
                    <v-tooltip 
                      bottom
                      :disabled="_deleteDashboard" 
                    >
                      <template #activator="{ on, attrs }">
                        <div 
                          v-bind="attrs" 
                          v-on="on"
                        >
                          <v-btn
                            icon
                            x-small
                            :disabled="!_deleteDashboard"
                            @click="confirmDelete()"
                          >
                            <DeleteIcon />
                          </v-btn>
                        </div>
                      </template>
                      <span>
                        {{ $t("dashboard.noPermissionToDo", { action: $t("delete").toLowerCase() }) }}
                      </span>
                    </v-tooltip>
                  </v-list-item-action>
                </template>
              </v-list-item>
            </v-list-item-group>
            <v-menu
              v-model="dashboardMenu"
              :close-on-content-click="false"
              :offset-x="true"
              :left="true"
              :nudge-left="3"
            >
              <template #activator="{ on, attrs }">
                <v-tooltip 
                  bottom 
                  :disabled="_writeDashboard"
                >
                  <template #activator="{ on: tooltipOn, attrs: tooltipAttrs }">
                    <div 
                      v-bind="tooltipAttrs" 
                      v-on="tooltipOn"
                    >
                      <v-list-item
                        v-bind="attrs"
                        :disabled="!_writeDashboard"
                        class="px-2"
                        v-on="on"
                      >
                        <v-list-item-icon>
                          <v-icon color="#2E0CF3">
                            mdi-plus
                          </v-icon>
                        </v-list-item-icon>
                        <v-list-item-content style="color: #2E0CF3">
                          {{ $t('dashboard.addNewView') }}
                        </v-list-item-content>
                      </v-list-item>
                    </div>
                  </template>
                  <span>
                    {{ $t("dashboard.noPermissionToDo", { action: $t("add").toLowerCase() }) }}
                  </span>
                </v-tooltip>
              </template>
              <v-card class="rounded-lg">
                <v-card-text class="py-2">
                  <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                    {{ $t('dashboard.viewName') }}
                  </v-label>
                  <v-text-field             
                    v-model="name"
                    :placeholder="$t('name')"
                    class="round-8 field-theme"
                    background-color="#F9F9FB"
                    dense
                    hide-details
                    height="38px"
                    required
                  />
                  <div>
                    <v-checkbox
                      id="remember-me-checkbox"
                      v-model="isDefault"
                      class="field-theme"
                      :ripple="false"
                      hide-details
                      off-icon="icon-checkbox-off"
                      on-icon="icon-checkbox-on"
                    >
                      <template #label>
                        <span class="fs-14px text-theme-label">{{ $t('dashboard.setDefault') }}</span>
                      </template>
                    </v-checkbox>
                  </div>
                </v-card-text>
                <v-card-actions>
                  <v-btn
                    class="rounded-lg text-none flex-grow-1"
                    height="40"
                    min-width="40"
                    elevation="0"
                    :disabled="loading"
                    @click="dashboardMenu = false"
                  >
                    <span class="mr-1">{{ $t('cancel') }}</span>
                  </v-btn>
                  <v-btn
                    class="text-capitalize rounded-lg flex-grow-1"
                    height="40"
                    color="primary"
                    depressed
                    :loading="loading"
                    @click="createDashboard"
                  >
                    Save
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-menu>
          </v-list>
        </v-menu>
        <v-skeleton-loader
          v-else-if="skeletonLoaderState && isOrgProjectView"
          class="rounded-lg mr-3"
          width="278"
          height="40"
          type="button"
        />
        <div
          v-if="!isOrgProjectView"
          class="dashboard__header-tags"
        >
          <v-autocomplete
            v-if="!skeletonLoaderState"
            v-model="value.selectedTags"
            :disabled="disableInputs"
            :items="tags"
            item-text="name"
            item-value="uid"
            multiple
            :placeholder="$t('chooseTags')"
            small-chips
            deletable-chips
            dense
            return-object
            background-color="#F9F9FB"
            class="rounded-lg field-theme custom-prepend mh-38px"
            hide-details
            no-filter
            @change="$emit('tagsUpdated', value.selectedTags)"
          >
            <template #item="{ item, on, attrs }">
              <v-list-item
                v-bind="attrs"
                v-on="on"
              >
                <v-list-item-action class="mr-2">
                  <v-checkbox
                    v-model="value.selectedTags"
                    :disabled="disableInputs" 
                    :value="item" 
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on" 
                    @click.stop
                  />
                </v-list-item-action>
                <v-list-item-content>
                  <v-list-item-title>{{ item.name }}</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </template>
          </v-autocomplete>
          <v-skeleton-loader 
            v-else
            class="rounded-lg mr-3"
            width="380"
            height="40"
            type="button"
          />
        </div>
        <v-btn
          v-if="!skeletonLoaderState"
          class="text-capitalize rounded-lg"
          height="40"
          color="primary"
          depressed
          :disabled="!currentDashboard?.editable && isOrgProjectView"
          @click="$emit('customize')"
        >
          {{ $t('customize') }}
        </v-btn>
        <v-skeleton-loader
          v-else
          class="rounded-lg primary"
          height="40"
          width="150"
          type="button"
        />
      </div>
    </div>
    <div class="d-flex align-center justify-space-between">
      <div class="dashboard__header-filters d-flex">
        <v-menu
          v-if="!skeletonLoaderState"
          left
          :nudge-bottom="4"
          :disabled="disableInputs"
          offset-y
        >
          <template #activator="{ on }">
            <v-btn
              class="rounded-lg text-none"
              height="40"
              elevation="0"
              color="#F9F9FB"
              v-on="on"
            >
              <span class="mr-1">{{ periodText ?? $t('period') }}</span>
              <v-icon>
                mdi-chevron-down
              </v-icon>
            </v-btn>
          </template>
          <v-list class="d-flex flex-column justify-space-between rounded-lg">
            <v-list-item
              v-for="(item, index) in periods"
              :key="index"
              class="custom-menu-item"
              :disabled="item.disabled"
              @click="setPeriod(item.text, item.value, item.key)"
            >
              <div class="custom-color-0c111d font-inter custom-text-14">
                {{ item.text }}
              </div>  
            </v-list-item>
            <v-menu
              ref="menu" 
              v-model="menu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-x
              min-width="290px"
            >
              <template #activator="{ on: menu }">
                <v-list-item v-on="{ ...menu}">
                  <div>{{ $t('custom') }}</div>
                </v-list-item>
              </template>
              <v-date-picker 
                v-model="periodValue"
                range
                no-title
                @change="setPeriod('Custom',periodValue, periodValue)"
              />
            </v-menu>
          </v-list>
        </v-menu>
        <v-skeleton-loader
          v-else
          height="36"
          width="150"
          type="button"
        />
        <template v-if="!isProjectScope">
          <v-autocomplete
            v-if="!skeletonLoaderState"
            v-model="value.selectedProjects"
            :disabled="disableInputs"
            type="text"
            dense
            single-line
            filled
            :placeholder="$t('allProjects')"
            hide-details
            small-chips
            deletable-chips
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            multiple
            :items="sortedProjects"
            return-object
            item-text="name"
            class="rounded-lg"
            clear-icon="body-2"
            @change="$emit('projectsUpdated', value.selectedProjects)"
          />
          <v-skeleton-loader
            v-else
            height="36"
            width="150"
            type="button"
          />
        </template>
        <template v-if="showMilestones">
          <v-autocomplete
            v-if="!skeletonLoaderState"
            v-model="value.selectedMilestones"
            :disabled="disableInputs"
            type="text"
            dense
            single-line
            filled
            :placeholder="$t('allMilestones')"
            hide-details
            small-chips
            deletable-chips
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            multiple
            :items="milestones"
            return-object
            item-text="name"
            class="rounded-lg"
            clear-icon="body-2"
            @update:search-input="$emit('update:searchInput', 'milestones', $event)"
            @blur="$emit('update:searchInput', 'testPlans', '')"
            @change="$emit('milestonesUpdated', value.selectedMilestones)"
          />
          <v-skeleton-loader
            v-else
            height="36"
            width="150"
            type="button"
          />
        </template>
        <template
          v-if="showPlans"
        >
          <v-autocomplete
            v-if="!skeletonLoaderState"
            v-model="value.selectedPlans"
            :disabled="disableInputs"
            type="text"
            dense
            single-line
            filled
            :placeholder="$t('allPlans')"
            hide-details
            small-chips
            deletable-chips
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            multiple
            :items="testPlans"
            return-object
            item-text="name"
            class="rounded-lg"
            clear-icon="body-2"
            @update:search-input="$emit('update:searchInput', 'testPlans', $event)"
            @blur="$emit('update:searchInput', 'testPlans', '')"
            @change="$emit('plansUpdated', value.selectedPlans)"
          />
          <v-skeleton-loader
            v-else
            height="36"
            width="150"
            type="button"
          />
        </template>
      </div>
      <div
        v-if="isOrgProjectView"
        class="dashboard__header-tags"
      >
        <v-autocomplete
          v-if="!skeletonLoaderState"
          v-model="value.selectedTags"
          :disabled="disableInputs"
          :items="tags"
          item-text="name"
          item-value="uid"
          multiple
          :placeholder="$t('chooseTags')"
          small-chips
          deletable-chips
          dense
          return-object
          background-color="#F9F9FB"
          class="rounded-lg field-theme custom-prepend mh-38px"
          hide-details
          no-filter
          @change="$emit('tagsUpdated', value.selectedTags)"
          @update:search-input="$emit('update:searchInput', 'tags', $event)"
        >
          <template #item="{ item, on, attrs }">
            <v-list-item
              v-bind="attrs"
              v-on="on"
            >
              <v-list-item-action class="mr-2">
                <v-checkbox
                  v-model="value.selectedTags" 
                  :value="item" 
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on" 
                  @click.stop
                />
              </v-list-item-action>
              <v-list-item-content>
                <v-list-item-title>{{ item.name }}</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </template>
        </v-autocomplete>
        <v-skeleton-loader 
          v-else
          class="rounded-lg mr-3"
          width="380"
          height="40"
          type="button"
        />
      </div>
    </div>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import { createNamespacedHelpers } from 'vuex';
const { mapActions, mapGetters } = createNamespacedHelpers('user')
import SearchIcon from '@/assets/svg/search-icon.svg';
import loader from '@/mixins/loader';
import LockIcon from '@/assets/svg/lock.svg';
import { showErrorToast, showSuccessToast } from '@/utils/toast';
import UnLockIcon from '@/assets/svg/unlock.svg';
import EditIcon from '@/assets/svg/editIcon.svg';
import DeleteIcon from '@/assets/svg/delete.svg';
import DeleteDashboardDialog from '@/components/Dashboard/DeleteDashboardDialog'
import EditDashboardDialog from '@/components/Dashboard/EditDashboardDialog'
const currentDate = dayjs(new Date).format('YYYY-MM-DD')
export default{
  components:{
    DeleteDashboardDialog,
    EditDashboardDialog,
    SearchIcon,
    EditIcon,
    UnLockIcon,
    LockIcon,
    DeleteIcon,
  },
  mixins: [loader],
  data(){
    return{
      periodText: this.$t('period'),
      periodValue: [],
      isDefault: false,
      periods:[
        {
          key: 'last7Days',
          text: this.$t('dashboard.last7Days'),
          value: [ this.getPeriodDate('7', 'day') ,currentDate]
        },
        {
          key: 'last14Days',
          text: this.$t('dashboard.last14Days'),
          value: [ this.getPeriodDate(14, 'day') ,currentDate]
        },
        {
          key: 'lastMonth',
          text: this.$t('dashboard.lastMonth'),
          value: [ this.getPeriodDate(1, 'month'), currentDate ]
        }
      ],
      searchDashboard: '',
      dashboardMenu: false,
      name: null,
      menu: false,
      rangePeriod: null,
      selectedMilestones: [],
      selectedPlans: [],
      selectedProjects: [],
      selectedTags: [],
      loading: false,
      showDeleteDialog: false,
      showEditDialog: false,
    }
  },
  computed:{
    ...mapGetters(['period', 'dashboardUid']),
    sortedProjects() {
      return [...this.projects].sort((a, b) => a.name.localeCompare(b.name));
    },
    selectedProject(){
      if(!this.$route.params.key)
        return this.$t('dashboard.name');
      if(this.projects.length){
        return this.projects.find(element => element.key == this.$route.params.key).name
      }
      return this.$t('loading')
    },
    _writeDashboard(){
      return this.authorityTo('write_dashboard')
    },
    _deleteDashboard(){
      return this.authorityTo('delete_dashboard')
    },
    isProjectScope(){
      return !!this.$route.params.key;
    },
    currentPeriod(){
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;
      return this.period({handle, projectKey, view: this.view})
    },
    selectedDashboard:{
      get(){
        if(this.filterDashboards.length && this.currentDashboard?.uid)
          return this.dashboards.findIndex(element => element?.uid == this.currentDashboard?.uid);
        return 0
      },
      set(index){
        if((this.filterDashboards[index].uid === this.currentDashboard.uid))
          return showErrorToast(this.$swal, this.$t('dashboard.dashboardSelected'))
          
        if(Number.isInteger(index))
          this.$emit('selectedDashboard', this.filterDashboards[index].uid)
      }
    },
    filterDashboards(){
      if(this.searchDashboard && this.searchDashboard.length){
        const dashboards = this.dashboards.filter(element => element.name.toLowerCase().indexOf(this.searchDashboard.toLowerCase()) >= 0);
        return this.moveDashboardToTop(dashboards)
      }
      return this.moveDashboardToTop(this.dashboards);
    }
  },
  props:{
    milestones: {
      type: Array,
      default: () => {
        return []
      }
    },
    dashboards:{
      type: Array,
      default: () =>{
        return []
      }
    },
    testPlans: {
      type: Array,
      default: () => {
        return []
      }
    },
    projects: {
      type: Array,
      default: () => {
        return []
      }
    },
    periodKey: {
      type: [String, Array]
    },
    value: {
      type: Object
    },
    currentDashboard: {
      type: Object
    },
    tags: {
      type: Array,
      default: () => {
        return []
      }
    },
    isOrgProjectView:{
      default: true,
      type: Boolean
    },
    showPlans: {
      default: true,
      type: Boolean
    },
    showMilestones: {
      detault: true,
      type: Boolean
    },
    view: {
      required: false,
      type: String
    },
    disableInputs: {
      type: Boolean,
      default: false
    }
  },
  methods:{
    ...mapActions(['storePeriod']),
    getPeriodDate(value, unit){
      return dayjs(currentDate).subtract(value, unit).format('YYYY-MM-DD') 
    },
    setEditable(){
      const payload = {
        name: this.currentDashboard?.name,
        editable: !this.currentDashboard?.editable,
        isDefault: this.currentDashboard?.systemDefault
      }
      this.$emit('editDashboard', this.currentDashboard?.uid, payload)
    },
    confirmDelete(){
      this.showDeleteDialog = true;
    },
    deleteDashboard(){
      this.showDeleteDialog = false;
      this.$emit('deleteDashboard', this.currentDashboard?.uid);
    },
    confirmEdit(){
      this.showEditDialog = true;
    },
    editDashboard(payload){
      this.showEditDialog = false;
      this.$emit('editDashboard', this.currentDashboard?.uid, payload)
    },
    formatValidDates(days){
      if(days.length == 2){
        const dt1 = new Date(days[0]).getTime();
        const dt2 = new Date(days[1]).getTime();
        if(dt2 < dt1)
          return [days[1], days[0]]
      }
      return days
    },
    async copyLink(){
      await navigator.clipboard.writeText(window.location.href);
      showSuccessToast(this.$swal, this.$t('linkCopied'));
    },
    setDefault(){
      if(this.currentDashboard?.systemDefault)
        return showErrorToast(this.$swal, this.$t('dashboard.alreadyDefault'))
        
      const payload = {
        name: this.currentDashboard?.name,
        editable: this.currentDashboard?.editable,
        isDefault: true
      }
      this.$emit('editDashboard', this.currentDashboard?.uid, payload)
    },
    setPeriod(text, value, periodKey){
      if(text && value?.length == 2){
        const validDates = this.formatValidDates(value);
        if(Array.isArray(periodKey)){
          this.periodValue = validDates;
          periodKey = validDates;
        }
        if(!this.currentPeriod || text !== this.currentPeriod?.text || this.currentPeriod?.value[0] !== validDates[0] || this.currentPeriod?.value[1] !== validDates[1]){
          this.storePeriod({
            text: text,
            value: validDates,
            handle: this.$route.params.handle,
            projectKey: this.$route.params.key,
            view: this.view
          })

          this.$emit('dateUpdated', this.periodValue, periodKey)
        }

        this.periodText = text;
        this.periodValue = validDates;
        this.menu = false;
      }
    },
    moveDashboardToTop(list){
      // Move the current dashboard to the top of the list
      if(Array.isArray(list) && list.length && this.$route.params.id){
        const currentIndex = list.findIndex(element => element.uid == this.$route.params.id);
        const itemToMove = list.splice(currentIndex,1)[0];
        list.unshift(itemToMove);
      }
      return list
    },
    createDashboard(){
      if(!this.name || (this.name && this.name.length < 1))
        return showErrorToast(this.$swal, this.$t('dashboard.nameRequired'))
      this.$emit('createDashboard', { name: this.name, isDefault: this.isDefault })
      this.loading = true;
    }
  },
  created(){
    if(this.periodKey){
      if(Array.isArray(this.periodKey)){
        const periodExists = this.periods.find(element => element.value[0] == this.periodKey[0] && element.value[1] == this.periodKey[1]);
        const periodText = periodExists?.text ?? this.$t('custom'); 
        this.setPeriod(periodText, this.periodKey, this.periodKey)
      }else{
        const period = this.periods.find(element => element.key == this.periodKey)
        this.setPeriod(period.text, period.value, period.key)
      }
    }
  }
}
</script>