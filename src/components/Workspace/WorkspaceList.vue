<template>
  <div>
    <v-container
      class="align-start card pa-6"
      fluid
    >
      <v-row>
        <v-col
          cols="6"
          sm="6"
        >
          <div class="d-flex flex-row justify-start align-center">
            <v-responsive
              v-if="!skeletonLoaderState"
              class="ma-0"
              max-width="344"
            >
              <v-text-field
                v-model="localSearchTerm"
                :placeholder="$t('placeHolder.searchByNameId')"
                background-color="#F9F9FB"
                class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0 flex-inherit"
                height="38"
                clearable
                clear-icon="mdi-close-circle"
                hide-details
              >
                <template #prepend-inner>
                  <SearchIcon />
                </template>
              </v-text-field>
            </v-responsive>
            <v-skeleton-loader
              v-else
              class="rounded-lg mr-3"
              width="235"
              height="40"
              type="button"
            />
            <WorkspaceFilter
              :projects="projects"
              :tags="tags"
              :filter-view="filterView"
              @applyFilters="applyFilters"
            />
          </div>
        </v-col>
        <v-col
          cols="6"
          sm="6"
          class="d-flex justify-end"
        >
          <SettingsMenu table-type="workspace" />
        </v-col>
      </v-row>
      <v-row>
        <v-col
          v-if="!caseItems.length && !skeletonLoaderState && !tableLoading"
          cols="12"
          sm="12"
        >
          <div class="white">
            <ActiveEmptyState
              :image-src="require('@/assets/png/empty-workspace.png')"
              image-max-width="322px"
            >
              <template #description>
                <h2 class="text-center">
                  {{ $t('noCasesYet') }}
                  <span class="d-block">{{ $t('itsVibeHere') }}</span>
                </h2>
                <p class="text-center">
                  {{ $t('waitingforcases') }}
                </p>
              </template>
            </ActiveEmptyState>
          </div>
        </v-col>
        <v-col
          v-else
          cols="12"
          sm="12"
        >
          <v-data-table
            v-if="!skeletonLoaderState"
            v-model="selectedRows"
            :headers="filteredHeaders"
            :server-items-length="totalExecutions"
            :items="caseItems"
            item-key="uid"
            show-select
            class="data-table-style table-fixed"
            v-resize-columns="{ type: 'workspace' }"
            :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
            :item-class="getItemClass"
            hide-default-footer
            disable-pagination
            @click:row="handleClick"
          >
            <template #[`header.data-table-select`]="{ props, on }">
              <div class="d-flex justify-start align-center">
                <v-checkbox
                  id="remember-me-checkbox"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  indeterminate-icon="icon-indeterminate"
                  :input-value="props.value"
                  :indeterminate="props.indeterminate"
                  @change="on.input"
                />
              </div>
            </template>

            <template #[`item.data-table-select`]="{ isSelected, select }">
              <div class="d-flex justify-start align-center">
                <v-checkbox
                  id="remember-me-checkbox"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :input-value="isSelected"
                  @change="select"
                  @click.stop
                />
              </div>
            </template>

            <template #[`header.actions`]="{ header }">
              <div class="d-none">
                {{ header.text }}
              </div>
            </template>
            <template #[`item.uid`]="{ item }">
              <div class="custom-attribute text-uppercase font-weight-regular text-truncate">
                {{ item.project?.key }}-{{ item.uid }}
              </div>
            </template>
            <template #[`item.priority`]="{ item }">
              <v-select
                v-model="item.priority"
                type="text"
                dense
                :placeholder="$t('choosePriority')"
                :items="priorities"
                class="pt-2"
                append-icon="mdi-chevron-down"
                item-text="name"
                item-value="id"
                :full-width="true"
                return-object
                @click.stop=""
                @change="$emit('updateExecution', {property: 'priority', value: item.priority.id, projectKey: item.project.key, selectedExecution: item.uid})"
              >
                <template #item="{ item }">
                  <span
                    class="fw-semibold"
                    :style="{ color: getPriorityColor(item.id, priorities) }"
                  >{{ item.name }}</span>
                </template>
                <template #selection="{ item }">
                  <span
                    class="fw-semibold"
                    :style="{ color: getPriorityColor(item.id, priorities) }"
                  >{{ item.name }}</span>
                </template>
                <template #no-data>
                  <v-icon>mdi-minus</v-icon>
                </template>
              </v-select>
            </template>
            <template #[`item.status`]="{ item }">
              <v-select
                v-model="item.status"
                type="text"
                dense
                :placeholder="$t('chooseStatus')"
                :items="statuses"
                class="pt-2"
                append-icon="mdi-chevron-down"
                item-text="name"
                item-value="id"
                :full-width="true"
                return-object
                @click.stop=""
                @change="$emit('updateExecution', {property: 'status', value: item.status.id, projectKey: item.project.key, selectedExecution: item.uid})"
              >
                <template #item="{ item }">
                  <span
                    class="fw-semibold"
                    :style="{ color: getStatusColor(item.id, statuses) }"
                  >{{ item.name }}</span>
                </template>
                <template #selection="{ item }">
                  <span
                    class="fw-semibold"
                    :style="{ color: getStatusColor(item.id, statuses) }"
                  >{{ item.name }}</span>
                </template>
                <template #no-data>
                  <v-icon>mdi-minus</v-icon>
                </template>
              </v-select>
            </template>
            <template #[`item.assignedTo`]="{ item }">
              <v-select
                v-model="item.assignedTo"
                type="text"
                dense
                :items="workspaceUsers"
                :placeholder="item.customFields?.assignee?.name || '---'"
                class="pt-2"
                append-icon="mdi-chevron-down"
                item-text="firstName"
                item-value="uid"
                :full-width="true"
                return-object
                @click.stop=""
                @change="$emit('updateExecution', {property: 'assignedTo', value: item.assignedTo.uid, projectKey: item.project.key, selectedExecution: item.uid})"
              >
                <template #item="{ item }">
                  <span>{{ `${item.firstName} ${item.lastName}` }}</span>
                </template>
                <template #selection="{ item }">
                  <v-tooltip
                    bottom
                    left
                    max-width="485px"
                    :disabled="!isTruncated"
                    content-class="tooltip-theme"
                  >
                    <template #activator="{ on, attrs }">
                      <div
                        v-if="item?.firstName || item?.lastName"
                        :ref="'assignedTo_' + item.uid"
                        class="custom-attribute text-truncate font-weight-regular"
                        v-bind="attrs"
                        v-on="on"
                        @mouseover="checkTruncate(item.uid, 'assignedTo')"
                      >
                        {{ `${item.firstName} ${item.lastName}` }}
                      </div>
                      <v-icon v-else>
                        mdi-minus
                      </v-icon>
                    </template>
                    <span v-if="item.firstName || item.lastName">{{ `${item.firstName} ${item.lastName}` }}</span>
                    <v-icon v-else>
                      mdi-minus
                    </v-icon>
                  </v-tooltip>
                </template>
              </v-select>
            </template>
            <template #[`item.lastAssignedAt`]="{ item }">
              <span
                v-if="item?.lastAssignedAt"
                class="custom-attribute text-center font-weight-regular"
              >{{ dateFormater(item.lastAssignedAt) }}</span>
              <template v-else>
                <v-icon>mdi-minus</v-icon>
              </template>
            </template>
            <template #[`item.dueAt`]="{ item }">
              <v-menu
                :close-on-content-click="true"
                :nudge-right="40"
                transition="scale-transition"
                offset-y
                min-width="auto"
                @blur="date = dateFormater(item.dueAt)"
                @click.stop.prevent=""
              >
                <template #activator="{ on, attrs }">
                  <div
                    class="calendar-textbox-container"
                    style="cursor: pointer;"
                    v-on="on"
                  >
                    <v-text-field
                      v-if="item.dueAt"
                      :value="dateFormater(item.dueAt)"
                      hint="MM/DD/YYYY"
                      readonly
                      v-bind="attrs"
                    >
                      <template #append>
                        <v-icon style="cursor: pointer">
                          mdi-chevron-down
                        </v-icon>
                      </template>
                    </v-text-field>
                    <div
                      v-else
                      class="d-flex justify-space-between"
                    >
                      <v-icon class="pointer-cursor">
                        mdi-minus
                      </v-icon>
                      <v-icon class="pointer-cursor">
                        mdi-chevron-down
                      </v-icon>
                    </div>
                  </div>
                </template>
                <v-date-picker
                  v-model="item.dueAt"
                  @input="item.dateSelectorMenu = false"
                  @change="$emit('updateExecution', {property: 'dueAt', value: item.dueAt, projectKey: item.project.key, selectedExecution: item.uid})"
                />
              </v-menu>
            </template>
          
            <template #[`item.name`]="{ item }">
              <v-tooltip
                v-if="item?.name && item?.name?.length"
                bottom
                left
                max-width="485px"
                :disabled="!isTruncated"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <div
                    :ref="'itemName_' + item.uid"
                    class="custom-attribute text-truncate fw-semibold"
                    v-bind="attrs"
                    v-on="on"
                    @mouseover="checkTruncate(item.uid, 'itemName')"
                  >
                    {{ item?.name }}
                  </div>
                </template>
                <span>{{ item?.name }}</span>
              </v-tooltip>
              <template v-else>
                <v-icon>mdi-minus</v-icon>
              </template>
            </template>
            <template #[`item.project`]="{ item }">
              <v-tooltip
                v-if="item.project?.name"
                bottom
                left
                max-width="485px"
                :disabled="!isTruncated"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <div
                    :ref="'projectName_' + item.uid"
                    class="custom-attribute text-truncate font-weight-regular"
                    v-bind="attrs"
                    v-on="on"
                    @mouseover="checkTruncate(item.uid, 'projectName')"
                  >
                    {{ item.project?.name }}
                  </div>
                </template>
                <span>{{ item.project?.name }}</span>
              </v-tooltip>
              <template v-else>
                <v-icon>mdi-minus</v-icon>
              </template>
            </template>

            <template #[`item.milestone`]="{ item }">
              <div v-if="relationsLoading">
                <v-skeleton-loader
                  type="text"
                  height="16"
                  class="w-100"
                />
              </div>
              <v-tooltip
                v-else-if="Array.isArray(item?.milestone) && item.milestone.length > 0"
                bottom
                left
                max-width="485px"
                :disabled="item.milestone.length < 1"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <div
                    class="custom-attribute font-weight-regular text-truncate"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ item.milestone[0]?.name }} <span v-if="item.milestone.length > 1">+{{ item.milestone.length - 1 }}</span>
                  </div>
                </template>
                <span>{{ item.milestone.flat().map(milestone => milestone.name).join(', ') }}</span>
              </v-tooltip>
              <template v-else>
                <v-icon>mdi-minus</v-icon>
              </template>
            </template>

            <template #[`item.testplan`]="{ item }">
              <div v-if="relationsLoading">
                <v-skeleton-loader
                  type="text"
                  height="16"
                  class="w-100"
                />
              </div>
              <v-tooltip
                v-else-if="Array.isArray(item?.testPlan) && item.testPlan.length > 0 && !relationsLoading"
                bottom
                left
                max-width="485px"
                :disabled="item.testPlan.length <= 1"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <div 
                    class="custom-attribute  font-weight-regular"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ item.testPlan[0]?.name }} <span v-if="item.testPlan.length > 1">+{{ item.testPlan.length - 1 }}</span>
                  </div>
                </template>
                <span>{{ item.testPlan.flat().map(plan => plan.name).join(', ') }}</span>
              </v-tooltip>
              <template v-else>
                <v-icon>mdi-minus</v-icon>
              </template>
            </template>

            <template #[`item.testrun`]="{ item }">
              <div v-if="relationsLoading">
                <v-skeleton-loader
                  type="text"
                  height="16"
                  class="w-100"
                />
              </div>
              <v-tooltip
                v-else-if="item.testRun?.name"
                bottom
                left
                max-width="485px"
                :disabled="!isTruncated"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <div
                    :ref="'testrunName_' + item.uid"
                    class="custom-attribute text-truncate font-weight-regular"
                    v-bind="attrs"
                    v-on="on"
                    @mouseover="checkTruncate(item.uid, 'testrunName')"
                  >
                    {{ item.testRun?.name ?? '-' }}
                  </div>
                </template>
                <span>{{ item.testRun?.name }}</span>
              </v-tooltip>
              <template v-else>
                <v-icon>mdi-minus</v-icon>
              </template>
            </template>

            <template #[`item.actions`]="{ item }">
              <div class="d-flex flex-row justify-center">
                <v-menu
                  left
                  offset-y
                  min-width="159px"
                >
                  <template #activator="{ on }">
                    <v-btn
                      icon
                      v-on="on"
                    >
                      <v-icon>mdi-dots-vertical</v-icon>
                    </v-btn>
                  </template>
                  <v-list
                    dense
                    class="text-left"
                  >
                    <!-- Hide Pinata for now -->
                    <v-list-item v-if="false">
                      <v-list-item-icon>
                        <pinata-icon />
                      </v-list-item-icon>
                      <v-list-item-title>{{ $t('runWithPinata') }}</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="$emit('selectedExecutions', [item])">
                      <v-icon
                        color="#344054"
                        class="mr-3"
                      >
                        mdi-plus
                      </v-icon>
                      <v-list-item-content>{{ $t('addResult') }}</v-list-item-content>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </template>

            <template #[`item.tags`]="{ item }">
              <div v-if="relationsLoading">
                <v-skeleton-loader
                  type="text"
                  height="16"
                  class="w-100"
                />
              </div>
              <v-tooltip
                v-else-if="Array.isArray(item.tags) && item.tags.length && !relationsLoading"
                bottom
                left
                max-width="485px"
                :disabled="item.tags.length <= 1"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-if="Array.isArray(item.tags) && item.tags.length"
                    class="custom-attribute font-weight-regular"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ item.tags[0].name }} <span v-if="item.tags.length > 1">+{{ item.tags.length - 1 }}</span>
                  </div>
                  <div v-else-if="typeof item.tags === 'string'">
                    {{ item.tags }}
                  </div>
                  <div v-else>
                    -
                  </div>
                </template>
                <span v-if="Array.isArray(item.tags) && item.tags.length">{{
                  item.tags.map((tags) => `${tags.name}`).join(', ')
                }}</span>
              </v-tooltip>
              <template v-else>
                <v-icon>mdi-minus</v-icon>
              </template>
            </template>
          </v-data-table>
          <template v-else>
            <WorkspaceTableSkeleton class="mt-6" />
          </template>
          <Pagination
            v-if="!skeletonLoaderState && totalExecutions > 0"
            :page="currentPage"
            :items-per-page="itemsPerPage"
            :total-pages="totalPages"
            :total-items="totalExecutions"
            @update:pagination="onUpdatePagination"
          />
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import WorkspaceFilter from '@/components/Workspace/WorkspaceFilter.vue';
import SearchIcon from '@/assets/svg/search-icon.svg';
import { createNamespacedHelpers } from 'vuex';
import pinataIcon from '@/assets/svg/pinata.svg';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import { mapGetters, mapActions } from 'vuex';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import handleLoading from '@/mixins/loader.js';
import WorkspaceTableSkeleton from '@/components/Skeletons/Workspace/WorkspaceTableSkeleton.vue';
import dayjs from 'dayjs';
import Pagination from '@/components/base/Pagination.vue';
const { mapState } = createNamespacedHelpers('user');


export default {
  components: {
    WorkspaceFilter,
    SearchIcon,
    SettingsMenu,
    pinataIcon,
    WorkspaceTableSkeleton,
    Pagination
  },
  mixins: [colorPreferencesMixin, handleLoading],
  props: {
    selectedExecutions: {
      type: Array,
      default: () => [],
    },
    selectedExecution: {
      type: Object,
      default: () => {},
    },
    caseItems: {
      type: Array,
      default: () => {},
    },
    allowAction: {
      type: Boolean,
      default: true,
    },
    execution: {
      type: Object,
      default: () => {},
    },
    totalExecutions: {
      type: Number,
      default: 0,
    },
    isDetailCollapsed: {
      type: Boolean,
      default: false,
    },
    projects: {
      type: Array,
      default: () => [],
    },
    tags: {
      type: Array,
      default: () => [],
    },
    filterView: {
      type: String,
      default: 'todo',
    },
    relationsLoading: {
      type: Boolean,
      default: false,
    },
    workspaceUsers: {
      type: Array, 
      default: () => []
    },
    currentPage: {
      type: Number,
      default: 1
    },
    itemsPerPage: {
      type: Number,
      default: 10
    },
    tableLoading: {
      type: Boolean,
      default: false
    },
    searchTerm: {
      type: String,
      default: ''
    }
  },
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },
  data() {
    return {
      headers: [],
      headersCompleted: [],
      selectedItems: [],
      statuses: [],
      priorities: [],
      isTruncated: false,
      dateSelectorMenu: false,
    };
  },

  computed: {
    ...mapState(['currentAccount']),
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders',
    }),
    selectedRows: {
      get() {
        return this.selectedExecutions;
      },
      set(value) {
        this.selectedItems = value;
        this.$emit('updateRows', this.selectedItems);
      },
    },
    localSearchTerm: {
      get() {
        return this.searchTerm;
      },
      set(value) {
        this.$emit('searchExecution', value);
      }
    },
    filteredHeaders() {
      const filtered = this.headers.filter((header) => header.checked);
      return filtered;
    },
    activeRow: {
      get() {
        return this.selectedExecution;
      },
      set(value) {
        this.$emit('update:selectedExecution', value);
      }
    },
    totalPages() {
      return Math.ceil(this.totalExecutions / this.itemsPerPage);
    },
  },
  created() {
    if (!this.dynamicHeaders.workspace) {
      this.initializeHeaders({ type: 'workspace' });
    }
    this.headers = this.dynamicHeaders.workspace;
    this.priorities = this.getPriorities('testCase');
    this.statuses = this.getStatuses('testExecution');
  },
  methods: {
    ...mapActions('headers', ['initializeHeaders']),
    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
    getColor(priority) {
      if (priority == 'High') {
        return 'text-red';
      } else if (priority == 'Medium') {
        return 'text-yellow';
      } else {
        return 'text-green';
      }
    },
    dateFormater(date) {
      return dayjs(date).format('MM/DD/YY');
    },


    applyFilters(filters) {
      this.$emit('applyExecutionFilter', filters);
    },
    handleClick(row) {
      this.$emit('expandDetail', row);
    },
    getItemClass(item) {
      return this.activeRow && item.uid === this.activeRow.uid ? 'active-row' : '';
    },
    updatePaginationOptions(options) {
      const itemsPerPage = options.itemsPerPage === -1 ? 10000 : options.itemsPerPage;
      this.$emit('update-pagination', { ...options, itemsPerPage });
    },
    onUpdatePagination(options) {
      this.$emit('update-pagination', options);
    },
  },
};
</script>
<style scoped>
.text-red {
  color: #ef5350;
}

.text-green {
  color: #66bb6a;
}

.text-yellow {
  color: #ffa726;
}

.v-text-field__append-inner .v-icon {
  cursor: pointer;
}
</style>
