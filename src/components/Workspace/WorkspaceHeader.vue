<template>
  <v-card
    class="white py-6 px-6 mt-3"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div class="d-flex align-center justify-space-between">
      <h2
        v-if="!skeletonLoaderState"
        class="text-theme-base"
      >
        {{ $t('myWorkspace') }}
      </h2>
      <v-skeleton-loader
        v-else
        class="rounded-lg mr-3"
        height="24"
        width="150"
        type="text"
      />
    </div>
    <div
      v-if="!skeletonLoaderState"
      class="mt-4 d-flex justify-start"
    >
      <div class="filter-chips">
        <v-chip 
          :class="{ 'blue--text': filter === 'todo', 
                    'text-theme-secondary': filter !== 'todo'
          }" 
          class="mr-2"
          width="200px" 
          :color="filter === 'todo' ? '#e6ecff' : '#f9fafb'"
          label 
          @click="$emit('updateView', 'todo')"
        >
          <div
            class="px-2"
            :class="[filter === 'todo' ? 'fw-semibold' : 'font-weight-medium']"
          >
            {{ $t('todo') }} <span class="ml-2">{{ todoCount }}</span>
          </div>
        </v-chip>
        <v-chip 
          :class="{ 'blue--text': filter === 'completed', 
                    'text-theme-secondary': filter !== 'completed'
          }" 
          width="200px" 
          :color="filter === 'completed' ? '#e6ecff' : '#f9fafb'"
          label 
          @click="$emit('updateView', 'completed')"
        >
          <div
            class="px-2"
            :class="[filter === 'completed' ? 'fw-semibold' : 'font-weight-medium']"
          >
            {{ $t('completed') }} <span class="ml-2">{{ completedCount }}</span>
          </div>
        </v-chip>
      </div>
    </div>
    <div
      v-else
      class="mt-4 d-flex"
    >
      <v-skeleton-loader
        class="rounded-sm d-flex gap-2 chip-primary"
        height="32"
        width="200"
        type="button@2"
      />
    </div>
    <div class="__workspace_filters">
      <v-row class="mt-2">
        <v-col> 
          <v-autocomplete
            v-if="!skeletonLoaderState"
            v-model="selectedUsers"
            type="text"
            dense
            single-line
            filled
            :placeholder="$t('assignedTo')"
            hide-details
            small-chips
            multiple
            :items="filterData?.users"
            deletable-chips
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            item-value="uid"
            item-text="name"
            class="rounded-lg mh-32"
            clear-icon="body-2"
            @change="onUsersChange"
          >
            <template #selection="{ item ,index }">
              <div 
                class="d-flex align-center"
              >
                <span  
                  v-if="index === 0" 
                  class="font-weight-medium"
                >{{ $t('assignedTo') }}:   </span>
                <span 
                  v-if="user?.uid == item.uid" 
                  class="ml-2 text-theme-label"
                > {{ $t('me') }}</span>
                <span 
                  v-else 
                  class="text-theme-label text-truncate ml-1"
                >{{ item.firstName }} {{ item.lastName }}</span>
                <v-icon 
                  size="16px"
                  class="ml-1" 
                  @click="onRemoveSelectedUsers(item.uid)"
                >
                  mdi-close
                </v-icon>
              </div>
            </template>
            <template #item="{ item, on }">
              <v-list-item
                :ripple="false"
                v-on="on"
              >
                <v-list-item-action>
                  <v-checkbox
                    hide-details
                    :input-value="userSelection(item.uid)"
                    class="field-theme mt-0 pt-0"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                  >
                    <template #label>
                      <span 
                        v-if="user.uid == item.uid" 
                        class="ml-2 text-theme-label"
                      > {{ $t('me') }}</span>
                      <span 
                        v-else 
                        class="fs-14px text-theme-label"
                      >{{ `${item.firstName} ${item.lastName}` }}</span>
                    </template>
                  </v-checkbox>
                </v-list-item-action>
              </v-list-item>
            </template>
          </v-autocomplete> 
          <v-skeleton-loader
            v-else
            height="46"
            width="100%"
            type="button"
          />
        </v-col>
        <v-col>  
          <v-autocomplete
            v-if="!skeletonLoaderState"
            v-model="selectedProjects"
            type="text"
            dense
            single-line
            filled
            :placeholder="$t('allProjects')"
            hide-details
            small-chips
            multiple
            :items="filterData?.projects"
            deletable-chips
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            item-value="uid"
            item-text="name"
            class="rounded-lg mh-32"
            clear-icon="body-2"
            @change="onProjectsChange"
          >
            <template #selection="{ item }">
              <div
                class="d-flex align-center custom-chip-theme mr-1 mb-1"
              >
                <div class="text-theme-label label text-truncate mr-1">
                  {{ item.name }}
                </div>
                <v-icon
                  v-if="selectedProjects.length > 1"
                  size="16px"
                  @click="onRemoveSelectedProjects(item.uid)"
                >
                  mdi-close
                </v-icon>
              </div>
            </template>
            <template #item="{ item, on: listItemOn }">
              <v-list-item
                :ripple="false"
                v-on="(selectedProjects.length === 1 && projectSelection(item.uid) || (selectedProjects.length >= 5 && !projectSelection(item.uid))) ? {} : listItemOn"
                @click="onProjectSelected(item.uid)"
              >
                <v-list-item-action>
                  <v-tooltip
                    bottom
                    :disabled="!(
                      (selectedProjects.length === 1 && projectSelection(item.uid)) ||
                      (selectedProjects.length >= 5 && !projectSelection(item.uid))
                    )"
                  >
                    <template #activator="{ on: tooltipOn, attrs: tooltipAttrs }">
                      <div
                        v-bind="tooltipAttrs"
                        v-on="tooltipOn"
                      >
                        <v-checkbox
                          hide-details
                          :input-value="projectSelection(item.uid)"
                          class="field-theme mt-0 pt-0"
                          :ripple="false"
                          off-icon="icon-checkbox-off"
                          on-icon="icon-checkbox-on"
                          :class="{ 'disabled-checkbox-style': ( selectedProjects.length === 1 && projectSelection(item.uid) ) || (selectedProjects.length == 5 && !projectSelection(item.uid)) }"
                        >
                          <template #label>
                            <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                          </template>
                        </v-checkbox>
                      </div>
                    </template>
                    <span>{{ selectedProjects.length > 1 ? $t('maxProjectsSelected') : $t('atLeastOneProjectRequired') }}</span>
                  </v-tooltip>
                </v-list-item-action>
              </v-list-item>
            </template>
          </v-autocomplete> 
          <v-skeleton-loader
            v-else
            height="46"
            width="100%"
            type="button"
          />
        </v-col>
        <v-col> 
          <v-autocomplete
            v-if="!skeletonLoaderState"
            v-model="selectedMilestones"
            type="text"
            dense
            single-line
            filled
            :placeholder="$t('allMilestones')"
            hide-details
            small-chips
            deletable-chips
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            multiple
            :items="filterData?.milestones"
            item-value="uid"
            item-text="name"
            class="rounded-lg mh-32"
            clear-icon="body-2"
            @update:search-input="$emit('update:searchMilestones', {
              query: $event,
            })"
            @blur="$emit('update:searchMilestones', {
              query: '',
            })"
            @change="onMilestonesChange"
          >
            <template #selection="{ item }">
              <div
                class="d-flex align-center custom-chip-theme mr-1 mb-1"
              >
                <div class="text-theme-label label text-truncate mr-1">
                  {{ item.name }}
                </div>
                <v-icon
                  size="16px"
                  @click="onRemoveSelectedMilestones(item.uid)"
                >
                  mdi-close
                </v-icon>
              </div>
            </template>
            <template #item="{ item, on }">
              <v-list-item
                :ripple="false"
                v-on="on"
              >
                <v-list-item-action>
                  <v-checkbox
                    hide-details
                    :input-value="milestoneSelection(item.uid)"
                    class="field-theme mt-0 pt-0"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                    </template>
                  </v-checkbox>
                </v-list-item-action>
              </v-list-item>
            </template>
          </v-autocomplete>  
          <v-skeleton-loader
            v-else
            height="46"
            width="100%"
            type="button"
          />
        </v-col>
        <v-col>  
          <v-autocomplete
            v-if="!skeletonLoaderState"
            v-model="selectedPlans"
            type="text"
            dense
            single-line
            filled
            :placeholder="$t('allPlans')"
            hide-details
            deletable-chips
            small-chips
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            multiple
            :items="filterData?.testPlans"
            item-value="uid"
            item-text="name"
            class="rounded-lg mh-32"
            clear-icon="body-2"
            @update:search-input="$emit('update:searchPlans', {
              query: $event,
            })"
            @blur="$emit('update:searchPlans', {
              query: '',
            })"
            @change="onPlansChange"
          >
            <template #selection="{ item }">
              <div
                class="d-flex align-center custom-chip-theme mr-1 mb-1"
              >
                <div class="text-theme-label label text-truncate mr-1">
                  {{ item.name }}
                </div>
                <v-icon
                  size="16px"
                  @click="onRemoveSelectedPlans(item.uid)"
                >
                  mdi-close
                </v-icon>
              </div>
            </template>
            <template #item="{ item, on }">
              <v-list-item
                :ripple="false"
                v-on="on"
              >
                <v-list-item-action>
                  <v-checkbox
                    hide-details
                    :input-value="planSelection(item.uid)"
                    class="field-theme mt-0 pt-0"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                    </template>
                  </v-checkbox>
                </v-list-item-action>
              </v-list-item>
            </template>
          </v-autocomplete> 
          <v-skeleton-loader
            v-else
            height="46"
            width="100%"
            type="button"
          />
        </v-col>
        <v-col>   
          <v-autocomplete
            v-if="!skeletonLoaderState"
            v-model="selectedRuns"
            type="text"
            dense
            single-line
            filled
            deletable-chips
            :placeholder="$t('allRuns')"
            hide-details
            small-chips
            multiple
            :items="filterData?.testRuns"
            item-value="uid"
            item-text="name"
            class="rounded-lg mh-32"
            clear-icon="body-2"
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            @update:search-input="$emit('update:searchRuns', {
              query: $event,
            })"
            @blur="$emit('update:searchRuns', {
              query: '',
            })"
            @change="onRunsChange"
          >
            <template #selection="{ item }">
              <div
                class="d-flex align-center custom-chip-theme mr-1 mb-1"
              >
                <div class="text-theme-label label text-truncate mr-1">
                  {{ item.name }}
                </div>
                <v-icon
                  size="16px"
                  @click="onRemoveSelectedRuns(item.uid)"
                >
                  mdi-close
                </v-icon>
              </div>
            </template>
            <template #item="{ item, on }">
              <v-list-item
                :ripple="false"
                v-on="on"
              >
                <v-list-item-action>
                  <v-checkbox
                    hide-details
                    :input-value="runSelection(item.uid)"
                    class="field-theme mt-0 pt-0"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                    </template>
                  </v-checkbox>
                </v-list-item-action>
              </v-list-item>
            </template>
          </v-autocomplete>
          <v-skeleton-loader
            v-else
            height="46"
            width="100%"
            type="button"
          />
        </v-col>
      </v-row>
      <!-- <v-row>
        <v-col class="d-flex">     
          <v-btn
            color="blue"
            class="white--text mr-2"
            dense
            @click="updateFilter"
          >
            <v-icon>mdi-magnify</v-icon>
            <span class="mx-2">{{ $t('search') }}</span>
          </v-btn>
          <v-btn
            dense
            @click="resetFilters"
          >
            <span class="mx-2">{{ $t('reset') }}</span>
          </v-btn>
        </v-col>
      </v-row> -->
    </div>
  </v-card>
</template>

<script>
import {mapState , mapActions } from 'vuex';
import handleLoading from '@/mixins/loader.js'
import { showErrorToast } from '@/utils/toast';
export default {
  mixins: [handleLoading],
  props: {
   filter: {
     type: String,
     default: '',
   },
   todoCount: {
     type: Number,
     default: 0,
   },
   completedCount: {
     type: Number,
     default: 0, 
   },
   filterData: {
     type: Object,
     default: () => ({}),
   },
   defaultProjects: {
     type: Array,
     default: () => [],
   },
  },
  data(){
    return{
      selectedProjects: [...this.defaultProjects],
      selectedMilestones: [],
      selectedRuns: [],
      selectedPlans: [],
      selectedUsers: []
    }
  },
  computed:{
   ...mapState('user', ['user']),
  ...mapState('workspace', ['workspaceFilter']),
    projects(){
      return this.filterData?.projects || [];
    },
    users(){
      return this.filterData?.users || [];
    },
    milestones(){
      return this.filterData?.milestones?.filter(element => this.selectedProjects.includes(element.projectUid));
    },
    testPlans() {
      return this.filterData?.testPlans?.filter(testPlan => {
      const hasMatchingMilestone = this.selectedMilestones.length > 0 && testPlan.milestoneUids.some((item) => this.selectedMilestones.includes(item.uid));
      const hasMatchingProject = this.selectedProjects.length > 0 && this.selectedProjects.includes(testPlan.projectUid);  
      if (hasMatchingMilestone && hasMatchingProject) {
         return testPlan
      }
     });
    },
    runs(){
      return this.filterData?.testRuns?.filter(run => {
        const hasMatchingPlan = this.selectedPlans.length > 0 && this.selectedPlans.includes(run.testPlanUid);
        const hasMatchingMilestone = this.selectedMilestones.length > 0 && run.milestoneUids.some((item) => this.selectedMilestones.includes(item.uid));
        const hasMatchingProject = this.selectedProjects.length > 0 && this.selectedProjects.includes(run.projectUid);
        if (hasMatchingPlan && hasMatchingMilestone && hasMatchingProject) {
          return run;
        }
      });
    }
  },
  created() {
    this.selectedUsers = [...this.workspaceFilter.assignToList];
  },
  methods: {
    ...mapActions({
      removeAssignTo: 'workspace/removeAssignTo',
      setAssignToList: 'workspace/setAssignToList',
      addRecentProject: 'workspace/addRecentProject'
    }),
    onProjectsChange(selectedItems) {
      if(this.selectedProjects.length > 6 )
        return showErrorToast(this.$swal, this.$t('maxProjectsSelected'))

      this.$emit('update:searchRuns', {
       projectUids: selectedItems
      });
      this.$emit('update:searchMilestones', {
       projectUids: selectedItems
      });
      this.$emit('update:searchPlans', {
       projectUids: selectedItems
      });
      this.updateFilter();
    },
    onMilestonesChange(selectedItems) {
      this.$emit('update:searchRuns', {
       milestoneUids: selectedItems
      });  
      this.$emit('update:searchPlans', {
       milestoneUids: selectedItems
      });
    
      this.updateFilter();
    },
    onPlansChange(selectedItems) {
      this.$emit('update:searchRuns', {
       planUids: selectedItems
      });  
      this.updateFilter();
    },
    onRunsChange(){
      this.updateFilter();
    },
     onUsersChange(){

      this.updateFilter();
    },
    updateFilter() { 
      this.setAssignToList(this.selectedUsers);
      this.$emit('update-filters', {
        projectUids: this.selectedProjects.length ? this.selectedProjects : [] ,
        milestoneUids: this.selectedMilestones.length ? this.selectedMilestones : [],
        planUids: this.selectedPlans ? this.selectedPlans : [],
        runUids: this.selectedRuns.length ? this.selectedRuns : [],
        userUids: this.selectedUsers.length ? this.selectedUsers : [],
      });
    },

    resetFilters(){
      this.selectedMilestones = []
      this.selectedPlans = []
      this.selectedRuns = []
      this.selectedProjects = []
      this.selectedUsers = []
      
      this.updateFilter();
    },
    onProjectSelected(selectedProject){
      this.$nextTick(() => {
        if(this.selectedProjects.includes(selectedProject))
          this.addRecentProject(selectedProject);
      })
    },
    projectSelection(projectUid){
      if(typeof this.selectedProjects === 'string'){
        this.selectedProjects = [this.selectedProjects];
      } else if(!Array.isArray(this.selectedProjects)){
        this.selectedProjects = []
      }
      return this.selectedProjects ? this.selectedProjects.some(project => project == projectUid) : false;
    },
    userSelection(userUid){
      if(typeof this.selectedProjects === 'string'){
        this.selectedUsers = [this.selectedUsers];
      } else if(!Array.isArray(this.selectedUsers)){
        this.selectedUsers = []
      }
      return this.selectedUsers ? this.selectedUsers.some(user => user == userUid) : false;
    },
    onRemoveSelectedProjects(projectName){
      const index = this.selectedProjects.indexOf(projectName);
      if (index !== -1) {
        this.selectedProjects.splice(index, 1);
      }
      this.onProjectsChange(this.selectedProjects);
    },
    milestoneSelection(milestoneName){
      if(typeof this.selectedMilestones === 'string'){
        this.selectedMilestones = [this.selectedMilestones];
      } else if(!Array.isArray(this.selectedMilestones)){
        this.selectedMilestones = []
      }

      return this.selectedMilestones ? this.selectedMilestones.some(milestone => milestone == milestoneName) : false;
    },
    onRemoveSelectedMilestones(milestoneName){
      const index = this.selectedMilestones.indexOf(milestoneName);
      if (index !== -1) {
        this.selectedMilestones.splice(index, 1);
      }
      this.onMilestonesChange(this.selectedMilestones);
    },
    planSelection(planName){
      if(typeof this.selectedPlans === 'string'){
        this.selectedPlans = [this.selectedPlans];
      } else if(!Array.isArray(this.selectedPlans)){
        this.selectedPlans = []
      }

      return this.selectedPlans ? this.selectedPlans.some(plan => plan == planName) : false;
    },
    onRemoveSelectedPlans(planName){
      const index = this.selectedPlans.indexOf(planName);
      if (index !== -1) {
        this.selectedPlans.splice(index, 1);
      }
      this.onPlansChange(this.selectedPlans);
    },
    runSelection(runName){
      if(typeof this.selectedRuns === 'string'){
        this.selectedRuns = [this.selectedRuns];
      } else if(!Array.isArray(this.selectedRuns)){
        this.selectedRuns = []
      }

      return this.selectedRuns ? this.selectedRuns.some(run => run == runName) : false;
    },
    onRemoveSelectedUsers(userUid){
       this.removeAssignTo(userUid);
      const index = this.selectedUsers.indexOf(userUid);
      if (index !== -1) {
        this.selectedUsers.splice(index, 1);
      }
      this.updateFilter();
  },
    onRemoveSelectedRuns(runName){
      const index = this.selectedRuns.indexOf(runName);
      if (index !== -1) {
        this.selectedRuns.splice(index, 1);
      }
      this.onRunsChange();
    }
  }
}
</script>
<style scoped>
.__workspace_filters .row .col .v-select__selections input{
  min-height: 46px !important;
}
.filter-item-close {
  cursor: pointer;
}
.disabled-checkbox-style {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}
</style>