<template>
  <div class="d-flex justify-center align-center flex-column">
    <img 
      :src="imageSrc" 
      alt="Empty state image" 
      class="placeholder-img mb-6" 
      :style="'max-width: ' + imageMaxWidth"
    >
    <div>
      <div class="d-flex flex-column align-center">
        <p class="ma-0 font-weight-bold fs-24px">
          {{ title }}
        </p>
        <p
          v-if="description"
          class="ma-0 fs-16px"
        >
          {{ description }}
        </p>
        <slot name="description" />
      </div>
      <div 
        v-if="isButtonTextNotEmpty"
        class="mt-6 d-flex align-center justify-center"
      >
        <slot name="extra-buttons" />
        <div>
          <v-tooltip 
            bottom
            :disabled="writeEntity" 
          >
            <template #activator="{ on, attrs }">
              <div 
                v-bind="attrs" 
                v-on="on"
              >
                <v-btn
                  depressed
                  :color="buttonColor"
                  height="40px"
                  class="rounded-lg btn-theme text-capitalize"
                  :class="{ 'disabled-action': isProjectArchived }"
                  :to="!isProjectArchived ? buttonRoute : null"
                  @click="onButtonClick"
                >
                  {{ buttonText }} 
                  <v-icon
                    class="ml-1" 
                    size="16px"
                  >
                    mdi-plus
                  </v-icon>
                </v-btn>
              </div>
            </template>
            <span>
              {{
                $t("noPermissionToDo", { action: $t('create').toLowerCase(), type: $t(`entity`) })
              }}
            </span>
          </v-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
  export default {
    props: {
      isProjectArchived: {
        type: Boolean,
        required: false
      },
      imageSrc: {
        type: String,
      },
      title: {
        type: String,
      },
      buttonText: {
        type: String,
      },
      buttonColor: {
        type: String,
        default: 'blue',
      },
      buttonRoute: {
        type: Object,
      },
      imageMaxWidth: {
        type: String,
        default: '480px',
      },
      hideButton: {
        type: Boolean,
        default: false,
      },
      writeEntity:{
        type: Boolean,
        default: true
      },
      description: {
        type: String,
      },
    },
    computed: {
      isButtonTextNotEmpty() {
          return !!this.buttonText;
      },
    },
    methods: {
      onButtonClick() {
        if (!this.isProjectArchived) {
        this.$emit('button-click');
        }
      },
    }
};
</script>

<style scoped>
.placeholder-img {
  width: 100%;
}
</style>
