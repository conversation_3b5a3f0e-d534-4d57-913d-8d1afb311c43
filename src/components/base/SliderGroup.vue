<template>
  <v-slide-group
    class="custom-carousel"
  >
    <v-slide-item
      v-for="(file, index) in files"
      :key="index"
      class="preview-container"
    >
      <v-card outlined>
        <template v-if="isImage(file)">
          <div class="position-relative">
            <a
              :href="filePreview(file)"
              target="_blank"
              class="cursor-pointer"
            >
              <img
                :src="filePreview(file)"
                class="preview-image"
              >
            </a>
            <v-btn
              v-if="!isPreview"
              icon
              small
              class="download-btn"
              @click="$emit('download-file', file)"
            >
              <DownloadIcon />
            </v-btn>
            <v-btn
              v-if="!isPreview"
              icon
              small
              color="red"
              class="delete-btn"
              @click="$emit('remove-file', file)"
            >
              <RemoveIcon />
            </v-btn>
          </div>
        </template>
        <template v-else-if="isVideo(file)">
          <div class="h-full">
            <a 
              :href="filePreview(file)" 
              target="_blank" 
              rel="noopener noreferrer"
              class="cursor-pointer"
            >
              <video
                class="preview-image"
                :src="filePreview(file)"
              />
            </a>
            <v-btn
              v-if="!isPreview"
              icon
              small
              class="download-btn"
              @click="$emit('download-file', file)"
            >
              <DownloadIcon />
            </v-btn>
            <v-btn
              icon
              small
              color="red"
              class="delete-btn"
              @click="$emit('remove-file', file)"
            >
              <removeIcon />
            </v-btn>
          </div>
        </template>
        <template v-else-if="isPdf(file)">
          <div class="position-relative">
            <a
              v-if="file.previewBlobUrl"
              :href="file.previewBlobUrl"
              target="_blank"
              class="cursor-pointer"
            >
              <VuePdfEmbed
                :page="1"
                :source="file.previewBlobUrl"
                class="pdf-preview"
              />
            </a>
            <v-btn
              v-if="!isPreview && file.previewBlobUrl"
              icon
              small
              class="download-btn"
              @click="$emit('download-file', file)"
            >
              <DownloadIcon />
            </v-btn>
            <v-btn
              v-if="!isPreview && file.previewBlobUrl"
              icon
              small
              color="red"
              class="delete-btn"
              @click="onRemovePdf(file)"
            >
              <RemoveIcon />
            </v-btn>
          </div>
        </template>
        <a
          v-else
          :href="file.previewUrl || file.url"
          target="_blank"
          class="cursor-pointer h-full non-image-preview"
        >
          <div class="file-placeholder">
            {{ $t("file") }}: {{ file.name || file.type }}
          </div> 
          <v-btn
            v-if="!isPreview"
            icon
            small
            class="download-btn"
            @click.prevent="$emit('download-file', file)"
          >
            <DownloadIcon />
          </v-btn>
          <v-btn
            v-if="!isPreview"
            icon
            small
            color="red"
            class="delete-btn"
            @click.prevent="$emit('remove-file', file)"
          >
            <RemoveIcon />
          </v-btn>
        </a>
      </v-card>
    </v-slide-item>
  </v-slide-group>
</template>

<script>
import RemoveIcon from "@/assets/svg/remove.svg";
import DownloadIcon from "@/assets/svg/download.svg";
import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'
import axios from "axios";

export default {
  components: {
    RemoveIcon,
    DownloadIcon,
    VuePdfEmbed,
  },
  props: {
    files: {
      type: Array,
      required: true,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    files: {
      async handler(newFiles) {
        for (let file of newFiles) {
          if (this.isPdf(file)) {
            file.previewBlobUrl = await this.pdfPreview(file);
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  beforeDestroy() {
    // Revoke object URLs to avoid memory leaks
    this.files.forEach((file) => {
      if (file.previewUrl) {
        URL.revokeObjectURL(file.previewUrl);
      }
    });
  },
  methods: {
    async onRemovePdf(file) {
      this.$emit("remove-file", file);
    },
    isImage(file) {
      return file?.type?.startsWith("image/") || file?.fileType?.startsWith("image/");
    },
    isPdf(file) {
      return file.type === "application/pdf" || file.fileType === "application/pdf";
    },
    isVideo(file) {
      return file?.type?.startsWith("video/") || file?.fileType?.startsWith("video/");
    },
    filePreview(file) {
      const url = file.previewUrl || file.url;
      if (!url && !this.isPreview) {
        return URL.createObjectURL(file);
      }
      return url;
    },
    async pdfPreview(file) {
      const url = file.previewUrl || file.url;
      if (!file.previewBlobUrl && url) {
        try {
          const response = await axios.get(url, {withCredentials: true, params: {download: true}}).then(res => res.data);
          this.$set(file, "previewBlobUrl", response);
          return response;
        } catch (error) {
          console.error("Error fetching Blob URL:", error);
          file.previewBlobUrl = null;
        }
      }
      return file.previewBlobUrl;
    }

    }
};
</script>

