<template>
  <div v-if="hasActiveFilters">
    <div class="d-flex align-center flex-wrap">
      <div v-if="showResultsCount">
        <span
          class="fw-bold font-weight-medium no-bg pr-2"
          width="300px"
        >{{ $t('results') }}: {{ resultsCount }}</span>
      </div>

      <!-- Type: Array filters -->
      <template v-for="(filter, filterKey) in arrayFilters">
        <v-chip
          v-for="(value, index) in filter.value"
          :key="`${filterKey}-${getValueName(value)}-${index}`"
          class="chips-style ma-2"
          close
          @click:close="removeArrayValue(filterKey, getValueName(value))"
        >
          {{ filter.label }}: {{ getValueName(value) }}
        </v-chip>
      </template>

      <!-- Type: Range filters -->
      <v-chip
        v-for="(filter, filterKey) in rangeFilters"
        :key="`${filterKey}`"
        class="chips-style ma-2"
        close
        @click:close="resetRangeFilter(filterKey)"
      >
        {{ filter.label }}: {{ filter.value[0] }}{{ filter.suffix || '' }} - {{ filter.value[1]
        }}{{ filter.suffix || '' }}
      </v-chip>

      <!-- Type: Date range filters -->
      <div class="d-flex align-center ml-2">
        <v-chip
          v-for="(filter, filterKey) in dateFilters"
          :key="`${filterKey}`"
          class="chips-style ma-2 black--text theme--light"
          close
          @click:close="resetDateFilter(filterKey)"
        >
          {{ filter.label }}: {{ formatDateRange(filter) }} 
        </v-chip>
      </div>

      <v-btn
        class="blue--text text-capitalize fw-semibold font-weight-medium"
        width="100px"
        text
        elevation="0"
        @click="clearAllFilters"
      >
        {{ $t('clearAll') }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import { useDateFormatter } from '@/composables/utils/dateFormatter';

export default {
  name: 'FilterChips',
  props: {
    filters: {
      type: Object,
      default: () => ({}),
      required: true,
    },

    resultsCount: {
      type: Number,
      default: 0,
    },

    showResultsCount: {
      type: Boolean,
      default: true,
    },

    dateFormat: {
      type: String,
      default: 'MM/DD/YY',
    },
  },
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },
  computed: {
    arrayFilters() {
      if (!this.filters) return {};

      return Object.entries(this.filters)
        .filter(([, filter]) => filter.type === 'array' && filter.value && filter.value.length > 0)
        .reduce((acc, [key, filter]) => {
          acc[key] = filter;
          return acc;
        }, {});
    },

    rangeFilters() {
      if (!this.filters) return {};

      return Object.entries(this.filters)
        .filter(([, filter]) => {
          if (filter.type !== 'range' || !filter.value) return false;

          const defaultValue = filter.defaultValue || [0, 100];
          return filter.value[0] !== defaultValue[0] || filter.value[1] !== defaultValue[1];
        })
        .reduce((acc, [key, filter]) => {
          acc[key] = filter;
          return acc;
        }, {});
    },

    dateFilters() {
      if (!this.filters) return {};

      return Object.entries(this.filters)
        .filter(
          ([, filter]) => filter.type === 'dateRange' && filter.value && (filter.value.start || filter.value.end)
        )
        .reduce((acc, [key, filter]) => {
          acc[key] = filter;
          return acc;
        }, {});
    },

    hasActiveFilters() {
      return (
        Object.keys(this.arrayFilters).length > 0 ||
        Object.keys(this.rangeFilters).length > 0 ||
        Object.keys(this.dateFilters).length > 0
      );
    },
  },

  methods: {
    getValueName(value) {
      return typeof value === 'object' && value !== null ? value.name : value;
    },

    getValueId(value) {
      if (typeof value === 'object' && value !== null) {
        return value.uid || value.id || null;
      }
      return null;
    },

    removeArrayValue(filterKey, valueName) {
      const updatedFilters = JSON.parse(JSON.stringify(this.filters));

      if (updatedFilters[filterKey].value && updatedFilters[filterKey].value.length > 0) {
        if (typeof updatedFilters[filterKey].value[0] === 'object') {
          updatedFilters[filterKey].value = updatedFilters[filterKey].value.filter(
            (item) => this.getValueName(item) !== valueName
          );
        } else {
          updatedFilters[filterKey].value = updatedFilters[filterKey].value.filter((item) => item !== valueName);
        }
      }

      this.$emit('update-filters', updatedFilters);
    },

    resetRangeFilter(filterKey) {
      const updatedFilters = JSON.parse(JSON.stringify(this.filters));
      const defaultValue = updatedFilters[filterKey].defaultValue || [0, 100];
      updatedFilters[filterKey].value = [...defaultValue];
      this.$emit('update-filters', updatedFilters);
    },

    resetDateFilter(filterKey) {
      const updatedFilters = JSON.parse(JSON.stringify(this.filters));
      updatedFilters[filterKey].value = {
        start: null,
        end: null,
      };
      this.$emit('update-filters', updatedFilters);
    },

    formatDateRange(filter) {
      const { value } = filter;

      if (value.start && value.end) {
        return `${this.formatDate(value.start, this.dateFormat)} - ${this.formatDate(value.end, this.dateFormat)}`;
      } else if (value.start) {
        return `${this.$t('from')} ${this.formatDate(value.start, this.dateFormat)}`;
      } else if (value.end) {
        return `${this.$t('until')} ${this.formatDate(value.end, this.dateFormat)}`;
      }

      return '';
    },

    clearAllFilters() {
      this.$emit('clear-filters');
    },
  },
};
</script>

<style scoped>
.chips-style {
  background-color: #f9fafb !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
}
</style>
