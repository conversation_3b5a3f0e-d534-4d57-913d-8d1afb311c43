<template>
  <div class="white card d-flex flex-column justify-space-between pa-3 rounded-lg mr-3 sticky-on-scroll">
    <v-list
      v-if="isCollapsed"
      nav
      class="pa-0"
    >
      <v-list-item-group color="primary">
        <v-list-item
          class="d-flex justify-center"
          :ripple="false"
          @click="searchCollapsedMenu"
        >
          <v-list-item-icon class="justify-center mx-0">
            <SearchIcon />
          </v-list-item-icon>
        </v-list-item>
        <v-list-item
          class="d-flex justify-center"
          :ripple="false"
          @click="unlinkedCollapsedMenu"
        >
          <v-list-item-icon class="justify-center mx-0">
            <UnlinkedIcon24 />
          </v-list-item-icon>
        </v-list-item>
        <v-list-item
          class="d-flex justify-center"
          :ripple="false"
          @click="linkedCollapsedMenu"
        >
          <v-list-item-icon class="justify-center mx-0">
            <LinkedIcon24 />
          </v-list-item-icon>
        </v-list-item>
      </v-list-item-group>
    </v-list>
    <div
      v-if="!isCollapsed"
      class="d-flex flex-column"
    >
      <v-text-field
        ref="searchField"
        v-model="searchPlan"
        :placeholder="$t('search')"
        background-color="#F9F9FB"
        class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0 mb-3 flex-inherit"
        height="38"
        dense
        hide-details
      >
        <template #prepend-inner>
          <SearchIcon />
        </template>
      </v-text-field>

      <v-btn
        text
        :color="displayedRuns == 'all' ? '#0C2FF3' : '#667085'"
        :class="displayedRuns == 'all' ? 'bg-theme-primary-light' : 'bg-gray-theme'"
        class="text-capitalize rounded-lg justify-start mb-3"
        width="100%"
        @click="onClickPlanActive('all')"
      >
        {{ $t('testruns.all') }}
      </v-btn>

      <v-btn
        text
        :color="displayedRuns == 'unlinked' ? '#0C2FF3' : '#667085'"
        :class="displayedRuns == 'unlinked' ? 'bg-theme-primary-light' : 'bg-gray-theme'"
        class="text-capitalize rounded-lg bg-gray-theme justify-start mb-3"
        width="100%"
        @click="onClickPlanActive('unlinked')"
      >
        <div class="d-flex align-center">
          <div class="mr-2">
            <UnlinkedIcon />
          </div>
          <span>{{ $t('testruns.unlinked') }} 0</span>
        </div>
      </v-btn>
      <v-btn
        text
        class="text-capitalize rounded-lg bg-gray-theme justify-start mb-2"
        width="100%"
        :color="Number.isInteger(displayedRuns) ? '#0C2FF3' : '#667085'"
        :class="Number.isInteger(displayedRuns) ? 'bg-theme-primary-light' : 'bg-gray-theme'"
        @click="onToPlanExpanded"
      >
        <div class="d-flex justify-space-between w-full">
          <div class="d-flex align-center">
            <div class="mt-1 mr-2">
              <LinkedIcon />
            </div>
            <span>{{ $t('testruns.toPlans') }} {{ getFilteredPlansCount }}</span>
          </div>
          <div
            v-if="getFilteredPlansCount > 0"
          >
            <v-icon>
              {{ isToPlanExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
            </v-icon>
          </div>
        </div>
      </v-btn>

      <div
        v-if="isToPlanExpanded && getFilteredPlansCount > 0"
        class="plan-list-wrapper"
      >
        <div
          v-for="(plan, index) in filteredPlans"
          :key="index"
        >
          <v-tooltip
            bottom
            left
            max-width="200px"
            :disabled="plan.name.length < 15"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <v-btn
                text
                :color="Number.isInteger(displayedRuns) && displayedRuns == plan.uid ? '#0C2FF3' : '#667085'"
                :class="Number.isInteger(displayedRuns) && displayedRuns == plan.uid ? 'bg-theme-primary-light' : 'bg-gray-theme'"
                class="text-capitalize btn-full font-weight-regular rounded-lg justify-start mb-2"
                width="100%"
                v-bind="attrs"
                @click="onClickPlanActive(plan)"
                v-on="on"
              >
                <div class="d-flex justify-between align-center w-full">
                  <div class="text-truncate">
                    {{ plan.name }}
                  </div>
                  <div>
                    <span>
                      {{ `(${plan.runCount || 0})` }}
                    </span>
                  </div>
                </div>
              </v-btn>
            </template>
            <span>{{ plan.name }}</span>
          </v-tooltip>
        </div>
      </div>
      <div
        v-else
        class="plan-list-wrapper"
      />
    </div>
    <div
      v-if="!isCollapsed"
      class="sticky-scroll"
    >
      <div class="text-left">
        <v-label class="text-left fs-14px text-theme-label font-weight-medium">
          {{ $t("createQuickPlan") }} 
        </v-label>
        <v-text-field
          v-model="planName"
          :placeholder="$t('enterName')"
          height="38"
          background-color="#F9F9FB"
          :class="{
            'field-theme mt-0 pt-1': true,
            'disabled-action': isProjectArchived
          }"
        />
      </div>
      <v-btn
        type="submit"
        block
        color="primary"
        :depressed="true"
        :class="{
          'btn-theme': true,
          'disabled-action': isProjectArchived || createButtonLoading
        }"
        width="100%"
        height="40"
        :loading="createButtonLoading"
        @click="createTestPlan"
      >
        {{ $t("create") }}
      </v-btn>
    </div>

    <div
      class="collapse-btn btn-runs-sticky"
      @click="toggleProjectMenu"
    >
      <v-icon
        class="collapse-icon"
        color="#0C2FF3"
      >
        {{ isCollapsed ? 'mdi-arrow-right-bottom' : 'mdi-arrow-left-bottom'
        }}
      </v-icon>
      <span
        v-if="!isCollapsed"
        class="collapse-text"
      >
        {{ $t('collapse') }}
      </span>
    </div>
  </div>
</template>

<script>
import makePlanService from '@/services/api/plan';
import SearchIcon from '@/assets/svg/search-icon.svg';
import UnlinkedIcon from '@/assets/svg/unlinked.svg';
import UnlinkedIcon24 from '@/assets/svg/unlinked24px.svg';
import LinkedIcon from '@/assets/svg/linked.svg';
import LinkedIcon24 from '@/assets/svg/linked24px.svg';
import projectStatus from '@/mixins/projectStatus';
import { showSuccessToast, showErrorToast } from '@/composables/utils/toast';
import colorPreferencesMixin from '@/mixins/colorPreferences';

let plansService;

export default {
  components: {
    SearchIcon,
    UnlinkedIcon,
    UnlinkedIcon24,
    LinkedIcon,
    LinkedIcon24
  },
  mixins: [projectStatus, colorPreferencesMixin],
  props: {
    isCollapsed: {
      type: Boolean,
      default: false,
    },
    selectedPlanId: {
      type: [Number, String],
      default: 'all',
    },
  },
  data() {
    return {
      isToPlanExpanded: false,
      plans: [],
      planName: '',
      createButtonLoading: false,
      searchPlan: '',
    };
  },
  computed: {
    displayedRuns() {
      return this.selectedPlanId;
    },
    getActivePlansCount() {
      return this.plans?.filter(plan => plan.archivedAt == null).length;
    },
    getActivePlans() {
      return this.plans.filter(plan => plan.archivedAt == null);
    },
    filteredPlans() {
      return this.getActivePlans.filter(plan => {
        return plan.name.toLowerCase().includes(this.searchPlan.toLowerCase());
      });
    },
    getFilteredPlansCount() {
      return this.filteredPlans.length;
    },
    localCollapsed: {
      get() {
        return this.isCollapsed; 
      },
      set(value) {
        this.$emit('update:isCollapsed', value);
      }
    },
    routeParamsHandle() {
      return this.$route.params.handle;
    },
    routeParamsKey() {
      return this.$route.params.key;
    },
  },
  created() {
    plansService = makePlanService(this.$api);
  },
  async mounted() {
    await this.getTestPlans();
  },
  methods: {
    searchCollapsedMenu() {
      this.localCollapsed = !this.localCollapsed;
      this.$nextTick(() => {
        if (this.$refs.searchField) {
          this.$refs.searchField.focus();
        }
      });
    },
    unlinkedCollapsedMenu() {
      this.localCollapsed = !this.localCollapsed;
      this.displayedRuns = 'unlinked';
    },
    linkedCollapsedMenu() {
      this.localCollapsed = !this.localCollapsed;
      this.isToPlanExpanded = true;
    },
    toggleProjectMenu() {
      this.localCollapsed = !this.localCollapsed;
    },
    onToPlanExpanded(){
      this.isToPlanExpanded = !this.isToPlanExpanded
    },
    async getTestPlans() {
      const handle = this.$route.params.handle;
      const projectKey = this.$route.params.key;

      try {
        const response = await plansService.getPlans(handle, projectKey, 1000, 0);
        this.plans = response.data?.items || [];
        return this.plans;
      } catch (err) {
        showErrorToast(this.$swal, 'fetchError', { item: 'test plans' }, err?.response?.data);
        return [];
      }
    },
    async createTestPlan() { 
      const statuses = this.getStatuses('testPlan');
      const priorities = this.getPriorities('testPlan');
      const defaultStatus = this.getDefaultStatus(statuses);
      const defaultPriority = this.getDefaultPriority(priorities);

      if(!this.isProjectArchived) {
      const payload = {
        name: this.planName,
        source: "testfiesta",
        status: defaultStatus.id,
        priority: defaultPriority.id,
        customFields: {
          archived: false
        }
     }
     try {
       this.createButtonLoading = true;
        const response = await plansService.createTestPlan(
        this.$route.params.handle,
        this.$route.params.key,
        payload
       );
       if (response.status === 200) {
        const newPlan = response.data;
        if (newPlan) {
          this.plans.push({...newPlan, runs: []});
        }
        showSuccessToast(this.$swal, 'createSuccess', { item: this.$t('testPlan') });

        this.planName = "";
       }
      } catch (err) {
        showErrorToast(this.$swal, err.response?.data?.message || 'Internal server error');
      } finally {
        this.createButtonLoading = false;
     }
    }
    },
    onClickPlanActive(plan) {
      const planId = plan?.uid || plan;
      this.$emit('onPlanActiveId', planId);
    },
    async refreshPlans() {
      await this.getTestPlans();
    }
  },
}
</script>

<style scoped>
.sticky-on-scroll {
  position: -webkit-sticky;
  position: sticky;
  height: calc(100vh - 6px);
}
.sticky-scroll {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  padding-bottom: 5rem;
  background: #fff;
    z-index: 1;
    padding-top: 1rem;
}
.plan-list-wrapper {
  height: calc(100vh - 28rem);
  overflow: auto;
  scrollbar-width: thin;
}
.btn-runs-sticky {
  position: -webkit-sticky;
  position: sticky;
  background: #fff;
  justify-content: flex-start;
  z-index: 9;
}
</style>