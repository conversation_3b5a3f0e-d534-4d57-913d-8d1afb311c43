<template>
  <v-card
    class="white py-6 px-6"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div class="d-flex align-center justify-space-between">
      <h2 v-if="!skeletonLoaderState">
        {{ title }}
      </h2>
      <v-skeleton-loader
        v-else
        height="36"
        width="140"
        type="heading"
      />
      <v-btn
        v-if="!skeletonLoaderState"
        :class="{ 'disabled-action': isProjectArchived }"
        depressed
        color="primary"
        class="rounded-lg btn-theme text-capitalize"
        height="40px"
        @click="$emit('create')"
      >
        {{ btnText }}
        <v-icon
          class="ml-1"
          size="xs"
        >
          mdi-plus
        </v-icon>
      </v-btn>
      <v-skeleton-loader
        v-else
        class="rounded-lg primary"
        height="40"
        width="150"
        type="button"
      />
    </div>
  </v-card>
</template>

<script>
import handleLoading from '@/mixins/loader.js'
export default {
  name: 'Header',
  mixins: [handleLoading],
  props: {
    title: String,
    btnText: String,
    isProjectArchived: {
      type: <PERSON><PERSON><PERSON>,
    },
  },

  methods: {
    updateStatus(value) {
      this.$emit('update-status', value);
    },
  }
}
</script>
