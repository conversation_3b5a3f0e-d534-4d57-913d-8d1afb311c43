<template>
  <div>
    <v-menu
      v-model="showMenu"
      bottom
      left
      offset-y
      max-width="400px"
      max-height="400px"
      :close-on-content-click="false" 
    >
      <template #activator="{ on }">
        <div
          class="tag-editor"
          v-on="on"
          @click.stop="showMenu = true"
        >
          <template v-if="value && value.length > 0">
            <span class="truncate"> {{ value[0]?.name }} </span>
            <span
              v-if="value.length > 1"
              class="ml-1"
            > +{{ value.length - 1 }} </span>
          </template>
          <span v-else>-</span>
          <v-icon color="black">
            mdi-chevron-down
          </v-icon>
        </div>
      </template>
      <v-list class="pa-0 position-relative">
        <v-list-item
          class="px-4 py-2"
        >
          <v-text-field
            v-model="tagSearch"
            hide-details
            clearable
            height="40"
            :placeholder="$t('search')"
            class="tag-search rounded-lg rounded-lg field-theme custom-prepend"
            background-color="#F9F9FB"
            clear-icon="mdi-close-circle"
            @click.stop
          >
            <template #prepend-inner>
              <SearchIcon />
            </template>
          </v-text-field>
        </v-list-item>
        <template v-if="filteredTags.length > 0">
          <v-list-item
            v-for="tag in filteredTags"
            :key="tag.uid"
            :class="{'disabled-action': isProjectArchived}"
          >
            <v-list-item-action class="custom-checkbox-container">
              <v-checkbox
                :input-value="isSelected(tag.uid)"
                class="field-theme"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                :hide-details="true"
                :disabled="isProjectArchived"
                @change="toggleTag(tag)"
              >
                <template #label>
                  <div
                    class="d-flex align-center justify-space-between w-100 gap-2"
                    @click.stop
                  >
                    <div
                      v-if="editingTagUid === tag.uid"
                      class="d-flex align-center"
                    >
                      <v-text-field
                        ref="titleInput"
                        v-model="editedTagName"
                        type="text"
                        background-color="transparent"
                        class="new-folder-input mt-0 pt-0 pa-0 editable"
                        hide-details
                        autofocus
                        height="24px"
                        solo
                        flat
                        dense
                        @blur="saveTagName(tag)"
                        @keyup.enter="saveTagName(tag)"
                        @click.stop
                      />
                    </div>
                    <span
                      v-else
                      class="fs-14 text-theme-label"
                    >{{ tag.name }}</span>
                    <div
                      class="d-flex align-center"
                    >
                      <v-btn
                        icon
                        x-small
                        class="mr-2"
                        @click.stop="startEditTagName(tag)"
                      >
                        <PencilIcon />
                      </v-btn>
                      <v-btn
                        icon
                        x-small
                        @click.stop="deleteTag(tag.uid)"
                      >
                        <DeleteIcon />
                      </v-btn>
                    </div>
                  </div>
                </template>
              </v-checkbox>
            </v-list-item-action>
          </v-list-item>
        </template>
        <template v-else>
          <div class="px-4 pb-4">
            <span class="fs-14px">{{ $t('noMatchesFound') }}</span>
          </div>
        </template>
        <div
          class="pa-3 sticky-div-bottom white"
          @click.stop="onCreateTagClick"
        >
          <v-btn
            plain
            color="primary"
            :ripple="false"
            small
            class="btn-plain-theme text-capitalize pa-0"
            :disabled="isProjectArchived"
          >
            <v-icon
              class="mr-2"
              size="16"
              color="primary"
            >
              mdi-plus
            </v-icon>
            <span class="fs-14px text-theme-label text-theme-primary">{{ $t('createATag') }}</span>
          </v-btn>
        </div>
      </v-list>
    </v-menu>
    <CreateTagDialog 
      v-model="showCreateTagDialog"
      :data="selectedTag"
      @create-new-tag="createTag"
      @close-dialog="showCreateTagDialog = false"
    />
  </div>
</template>

<script>
import SearchIcon from '@/assets/svg/search-icon.svg';
import makeTagService from '@/services/api/tag';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import PencilIcon from '@/assets/svg/pencil-16.svg';
import DeleteIcon from '@/assets/svg/delete-16.svg';
import CreateTagDialog from '@/components/User/CreateTagDialog.vue';

let tagService;

export default {
  name: 'TagEditor',
  components: {
    SearchIcon,
    CreateTagDialog,
    PencilIcon,
    DeleteIcon,
  },
  props: {
    items: {
      type: Array,
      required: true,
    },
    value: {
      type: Array,
      default: () => [],
    },
    isProjectArchived: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isEditingTagName: false,
      editedTagName: '',
      showMenu: false,
      selectedTag: {},
      selectedTags: [],
      tagSearch: '',
      showCreateTagDialog: false,
      editingTagUid: null, 
      initialValue: {
        name: '',
      },
      isAddTags: false,
      originalTags: [], // Store original tags when menu opens
      hasChanges: false, // Track if there are actual changes
    };
  },
  computed: {
    sortedTags() {
      return [...this.items].sort((a, b) => a.name.localeCompare(b.name));
    },
    filteredTags() {
      if (!this.tagSearch) return this.sortedTags;
        const search = this.tagSearch.toLowerCase();
        return this.sortedTags.filter(tag =>
        tag.name.toLowerCase().includes(search)
      );
    }
  },
  watch: {
    value: {
      handler(newValue) {
        // Update selectedTags when the value prop changes
        this.selectedTags = [...(newValue || [])];
      },
      immediate: true,
      deep: true
    },
    showMenu(newValue, oldValue) {
      if (newValue) {
        // When menu opens, store the original tags
        this.originalTags = [...this.selectedTags];
        this.hasChanges = false;
      } else if (oldValue && this.hasChanges) {
        // When menu closes and there are changes, emit the update
        this.$emit('updateTag', this.selectedTags);
      }
    }
  },
  created() {
    tagService = makeTagService(this.$api);
  },
  methods: {
    startEditTagName(tag) {
      this.editingTagUid = tag.uid;
      this.isEditingTagName = true;
      this.editedTagName = tag.name;
      this.$nextTick(() => {
        const comp = this.$refs.titleInput;
        const input = comp?.$el?.querySelector('input');
        input?.focus();
      });
    },
    async saveTagName(tag) {
      this.editingTagUid = null;
      this.isEditingTagName = false;
      let payload = {
        ...tag,
        name: this.editedTagName,
      }
      await tagService.updateTag(this.$route.params.handle, payload);
      this.$emit('refreshTags');
    },
    async deleteTag(uid) {
      try {
        await tagService.deleteTag(this.$route.params.handle, uid);
        this.$emit('refreshTags');
      } catch (err) {
        showErrorToast(this.$swal, "deleteError", { item: "Tag" }, err?.response?.data);
      }
    },
    isSelected(uid) {
      return this.selectedTags.some(t => t.uid === uid);
    },
    toggleTag(tag) {
      const index = this.selectedTags.findIndex(t => t.uid === tag.uid);
      if (index === -1) {
        this.selectedTags.push(tag);
      } else {
        this.selectedTags.splice(index, 1);
      }
      
      // Check if there are actual changes compared to original
      const originalUids = this.originalTags.map(t => t.uid).sort();
      const currentUids = this.selectedTags.map(t => t.uid).sort();
      this.hasChanges = JSON.stringify(originalUids) !== JSON.stringify(currentUids);
    },
    onCreateTagClick() {
      this.showCreateTagDialog = true;
      this.showMenu = false;
      this.initialValue = {
        name: this.tagSearch,
      }
    },
    onCloseTagDialog() {
      this.showCreateTagDialog = false;
      this.tagSearch = '';
    },
    async createTag(tag) {
      
      this.showCreateTagDialog = false;
    
      try {
        const payload = {
          name: tag.name,
          description: tag.description,
          entityTypes: [tag.entityTypes],
        }
        const response = await tagService.createTag(this.$route.params.handle, payload);
        if(response.status === 200){
          this.$emit('refreshTags');
          showSuccessToast(this.$swal, "createSuccess", { item: "Tag" });
        }
        
      } catch (err) {
        showErrorToast(this.$swal, "createError", { item: "Tag" }, err?.response?.data);
      } finally {
        this.showCreateTagDialog = false;
        this.tagSearch = '';
      }
    },
  },
};
</script>

<style scoped lang="scss">
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tag-editor {
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
  font-weight: 400;
  align-items: center;
  cursor: pointer;
}
.btn {
  width: 100%;
  height: 40px;
  color: white !important;
  padding: 8px 24px 8px 24px;
  gap: 8px;
  border-radius: 6px;
}
</style>
