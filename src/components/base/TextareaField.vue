<template>
  <component
    :is="componentType"
    v-bind="$attrs"
    v-model="content"
    :support-test-rail="enableTestRail"
    :enable-image-upload="enableImageUpload"
    :max-image-size="maxImageSize"
    :allowed-image-types="allowedImageTypes"
    @input="handleInput"
    @image-uploaded="$emit('image-uploaded', $event)"
    @image-upload-error="$emit('image-upload-error', $event)"
  >
    <template
      v-if="$slots.append"
      #append
    >
      <slot name="append" />
    </template>
  </component>
</template>

<script>
import MarkdownTextarea from './MarkdownTextarea.vue';

export default {
  name: 'TextareaField',
  components: { MarkdownTextarea },
  props: {
    value: { type: String, default: '' },
    enableMarkdown: { type: Boolean, default: true },
    enableTestRail: { type: Boolean, default: true },
    enableImageUpload: { type: Boolean, default: true },
    maxImageSize: { type: Number, default: 10 * 1024 * 1024 },
    allowedImageTypes: { type: Array, default: () => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] }
  },
  computed: {
    componentType() { return this.enableMarkdown ? 'MarkdownTextarea' : 'v-textarea'; },
    content: {
      get() { return this.value; },
      set(val) { this.$emit('input', val); }
    }
  },
  methods: {
    handleInput(value) { this.$emit('input', value); }
  }
};
</script> 