<template>
  <div class="tiptap-theme">
    <div class="tiptap-toolbar">
      <button
        type="button"
        :class="{ 'is-active': editor.isActive('bold') }"
        @click="editor.chain().focus().toggleBold().run()"
      >
        <BoldIcon class="d-flex align-center" />
      </button>
      <button
        type="button"
        :class="{ 'is-active': editor.isActive('italic') }"
        @click="editor.chain().focus().toggleItalic().run()"
      >
        <ItalicIcon class="d-flex align-center" />
      </button>
      <button
        type="button"
        :class="{ 'is-active': editor.isActive('strike') }"
        @click="editor.chain().focus().toggleStrike().run()"
      >
        <StrikeIcon class="d-flex align-center" />
      </button>
      <span class="divider">|</span>
      <button
        type="button"
        :class="{ 'is-active': editor.isActive('bulletList') }"
        @click="editor.chain().focus().toggleBulletList().run()"
      >
        <BulletListIcon class="d-flex align-center" />
      </button>
      <button
        type="button" 
        :class="{ 'is-active': editor.isActive('orderedList') }"
        @click="editor.chain().focus().toggleOrderedList().run()"
      >
        <OrderedListIcon class="d-flex align-center" />
      </button>
    </div>
    <EditorContent
      :editor="editor"
      class="tiptap-editor"
    />
    <div class="tiptap-footer">
      <div class="d-flex align-center justify-space-between w-full">
        <div class="d-flex align-center">
          <button
            type="button"
            class="mr-1"
            @click="toggleLink"
          >
            <LinkIcon class="d-flex align-center" />
          </button>
        </div>
        <div class="d-flex align-center">
          <slot name="action" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Editor, EditorContent } from '@tiptap/vue-2'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import BoldIcon from '@/assets/svg/tiptap/bold.svg';
import ItalicIcon from '@/assets/svg/tiptap/italic.svg';
import StrikeIcon from '@/assets/svg/tiptap/strike.svg';
import BulletListIcon from '@/assets/svg/tiptap/bullet-list.svg';
import OrderedListIcon from '@/assets/svg/tiptap/ordered-list.svg';
import LinkIcon from '@/assets/svg/tiptap/link.svg';

export default {
  name: 'TiptapEditor',
  components: {
    EditorContent,
    BoldIcon,
    ItalicIcon,
    StrikeIcon,
    BulletListIcon,
    OrderedListIcon,
    LinkIcon,
  },
  props: {
    value: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      editor: null,
    };
  },
  watch: {
    value(newValue) {
      if (this.editor && this.editor.getHTML() !== newValue) {
        this.editor.commands.setContent(newValue, false);
      }
    },
  },
  created() {
    this.editor = new Editor({
      content: this.value,
      extensions: [
        StarterKit,
        Image.configure({
          HTMLAttributes: {
            class: 'defect-description-image',
            style: 'max-width: 100%; width: 100%; height: auto; object-fit: contain; max-height: 400px;',
          },
          allowBase64: false,
          inline: false,
        }),
      ],
      editorProps: {
        attributes: {
          style: 'color: #000000 !important;',
        },
      },
      onUpdate: () => {
        this.$emit('input', this.editor.getHTML());
      },
    });
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor.destroy();
    }
  },
  methods: {
    toggleLink() {
      if (!this.editor) return;

      const previousUrl = this.editor.getAttributes('link').href;
      const url = prompt('Enter the URL', previousUrl);
      
      if (url === null) {
        return;
      }

      if (url === '') {
        this.editor.chain().focus().extendMarkRange('link').unsetLink().run();
        return;
      }

      this.editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
    },
  },
};
</script>

<style scoped>
.tiptap-editor {
  flex: 1;
  border: 1px solid #ddd;
  border-bottom: none;
  outline: none !important;
}
.tiptap-toolbar {
  border: 1px solid #ddd;
  border-bottom: none;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}
::v-deep .ProseMirror {
  padding: 12px;
  outline: none;
  min-height: inherit;
  border: none;
  background: transparent;
}

/* Style the editor to look more like v-textarea */
::v-deep .ProseMirror p {
  margin: 0;
  line-height: 1.5;
}

::v-deep .ProseMirror:empty:before {
  content: attr(data-placeholder);
  color: #9e9e9e;
  pointer-events: none;
  position: absolute;
}
.tiptap-toolbar button {
  padding: 4px 8px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
}

.tiptap-toolbar button:hover {
  background-color: #f0f0f0;
}

.tiptap-toolbar button.is-active {
  background-color: #e0e0e0;
}

.divider {
  margin: 0 8px;
  color: #ddd;
}

.tiptap-footer {
  border: 1px solid #ddd;
  border-top: none;
  padding: 8px;
}

:deep(.ProseMirror) {
  border: 1px solid #ddd;
  padding: 12px;
  min-height: 100px;
  outline: none !important;
}

:deep(.ProseMirror:focus) {
  border-top: none;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  outline: none !important;
}

/* Enhanced styles for images in defect descriptions */
:deep(.defect-description-image) {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  border-radius: 8px !important;
  margin: 12px 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  object-fit: contain !important;
  max-height: 400px !important;
  display: block !important;
  position: relative !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  cursor: pointer !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

:deep(.defect-description-image:hover) {
  transform: scale(1.02) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

:deep(.ProseMirror a) {
  color: #1976D2;
  text-decoration: underline;
  word-break: break-word;
}

:deep(.ProseMirror a:hover) {
  color: #1565C0;
}

/* Comprehensive image styling for all images in ProseMirror */
:deep(.ProseMirror img) {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  border-radius: 8px !important;
  margin: 12px 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  object-fit: contain !important;
  max-height: 400px !important;
  display: block !important;
  position: relative !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  cursor: pointer !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

:deep(.ProseMirror img:hover) {
  transform: scale(1.02) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Ensure proper text wrapping */
:deep(.ProseMirror p) {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Container overflow control */
:deep(.ProseMirror) {
  overflow: hidden !important;
  word-wrap: break-word !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* Force the TipTap root container to be constrained */
.tiptap-theme {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* Force all content to be contained */
:deep(.ProseMirror *) {
  max-width: 100% !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
}

/* Ensure all content is properly contained */
:deep(.ProseMirror *:not(img)) {
  max-width: 100% !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}

/* Style for the image button */
.tiptap-footer button {
  padding: 4px 8px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  font-size: 16px;
}

.tiptap-footer button:hover {
  background-color: #f0f0f0;
}
</style>

