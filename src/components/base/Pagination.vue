<template>
  <div
    v-if="totalPages > 0"
    class="pagination-bar d-flex align-center justify-end mt-4"
    style="gap: 16px;"
  >
    <span class="rows-label">{{ $t('pagination.rowsPerPage') }}:</span>
    <v-select
      v-model="localItemsPerPage"
      :items="pageSizes"
      hide-details
      dense
      class="rows-select"
      style="max-width: 72px; min-width: 60px; margin: 0 8px; font-size: 12px !important;"
      @change="onPageSizeChange"
    />
    <span class="page-info">{{ pageRangeText }}</span>
    <div
      class="pagination-buttons d-flex align-center"
      style="gap: 8px;"
    >
      <v-btn
        icon
        class="custom-pagination-btn"
        :disabled="localPage === 1"
        @click="onPageChange(localPage - 1)"
      >
        <span :aria-label="$t('pagination.previous')">&#8592;</span>
      </v-btn>
      <v-btn
        icon
        class="custom-pagination-btn"
        :disabled="localPage === totalPages || totalPages === 0"
        @click="onPageChange(localPage + 1)"
      >
        <span :aria-label="$t('pagination.next')">&#8594;</span>
      </v-btn>
    </div>
  </div>
</template>

<script>
import { ref, watch, computed } from 'vue';
export default {
  name: 'Pagination',
  props: {
    page: {
      type: Number,
      default: 1
    },
    itemsPerPage: {
      type: Number,
      default: 10
    },
    totalPages: {
      type: Number,
      default: 1
    },
    totalItems: {
      type: Number,
      default: 0
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    }
  },
  emits: ['update:pagination'],
  setup(props, { emit }) {
    const localPage = ref(props.page);
    const localItemsPerPage = ref(props.itemsPerPage);

    watch(() => props.page, (val) => { localPage.value = val; });
    watch(() => props.itemsPerPage, (val) => { localItemsPerPage.value = val; });

    function onPageChange(page) {
      emit('update:pagination', { page, itemsPerPage: localItemsPerPage.value });
    }
    function onPageSizeChange(perPage) {
      emit('update:pagination', { page: 1, itemsPerPage: perPage }); // Reset to first page
    }

    // Compute the range text (e.g., '1-10 of 12')
    const pageRangeText = computed(() => {
      const start = props.totalItems === 0 ? 0 : (props.page - 1) * props.itemsPerPage + 1;
      const end = Math.min(props.page * props.itemsPerPage, props.totalItems);
      return `${start}-${end} of ${props.totalItems}`;
    });

    return {
      localPage,
      localItemsPerPage,
      onPageChange,
      onPageSizeChange,
      pageRangeText
    };
  }
};
</script>

<style scoped>
.pagination-bar {
  gap: 16px;
  font-size: 12px;
}
.rows-label {
  margin-right: 2px;
  color: #444;
  font-size: 12px;
}
.rows-select {
  vertical-align: middle;
}
.page-info {
  min-width: 90px;
  text-align: center;
  color: #444;
}
.pagination-arrows {
  --v-pagination-item-bg: transparent;
  --v-pagination-item-color: #444;
  --v-pagination-item-active-bg: #eee;
  --v-pagination-item-active-color: #222;
}
.custom-pagination-btn {
  min-width: 32px;
  height: 32px;
  border-radius: 50%;
  color: #444;
  background: transparent;
  transition: background 0.2s;
}
.custom-pagination-btn:hover:not(:disabled) {
  background: #eee;
}
.custom-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style> 