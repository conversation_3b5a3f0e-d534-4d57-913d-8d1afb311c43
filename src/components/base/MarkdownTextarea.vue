<template>
  <div class="tiptap-markdown-wrapper">
    <div class="tiptap-input-container">
      <editor-content
        :editor="editor"
        :class="[editorClass, 'tiptap-editor']"
      />
      <div 
        v-if="$slots.append" 
        class="tiptap-append-slot"
      >
        <slot name="append" />
      </div>
    </div>
    
    <!-- Image upload progress indicator -->
    <div
      v-if="uploadingImages.length > 0"
      class="upload-progress"
    >
      <div
        v-for="upload in uploadingImages"
        :key="upload.id"
        class="upload-item"
      >
        <span>{{ upload.name }}</span>
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: upload.progress + '%' }"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Editor, EditorContent } from '@tiptap/vue-2';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import { Markdown } from 'tiptap-markdown';
import UploadImage from 'tiptap-extension-upload-image';
import GlobalDragHandle from 'tiptap-extension-global-drag-handle';
import { 
  formatContent, 
  isTestRailMarkdown, 
  convertTestRailMarkdown,
  convertToTestRailMarkdown,
  uploadImage,
  validateImageFile,
  extractTestRailImages
} from '@/utils/markdown';

export default {
  name: 'MarkdownTextarea',
  components: {
    EditorContent
  },
  props: {
    value: { type: String, default: '' },
    placeholder: { type: String, default: '' },
    autoGrow: { type: Boolean, default: true },
    rows: { type: [Number, String], default: 3 },
    rules: { type: Array, default: () => [] },
    supportTestRail: { type: Boolean, default: true },
    textareaClass: { type: String, default: '' },
    editorClass: { type: String, default: '' },
    enableImageUpload: { type: Boolean, default: true },
    maxImageSize: { type: Number, default: 10 * 1024 * 1024 }, // 10MB
    allowedImageTypes: { type: Array, default: () => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] }
  },
  data() {
    return {
      editor: null,
      showPreview: false,
      showMarkdownView: false,
      showHelp: false,
      markdownFormat: null,
      markdownContent: '',
      uploadingImages: [],
      uploadCounter: 0
    };
  },
  computed: {
    content: {
      get() { return this.value; },
      set(val) { this.$emit('input', val); }
    },
    previewContent() { 
      return formatContent(this.markdownContent || this.getMarkdownFromEditor()); 
    },
    // Upload configuration computed from context
    uploadHandle() {
      return this.$route?.params?.handle || '';
    },
    uploadApiService() {
      // Determine the appropriate service based on route context
      if (this.$route?.query?.isExecution && this.$route?.query?.executionId) {
        // For executions, we need to import the service dynamically
        return this.getExecutionsService();
      } else {
        // For cases, we need to import the service dynamically  
        return this.getCaseService();
      }
    },
    uploadParams() {
      const handle = this.$route?.params?.handle;
      const projectKey = this.$route?.params?.key;
      
      if (this.$route?.query?.isExecution && this.$route?.query?.executionId) {
        return {
          handle,
          projectKey,
          executionUid: this.$route.query.executionId
        };
      } else {
        return {
          handle,
          projectKey,
          caseId: this.$route.params.uid,
          // Add caseId if available from parent component context
          ...(this.$parent?.editItem?.testCaseRef && { caseId: this.$parent.editItem.testCaseRef })
        };
      }
    }
  },
  watch: {
    content: {
      immediate: true,
      handler(val) {
        if (this.editor && val !== this.getMarkdownFromEditor()) {
          this.setEditorContent(val);
        }
        if (this.supportTestRail && val) {
          this.markdownFormat = isTestRailMarkdown(val) ? 'TestRail' : 'Markdown';
        }
      }
    }
  },
  mounted() {
    this.initEditor();
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor.destroy();
    }
  },
  methods: {
    initEditor() {
      const extensions = [
        StarterKit,
        Image.configure({
          inline: false,
          allowBase64: false,
          HTMLAttributes: {
            class: 'uploaded-image'
          }
        }),
        Markdown.configure({
          html: true,
          tightLists: true,
          bulletListMarker: '-',
          linkify: false,
          breaks: true,
          transformPastedText: true,
          transformCopiedText: true
        })
      ];

      // Add image upload extension if enabled
      if (this.enableImageUpload) {
        extensions.push(
          UploadImage.configure({
            acceptMimes: this.allowedImageTypes,
            upload: this.handleImageUpload,
            onError: this.handleUploadError
          })
        );
      }

      // Add global drag handle
      extensions.push(
        GlobalDragHandle.configure({
          dragHandleWidth: 20,
          scrollTreshold: 100
        })
      );

      this.editor = new Editor({
        content: this.processInitialContent(this.content),
        extensions,
        onUpdate: () => {
          const markdown = this.getMarkdownFromEditor();
          this.content = markdown;
          this.updateMarkdownFormat(markdown);
          
          // Handle auto-grow
          if (this.autoGrow) {
            this.$nextTick(() => {
              this.adjustHeight();
            });
          }
        },
        editorProps: {
          attributes: {
            class: `tiptap-editor ${this.editorClass}`,
            style: `min-height: ${this.rows * 24}px;`,
            'data-placeholder': this.placeholder
          },
          handleDrop: this.handleDrop,
          handlePaste: this.handlePaste
        }
      });
    },

    processInitialContent(content) {
      if (!content) return '';
      
      // If it's TestRail markdown, convert it to standard markdown for TipTap
      if (this.supportTestRail && isTestRailMarkdown(content)) {
        return convertTestRailMarkdown(content);
      }
      
      return content;
    },

    setEditorContent(content) {
      if (!this.editor) return;
      
      const processedContent = this.processInitialContent(content);
      this.editor.commands.setContent(processedContent);
    },

    getMarkdownFromEditor() {
      if (!this.editor) return '';
      return this.editor.storage.markdown.getMarkdown();
    },

    updateMarkdownFormat(content) {
      if (this.supportTestRail && content) {
        this.markdownFormat = isTestRailMarkdown(content) ? 'TestRail' : 'Markdown';
      }
    },

    async handleImageUpload(file) {
      try {
        // Validate file
        validateImageFile(file, this.maxImageSize, this.allowedImageTypes);
        
        // Add to uploading list
        const uploadId = ++this.uploadCounter;
        this.uploadingImages.push({
          id: uploadId,
          name: file.name,
          progress: 0
        });

        // Simulate progress (you can implement real progress tracking)
        const progressInterval = setInterval(() => {
          const upload = this.uploadingImages.find(u => u.id === uploadId);
          if (upload && upload.progress < 90) {
            upload.progress += 10;
          }
        }, 100);

        let imageUrl;
        
        // Check if we have the required parameters for the new upload method
        if (this.uploadHandle && this.$store) {
          try {
            // Get the API service (this is now async)
            const apiService = await this.uploadApiService;
            
            // Use the new upload method with Vuex store
            imageUrl = await uploadImage(file, {
              handle: this.uploadHandle,
              apiService: apiService,
              params: this.uploadParams,
              store: this.$store,
              mediaType: 'attachment'
            });
          } catch (serviceError) {
            console.error('Failed to get API service for upload:', serviceError);
            throw new Error('Upload service not available. Please ensure you are in a valid context for file uploads.');
          }
        } else {
          // No fallback available - throw error
          const missingParams = [];
          if (!this.uploadHandle) missingParams.push('uploadHandle');
          if (!this.$store) missingParams.push('store');
          
          throw new Error(`Upload not available. Missing required parameters: ${missingParams.join(', ')}`);
        }
        
        // Clear progress
        clearInterval(progressInterval);
        this.uploadingImages = this.uploadingImages.filter(u => u.id !== uploadId);
        
        // Emit upload success event
        this.$emit('image-uploaded', { file, url: imageUrl });
        return imageUrl;
      } catch (error) {
        // Remove from uploading list
        const uploadId = this.uploadCounter;
        this.uploadingImages = this.uploadingImages.filter(u => u.id !== uploadId);
        
        // Emit error event
        this.$emit('image-upload-error', { file, error });
        
        throw error;
      }
    },

    handleUploadError(error) {
      console.error('Image upload error:', error);
      this.$emit('image-upload-error', { error });
    },

    handleDrop(view, event, slice, moved) {
      if (!this.enableImageUpload || moved) return false;
      
      const files = Array.from(event.dataTransfer?.files || []);
      const imageFiles = files.filter(file => 
        this.allowedImageTypes.includes(file.type)
      );
      
      if (imageFiles.length === 0) return false;
      
      event.preventDefault();
      
      imageFiles.forEach(async (file) => {
        try {
          const url = await this.handleImageUpload(file);
          
          // Insert image at drop position
          const coordinates = view.posAtCoords({ 
            left: event.clientX, 
            top: event.clientY 
          });
          
          if (coordinates) {
            const { schema } = view.state;
            const node = schema.nodes.image.create({ src: url, alt: file.name });
            const transaction = view.state.tr.insert(coordinates.pos, node);
            view.dispatch(transaction);
          }
        } catch (error) {
          console.error('Drop upload failed:', error);
        }
      });
      
      return true;
    },

    handlePaste(view, event) {
      if (!this.enableImageUpload) return false;
      
      const items = Array.from(event.clipboardData?.items || []);
      const imageItems = items.filter(item => 
        item.type.startsWith('image/')
      );
      
      if (imageItems.length === 0) return false;
      
      event.preventDefault();
      
      imageItems.forEach(async (item) => {
        const file = item.getAsFile();
        if (!file) return;
        
        try {
          const url = await this.handleImageUpload(file);
          
          // Insert image at cursor position
          const { schema } = view.state;
          const node = schema.nodes.image.create({ 
            src: url, 
            alt: `Pasted image ${Date.now()}` 
          });
          const transaction = view.state.tr.replaceSelectionWith(node);
          view.dispatch(transaction);
        } catch (error) {
          console.error('Paste upload failed:', error);
        }
      });
      
      return true;
    },

    handleMarkdownInput(value) {
      this.markdownContent = value;
      if (this.editor) {
        this.editor.commands.setContent(this.processInitialContent(value));
      }
      this.content = value;
    },

    addLink() {
      const url = prompt(this.$t('formatting.enterUrl'), 'https://');
      if (url) {
        this.editor?.chain().focus().setLink({ href: url }).run();
      }
    },

    toggleMarkdownView() {
      this.showMarkdownView = !this.showMarkdownView;
      this.showPreview = false;
      
      if (this.showMarkdownView) {
        this.markdownContent = this.getMarkdownFromEditor();
      } else {
        // When switching back to editor, update content
        this.setEditorContent(this.markdownContent);
      }
    },

    togglePreview() {
      this.showPreview = !this.showPreview;
      this.showMarkdownView = false;
    },

    convertFormat(toTestRail = false) {
      const currentContent = this.getMarkdownFromEditor();
      let convertedContent;
      
      if (toTestRail && this.markdownFormat !== 'TestRail') {
        convertedContent = convertToTestRailMarkdown(currentContent);
      } else if (!toTestRail && this.markdownFormat === 'TestRail') {
        convertedContent = convertTestRailMarkdown(currentContent);
      } else {
        return;
      }
      
      this.content = convertedContent;
      this.setEditorContent(convertedContent);
    },

    adjustHeight() {
      if (!this.editor || !this.autoGrow) return;
      
      const editorElement = this.editor.view.dom;
      if (editorElement) {
        const minHeight = this.rows * 24;
        const contentHeight = editorElement.scrollHeight;
        const newHeight = Math.max(minHeight, contentHeight);
        
        editorElement.style.minHeight = `${newHeight}px`;
      }
    },

    // Extract TestRail images for migration
    getTestRailImages() {
      return extractTestRailImages(this.content);
    },

    // Helper methods to get API services
    async getCaseService() {
      // Import and return case service
      if (!this._caseService) {
        const { default: makeCasesService } = await import('@/services/api/case');
        this._caseService = makeCasesService(this.$api);
      }
      return this._caseService;
    },

    async getExecutionsService() {
      // Import and return executions service
      if (!this._executionsService) {
        const { default: makeExecutionsService } = await import('@/services/api/execution');
        this._executionsService = makeExecutionsService(this.$api);
      }
      return this._executionsService;
    }
  }
};
</script>

<style scoped>
.tiptap-markdown-wrapper { 
  position: relative; 
  border: none;
  border-radius: 4px;
  background-color: #f9f9fb;
  width: 100%;
}

.tiptap-input-container {
  position: relative;
  display: flex;
  align-items: flex-start;
  width: 100%;
}

.tiptap-editor {
  min-height: 130px;
  flex: 1;
  padding-right: 48px; /* Make space for append slot */
}

.tiptap-append-slot {
  position: absolute;
  right: 10px;
  top: 10px;
  display: flex;
  align-items: center;
  z-index: 1;
}

.upload-progress {
  padding: 8px 12px;
  border-top: 1px solid #e0e0e0;
  background-color: #f5f5f5;
}

.upload-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.upload-item:last-child {
  margin-bottom: 0;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #1976d2;
  transition: width 0.3s ease;
}

/* Ensure the editor content area has proper padding */
::v-deep .ProseMirror {
  padding: 12px;
  outline: none;
  min-height: inherit;
  border: none;
  background: transparent;
}

/* Style the editor to look more like v-textarea */
::v-deep .ProseMirror p {
  margin: 0;
  line-height: 1.5;
}

::v-deep .ProseMirror:empty:before {
  content: attr(data-placeholder);
  color: #9e9e9e;
  pointer-events: none;
  position: absolute;
}

/* Focus styles */
::v-deep .ProseMirror:focus {
  outline: none;
}

.tiptap-markdown-wrapper:focus-within {
  border-color: #1976d2;
}

/* Image styles */
::v-deep .uploaded-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

/* Drag handle styles */
::v-deep .drag-handle {
  position: absolute;
  left: -30px;
  top: 0;
  width: 20px;
  height: 20px;
  background: #e0e0e0;
  border-radius: 2px;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

::v-deep .drag-handle:hover {
  background: #bdbdbd;
}

::v-deep .drag-handle:active {
  cursor: grabbing;
}

::v-deep .ProseMirror:hover .drag-handle {
  opacity: 1;
}

/* Image uploading placeholder */
::v-deep .image-uploading {
  position: relative;
  background: #f5f5f5;
  border: 2px dashed #ccc;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  color: #666;
}

::v-deep .image-uploading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #ccc;
  border-top-color: #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style> 