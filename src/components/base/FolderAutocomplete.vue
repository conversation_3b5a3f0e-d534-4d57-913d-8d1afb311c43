<template>
  <div class="folder-autocomplete">
    <!-- Dropdown Menu -->
    <v-menu
      v-model="isMenuOpen"
      :close-on-content-click="false"
      offset-y
      min-width="379"
      max-height="300"
      content-class="folder-dropdown"
      :nudge-bottom="2"
    >
      <template #activator="{ on, attrs }">
        <!-- Display Input Field -->
        <div
          class="input-wrapper"
          v-bind="attrs"
          v-on="on"
        >
          <input
            :value="displayText"
            :placeholder="placeholder"
            readonly
            class="custom-input field-theme"
            :class="{ 'error-info': hasError }"
          >
          <v-icon
            class="dropdown-icon"
            :class="{ 'rotated': isMenuOpen }"
          >
            mdi-chevron-down
          </v-icon>
        </div>
      </template>

      <div class="dropdown-content">
        <div class="search-wrapper">
          <v-text-field
            v-model="searchText"
            :placeholder="$t('searchFolders')"
            prepend-inner-icon="mdi-magnify"
            dense
            hide-details
            clearable
            autofocus
            background-color="#F9F9FB"
            class="mr-0 custom_input rounded-lg w-full field-theme"
            @input="handleSearchInput"
            @keydown.esc="isMenuOpen = false"
          />
        </div>

        <!-- Loading State -->
        <div
          v-if="isLoading"
          class="loading-wrapper"
        >
          <v-progress-circular 
            indeterminate 
            size="24" 
            width="2"
            color="primary"
          />
          <span class="loading-text">{{ $t('searching') || 'Searching...' }}</span>
        </div>

        <!-- Folder List -->
        <ul
          v-else-if="allFolders.length > 0"
          class="folder-list"
        >
          <li
            v-for="folder in allFolders"
            :key="folder.uid"
            class="folder-item"
            :class="{ 'selected': folder.uid === internalValue }"
            @click="selectFolder(folder)"
          >
            <div class="folder-content">
              <div class="folder-name">
                {{ folder.name }}
              </div>
              <div
                v-if="folder.path && folder.path !== folder.name"
                class="folder-path"
              >
                <v-tooltip
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      class="folder-path-text"
                      v-on="on"
                    >
                      {{ folder.path }}
                    </span>
                  </template>
                  <span>{{ folder.path }}</span>
                </v-tooltip>
              </div>
            </div>
          </li>
        </ul>

        <!-- No Data State -->
        <div
          v-else
          class="no-data"
        >
          <div class="no-data-text">
            {{ searchText && searchText.length > 0 ? ($t('noFoldersFound') || 'No folders found') : ($t('startTypingToSearch') || 'Start typing to search') }}
          </div>
        </div>
      </div>
    </v-menu>

    <!-- Error Messages -->
    <div
      v-if="errorMessages.length > 0"
      class="error-messages"
    >
      <div
        v-for="(message, index) in errorMessages"
        :key="index"
        class="error-text"
      >
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script>
import { debounce } from 'lodash';
import makeFoldersService from '@/services/api/folder';
import { showErrorToast } from '@/utils/toast';

export default {
  name: 'FolderAutocomplete',
  props: {
    value: {
      type: [String, Number],
      default: null,
    },
    placeholder: {
      type: String,
      default: function() {
        return this.$t('selectFolder') || 'Select folder';
      },
    },
    label: {
      type: String,
      default: function() {
        return this.$t('folder') || 'Folder';
      },
    },
    rules: {
      type: Array,
      default: () => [],
    },
    handle: {
      type: String,
      required: true,
    },
    projectKey: {
      type: String,
      required: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      internalValue: this.value,
      searchText: '',
      isLoading: false,
      isMenuOpen: false,
      allFolders: [],
      initialFolders: [],
      searchResults: [],
      folderService: null,
      selectedFolder: null,
    };
  },
  computed: {
    displayText() {
      if (this.selectedFolder) {
        return this.selectedFolder.name;
      }
      return '';
    },
    errorMessages() {
      if (!this.rules || this.rules.length === 0) return [];
      
      const errors = [];
      for (const rule of this.rules) {
        const result = rule(this.internalValue);
        if (result !== true) {
          errors.push(result);
        }
      }
      return errors;
    },
    hasError() {
      return this.errorMessages.length > 0;
    },
  },
  watch: {
    value(newVal) {
      this.internalValue = newVal;
      this.updateSelectedFolder();
    },
    internalValue() {
      this.updateSelectedFolder();
    },
    isMenuOpen(newVal) {
      if (newVal) {
        // Menu opened - reset search and show initial folders
        this.searchText = '';
        this.allFolders = [...this.initialFolders];
      }
    },
  },
  async created() {
    this.folderService = makeFoldersService(this.$api);
    await this.loadInitialFolders();
    this.updateSelectedFolder();

    this.debouncedSearch = debounce(this.searchFolders, 300);
  },

  methods: {
    async loadInitialFolders() {
      try {
        this.isLoading = true;
        
        // Get hierarchical folder structure from backend
        const response = await this.folderService.getProjectFolders(this.handle, this.projectKey, {returnType: 'all', limit: 250, includePath: true});
        
        if (response.status !== 200 || !response.data?.folders) {
          showErrorToast(
            this.$swal, 
            'fetchError', 
            { item: this.$t('projectFolders') },
            response?.error || response?.data?.error
          );
          throw new Error('Invalid response structure from folder service');
        }
        
        const folders = response.data.folders;

        // Since the backend is returning flat folders with completePath, use them directly
        this.initialFolders = folders.map(folder => ({
          ...folder,
          path: this.buildDisplayPath(folder)
        }));
        
        // Initialize display list with initial folders
        this.allFolders = [...this.initialFolders];
      } catch (error) {
        console.error('Failed to load initial folders:', error);
        showErrorToast(
          this.$swal, 
          'fetchError', 
          { item: this.$t('projectFolders') || 'project folders' }, 
          error?.response?.data
        );
        this.allFolders = [];
        this.initialFolders = [];
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Handle search input changes
     */
    handleSearchInput(searchValue) {
      if (!searchValue || searchValue.length < 1) {
        // Show initial folders when search is cleared
        this.allFolders = [...this.initialFolders];
        this.isLoading = false;
        return;
      }
      
      // Show loading immediately when user starts typing
      this.isLoading = true;
      
      // Use debounced search
      this.debouncedSearch(searchValue);
    },

    /**
     * Search folders based on query
     */
    async searchFolders(query) {
      if (!query || query.length < 1) {
        return;
      }

      try {
        // Loading state is already set in handleSearchInput for immediate feedback
        
        // First, filter initial folders locally
        const localResults = this.initialFolders.filter(folder =>
          folder.name.toLowerCase().includes(query.toLowerCase())
        );
        
        // Then search from backend for more comprehensive results
        const response = await this.folderService.searchFolders(
          this.handle,
          this.projectKey,
          { 
            q: query, 
            includePath: true,
            limit: 10,
            offset: 0
          }
        );
        
        if (response.status === 200) {
          const backendResults = response.data.items || [];
          
          // Transform backend results to match expected format
          const transformedResults = backendResults.map(folder => ({
            name: folder.name,
            uid: folder.uid,
            parentUid: folder.parentUid,
            // Build display path from completePath array
            path: this.buildDisplayPath(folder),
          }));
          
          // Combine and deduplicate results
          const combinedResults = this.deduplicateFolders([
            ...localResults,
            ...transformedResults
          ]);
          
          this.allFolders = combinedResults;
        } else {
          this.allFolders = localResults;
        }
      } catch (error) {
        // On error, fall back to local search results
        const localResults = this.initialFolders.filter(folder =>
          folder.name.toLowerCase().includes(query.toLowerCase())
        );
        this.allFolders = localResults;
        
        console.warn('Folder search failed, using local results:', error);
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Build display path from completePath array returned by backend
     */
    buildDisplayPath(folder) {
      if (folder.completePath && Array.isArray(folder.completePath) && folder.completePath.length > 0) {
        // Build path from completePath array: "Parent1 / Parent2 / FolderName"
        const pathSegments = folder.completePath.map(parent => parent.name);
        return pathSegments.join(' / ');
      }
      
      // Fallback to just the folder name if no completePath
      return folder.name;
    },

    deduplicateFolders(folders) {
      const seen = new Set();
      return folders.filter(folder => {
        if (seen.has(folder.uid)) {
          return false;
        }
        seen.add(folder.uid);
        return true;
      });
    },

    /**
     * Select a folder
     */
    selectFolder(folder) {
      this.internalValue = folder.uid;
      this.selectedFolder = folder;
      this.isMenuOpen = false; // Close the menu
      
      this.$emit('input', folder.uid);
      this.$emit('change', folder.uid);
      this.$emit('folder-selected', folder);
    },

    /**
     * Clear selection
     */
    clearSelection() {
      this.internalValue = null;
      this.selectedFolder = null;
      
      this.$emit('input', null);
      this.$emit('change', null);
      this.$emit('folder-selected', null);
    },

    /**
     * Update selected folder object based on current value
     */
    updateSelectedFolder() {
      if (this.internalValue && this.allFolders.length > 0) {
        this.selectedFolder = this.allFolders.find(folder => folder.uid === this.internalValue) || null;
      } else if (this.internalValue && this.initialFolders.length > 0) {
        // Fallback to searching in initial folders if not found in current list
        this.selectedFolder = this.initialFolders.find(folder => folder.uid === this.internalValue) || null;
      } else {
        this.selectedFolder = null;
      }
      
      // If we still don't have a selected folder but have a value, try to load it
      if (this.internalValue && !this.selectedFolder && this.folderService) {
        this.loadSelectedFolderById(this.internalValue);
      }
    },

    /**
     * Load a specific folder by ID (useful when value is set but folder not in current list)
     */
    async loadSelectedFolderById(folderId) {
      try {
        const response = await this.folderService.getFolder(this.handle, this.projectKey, folderId);
        if (response.status === 200 && response.data) {
          const folder = response.data;
          this.selectedFolder = {
            name: folder.name,
            uid: folder.uid,
            parentUid: folder.parentUid,
            path: folder.name, // For individual folder loads, just use the name
          };
        }
      } catch (error) {
        console.warn('Failed to load folder by ID:', error);
        showErrorToast(
          this.$swal, 
          'fetchError', 
          { item: this.$t('projectFolders') || 'project folders' }, 
          error
        );
        this.selectedFolder = null;
      }
    },
  },
};
</script>

<style scoped>
.folder-autocomplete {
  position: relative;
  width: 100%;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  background-color: #F9F9FB;
  border: none;
  border-radius: 8px;
  height: 38px;
  transition: border-color 0.2s ease;
}

.input-wrapper:hover {
  border-color: #D1D5DB;
}

.input-wrapper.focused {
  border-color: #3B82F6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.custom-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  padding: 8px 12px;
  font-family: Inter, sans-serif;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
}

.custom-input::placeholder {
  color: #9CA3AF;
}

.custom-input.error-info {
  color: #EF4444;
}

.dropdown-icon {
  margin-right: 8px;
  color: #6B7280;
  transition: transform 0.2s ease;
  font-size: 20px;
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

.clear-icon {
  margin-right: 8px;
  color: #6B7280;
  cursor: pointer;
  font-size: 18px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.clear-icon:hover {
  opacity: 1;
  color: #374151;
}

.error-messages {
  margin-top: 4px;
}

.error-text {
  color: #EF4444;
  font-size: 12px;
  line-height: 1.4;
}



/* Dropdown Styles */
.dropdown-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-width: 280px;
}

.search-wrapper {
  padding: 12px;
}

.search-input {
  font-size: 14px;
}

.loading-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 12px;
}

.loading-text {
  font-size: 14px;
  color: #6B7280;
}

.folder-list {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 200px;
  overflow-y: auto;
}

.folder-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #F9FAFB;
  transition: background-color 0.15s ease;
}

.folder-item:last-child {
  border-bottom: none;
}

.folder-item:hover {
  background-color: #F9FAFB;
}

.folder-item.selected {
  background-color: #EBF4FF;
  color: #1D4ED8;
}

.folder-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.folder-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.folder-item.selected .folder-name {
  color: #1D4ED8;
}

.folder-path {
  font-size: 12px;
  color: #6B7280;
  opacity: 0.8;
  max-width: 100%;
  overflow: hidden;
}

.folder-item.selected .folder-path {
  color: #3B82F6;
}

.folder-path-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.no-data {
  padding: 20px;
  text-align: center;
}

.no-data-text {
  font-size: 14px;
  color: #6B7280;
  font-style: italic;
}

/* Custom scrollbar for folder list */
.folder-list::-webkit-scrollbar {
  width: 6px;
}

.folder-list::-webkit-scrollbar-track {
  background: #F9FAFB;
}

.folder-list::-webkit-scrollbar-thumb {
  background: #D1D5DB;
  border-radius: 3px;
}

.folder-list::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}

/* Global menu styles */
:global(.folder-dropdown) {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
}
</style> 
