<template>
  <BaseDialog
    v-model="dialog"
    max-width="500px"
  >
    <v-card class="bg-white">
      <v-card-text class="py-8 px-sm-10">
        <v-flex class="d-flex align-start">
          <p class="ma-0 font-weight-bold text-h6 text-sm-h5 text-start black--text">
            {{ title }}
          </p>
          <v-icon
            class="mt-1 ml-4 pointer"
            @click="$emit('close')"
          >
            mdi-close
          </v-icon>
        </v-flex>
        <slot name="content">
          <v-flex class="mt-4">
            <p
              v-if="description" 
              class="text-start"
            >
              {{ description }}
            </p>
            <p
              v-else 
              class="text-start"
            >
              {{ $t('discard_description') }}
            </p>
          </v-flex>
        </slot>
        <slot name="footer">
          <v-row>
            <v-col cols="6">
              <v-btn
                depressed
                width="100%"
                class="text-capitalize fw-semibold rounded-lg black--text mt-2"
                color="#F2F4F7"
                elevation="0"
                height="40"
                @click="$emit('close')"
              >
                {{ $t('discard_cancel') }}
              </v-btn>
            </v-col>
            <v-col cols="6">
              <v-btn
                depressed
                width="100%"
                class="text-capitalize fw-semibold rounded-lg white--text mt-2"
                color="danger"
                height="40"
                elevation="0"
                @click="$emit('handleConfirmClick')"
              >
                {{ confirmText ? confirmText : $t('discard_confrim') }}
              </v-btn>
            </v-col>
          </v-row>
        </slot>
      </v-card-text>
    </v-card>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/base/BaseDialog.vue';

export default {
  components: {
    BaseDialog,
  },
  props: {
    value: Boolean,
    title: {
      type: String,
    },
    description: {
      type: String,
    },
    confirmText: {
      type: String,
    },
  },
  data() {
    return {
      dialog: this.value,
    };
  },
  watch: {
    value(newVal) {
      this.dialog = newVal;
    },
    dialog(newVal) {
      this.$emit('input', newVal);
    },
  },
};
</script>

<style scoped></style>
