<template>
  <div class="file-preview">
    <div
      v-if="internalFiles.length"
      class="file-previews mt-4 pa-3"
    >
      <div class="preview-item row justify-start">
        <div
          v-for="(file, index) in internalFiles"
          :key="index"
          class="preview-container mr-3 mb-3"
        >
          <template v-if="isImage(file)">
            <div class="h-full">
              <a 
                :href="filePreview(file)" 
                target="_blank" 
                rel="noopener noreferrer"
                class="cursor-pointer"
              >
                <img
                  :src="filePreview(file)"
                  class="preview-image"
                >
              </a>
              <v-btn
                icon
                small
                color="red"
                class="delete-btn"
                @click="removeFile(index)"
              >
                <removeIcon />
              </v-btn>
            </div>
          </template>
          <template v-else-if="isVideo(file)">
            <div class="h-full">
              <a 
                :href="filePreview(file)" 
                target="_blank" 
                rel="noopener noreferrer"
                class="cursor-pointer"
              >
                <video
                  :src="filePreview(file)"
                  class="preview-video"
                />
              </a>
              <v-btn
                icon
                small
                color="red"
                class="delete-btn"
                @click="removeFile(index)"
              >
                <removeIcon />
              </v-btn>
            </div>
          </template>
          <template v-else-if="isPdf(file)">
            <div class="position-relative">
              <VuePdfEmbed
                v-if="file.previewUrl"
                :source="file.previewUrl"
                class="pdf-preview"
              />
              <v-btn
                v-if="!isPreview && file.previewUrl"
                icon
                small
                color="red"
                class="delete-btn"
                @click="removeFile(index)"
              >
                <removeIcon />
              </v-btn>
            </div>
          </template>
          <div 
            v-else
            class="h-full non-image-preview"
          >
            <div class="file-placeholder">
              {{ $t("file") }}: {{ file.name || file.type }}
            </div>
            <v-btn
              icon
              small
              color="red"
              class="delete-btn"
              @click="removeFile(index)"
            >
              <removeIcon />
            </v-btn>
          </div>
        </div>
      </div>
    </div>

    <div 
      class="drop-zone"
      :class="{ 'drag-over': isDragOver }"
      @dragover.prevent="handleDragOver"
      @dragenter.prevent="handleDragEnter"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <v-file-input
        ref="fileInput"
        class="file-input-bg field-theme-file rounded-lg"
        dense
        placeholder="Browse files"
        hide-details
        multiple
        :accept="type"
        @change="syncFilesWithParent"
      >
        <template #prepend-inner>
          <slot />
        </template>
      </v-file-input>
    </div>
  </div>
</template>

<script>
import removeIcon from '@/assets/svg/remove.svg';
import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'

export default {
    components: {
      removeIcon,
      VuePdfEmbed
    },
    props: {
      files: {
          type: Array,
          default: () => [],
      },
      type: {
          type: String,
          default: '',
      },
      isPreview: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        isDragOver: false,
      };
    },
    computed: {
      internalFiles: {
        get() {
          return this.files;
        },
        set(newFiles) {
          this.$emit('update:files', newFiles);
        },
      },
    },
    methods: {
      isImage(file) {
        return file.type.startsWith("image/");
      },
      filePreview(file) {
        if (!file.previewUrl && !this.isPreview) {
          return URL.createObjectURL(file);
        }
        return file.previewUrl;
      },
      isVideo(file) {
        return file.type.startsWith("video/");
      },
      isPdf(file) {
        return file.type === "application/pdf";
      },
      resetFileInput() {
        this.$refs.fileInput.reset();
        this.internalFiles = [];
      },
      removeFile(index) {
        const file = this.internalFiles[index];
        if (file.previewUrl) {
          URL.revokeObjectURL(file.previewUrl);
        }
        const updatedFiles = this.internalFiles.slice();
        updatedFiles.splice(index, 1);
        this.internalFiles = updatedFiles;
        if(file.uid)
          this.$emit('deleteAttachment', file.uid)
      },
      syncFilesWithParent(event) {
        const newFiles = event.map((file) => {
          if (!file.previewUrl) {
            file.previewUrl = URL.createObjectURL(file);
          }
          return file;
        });
        this.internalFiles = [...this.internalFiles, ...newFiles];
        this.$emit('onFileChange', this.internalFiles);
      },
      handleDragOver(event) {
        event.preventDefault();
        this.isDragOver = true;
      },
      handleDragEnter(event) {
        event.preventDefault();
        this.isDragOver = true;
      },
      handleDragLeave(event) {
        event.preventDefault();
        if (!event.currentTarget.contains(event.relatedTarget)) {
          this.isDragOver = false;
        }
      },
      handleDrop(event) {
        event.preventDefault();
        this.isDragOver = false;
        
        const files = Array.from(event.dataTransfer.files);
        if (files.length > 0) {
          this.syncFilesWithParent(files);
        }
      },
    },
};
</script>

<style scoped>
  .preview-video {
    max-width: 100%;
    border-radius: 4px;
  }

  .drop-zone {
    transition: all 0.3s ease;
    border: 2px dashed transparent;
    border-radius: 8px;
  }

  .drop-zone.drag-over {
    border-color: #1976d2;
    background-color: rgba(25, 118, 210, 0.1);
  }
</style>