<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-3">
      <span class="font-weight-regular fs-14px text-theme-secondary">
        {{ $t("attachments") }}
      </span>
      <v-btn
        class="text-capitalize bg-white px-2 toggle-btn"
        depressed
        color="primary"
        text
        @click="openDialog"
        :class="{
          invisible: hideAddAttachment
        }"
      >
        <div class="d-flex align-center">
          <PlusBlueIcon />
          <span class="ml-2 fw-semibold fs-14px">{{ $t("add") }}</span>
        </div>
      </v-btn>
    </div>

    <template v-if="sliderItems.length > 0">
      <slider-group
        :files="sliderItems"
        @remove-file="removeFile"
        @download-file="downloadFile"
      />
    </template>
    <template v-else>
      <div class="font-weight-medium text-theme-secondary fs-14px text-center">
        {{ $t("uploadFilesToProvide") }}
      </div>
    </template>

    <v-dialog
      v-model="dialogVisible"
      max-width="480"
      persistent
    >
      <v-card>
        <v-card-title class="d-flex justify-between">
          <h4>{{ $t("addAttachment") }}</h4>
          <v-btn
            text
            small
            depressed
            @click="closeDialog"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-form class="text-left">
            <fileInput
              ref="fileInput"
              :files.sync="newFiles"
              class="w-full"
            />
          </v-form>
        </v-card-text>
        <v-card-actions class="justify-space-between px-6 pb-6">
          <v-btn
            depressed
            class="text-theme-label btn-theme text-capitalize rounded-lg"
            color="gray-100"
            width="204.5px"
            height="40px"
            @click="cancelDialog"
          >
            {{ $t("cancel") }}
          </v-btn>
          <v-btn
            depressed
            class="f-color-white btn-theme text-capitalize rounded-lg"
            color="primary"
            width="204.5px"
            height="40px"
            @click="addAttachments"
          >
            {{ $t("addAttachment") }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import fileInput from "@/components/base/FileInput.vue";
import PlusBlueIcon from '@/assets/svg/plus-blue.svg';
import SliderGroup from "@/components/base/SliderGroup.vue";
import makeExecutionsService from '@/services/api/execution'
import makeAttachmentService from '@/services/api/attachment'
import makeCasesService from '@/services/api/case';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import { getExtension, generateDateUid } from "@/utils/util";
import axios from 'axios'

let executionsService;
let attachmentService;
let casesService;

export default {
  components: {
    fileInput,
    PlusBlueIcon,
    SliderGroup
  },
  props: {
    files: {
      type: Array,
      default: () => [],
    },
    projectKey: {
      type: String,
    },
    hideAddAttachment: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      newFiles: [],
    };
  },
  computed: {
    sliderItems: {
      get() {
        return this.files;
      },
      set(newFiles) {
        this.$emit("update:files", newFiles);
      },
    },
  },
  created() {
    executionsService = makeExecutionsService(this.$api);
    attachmentService = makeAttachmentService(this.$api);
    casesService = makeCasesService(this.$api);
  },
  methods: {
    async removeFile(file) {
      if(this.$route.name == 'TestRunCaseEditExecutions' || this.$route.name == 'Workspace') {
        if(file && file.uid) {
          try {
            const response = await executionsService.deleteAttachments({ id: file.uid, params: {
                handle: this.$route.params.handle,
                projectKey: this.projectKey || this.$route.params.key
              }});
              this.$emit('removeFile');
            if (response.status === 200) {
              showSuccessToast(this.$swal, this.$t('toast.deleteSuccess', { item: 'Attachment' }));
            } 
          } catch (err) {
            showErrorToast(this.$swal, this.$t('toast.deleteError', { item: 'Attachment' }), {}, err?.response?.data)
          }
        } 
      } else {
        if(file && file.uid) {
          try {
            const response = await casesService.deleteAttachments({ id: file.uid, params: {
                handle: this.$route.params.handle,
                projectKey: this.$route.params.key
              }});
            if (response.status === 200) {
              this.$emit('removeFile', file.uid);
              showSuccessToast(this.$swal, this.$t('toast.deleteSuccess', { item: 'Attachment' }));
            } 
          } catch (err) {
            console.error(err);
            showErrorToast(this.$swal, this.$t('toast.deleteError', { item: 'Attachment' }), {}, err?.response?.data)
          }
        }
      }
      
    },
    async downloadFile(file) {
      if (!file.previewUrl) {
        console.error("File URL is missing");
        return;
      }

      try {
        const params = {
          download: true
        }
        const response = await attachmentService.getAttachmentUrl(this.$route.params.handle, "executions", file.uid, params);
        const blob = await axios.get(response.data, { responseType: 'arraybuffer' }).then(res => res.data);
        const uid = generateDateUid();
        const filename = file.name || `${uid}.${getExtension(file.type)}`;
        const url = URL.createObjectURL(new Blob([blob]));
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } catch (err) {
        console.log(err)
        showErrorToast(this.$swal, this.$t('toast.downloadError', { item: 'Attachment' }), {}, err?.response?.data)
      }
    },

    openDialog() {
      this.dialogVisible = true;
    },
    closeDialog() {
      this.newFiles.forEach((file) => {
        if (file.previewUrl) {
          URL.revokeObjectURL(file.previewUrl);
        }
      });
      this.newFiles = []; 
      this.dialogVisible = false;
      this.$refs.fileInput.resetFileInput();
    },
    cancelDialog() {
      this.closeDialog();
    },
    addAttachments() {
      this.$emit('uploadAttachments', this.newFiles)
      this.closeDialog();
    },
  },
};
</script>

