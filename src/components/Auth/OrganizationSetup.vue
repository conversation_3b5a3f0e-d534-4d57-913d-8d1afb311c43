<template>
  <div id="create-org-container">
    <v-card
      class="pt-2 px-6 mt-3"
      rounded="lg"
      elevation="0"
      width="100%"
    >
      <v-form
        id="create-org-form"
        ref="form"
        v-model="validForm"
        lazy-validation
      >
        <v-row>
          <v-col
            cols="12"
            class="text-center max-width mb-5"
          >
            <h1 class="create-org-title">
              {{ $t('organization.createOrganizationTitle') }}
            </h1>
            <p class="create-org-description">
              {{ $t('organization.createOrganizationDescription') }}
            </p>

            <section class="d-flex ga-8 justify-center">
              <div class="card bg-primary">
                <span class="card-sub-title">
                  {{ $t('organization.freeTrial') }}
                </span>
                <p class="card-title">
                  <span class="font-weight-bold fs-32px">14</span>
                  {{ $t('organization.days') }}
                </p>
              </div>
              <div class="card bg-grey">
                <span class="card-sub-title">
                  {{ $t('organization.afterTrial') }}
                </span>
                <p class="card-title">
                  <v-skeleton-loader
                    v-if="loadingSubscriptionPlans"
                    type="text"
                    width="80"
                  />
                  <template v-else>
                    <span class="font-weight-bold fs-32px">{{ pricePerMonth }}</span>
                    {{ $t('organization.perUserPerMonth') }}
                  </template>
                </p>
              </div>
            </section>
          </v-col>
          <v-col
            cols="12"
            class="pb-0 max-width"
          >
            <p class="font-weight-medium body-2 text-left mb-1">
              {{ $t('orgName') }} <strong class="red--text text--lighten-1">*</strong>
            </p>
            <v-text-field
              v-model="org.name"
              type="text"
              dense
              filled
              :placeholder="$t('enterOrgname')"
              :rules="orgNameValidation"
            />
          </v-col>

          <v-col
            cols="12"
            class="pb-0 max-width"
          >
            <p class="font-weight-medium body-2 text-left mb-1">
              {{ $t('orgAccountName') }}  <strong class="red--text text--lighten-1">*</strong>
            </p>

            <v-text-field
              v-model="org.handle"
              type="text"
              dense
              filled
              :placeholder="$t('enterOrgHandle')"
              :loading="handleRequestState.isLoading"
              :rules="accountNameValidation"
              :hint="orgHandleHint"
              persistent-hint
              @input="usernameInUse(org.handle)"
            />
          </v-col>

          <v-col
            cols="12"
            class="d-flex justify-center mt-4 max-width"
          >
            <div class="d-flex ga-3">
              <v-btn
                depressed
                background-color="#F2F4F7"
                class="font-inter text-capitalize black--text mr-4"
                height="40"
                width="211px"
                @click="$router.push('/setup')"
              >
                {{ $t('organization.skipForNow') }}
              </v-btn>

              <v-btn
                color="blue"
                width="211px"
                elevation="0"
                class="white--text text-capitalize rounded"
                :disabled="isDisabled"
                :loading="isLoading"
                @click="onCreateOrganization(org)"
              >
                {{ $t('organization.startFreeTrial') }}
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-form>
    </v-card>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';

import makeBillingService from '@/services/api/billing';
import {  $api } from '@/main';
import Swal from 'sweetalert2';
import { useRegister } from '@/composables/modules/auth/register';
import {  showErrorToast } from '@/utils/toast';
export default {
  name: 'OrganizationSetup',
  setup() {
    const {
      onCreateOrganization, 
      usernameInUse,
      handleRequestState 
    } = useRegister();
    
    // Reactive state
    const validForm = ref(false);
    const org = ref({
      name: '',
      handle: '',
    });
    const isLoading = ref(false);
    const loadingSubscriptionPlans = ref(false);
    const subscriptionPlans = ref([]);
    
    // Validation rules
    const orgNameValidation = [
      value => !!value || 'This field is required',
      value => (value.length >= 2 && value.length <= 50) || 'Must be between 2 and 50 characters'
    ];
    
    // Computed properties
    const orgHandleHint = computed(() => {
      if (org.value.handle === '') {
        return 'Organization handle (URL identifier)';
      }
      if (!handleRequestState.value.error && handleRequestState.value.isAvailable) {
        return "Handle is available";
      }
      if( handleRequestState.value.error) {
        return handleRequestState.value.error;
      }
      return '';
    });
    const isDisabled = computed(() => {
      return !validForm.value || isLoading.value || handleRequestState.value.isLoading || !handleRequestState.value.isAvailable;
    });
    
    const accountNameValidation = computed(() => {
      const defaultRules = [
        value => !!value || 'This field is required',
        value => /^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/.test(value) || 'Invalid handle format',
      ];
      if (handleRequestState.value.error) {
        return [
          ...defaultRules,
          handleRequestState.value.error
        ];
      }
      return defaultRules;
    });
    
    const pricePerMonth = computed(() => {
      if (subscriptionPlans.value.length) {
        const price = subscriptionPlans.value.find(plan => plan.amount != 0)?.amount;
        return price ? `$${price/100}` : '$0';
      }
      return '$0';
    });
    

    
    const getSubscriptionPlans = async () => {
      loadingSubscriptionPlans.value = true;
      try {
        const billingService = makeBillingService($api);
        const response = await billingService.getSubscriptionPlans({
          model: 'org',
        });
        subscriptionPlans.value = response.data;
      } catch (error) {
        console.error(error);
        showErrorToast(Swal, 'fetchError', { item: 'subscription plans' }, error?.response?.data);
      } finally {
        loadingSubscriptionPlans.value = false;
      }
    };
    
    // Lifecycle hooks
    onMounted(() => {
      getSubscriptionPlans();
    });
    
    return {
      validForm,
      org,
      isLoading,
      isDisabled,
      loadingSubscriptionPlans,
      subscriptionPlans,
      handleRequestState,
      orgNameValidation,
      orgHandleHint,
      accountNameValidation,
      pricePerMonth,
      onCreateOrganization,
      usernameInUse
    };
  }
};
</script>

<style lang="scss" scoped>
.fs-32px{
  font-size: 32px;
}
.card{
  width: 215px;
  height: 112px;
  border-radius: 8px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
}
.card-sub-title{
  font-weight: 500;
  font-size: 14px;
  line-height: 28px;
  letter-spacing: 0%;
}

.card-title{
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  margin-top: 12px;
}
.create-org-title {
  font-family: Inter;
  font-weight: 600;
  font-size: 24px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  margin-top: 48px;
}

.create-org-description {
  font-family: Inter;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  margin-top: 16px;
}
.rounded{
  border-radius: 6px !important;
}
.bg-primary{
  background-color: #0C2FF3;
  color: white;
}
.bg-grey{
  background-color: #F9F9FB;
  color: #667085;
}
.max-width{
  max-width: 512px !important;
  margin: 0 auto;
}
.ga-8 {
  gap: 8px;
}
.ga-3 {
  gap: 12px;
}
</style> 