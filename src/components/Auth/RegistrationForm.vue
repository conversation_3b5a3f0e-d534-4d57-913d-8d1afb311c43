<template>
  <v-container
    id="container"
    fluid
    class="d-flex align-center justify-center"
  >
    <v-row class="d-flex align-center">
      <v-col
        offset="1"
        offset-sm="2"
        offset-md="2"
        offset-lg="3"
        cols="10"
        sm="8"
        md="8"
        lg="6"
      >
        <template v-if="invite">
          <ValidationObserver
            id="inviteObserver"
            ref="inviteObserverRef"
            v-slot="{ handleSubmit }"
          >
            <v-form
              id="form"
              ref="formRef"
              role="registerForm"
              @submit.prevent="handleSubmit(completeRegistration)"
              @reset="resetForm"
            >
              <v-row
                class="text-h4 font-weight-bold "
                justify="center"
              >
                {{ $t("completeYourRegistration") }}
              </v-row>
              
              <v-row
                class="text-theme-secondary my-8"
                justify="center"
              >
                {{ $t("alreadyHaveAccount") }}
                <router-link
                  id="sign-up-link"
                  :to="{ name: 'Login' }"
                  class="text-decoration-none fw-semibold fs-14 ml-6"
                >
                  {{ $t("logintoyouraccount") }} 
                </router-link>
              </v-row>

              <v-row
                class="text-subtitle-1 mb-8"
                justify="center"
              >
                {{ $t("inviteSetupAccount", { name: invite.senderName }) }}
              </v-row>

              
              <v-row class="mb-6">
                <div
                  class="flex flex-column align-start rounded-lg py-2 px-3"
                  style="background-color: #F9F9FB;"
                >
                  <label
                    for="organization"
                    class="text-caption"
                  >{{ $t("org") }}</label>
                  <div class="flex-grow-1" />
                  <span class="font-weight-bold">{{ invite.organization }}</span>
                </div>
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("username") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="username"
                  v-model="user.handle"
                  name="username"
                  :rules="usernameValidations"
                  :placeholder="$t('inputPlaceholder', { field: $t('username') })"
                  height="38"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="signupBtnLoading"
                />
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("firstName") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="firstname"
                  v-model="user.firstName"
                  name="First name"
                  :rules="firstNameValidation"
                  :placeholder="$t('inputPlaceholder', { field: $t('first name') })"
                  height="45"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="signupBtnLoading"
                />
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("lastName") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="lastname"
                  v-model="user.lastName"
                  name="Last name"
                  :rules="lastNameValidation"
                  :placeholder="$t('inputPlaceholder', { field: $t('surname') })"
                  height="45"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="signupBtnLoading"
                />
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("email") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="email"
                  v-model="user.email"
                  name="email"
                  :rules="emailValidation"
                  :placeholder="$t('inputPlaceholder', { field: $t('email') })"
                  height="45"
                  background-color="#F9F9FB"
                  class="field-theme"
                  disabled
                />
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("password") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="password"
                  v-model="user.password"
                  name="password"
                  :rules="passwordValidation"
                  :placeholder="$t('inputPlaceholder', { field: $t('password') })"
                  height="45"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="signupBtnLoading"
                  :append-icon="visiblePassword ? 'mdi-eye' : 'mdi-eye-off'"
                  :type="visiblePassword ? 'text' : 'password'"
                  @click:append="visiblePassword = !visiblePassword"
                />
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("reEnterPassword") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="confirmPassword"
                  v-model="confirmPassword"
                  :placeholder="$t('reEnterPassword')"
                  height="45"
                  :rules="[passwordConfirmationRule]"
                  :append-icon="visibleConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                  :type="visibleConfirmPassword ? 'text' : 'password'"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="signupBtnLoading"
                  @click:append="visibleConfirmPassword = !visibleConfirmPassword"
                />
              </v-row>
      
              <v-row class="mt-4">
                <v-col cols="12">
                  <v-btn
                    id="signup"
                    block
                    color="primary"
                    type="submit"
                    :depressed="true"
                    class="btn-theme"
                    height="40"
                    :loading="signupBtnLoading"
                    :disabled="!isFormValid || signupBtnLoading"
                    :class="{ 'btn-loading-opacity': signupBtnLoading }"
                  >
                    {{ $t("completeRegistration") }}
                  </v-btn>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-btn
                    block
                    class="btn-theme"
                    color="error"
                    bg-color="error"
                    :depressed="true"
                    @click="declineInvite"
                  >
                    {{ $t("decline") }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-form>
          </ValidationObserver>
        </template>
        <template v-else>
          <ValidationObserver
            id="observer"
            ref="observerRef"
            v-slot="{ handleSubmit }"
          >
            <v-form
              id="form"
              ref="formRef"
              role="registerForm"
              @submit.prevent="handleSubmit(saveUser)"
              @reset="resetForm"
            >
              <v-row
                class="fs-24px fw-semibold"
                justify="center"
              >
                {{ $t("signUpHeader") }}
              </v-row>
              <v-row
                class="text-theme-secondary my-8"
                justify="center"
              >
                {{ $t("alreadyHaveAccount") }}
                <router-link
                  id="sign-up-link"
                  :to="{ name: 'Login' }"
                  class="text-decoration-none fw-semibold fs-14 ml-6"
                >
                  {{ $t("logintoyouraccount") }} 
                </router-link>
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("username") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="username"
                  v-model="user.handle"
                  name="username"
                  :rules="usernameValidations"
                  :placeholder="$t('inputPlaceholder', { field: $t('username') })"
                  height="38"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="signupBtnLoading"
                />
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("firstName") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="firstname"
                  v-model="user.firstName"
                  name="First name"
                  :rules="firstNameValidation"
                  :placeholder="$t('inputPlaceholder', { field: $t('name') })"
                  height="38"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="signupBtnLoading"
                />
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("lastName") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="lastname"
                  v-model="user.lastName"
                  name="Last name"
                  :rules="lastNameValidation"
                  :placeholder="$t('inputPlaceholder', { field: $t('surname') })"
                  height="38"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="signupBtnLoading"
                />
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("email") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="email"
                  v-model="user.email"
                  name="email"
                  :rules="emailValidation"
                  :placeholder="$t('inputPlaceholder', { field: $t('email') })"
                  height="38"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="signupBtnLoading"
                />
              </v-row>
              <v-row class="d-flex flex-column">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t("password") }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
                <v-text-field
                  id="password"
                  v-model="user.password"
                  name="password"
                  :rules="passwordValidation"
                  :placeholder="$t('inputPlaceholder', { field: $t('password') })"
                  height="38"
                  background-color="#F9F9FB"
                  class="field-theme"
                  :disabled="signupBtnLoading"
                  :append-icon="visiblePassword ? 'mdi-eye' : 'mdi-eye-off'"
                  :type="visiblePassword ? 'text' : 'password'"
                  @click:append="visiblePassword = !visiblePassword"
                />
              </v-row>
              <v-row class="my-8 gap-4">
                <v-btn
                  id="signup"
                  block
                  color="primary"
                  type="submit"
                  :depressed="true"
                  class="btn-theme"
                  height="40"
                  :loading="signupBtnLoading"
                  :class="{ 'btn-loading-opacity': signupBtnLoading }"
                >
                  {{ $t("signUp") }}
                </v-btn>

                <ContinueWithGoogleButton
                  :loading="signupBtnLoading"
                />
              </v-row>
            </v-form>
          </ValidationObserver>
        </template>
      </v-col>
    </v-row>
    <v-dialog
      v-model="showDeclineDialog"
      max-width="480px"
      style="z-index: 1000;"
    >
      <v-card class="pa-6">
        <v-card-title
          class="font-weight-bold  text-start d-flex align-start justify-space-between flex-nowrap"
        >
          <div
            class="pr-8 dialogTitle"
          >
            {{ step === 1 ? $t('areYouSureDecline') : $t('whyDeclineInvitation') }}
          </div>
          <v-icon color="black">
            mdi-close
          </v-icon>
        </v-card-title>
        <v-card-text
          v-if="step === 1"
          class="mb-4 text-start"
        >
          {{ $t('sureDeclineInvitation', { name: invite?.senderName }) }}
        </v-card-text>
        <v-card-text
          v-if="step === 2"
          class="mb-4 text-start"
        >
          <v-radio-group v-model="declineReason">
            <v-radio
              :label="$t('declineReasons.alreadyPartOfOrganization')"
              value="alreadyPartOfOrganization"
            />
            <v-radio
              :label="$t('declineReasons.notInterestedInJoining')"
              value="notInterestedInJoining"
            />
            <v-radio
              :label="$t('declineReasons.preferDifferentPlatform')"
              value="preferDifferentPlatform"
            />
            <v-radio
              :label="$t('declineReasons.other')"
              value="other"
            />
          </v-radio-group>
          <v-textarea
            v-if="declineReason === 'other'"
            v-model="otherReason"
            variant="solo-filled"
            :label="$t('declineReasons.specifyReason')"
            :placeholder="$t('declineReasons.specifyReasonPlaceholder')"
            style="background-color: #F9F9FB; border-radius: 8px;"
            class="pa-2"
          />
        </v-card-text>
        <v-card-actions class="flex">
          <v-spacer />
          <v-btn
            color="grey lighten-1"
            class="btn-theme "

            @click="cancelDecline"
          >
            {{ $t('cancel') }}
          </v-btn>
          <v-btn
            v-if="step === 1"
            color="error"
            class="btn-theme"
            @click="step = 2"
          >
            {{ $t('decline') }}
          </v-btn>
          <v-btn
            v-if="step === 2"
            color="primary"
            class="btn-theme"
            @click="confirmDecline"
          >
            {{ $t('confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>
<script>
import { useRegister } from '@/composables/modules/auth/register';
import { handleDuplicateMixin } from "@/mixins/handleDuplicate.js";
import ContinueWithGoogleButton from '@/components/Auth/ContinueWithGoogleButton.vue';

export default {
  components: {
    ContinueWithGoogleButton
  },
  mixins: [handleDuplicateMixin],
  setup() {
    const {
      // Reactive data
      user,
      visiblePassword,
      visibleConfirmPassword,
      confirmPassword,
      invite,
      signupBtnLoading,
      showDeclineDialog,
      step,
      declineReason,
      otherReason,
      formRef,
      observerRef,
      inviteObserverRef,
      
      // Computed properties
      emailValidation,
      firstNameValidation,
      lastNameValidation,
      passwordValidation,
      usernameValidations,
      disableEmail,
      passwordConfirmationRule,
      isFormValid,
      
      // Methods
      validate,
      reset,
      resetValidation,
      resetForm,
      saveUser,
      checkForInvite,
      completeRegistration,
      declineInvite,
      confirmDecline,
      cancelDecline,
      usernameInUse
    } = useRegister();
    
    return {
      // Reactive data
      user,
      visiblePassword,
      visibleConfirmPassword,
      confirmPassword,
      invite,
      signupBtnLoading,
      showDeclineDialog,
      step,
      declineReason,
      otherReason,
      formRef,
      observerRef,
      inviteObserverRef,
      
      // Computed properties
      emailValidation,
      firstNameValidation,
      lastNameValidation,
      passwordValidation,
      usernameValidations,
      disableEmail,
      passwordConfirmationRule,
      isFormValid,
      
      // Methods
      validate,
      reset,
      resetValidation,
      resetForm,
      saveUser,
      checkForInvite,
      completeRegistration,
      declineInvite,
      confirmDecline,
      cancelDecline,
      usernameInUse
    };
  }
};
</script>


<style scoped>
.dialogTitle{
font-family: Inter;
font-size: 22px;
font-weight: 700;
line-height: 28px;
text-align: left;
max-width: 320px;
word-break: keep-all;
}
.btn-theme{
  width: 50%;
}
</style>

