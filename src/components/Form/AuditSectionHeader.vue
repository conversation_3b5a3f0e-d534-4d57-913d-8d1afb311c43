<template>
  <v-card
    class="white py-4 px-7"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div class="d-flex align-center justify-space-between">
      <h2
        v-if="!skeletonLoaderState"
        class="text-theme-base"
      >
        {{ title }}
      </h2>
      <v-skeleton-loader
        v-else
        height="36"
        width="250"
        type="heading"
      />
      <div
        v-if="!skeletonLoaderState"
        class="d-flex align-center"
      >
        <div class="dropdown-container">
          <v-btn
            v-if="hasAction"
            :class="{
              'text-capitalize tray-btn-outline btn-theme': true,
              'disabled-action': isProjectArchived
            }"
            color="primary"
            :depressed="true"
            height="40"
            :disabled="loading || isProjectArchived"
            @click="toggleDropdown"
          >
            {{ actionText }}
            <v-icon
              class="ml-3"
              size="18"
            >
              mdi-tray-arrow-down
            </v-icon>
            <v-progress-circular
              v-if="loading"
              indeterminate
              color="gray"
              size="18"
              class="ml-3"
            />
          </v-btn>

          <transition name="fade">
            <div 
              v-if="showDropdown" 
              class="dropdown-menu"
            >
              <div
                v-for="(item, index) in items"
                :key="index"
                v-ripple
                class="dropdown-item"
              >
                <div
                  class="dropdown-label"
                  @click="toggleSubmenu(index)"
                >
                  <v-icon v-if="item.submenu.length">
                    mdi-chevron-left
                  </v-icon>
                  <span>{{ item.label }}</span>
                </div>

                <!-- Submenu -->
                <transition name="slide-right">
                  <div
                    v-if="activeSubmenuIndex === index && item.submenu.length"
                    class="nested-dropdown"
                  >
                    <div
                      v-for="(subItem, subIndex) in item.submenu"
                      :key="subIndex"
                      v-ripple
                      class="dropdown-subitem"
                      @click="handleExport(item.label.split(' ')[2])"
                    >
                      <div class="dropdown-label">
                        <span>{{ subItem.label }}</span>
                        <v-icon 
                          v-if="subItem.submenu.length" 
                          class="ml-2"
                        >
                          mdi-chevron-right
                        </v-icon>
                      </div>

                      <!-- Sub-submenu -->
                      <transition name="slide-right">
                        <div
                          v-if="activeSubSubmenuIndex === subIndex && subItem.submenu.length"
                          class="nested-dropdown"
                        >
                          <div
                            v-for="(subSubItem, subSubIndex) in subItem.submenu"
                            :key="subSubIndex"
                            v-ripple
                            class="dropdown-subitem"
                          >
                            {{ subSubItem }}
                          </div>
                        </div>
                      </transition>
                    </div>
                  </div>
                </transition>
              </div>
            </div>
          </transition>
        </div>
      </div>
      <div
        v-else
        class="d-flex"
      >
        <v-skeleton-loader
          class="rounded-lg mr-3"
          height="40"
          width="40"
          type="button"
        />
        <v-skeleton-loader
          class="rounded-lg primary"
          height="40"
          width="180"
          type="button"
        />
      </div>
    </div>
    <slot name="additional-actions" />
  </v-card>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import axios from 'axios';
import makeAuditLogService from '@/services/api/auditLog';
import makeAttachmentService from '@/services/api/attachment'
import { showErrorToast } from '@/utils/toast';
import { startPolling, stopPolling, generateDateUid } from '@/utils/util';

let initializeAuditLogService;
let attachmentService;
export default {
  name: 'AuditSectionHeader',
  props: {
    title: String,
    actionText: String,
    hasAction: {
      type: Boolean,
      default: true,
    },
    isProjectArchived: {
      type: Boolean,
      default: false,
    },
    apiFilter: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      showDropdown: false,
      activeSubmenuIndex: null,
      activeSubSubmenuIndex: null,
      pollingInterval: null,
      taskUid: null,
      format: null,
      loading: false,
      items: [
        {
          label: this.$t('auditLog.exportCSV'),
          submenu: [
            { label: this.$t('auditLog.today'), submenu: [], daysBefore: 0 },
            { label: this.$t('auditLog.yesterday'), submenu: [], daysBefore: 1 },
            { label: this.$t('auditLog.last3Days'), submenu: [], daysBefore: 3 },
            { label: this.$t('auditLog.last7Days'), submenu: [], daysBefore: 7 }
          ],
        },
        {
          label: this.$t('auditLog.exportXLSX'),
          submenu: [
            { label: this.$t('auditLog.today'), submenu: [], daysBefore: 0 },
            { label: this.$t('auditLog.yesterday'), submenu: [], daysBefore: 1 },
            { label: this.$t('auditLog.last3Days'), submenu: [], daysBefore: 3 },
            { label: this.$t('auditLog.last7Days'), submenu: [], daysBefore: 7 }
          ],
        },
      ],
    };
  },
  computed: {
    ...mapState('user', ['currentAccount']),
    ...mapState('storage', ['task']),
  },
  created() {
    initializeAuditLogService = makeAuditLogService(this.$api);
    attachmentService = makeAttachmentService(this.$api);
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    ...mapActions('storage', ['fetchAndStoreTask']),
    toggleDropdown() {
      this.showDropdown = !this.showDropdown;
      this.activeSubmenuIndex = null;
      this.activeSubSubmenuIndex = null;
    },
    toggleSubmenu(index) {
      if (this.activeSubmenuIndex === index) {
        this.activeSubmenuIndex = null;
      } else {
        this.activeSubmenuIndex = index;
        this.activeSubSubmenuIndex = null;
      }
    },
    toggleSubSubmenu(index) {
      if (this.activeSubSubmenuIndex === index) {
        this.activeSubSubmenuIndex = null;
      } else {
        this.activeSubSubmenuIndex = index;
      }
    },
    calculateDateRange(daysBefore = 0) {
      if (typeof daysBefore !== 'number' || daysBefore < 0) return;

      const currentDate = new Date();
      const fromDate = new Date(currentDate);
      const endDate = new Date(currentDate);

      fromDate.setHours(0, 0, 0, 0);
      fromDate.setDate(currentDate.getDate() - daysBefore);

      endDate.setHours(23, 59, 59, 999);

      return {
        $gte: fromDate.toISOString(),
        $lte: endDate.toISOString(),
      };
    },

    async handleExport(format, daysBefore) {
      this.loading = true;
      this.format = format;

      try {
        const createdAtFilter = this.calculateDateRange(daysBefore);
        const filters = this.apiFilter && Object.keys(this.apiFilter).length > 0
          ? {
              ...(this.apiFilter.entity_name?.length && { tablename: this.apiFilter.entity_name }),
              ...(this.apiFilter.actor?.length && { actor: this.apiFilter.actor }),
              ...(this.apiFilter.action?.length && { action: this.apiFilter.action }),
              ...(this.apiFilter.project_name?.length && { projectUid: this.apiFilter.project_name }),
              createdAt: createdAtFilter,
            }
          : { createdAt: createdAtFilter };

        const response = await initializeAuditLogService.exportAuditLogs(this.$route.params.handle, { filters, format });
        this.taskUid = response.data.taskUid;

        if (!this.taskUid) {
          this.loading = false;
          return;
        }

        await this.fetchAndStoreTask({handle: this.currentAccount.handle, id: this.taskUid});
        this.beginPollingProcess();
        this.showDropdown = false;
      } catch (error) {
        showErrorToast(this.$swal, this.$t('toast.exportError'), { format }, error?.response?.data);
        this.loading = false;
      }
    },
    async pollTaskUpdates() {
      try {
        const params = { download: true }
        const response = await this.fetchAndStoreTask({ handle: this.currentAccount.handle, id: this.taskUid });
        if (response.status === 'error' || response.status === 'failed' || response.status === 'completed') {
          const extension = this.format === 'CSV' ? 'csv' : 'xlsx';
          this.endPollingProcess();
          if (response.status === 'completed' && response.results?.attachmentUid) {
            try {
              const file = await attachmentService.getAttachmentUrl(this.$route.params.handle, "exports", response.results.attachmentUid, params);
              const blob = await axios.get(file.data, { responseType: 'arraybuffer' }).then(res => res.data);
              const uid = generateDateUid();
              const filename = `${this.$t("auditLog.title")}_${uid}.${extension}`;
              const url = URL.createObjectURL(new Blob([blob]));
              const link = document.createElement("a");
              link.href = url;
              link.download = filename;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              URL.revokeObjectURL(url);
            } catch (error) {
              showErrorToast(this.$swal, this.$t('toast.attachmentError'), error?.response?.data);
            }
          }
        }
      } catch (error) {
        showErrorToast(this.$swal, error.response.data.message);
        this.endPollingProcess();
      }
    },
    beginPollingProcess() {
      if (!this.pollingInterval) {
        this.pollingInterval = startPolling(this.pollTaskUpdates, 3000);
      }
    },
    endPollingProcess() {
      stopPolling(this.pollingInterval);
      this.pollingInterval = null;
      this.loading = false;
      this.taskUid = null;
      this.format = null;
    },
    handleClickOutside(event) {
      const dropdown = this.$el.querySelector('.dropdown-container');
      if (dropdown && !dropdown.contains(event.target)) {
        this.showDropdown = false;
        this.activeSubmenuIndex = null;
        this.activeSubSubmenuIndex = null;
      }
    },
  },
};
</script>

<style scoped>
.dropdown-container {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 110%;
  left: 0;
  background-color: white;
  padding: 16px 0px;
  border-radius: 8px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.dropdown-item {
  padding: 10px 20px;
  position: relative;
  white-space: nowrap;
  cursor: pointer;
}

.dropdown-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 400;
}

.dropdown-item:hover {
  background-color: #f0f0f0;
}

.nested-dropdown {
  position: absolute;
  top: 0;
  right: 105%;
  background-color: white;
  z-index: 20;
  padding: 8px 0;
  border-radius: 8px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
}

.dropdown-subitem {
  padding: 10px 40px 10px 20px;
  cursor: pointer;
}

.dropdown-subitem:hover {
  background-color: #f0f0f0;
}

/* Transition styles */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-right-enter-active, .slide-right-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}
.slide-right-enter, .slide-right-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}
</style>
