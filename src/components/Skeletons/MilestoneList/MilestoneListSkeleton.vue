<template>
  <div>
    <template v-if="loading">
      <v-skeleton-loader
        width="100px"
        height="20px"
        type="text"
      />
    </template>
    <v-tooltip
      v-else-if="isResponseSuccess"
      bottom
      left
      max-width="485px"
      :disabled="!Array.isArray(milestones) || milestones.length < 2"
      content-class="tooltip-theme"
    >
      <template #activator="{ on, attrs }">
        <span
          class="custom-attribute font-weight-regular text-theme-table-text"
          v-bind="attrs"
          v-on="on"
        >
          <template>
            <div class="text-truncate">
              <span v-if="Array.isArray(milestones) && milestones.length > 0">
                {{ milestones.map(milestone => `${milestone.name}`).join(', ') }}
              </span>
              <span v-else-if="typeof milestones === 'string' && milestones.trim() !== ''">
                {{ milestones }}
              </span>
              <v-icon v-else>mdi-minus</v-icon>
            </div>
          </template>
        </span>
      </template>
      <span>
        {{
          Array.isArray(milestones)
            ? milestones.map(milestone => `${milestone.name}`).join(', ')
            : ''
        }}
      </span>
    </v-tooltip>
    <template v-else>
      <v-icon>mdi-minus</v-icon>
    </template>
  </div>
</template>

<script>
export default {
  name: 'MilestoneListSkeleton',
  props: {
    endpointFunc: { type: Function, required: true },
  },
  data() {
    return {
      milestones: null,
      loading: true,
      isResponseSuccess: false,
    };
  },
  async mounted() {
    await this.fetchMilestone();
  },
  methods: {
    async fetchMilestone() {
      this.loading = true;
      try {
        
        const res = await this.endpointFunc();
        this.milestones = res.data?.testMilestones || [];

        this.isResponseSuccess = (res.status >= 200 && res.status < 300) || res.status === 304;
      } catch (err) {
        this.isResponseSuccess = false;
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>