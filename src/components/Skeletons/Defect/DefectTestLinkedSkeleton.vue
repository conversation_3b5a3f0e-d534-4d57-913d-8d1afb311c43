<template>
  <div>
    <template v-if="loading">
      <v-skeleton-loader
        width="100px"
        height="20px"
        type="text"
      />
    </template>

    <template v-else-if="isResponseSuccess && data && data.length > 0">
      <div class="align-left contents fw-semibold fs-14px">
        <template v-if="!data || data.length === 0">
          {{ $t('none') }}
        </template>
        <template v-else-if="data.length === 1">
          {{ data[0].name }}
        </template>
        <v-menu
          v-else
          offset-y
        >
          <template #activator="{ on, attrs }">
            <div
              class="d-flex align-center cursor-pointer"
              v-bind="attrs"
              v-on="on"
            >
              {{ data[0].name }}
              <v-icon
                small
                class="ml-1"
              >
                mdi-chevron-down
              </v-icon>
            </div>
          </template>
          <v-list dense>
            <v-list-item
              v-for="list in data"
              :key="list.uid"
              dense
            >
              <v-list-item-title>{{ list.name }}</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </template>

    <template v-else>
      <v-icon>mdi-minus</v-icon>
    </template>
  </div>
</template>

<script>
export default {
  name: 'DefectExecutionSkeleton',
  props: {
    endpointFunc: { type: Function, required: true },
  },
  data() {
    return {
      data: [],
      loading: true,
      isResponseSuccess: false,
    };
  },
  async mounted() {
    await this.fectDefectList();
  },
  methods: {
    async fectDefectList() {
      this.loading = true;
      try {
        const res = await this.endpointFunc();
        this.data = res.data || [];
        this.isResponseSuccess = (res.status >= 200 && res.status < 300) || res.status === 304;
      } catch (err) {
        this.isResponseSuccess = false;
      } finally {
        this.loading = false;
      }
    },
    handleExecutionClick(execution) {
      this.$emit('execution-click', execution);
    },
  },
};
</script>
