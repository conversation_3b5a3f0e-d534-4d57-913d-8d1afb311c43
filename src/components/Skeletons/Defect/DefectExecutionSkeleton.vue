<template>
  <div>
    <template v-if="loading">
      <v-skeleton-loader
        width="100px"
        height="20px"
        type="text"
      />
    </template>

    <template v-else-if="isResponseSuccess && linkedExecutions && linkedExecutions.length > 0">
      <div class="d-flex align-center">
        <template v-if="!linkedExecutions || linkedExecutions.length === 0">
          <span class="text-caption">{{ $t('-') }}</span>
        </template>
        <template v-else-if="linkedExecutions.length === 1">
          <v-tooltip
            bottom
            left
            max-width="485px"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <div
                :ref="'executionName_' + linkedExecutions[0].uid"
                class="d-flex align-center cursor-pointer text-truncate"
                style="max-width: 100%; overflow: hidden;"
                v-bind="attrs"
                v-on="on"
                @click.stop="handleExecutionClick(linkedExecutions[0])"
              >
                <span class="text-body-2 text-truncate">{{ linkedExecutions[0].executionName }}</span>
              </div>
            </template>
            <span>{{ linkedExecutions[0].executionName }}</span>
          </v-tooltip>
        </template>
        <template v-else>
          <div class="d-flex align-center gap-1">
            <!-- Show first execution -->
            <v-tooltip
              bottom
              left
              max-width="485px"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <div
                  :ref="'firstExecutionName_' + linkedExecutions[0].uid"
                  class="d-flex align-center cursor-pointer text-truncate"
                  style="max-width: 100%; overflow: hidden;"
                  v-bind="attrs"
                  v-on="on"
                  @click.stop="handleExecutionClick(linkedExecutions[0])"
                >
                  <span class="text-body-2 text-truncate">{{ linkedExecutions[0].executionName }}</span>
                </div>
              </template>
              <span>{{ linkedExecutions[0].executionName }}</span>
            </v-tooltip>
            
            <!-- Show remaining executions as simple number -->
            <v-tooltip
              v-if="linkedExecutions.length > 1"
              bottom
              left
              max-width="485px"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <span
                  class="text-caption font-weight-medium cursor-pointer"
                  style="color: #2196F3;"
                  v-bind="attrs"
                  v-on="on"
                >
                  +{{ linkedExecutions.length - 1 }}
                </span>
              </template>
              <div>
                <div
                  v-for="execution in linkedExecutions.slice(1)"
                  :key="execution.uid"
                  class="mb-1"
                >
                  {{ execution.executionName }}
                </div>
              </div>
            </v-tooltip>
          </div>
        </template>
      </div>
    </template>

    <template v-else>
      <v-icon>mdi-minus</v-icon>
    </template>
  </div>
</template>

<script>
// Global cache to share across all DefectExecutionSkeleton instances
const globalExecutionsCache = new Map();

export default {
  name: 'DefectExecutionSkeleton',
  props: {
    endpointFunc: { type: Function, required: true },
    defectUid: { type: [String, Number], default: null },
  },
  data() {
    return {
      linkedExecutions: [],
      loading: true,
      isResponseSuccess: false,
    };
  },
  async mounted() {
    await this.fetchDefectExecutions();
  },
  beforeDestroy() {
    // Cleanup any pending operations
    this.loading = false;
  },
  methods: {
    async fetchDefectExecutions() {
      // Use the passed defect UID or extract it from the function
      const defectUid = this.defectUid || this.extractDefectUid();
      const cacheKey = `defect-executions-${defectUid}`;
      
      // Check global cache first
      const cachedData = globalExecutionsCache.get(cacheKey);
      
      if (cachedData && Date.now() - cachedData.timestamp < 5 * 60 * 1000) { // 5 minutes cache
        this.linkedExecutions = cachedData.data;
        this.isResponseSuccess = cachedData.success;
        this.loading = false;
        return;
      }

      this.loading = true;
      try {
        const res = await this.endpointFunc();
        this.linkedExecutions = res.data || [];
        this.isResponseSuccess = (res.status >= 200 && res.status < 300) || res.status === 304;
        
        // Cache the result globally
        globalExecutionsCache.set(cacheKey, {
          data: this.linkedExecutions,
          success: this.isResponseSuccess,
          timestamp: Date.now(),
        });
      } catch (err) {
        this.isResponseSuccess = false;
        console.warn('Failed to fetch defect executions:', err);
      } finally {
        this.loading = false;
      }
    },
    
    extractDefectUid() {
      // Try to extract defect UID from the endpoint function
      // This is a simple approach - the function is typically () => getDefectExecutions(item.uid)
      try {
        const funcStr = this.endpointFunc.toString();
        const match = funcStr.match(/getDefectExecutions\(([^)]+)\)/);
        if (match) {
          return match[1];
        }
      } catch (e) {
        // Fallback to function string
      }
      return this.endpointFunc.toString();
    },
    
    handleExecutionClick(execution) {
      this.$emit('execution-click', execution);
    },
  },
  
  // Static methods for cache management
  static: {
    clearCache() {
      globalExecutionsCache.clear();
    },
    
    clearCacheForDefect(defectUid) {
      const cacheKey = `defect-executions-${defectUid}`;
      globalExecutionsCache.delete(cacheKey);
    },
    
    getCacheSize() {
      return globalExecutionsCache.size;
    },
  },
};
</script>
