<template>
  <div>
    <template v-if="loading">
      <div class="d-flex flex-row">
        <v-skeleton-loader
          v-for="n in 5"
          :key="n"
          :max-width="size"
          :max-height="size"
          type="actions"
          class="circle mr-n2"
        />
      </div>
    </template>

    <template v-else-if="isResponseSuccess">
      <span class="gray-ish--text text-subtitle-1">
        <template v-if="members.length">
          <div class="avatar-group">
            <v-tooltip
              v-for="(member, index) in members.slice(0, 5)"
              :key="index"
              bottom
            >
              <template #activator="{ on, attrs }">
                <div
                  v-bind="attrs"
                  v-on="on"
                >
                  <Avatar
                    class="mr-n2"
                    :name="`${member.firstName} ${member.lastName}`"
                    :avatar="member.avatar"
                    :size="size"
                  />
                </div>
              </template>
              <span>{{ member.firstName }} {{ member.lastName }}</span>
            </v-tooltip>

            <v-avatar
              v-if="members.length > 5"
              class="mr-n2 img-rest-amount"
              :size="size"
            >
              <span class="font-weight-bold fs-14px">
                +{{ members.length - 5 }}
              </span>
            </v-avatar>
          </div>
        </template>
      </span>
    </template>

    <template v-else>
      <span class="text-theme-secondary fs-14px font-italic text-truncate mw-100px"> {{ $t('noPermissionToViewProjectUsers') }}</span>
    </template>
  </div>
</template>

<script>
export default {
  name: "AvatarGroup",
  props: {
    endpointFunc: { type: Function, required: true },
    size: { type: Number, default: 36 },
  },
  data() {
    return {
      members: [],
      loading: true,
      isResponseSuccess: false,
    };
  },
  async mounted() {
    await this.fetchMembers();
  },
  methods: {
    async fetchMembers() {
      this.loading = true;
      try {
        const res = await this.endpointFunc();
        this.members = res.data.items || [];
        this.isResponseSuccess =
          (res.status >= 200 && res.status < 300) ||
          res.status === 304;
      } catch (err) {
        this.isResponseSuccess = false;
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
