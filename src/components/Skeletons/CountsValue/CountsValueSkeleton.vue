<template>
  <div>
    <template v-if="loading">
      <v-skeleton-loader
        width="100px"
        height="20px"
        type="text"
      />
    </template>

    <template v-else-if="isResponseSuccess">
      <slot
        name="value"
        :count="count"
        :loading="loading"
      >
        <span class="text-theme-table-text fs-14px">
          {{ count }} <span class="text-lowercase">{{ countFor }}</span>
        </span>
      </slot>
    </template>
    <template v-else>
      <v-icon>mdi-minus</v-icon>
    </template>
  </div>
</template>

<script>
import { executeApiQuery } from '@/composables/utils/useApiQuery';

export default {
  name: 'CountsValue',
  props: {
    endpointFunc: { type: Function, required: true },
    countFor: { type: String, required: true },
    cacheKey: { type: String, default: null },
  },
  data() {
    return {
      count: 0,
      loading: true,
      isResponseSuccess: false,
    };
  },
  async mounted() {
    await this.fetchCount();
  },
  methods: {
    async fetchCount() {
      this.loading = true;
      try {
        let res;
        
        if (this.cacheKey) {
          const queryKey = ['count', this.cacheKey];
          res = await executeApiQuery(queryKey, this.endpointFunc, { debounce: true });
        } else {
          res = await this.endpointFunc();
        }
        
        this.count = res.data?.count || 0;
        this.isResponseSuccess = (res.status >= 200 && res.status < 300) || res.status === 304;
      } catch (err) {
        this.isResponseSuccess = false;
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
