<template>
  <div>
    <v-skeleton-loader
      type="table"
      class="rounded-lg mt-6"
      height="48"
    />
    <div
      v-for="i in 8"
      :key="i"
      class="d-flex justify-start align-center w-full"
      style="height: 80px;"
    >
      <div class="d-flex align-center w-35">
        <v-skeleton-loader
          class="w-90"
          type="text"
          height="16"
        />
      </div>
      <div class="d-flex align-center w-20">
        <v-skeleton-loader
          class="w-90"
          type="text"
          height="16"
        />
      </div>
      <div class="d-flex align-center w-20">
        <v-skeleton-loader
          class="w-90"
          type="text"
          height="16"
        />
      </div>
      <div class="d-flex align-center w-20">
        <v-skeleton-loader
          class="w-90"
          type="text"
          height="16"
        />
      </div>
      <div class="d-flex justify-end align-center w-5">
        <div class="d-flex justify-space-between gap-3 mr-3">
          <v-skeleton-loader
            type="button"
            height="24"
            width="24"
          />
          <v-skeleton-loader
            type="button"
            height="24"
            width="24"
          />
        </div>
      </div>
    </div>
  </div>
</template>