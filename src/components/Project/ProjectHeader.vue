<template>
  <v-card
    class="white py-6 px-6 mt-3"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div class="d-flex align-center justify-space-between">
      <h2
        v-if="!skeletonLoaderState"
      >
        {{ $t('projects.projects_list') }}
      </h2> 
      <v-skeleton-loader
        v-else
        height="36"
        width="140"
        type="heading"
      />
      <v-tooltip 
        bottom
        :disabled="writeProject" 
      >
        <template #activator="{ on, attrs }">
          <div 
            v-bind="attrs" 
            v-on="on"
          >
            <template v-if="skeletonLoaderState">
              <v-skeleton-loader
                class="rounded-lg"
                height="40px"
                width="140px"
                type="button"
              />
            </template>
            <v-btn
              depressed
              :disabled="!writeProject || isCreatingDemoProject"
              height="40px"
              class="text-capitalize rounded-lg btn-theme black--text mr-3"
              background-color="#F2F4F7"
              @click="$emit('create-demo-project')"
            >
              <v-progress-circular
                v-if="isCreatingDemoProject"
                indeterminate
                color="black"
                size="16"
                width="2"
                class="mr-2"
              />
              {{ $t('createDemoProject') }}
            </v-btn>
            <v-btn
              color="blue"
              depressed
              :disabled="!writeProject"
              height="40px"
              class="text-capitalize rounded-lg btn-theme white--text"
              :to="{ name: 'ProjectCreateView'}"
            >
              {{ $t('createProject') }} <v-icon
                class="ml-1" 
                size="16px"
              >
                mdi-plus
              </v-icon>
            </v-btn>
          </div>
        </template>
        <span>
          {{
            $t("orgLevelActionDisabled")
          }}
        </span>
      </v-tooltip>
    </div>
    <div
      v-if="!skeletonLoaderState"
      class="mt-4 d-flex"
    >
      <v-chip 
        :class="{ 'blue--text': filter === 'active',
                  'fw-semibold': filter === 'active',
                  'font-weight-medium': filter != 'active'
        }"
        width="200px" 
        :color="filter === 'active' ? '#e6ecff' : '#f9fafb'"
        label 
        @click="updateFilter('active')"
      >
        <div class="px-2">
          {{ $t('active') }} <span class="ml-2">{{ activeItemCount }}</span>
        </div>
      </v-chip>
      <div class="ml-2">
        <v-chip 
          :class="{ 'blue--text': filter === 'archived',
                    'fw-semibold': filter === 'archived',
                    'font-weight-medium': filter != 'archived'
          }" 
          width="200px" 
          :color="filter === 'archived' ? '#e6ecff' : '#f9fafb'"
          label 
          @click="updateFilter('archived')"
        >
          <div class="px-2">
            {{ $t('archived') }} <span class="ml-2">{{ archivedItemCount }}</span>
          </div>
        </v-chip>
      </div>
    </div>
    <div
      v-else
      class="mt-4 d-flex"
    >
      <v-skeleton-loader
        class="rounded-sm d-flex gap-2 chip-primary"
        height="32"
        width="200"
        type="button@2"
      />
    </div>
  </v-card>
</template>

<script>
import handleLoading from '@/mixins/loader.js'
export default {
  mixins: [handleLoading],
  props: {
    filter: String,
    activeItemCount: Number,
    archivedItemCount: Number,
    writeProject: {
      type: Boolean,
      default: false
    },
    isCreatingDemoProject: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update-filter', 'create-demo-project'],
  methods: {
    updateFilter(filter) {
      this.$emit('update-filter', filter);
    },
  }
}
</script>

<style scoped>
.actions-btn {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style>