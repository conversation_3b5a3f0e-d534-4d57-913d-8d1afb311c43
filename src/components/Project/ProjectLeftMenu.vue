<template>
  <v-card
    class="white mt-3 left-menu-card sticky-on-scroll pt-4"
    rounded="lg"
    elevation="0"
    :class="{ 'is-collapsed-menu': isProjectMenuCollapsed }"
  >
    <v-select
      v-if="!skeletonLoaderState"
      v-model="selectedProject"
      :items="projectsWithViewAll"
      item-text="name"
      dense
      item-value="key"
      class="rounded-lg field-theme custom-prepend mx-5 select-input-none"
      append-icon="mdi-chevron-down"
      background-color="#F9F9FB"
      min-height="40px"
      :menu-props="{ offsetY: true }"
    />
    <v-skeleton-loader
      v-else
      :class="{
        'd-none': isProjectMenuCollapsed,
      }"
      type="text"
      height="32"
      class="mx-5"
    />

    <v-list
      :class="isProjectMenuCollapsed ? 'px-3' : 'px-5'"
      nav
    >
      <div
        v-for="(item, index) in menuItems"
        :key="index"
      >
        <div class="text-left ml-3 my-4 group">
          <span class="fs-16px font-weight-regular disabled-action">{{ item.group }}</span>
        </div>
        <v-list-item-group
          v-model="selectedItem"
          color="primary"
        >
          <v-list-item
            v-for="(childItem) in item.list"
            :key="childItem.id"
            :to="childItem.to"
            class="mh-36px"
            :class="{ 'px-3': !isProjectMenuCollapsed, 'mb-3': isProjectMenuCollapsed }"
            active-class="active-menu-item"
            @click="setActive(childItem.id)"
          >
            <v-list-item-icon
              v-if="!isProjectMenuCollapsed"
              class="my-3 mr-2"
              :class="childItem.className"
            >
              <component :is="childItem.icon" />
            </v-list-item-icon>
            <v-tooltip
              :disabled="!isProjectMenuCollapsed"
              bottom
            >
              <template #activator="{ on, attrs }">
                <v-list-item-content
                  class="pa-0"
                  :class="childItem.className"
                  v-bind="attrs"
                  v-on="on"
                >
                  <component
                    :is="childItem.icon"
                    v-if="isProjectMenuCollapsed"
                  />
                  <v-list-item-title
                    v-if="!isProjectMenuCollapsed"
                    class="text-left"
                  >
                    <span class="fs-14px font-weight-regular text-theme-secondary">{{ childItem.title }}</span>
                  </v-list-item-title>
                </v-list-item-content>
              </template>
              <span>
                <span class="fs-14px font-weight-regular text-theme-secondary">{{ childItem.title }}</span>
              </span>
            </v-tooltip>
          </v-list-item>
        </v-list-item-group>
      </div>
    </v-list>
    
    
    <div
      class="collapse-btn"
      @click="toggleProjectMenu"
    >
      <v-icon
        class="collapse-icon"
        color="#0C2FF3"
      >
        {{ isProjectMenuCollapsed ? 'mdi-arrow-right-bottom' : 'mdi-arrow-left-bottom'
        }}
      </v-icon>
      <span
        v-if="!isProjectMenuCollapsed"
        class="collapse-text"
      >
        {{ $t('collapse') }}
      </span>
    </div>
  </v-card>
</template>

<script>
import { mapGetters, mapMutations, createNamespacedHelpers } from 'vuex';
import ProjectsService from '@/services/api/project';
import { showErrorToast } from '@/utils/toast';
import handleLoading from '@/mixins/loader.js'
import { useProjectView } from '@/composables/modules/project/index'; 
import { buildCleanRoutePath } from '@/utils/util';

const { mapState: mapProjectState, mapMutations: mapProjectMutations } = createNamespacedHelpers('project');

export default {
  name: 'ProjectLeftMenuCard',
  mixins: [handleLoading],
  props: {
    menuItems: {
      type: Array,
      default: () => [],
    }
  },

  data()
  {
    return {
      handle: this.$route.params.handle,
      selectedItem: 0,
      activeIndex: null,
      filter: 'active',
      makeProjectService: null,
    };
  },
  computed: {
    ...mapGetters(['isProjectMenuCollapsed']),
    ...mapProjectState(['projects']),
    selectedProject: {
      get() {
        return this.$route.params.key;
      },
      set(key) {
        if (key === 'all') {
          this.$router.push({ name: 'ProjectsView', params: { handle: this.handle } });
        } else {
          this.reloadCurrentRoute(key);
        }
      },
    },
    projectsWithViewAll() {
      const sortedProjects = [...this.projects]
        .sort((a, b) => a.name.localeCompare(b.name))
        .map((p) => {
          return {
            ...p,
            name: !p.archivedAt ? p.name : `${p.name} (${this.$t('archived')})`
          };
        });
      return [{ key: 'all', name: 'View All Projects' }, ...sortedProjects]
    }
  },
  created() {
    this.makeProjectService = ProjectsService(this.$api);
  },
  mounted() {
    this.getProjects();
  },
  methods: {
    ...mapMutations(['toggleProjectMenu']),
    ...mapProjectMutations(['SET_PROJECTS']),

    setActive(index)
    {
        this.$nextTick(() =>
        {
          this.selectedItem = index;
          this.activeIndex = index;
        });
    },
    reloadCurrentRoute(key){
      const currentRoute = this.$route;
      const { setProject } = useProjectView();
      
      const project = this.projects.find((project) => project.key === key);
      setProject(project);
      
      // function to build a clean path which remove the id from the path
      const basePath = buildCleanRoutePath(currentRoute.path, key);

      // Remove query params as they might be project-specific
      this.$router.replace({
        path: basePath,
        query: {}
      }).then(() => {
        this.$router.go();
      });
    },
    async getProjects() {
      const searchParams = new URLSearchParams();
      
      searchParams.set('includeCount', true); 
      try {
        const projectKey = this.$route.params.key;
        const response = await this.makeProjectService.getProjects(this.handle, searchParams.toString());

        const foundProject = response.data.items.find((project) => project.key === projectKey);

        if (foundProject) {
          const isProjectActive = foundProject.archivedAt == null;

        const filteredProjects = response.data.items.filter(
          (project) => (isProjectActive ? project.archivedAt == null : project.key === projectKey)
        );

          this.SET_PROJECTS(filteredProjects);
        } else {
          this.SET_PROJECTS([]);
        }
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'projects' }, error?.response?.data);
      }

    },
  },
};
</script>

<style scoped>
.left-menu-card {
  position: relative;
  /* height: calc(100vh - 8rem); */
  transition: width 0.3s;
}

.text-left {
  text-align: left;
}

.left-menu-card.sticky-on-scroll {
  position: -webkit-sticky;
  position: sticky;
  top: 12px;
  /* height: calc(100vh - 24px); */
}


.left-menu-card.collapsed .collapse-btn .collapse-text {
  display: none;
}

.left-menu-card.collapsed .collapse-btn {
  justify-content: center;
}

.v-select .v-select__selections {
  white-space: normal !important;
  overflow: visible !important;
}

.v-select .v-select__selection {
  white-space: normal !important;
  word-wrap: break-word;
}
.v-select input {
  display: none;
}
</style>
