<template>
  <v-expansion-panels
    :mandatory="mandatoryStatus"
    flat
  >
    <v-expansion-panel>
      <v-expansion-panel-header
        class="pa-0"
        @click="onHeaderClick"
      >
        <div class="d-flex align-center">
          <div class="checkbox-container">
            <v-checkbox
              v-model="parentCheckbox"
              :indeterminate="isIndeterminate"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
              indeterminate-icon="icon-indeterminate"
              class="text-body-2 field-theme"
              @click="onParentCheckboxClick($event)"
            >
              <template #label>
                <span class="text-theme-label fs-14px">{{ parentLabel }}</span>
              </template>
            </v-checkbox>
          </div>
          <p
            v-if="hasDescription && description"
            class="text-body-2 mb-0 grey--text description-text"
          >
            {{ description }}
          </p>
        </div>
      </v-expansion-panel-header>
      <v-expansion-panel-content>
        <div
          v-for="(item) in items"
          :key="item.id"
          class="d-flex align-center ml-5"
        >
          <div class="checkbox-container">
            <v-checkbox
              v-model="selected"
              class="text-body-2 field-theme"
              :value="item.value"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
              indeterminate-icon="icon-indeterminate"
              @change="updateParentCheckbox(item)"
            >
              <template #label>
                <span class="text-theme-label fs-14px">{{ item.name }}</span>
              </template>
            </v-checkbox>
          </div>
          <p
            v-if="item.description && hasDescription"
            class="text-body-2 mb-0 grey--text description-child-text"
          >
            {{ item.description }}
          </p>
        </div>
      </v-expansion-panel-content>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script>
export default {
  name: 'CheckboxPanel',
  props: {
    items: {
      type: Array,
      required: true,
    },
    parentLabel: {
      type: String,
      default: 'Select All',
    },
    description: {
      type: String,
      default: '',
    },
    initial: {
      type: Array,
      default: () => [],
    },
    hasDescription: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      parentCheckbox: false,
      mandatoryStatus: true,
      selected: []
    };
  },
  computed: {
    isIndeterminate() {
      return this.selected.length > 0 && this.selected.length < this.items.length;
    },
    initialSelect(){
      return this.initial
    }
  },
  watch:{
    items(val){
      if(val?.length)
        val.map(item => {
          if (item.selected && !this.selected.includes(item.value)) {
            this.selected.push(item.value)
            this.updateParentCheckbox(item)
          }
        })
    },

  },
   mounted(){
      this.items.forEach(element => {
      if(this.initial.includes(element.value)){
        this.selected.push(element.value)
        this.updateParentCheckbox(element)
      }})
  },
  methods: {
    updateParentCheckbox(permission) {
      if(permission.required?.length){
        this.$emit('update-required', permission.required)
      }
      // Update parent checkbox based on children state
      if (this.selected.length === this.items.length) {
        this.parentCheckbox = true;
      } else if (this.selected.length === 0) {
        this.parentCheckbox = false;
      }
      this.$emit('update-items', this.selected);
    },
    onHeaderClick() {
      this.mandatoryStatus = false;
    },
    onParentCheckboxClick(event) {
      event.stopPropagation();
      // Set children based on the new parent state (opposite of current state)
      const newState = this.parentCheckbox;
      this.selected = newState ? this.items.map(item => item.value) : [];
      this.parentCheckbox = newState;
      this.$emit('update-items', this.selected);
    }
  }
};
</script>

<style scoped>
.checkbox-container {
  width: 200px;
  flex-shrink: 0;
}
.description-text {
  margin-left: 4.75rem;
}
.description-child-text{
  margin-left: 2rem;
}
</style>