<template>
  <v-text-field
    v-if="!skeletonLoaderState"
    v-model="internalSearch"
    class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
    height="40"
    background-color="#F9F9FB"
    clear-icon="mdi-close-circle"
    clearable
    :hide-details="true"
    :placeholder="placeholder"
    @input="$emit('update:search', $event)"
  >
    <template #prepend-inner>
      <SearchIcon />
    </template>
  </v-text-field>
  <v-skeleton-loader
    v-else
    class="rounded-lg"
    height="40"
    width="235"
    type="button"
  />
</template>

<script>
import handleLoading from '@/mixins/loader.js';
import SearchIcon from '@/assets/svg/search-icon.svg';
export default {
  name: 'SearchComponent',
  components: {
    SearchIcon,
  },
  mixins: [handleLoading],
  props: {
    search: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: 'Search',
    },
  },
  data() {
    return {
      internalSearch: this.search,
    };
  },
  watch: {
    search(newValue) {
      this.internalSearch = newValue;
    },
  },
};
</script>
