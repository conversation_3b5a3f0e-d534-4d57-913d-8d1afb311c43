<template>
  <v-snackbar
    v-model="show"
    :timeout="-1"
    color="white"
    bottom
    right
  >
    <div class="d-flex align-start gap-2 w-full black--text">
      <SwalAlertIcon class="mr-2" />
      <div class="d-flex flex-column w-full">
        <span class="mb-2">{{ statusMessage }}</span>
        <div class="d-flex justify-center align-center w-full">
          <v-progress-linear
            :value="progress"
            height="5"
            color="primary"
            class="mb-0 w-full"
            rounded
          />
          <span class="text-right ml-2 mb-0">{{ progress }}%</span>
        </div>
      </div>
    </div>
  </v-snackbar>
</template>

<script>
import SwalAlertIcon from '@/assets/svg/swal_alert.svg';


export default {
  name: 'ProjectCreationStatus',
  components: {
    SwalAlertIcon
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    progress: {
      type: Number,
      default: 0
    },
    status: {
      type: String,
      default: 'pending',
      validator: (value) => ['pending', 'active', 'failed'].includes(value)
    }
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('input', value);
      }
    },
    statusMessage() {
      switch (this.status) {
        case 'pending':
          return 'Creating demo project...';
        case 'active':
          return 'Demo project created successfully!';
        case 'failed':
          return 'Failed to create demo project';
        default:
          return 'Creating demo project...';
      }
    }
  }
};
</script>
