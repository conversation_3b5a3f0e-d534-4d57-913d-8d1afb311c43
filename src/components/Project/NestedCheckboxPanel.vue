<template>
  <v-expansion-panels
    :mandatory="mandatoryStatus"
    flat
  >
    <v-expansion-panel>
      <v-expansion-panel-header
        class="pa-0"
        @click="onHeaderClick"
      >
        <div class="d-flex align-center">
          <div class="checkbox-container">
            <v-checkbox
              v-model="parentCheckbox"
              :indeterminate="isIndeterminate"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
              indeterminate-icon="icon-indeterminate"
              class="text-body-2 field-theme"
              @change="toggleAll()"
              @click="onParentCheckboxClick($event)"
            >
              <template #label>
                <span class="text-theme-label mb-2 fs-14px">{{ parentLabel }}</span>
              </template>
            </v-checkbox>
          </div>
          <p
            v-if="description && hasDescription"
            class="text-body-2 mb-0 grey--text description-text"
          >
            {{ description }}
          </p>
        </div>
      </v-expansion-panel-header>
      <v-expansion-panel-content>
        <div
          v-for="(item) in internalItems"
          :key="item.id"
        >
          <div class="d-flex align-center ml-5">
            <div class="checkbox-container">
              <v-checkbox
                v-model="item.selected"
                :indeterminate="isIndeterminateForActions(item)"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                indeterminate-icon="icon-indeterminate"
                class="text-body-2 field-theme"
                @change="toggleItemAndActions(item)"
              >
                <template #label>
                  <span class="text-theme-label mb-2 fs-14px">{{ item.name }}</span>
                </template>
              </v-checkbox>
            </div>
            <p
              v-if="hasDescription && item.description"
              class="text-body-2 mb-0 grey--text description-text"
            >
              {{ item.description }}
            </p>
          </div>

          <!-- Nested actions (child checkboxes) -->
          <div v-if="item.actions">
            <div
              v-for="(nestedItem) in item.actions"
              :key="nestedItem.id"
              class="d-flex align-center ml-10"
            >
              <div class="checkbox-container">
                <v-checkbox
                  v-model="nestedItem.selected"
                  class="text-body-2 field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  indeterminate-icon="icon-indeterminate"
                  @change="updateParentCheckbox"
                >
                  <template #label>
                    <span class="text-theme-label mb-2 fs-14px">{{ nestedItem.name }}</span>
                  </template>
                </v-checkbox>
              </div>
              <p
                v-if="hasDescription && nestedItem.description"
                class="text-body-2 mb-0 grey--text description-child-text"
              >
                {{ nestedItem.description }}
              </p>
            </div>
          </div>
        </div>
      </v-expansion-panel-content>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script>
export default {
    name: 'NestedCheckboxPanel',
    props: {
        items: {
            type: Array,
            required: true,
        },
        parentLabel: {
            type: String,
            default: 'Select All',
        },
        description: {
            type: String,
            default: '',
        },
        initial: {
            type: Array,
            default: () => [],
      },
        hasDescription: {
            type: Boolean,
            default: true,
        }
    },
    data() {
        return {
            internalItems: this.items.map(item => ({
                ...item,
                selected: this.initial.includes(item.value), // Set selected based on the initial array for parent items
                actions: item.actions
                    ? item.actions.map(action => ({
                        ...action,
                        selected: this.initial.includes(action.value), // Set selected based on the initial array for nested actions
                    }))
                    : [],
            })),
            parentCheckbox: false,
            mandatoryStatus: true,
        };
    },
    computed: {
        isIndeterminate() {
            const selectedCount = this.internalItems.filter(item => item.selected || this.isIndeterminateForActions(item)).length;
            return selectedCount > 0 && selectedCount < this.internalItems.length;
        },
        getValueOnSelected() {
            let data = [];
            const parent = this.internalItems
                .filter(item => item.selected && item.value !== undefined && item.value !== null)
                .map(item => item.value);
            const child = this.internalItems
                .filter(item => item.actions)
                .map(item => item.actions
                    .filter(action => action.selected && action.value !== undefined && action.value !== null)
                    .map(action => action.value))
                .flat();
            data = [...parent, ...child];
            return data;
        }
    },
    watch: {
        internalItems: {
            handler() {
                this.$emit('update-items', this.getValueOnSelected);
            },
            deep: true,
        },
        initial: {
            handler() {
                this.internalItems = this.items.map(item => ({
                    ...item,
                    selected: this.initial.includes(item.value), // Set selected based on the initial array for parent items
                    actions: item.actions
                        ? item.actions.map(action => ({
                            ...action,
                            selected: this.initial.includes(action.value), // Set selected based on the initial array for nested actions
                        }))
                        : [],
                }));
            },
            deep: true,
        },
    },
    methods: {
        toggleAll() {
            const allSelected = this.parentCheckbox;
            this.internalItems.forEach(item => {
                item.selected = allSelected;
                if (item.actions) {
                    item.actions.forEach(action => (action.selected = allSelected));
                }
            });
        },
        toggleItemAndActions(item) {
            const allSelected = item.selected;
            if (item.actions) {
                item.actions.forEach(action => (action.selected = allSelected));
            }
            this.updateParentCheckbox();
        },
        updateParentCheckbox() {
            const selectedCount = this.internalItems.filter(item => item.selected || this.isIndeterminateForActions(item)).length;
            this.parentCheckbox = selectedCount === this.internalItems.length;
        },
        isIndeterminateForActions(item) {
            if (!item.actions || item.actions.length === 0) return false;
            const selectedCount = item.actions.filter(action => action.selected).length;
            return selectedCount > 0 && selectedCount < item.actions.length;
        },
        onHeaderClick() {
            this.mandatoryStatus = false;
        },
        onParentCheckboxClick(event) {
            event.stopPropagation();
        }
    },
};
</script>

<style scoped>
.checkbox-container {
    width: 200px;
    flex-shrink: 0;
}
.description-text {
  margin-left: 4.75rem;
}
.description-child-text{
  margin-left: 2rem;
}
</style>
