<template>
  <v-card
    class="mt-3"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div
      v-if="!isSelf"
      class="text-left mb-1"
    >
      <v-btn
        color="blue"
        dark
        class="text-capitalize"
        text
        @click="$emit('go-back')"
      >
        <v-icon
          class="mr-1"
          size="xs"
        >
          mdi-chevron-left
        </v-icon>
        {{ $t('organization.backToOrganizations') }} 
      </v-btn>
    </div>

    <v-form
      ref="form"
      v-model="validForm"
      lazy-validation
    >
      <div class="d-flex justify-center flex-column align-center">
        <div
          v-if="isEditMode"
          class="text-center w-full"
        >
          <v-badge
            overlap
            color="white"
          >
            <template #badge>
              <upload-avatar
                profile-image="org"
                media-type="profile-picture"
                @uploaded="updatedAvatar"
              />
            </template>
            <Avatar
              class="mb-4"
              :size="144" 
              :avatar="{ user: org.avatarUrl }"
            />
          </v-badge>
        </div>
        <div
          v-else
          class="text-center max-width mb-5 w-full"
        >
          <h1 class="create-org-title">
            {{ $t('organization.createOrganizationTitle') }}
          </h1>
          <p class="create-org-description">
            {{ $t('organization.createOrganizationDescription') }}
          </p>

          <section class="d-flex ga-8 justify-center">
            <div class="card bg-primary">
              <span class="card-sub-title">
                {{ $t('organization.freeTrial') }}
              </span>
              <p class="card-title">
                <span class="font-weight-bold fs-32px">14</span>
                {{ $t('organization.days') }}
              </p>
            </div>
            <div class="card bg-grey">
              <span class="card-sub-title">
                {{ $t('organization.afterTrial') }}
              </span>
              <p class="card-title">
                <v-skeleton-loader
                  v-if="loadingSubscriptionPlans"
                  type="text"
                  width="80"
                  class="rounded-lg primary"
                />
                <template v-else>
                  <span class="font-weight-bold fs-32px">{{ pricePerMonth }}</span>
                  {{ $t('organization.perUserPerMonth') }}
                </template>
              </p>
            </div>
          </section>
        </div>
        <div class="pb-0 max-width w-full">
          <p class="font-weight-medium body-2 text-left mb-1">
            {{ $t('orgName') }} <strong class="red--text text--lighten-1">*</strong>
          </p>
          <v-text-field
            v-model="org.name"
            type="text"
            dense
            filled
            :placeholder="$t('enterOrgname')"
            :rules="orgNameValidation"
          />
        </div>

        <div
          class="pb-0 max-width w-full"
        >
          <p class="font-weight-medium body-2 text-left mb-1">
            {{ $t('orgHandle') }}  <strong class="red--text text--lighten-1">*</strong>
          </p>
          <v-text-field
            v-model="org.handle"
            type="text"
            dense
            filled
            :disabled="isEditMode"
            :placeholder="$t('enterOrgHandle')"
            :loading="handleRequestState.isLoading"
            :rules="accountNameValidation"
            :hint="orgHandleHint"
            :hide-details="isEditMode ? true : false"
            persistent-hint
          />
        </div>

        <div class="d-flex justify-center mt-4 max-width w-full">
          <v-btn
            color="blue"
            width="211px"
            elevation="0"
            class="white--text text-capitalize rounded  mx-auto"
            :disabled="!validForm || loadingFile"
            :loading="loadingFile"
            @click="onUpdateOrganization()"
          >
            {{ isEditMode ? $t('organization.updateOrganization') : $t('organization.startFreeTrial') }}
          </v-btn>
        </div>

        <template v-if="isEditMode">
          <div class="pb-4 mt-8 text-left w-full d-flex flex-column align-start max-width mt-12">
            <template v-if="_isOwner">
              <p class="font-weight-bold text-h6">
                {{ $t('organization.deleteOrganization') }}
              </p>
              <p class="font-weight-medium body-2">
                {{ $t('organization.deleteOrganizationNotice') }}
              </p>
              <v-btn
                color="error"
                width="200px"
                elevation="0"
                class="white--text text-capitalize mt-2"
                @click="onDeleteOrganization()"
              >
                {{ $t('organization.deleteOrganization') }}
              </v-btn>
            </template>
            <template v-else>
              <v-btn
                color="error"
                width="200px"
                elevation="0"
                class="white--text text-capitalize mt-2"
                @click="onLeaveOrganization()"
              >
                {{ $t('organization.leaveOrganization') }}
              </v-btn>
            </template>
          </div>
        </template>
      </div>
    </v-form>
    <LeaveOrganizationConfirmDialog
      v-model="showLeaveConfirmDialog"
      @leave="leaveOrganization"
    />
    <DeleteOrganizationConfirmDialog
      v-model="showDeleteConfirmDialog"
      @delete="deleteOrganization"
    />
  </v-card>
</template>

<script>
import { formatDate } from '@/utils/util';

import UploadAvatar from '@/components/Profile/UploadAvatar.vue';
import DeleteOrganizationConfirmDialog from '@/components/Settings/Organization/DeleteOrganizationConfirmDialog.vue';
import LeaveOrganizationConfirmDialog from '@/components/Settings/Organization/LeaveOrganizationConfirmDialog.vue';
import { handleDuplicateMixin } from '@/mixins/handleDuplicate';
import { createNamespacedHelpers } from 'vuex'
const { mapMutations, mapState } = createNamespacedHelpers('user');
import fileValidator from '@/mixins/fileValidator.js'
import { showErrorToast } from '@/utils/toast';
import { orgImageTypes } from '@/constants/fileTypes.js'
import makeBillingService from '@/services/api/billing';
import Avatar from "@/components/base/Avatar.vue"
export default {
  name: 'CreateUpdateOrganization',

  components: {
    UploadAvatar,
    Avatar,
    DeleteOrganizationConfirmDialog,
    LeaveOrganizationConfirmDialog
  },
  mixins: [ handleDuplicateMixin, fileValidator ],

  props: {
    data: {
      type: Object,
      default: () => ({})
    },

    isSelf: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      orgImageTypes,
      orgNameValidation: [
        value => !!value || this.$t('error.requiredField'),
        value => (value?.length >= 2 && value?.length <= 50) || this.$t('min2max50Chars')
      ],
      validForm: false,
      imageSrc: null,
      org: {
        uid: '',
        avatarUrl: '',
        name: '',
        handle: '',
        createdBy: '',
        createdAt: null,
      },
      file: null,
      loadingFile: false,
      showDeleteConfirmDialog: false,
      showLeaveConfirmDialog: false,
      loadingSubscriptionPlans: false,
      subscriptionPlans: [],
    }
  },

  computed: {
    pricePerMonth(){
      if(this.subscriptionPlans?.length){
        const price = this.subscriptionPlans.find(plan => plan.amount != 0).amount;
        return `$${price/100}`;
      }
      return '$0';
    },
    ...mapState(['user', 'currentAccount']),
    isEditMode() {
      return !!this.org.uid
    },
    _isOwner() {
      return this.authorityTo('owner');
    },
    imageStyle() {
      return this.imageSrc ? { backgroundImage: `url(${this.imageSrc})` } : {};
    },
    orgHandleHint() {
      if (this.org.handle === '') {
        return this.$t('orgAccountNameLabel')
      }
      if (!this.handleError && this.handleAvailable) {
        return "handle is available"
      }
      return ''
    },
    accountNameValidation()
    {
      if (this.isEditMode) {

        const defaultRules = [
          value => !!value || this.$t('error.requiredField'),
          value => /^(?=.{3,20}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$/.test(value) || this.$t('invalidUsername'),

        ]
        if (this.handleError) {
          return [
            ...defaultRules,
          ]
        }
        return defaultRules
      } else {
        return []
      }
    }
  },
  watch: {
    'org.handle': {
      handler: 'usernameInUse',
      immediate: true,
    },
  },

  mounted() {
    this.org = {
      uid: this.data?.uid || '',
      avatarUrl: this.data?.avatarUrl || '',
      name: this.data?.name || '',
      handle: this.data?.handle || '',
      createdBy: this.data?.created_by || '',
      createdAt: this.data?.created_at ? formatDate(this.data.created_at, 'MM/dd/yy') : null,
    }
    this.getSubscriptionPlans();
  },

  methods: {
    ...mapMutations(['updateOrg']),

    async getSubscriptionPlans() {
      this.loadingSubscriptionPlans = true;
      try {
        const billingService = makeBillingService(this.$api);
        const response = await billingService.getSubscriptionPlans({
          model: 'org',
        });
        this.subscriptionPlans = response.data;
      } catch (error) {
        console.log(error);
        showErrorToast(this.$swal, 'fetchError', { item: 'subscription plans' }, error?.response?.data);
      } finally {
        this.loadingSubscriptionPlans = false;
      }
    },
    removeImage() {
      this.imageSrc = '';
    },
    openFileDialog() {
      this.$refs.fileInput.click();
    },
    handleDrop(event) {
      const file = event.dataTransfer.files[0];
      this.previewImage(file);
    },
    previewImage(file) {
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.imageSrc = e.target.result;
        };
        reader.readAsDataURL(file);

        this.file = file;
      }
    },
    handleFileChange(event) {
      const files = Array.from(event.target.files);

      const validationResult = this.validateMimeTypes(files, orgImageTypes);

      if (!validationResult.valid) {
        showErrorToast(this.$swal, this.$t('error.fileFormatNotSupported'));
      } else {
        const file = event.target.files[0];

        this.previewImage(file);
      }
    },
    updatedAvatar(avatarURL) {
      this.org.avatarUrl = avatarURL;
      this.updateOrg(this.org)
    },
    async onUpdateOrganization() {
      const isValidForm = this.$refs.form.validate()

      if (!isValidForm) {
        return
      }

      this.$emit(this.isEditMode ? 'update' : 'create', this.org, this.file)
    },

    onDeleteOrganization() {
      this.showDeleteConfirmDialog = true
    },
    onLeaveOrganization() {
      this.showLeaveConfirmDialog = true
    },
    deleteOrganization(password) {
      this.showDeleteConfirmDialog = false

      this.$emit('delete', password)
    },
    leaveOrganization() {
      this.showLeaveConfirmDialog = false
      this.$emit('leave')
    },
  }
}
</script>

<style lang="scss" scoped>
.fs-32px{
  font-size: 32px;
}
.card{
width: 215px;
height: 112px;
border-radius: 8px;
padding: 24px;
display: flex;
flex-direction: column;
justify-content: start;
align-items: start;
}
.card-sub-title{
font-weight: 500;
font-size: 14px;
line-height: 28px;
letter-spacing: 0%;
}

.card-title{
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
margin-top: 12px;


}
.create-org-title {
  font-family: Inter;
font-weight: 600;
font-size: 24px;
line-height: 100%;
letter-spacing: 0%;
text-align: center;
margin-top: 48px;
}

.create-org-description {
font-family: Inter;
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
margin-top: 16px;
}
.rounded{
  border-radius: 6px !important;
}
.org-logo {
  border-radius: 50%;
  border: 2px dashed grey;
  width: 150px;
  height: 150px;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
  cursor: pointer;
  transition: border-color 0.3s;
  background-size: cover;
  background-position: center;
  position: relative;
}

.org-logo:hover {
  border-color: #2196f3;
}

.hovering .edit-icon,
.hovering .delete-icon {
  display: block;
}
.org-logo:hover .edit-icon,
.org-logo:hover .delete-icon {
  display: block;
}
</style>
<style scoped>
.org-logo .edit-icon,
.org-logo .delete-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateX(-30px);
  display: none;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 10px;
  cursor: pointer;
}
.org-logo .img-actions {
  display: flex;
  gap: 12px;
}
.org-logo:hover .edit-icon,
.org-logo:hover .delete-icon {
  display: block;
}
.bg-primary{
  background-color: #0C2FF3;
  color: white;
}
.bg-grey{
  background-color: #F9F9FB;
  color: #667085;
}
.max-width{
  max-width: 512px !important;
}
</style>
