<template>
  <v-data-table
    :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
    class="custom-table mt-6"
    :headers="headers"
    :items="items"
    :item-key="itemKey"
    @click:row="handleOrgClick"
  >
    <template #[`item.name`]="{ item }">
      <Avatar
        v-if="item"
        :avatar-src="item.avatarUrl"
      />
      <span class="ml-4">{{ item.name }}</span>
    </template>

    <template #[`item.uid`]="{ item }">
      <div class="d-flex justify-space-between">
        <v-btn
          icon
          color="primary"
          @click="$emit('edit', item)"
        >
          <EditIcon />
        </v-btn>
      </div>
    </template>
  </v-data-table>
</template>

<script>
import { formatDate } from '@/utils/util';
import Avatar from "@/components/base/Avatar.vue"
import EditIcon from '@/assets/svg/edit.svg';

export default {
  name: 'OrganizationsTable',

  components: {
    EditIcon,
    Avatar
  },

  props: {
    headers: Array,
    itemKey: String,
    items: Array,
  },

  methods: {
    formatCreatedDate(createdAt) {
      return formatDate(createdAt, 'MM/dd/yy')
    },
    handleOrgClick(item){
      this.$emit('select-row', item);
    }
  },
}
</script>
