<template>
  <v-dialog
    v-model="showDialog"
    class="test-cases-filter-drawer dialog-theme"
    transition="slide-x-transition"
    attach
    fullscreen
    width="485px"
    @click:outside="onCancel"
  >
    <v-card class="d-flex flex-column justify-space-between overflow-hidden">
      <v-card-text class="black--text mt-8">
        <div class="d-flex align-center justify-space-between">
          <h2 class="black--text">
            {{ $t('storage.storageConfiguration') }}
          </h2>
          <v-btn 
            icon 
            @click="onCancel"
          >
            <v-icon color="black"> 
              mdi-close 
            </v-icon>
          </v-btn>
        </div>
        <v-form
          v-if="currentStep === 0"
          :key="storageConfig.provider"
          ref="form" 
          v-model="validForm" 
          lazy-validation 
          class="mt-10"
        >
          <v-col 
            cols="12" 
            class="pb-0"
          >
            <v-label class="fs-14px text-theme-label font-weight-medium">
              {{ $t('storage.selectConfig') }}
            </v-label>
            <v-select
              v-model="storageConfig.provider"
              :items="storageTypes"
              class="rounded-lg field-theme"
              background-color="#F9F9FB"
              :placeholder="$t('storage.selectConfig')"
              append-icon="mdi-chevron-down"
              :menu-props="{ offsetY: true }"
              @change="onStorageTypeChange"
            />
          </v-col>
          <div 
            v-for="(field, key) in textFields" 
            :key="key"
          >
            <v-col 
              cols="12" 
              class="pb-0"
            >
              <v-label class="fs-14px text-theme-label font-weight-medium">
                {{ $t(`storage.${key}`) }} <strong class="red--text text--lighten-1">*</strong>
              </v-label>
              <v-text-field
                v-if="key !== 'credentials.region'"
                :value="getFieldValue(key)"
                :type="field.type || 'text'"
                class="rounded-lg field-theme"
                background-color="#F9F9FB"
                dense
                height="38px"
                :placeholder="$t(`storage.enter${key.charAt(0).toUpperCase() + key.slice(1)}`)"
                :rules="requiredRules"
                @input="setFieldValue(key, $event)"
              />
              <v-select
                v-else
                v-model="storageConfig.credentials.region"
                :items="cloudRegions"
                class="rounded-lg field-theme"
                background-color="#F9F9FB"
                dense
                height="38px"
                :placeholder="$t('storage.selectRegion')"
                append-icon="mdi-chevron-down"
                :rules="requiredRules"
                :menu-props="{ offsetY: true }"
              />
            </v-col>
          </div>
          <div 
            v-for="(field, key) in checkboxFields" 
            :key="key"
          >
            <v-row class="pb-0 pl-5">
              <v-checkbox
                :input-value="getFieldValue(key)"
                class="field-theme"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                :hide-details="true"
                @change="setFieldValue(key, $event)"
              >
                <template #label>
                  <span class="fs-14px text-theme-label">{{ $t(`storage.${key}`) }}</span>
                </template>
              </v-checkbox>
            </v-row>
          </div>
        </v-form>
      </v-card-text>
      <div>
        <div
          v-if="currentStep === 1"
          class="d-flex flex-column justify-center align-center mt-4"
        >
          <Mascot2 class="mb-4" />
          <h6 class="text-h6 mb-5 text-center">
            Files are ready to be migrated
          </h6>
        </div>
        <div
          v-if="currentStep === 4"
          class="d-flex flex-column justify-center align-center my-4"
        >
          <Mascot1 class="mb-4" />
          <h6 
            class="text-h6 mb-5 text-center" 
            v-html="task.status === 'failed' || task.status === 'error' ? errorWarning : migratingWarning" 
          />
          <a
            href="mailto:<EMAIL>"
            class="text-h6 text-decoration-none"
          >
            <EMAIL>
          </a>
        </div>
        <div
          v-if="currentStep === 2"
          class="d-flex flex-column justify-center align-center mt-4"
        >
          <LogoIcon class="mb-4" />
          <h6 
            class="text-h6 mb-5 text-center" 
            v-html="analyzingMessage" 
          />
        </div>
        <div
          v-if="currentStep === 3"
          class="d-flex flex-column justify-center align-center pa-7 ga-5 mx-auto"
          style="width: 90%;"
        >
          <div>
            <v-progress-circular
              :value="progressValue"
              :rotate="270"
              :size="150"
              :width="6"
              color="primary"
            >
              <template #default>
                <div
                  class="d-flex text-center justify-center align-center flex-column"
                >
                  <div class="svg-container mt-3">
                    <UploadCloud class="upload-cloud-icon" />
                  </div>
                  <p 
                    class="text-h6 text-center progress"
                  >
                    {{ progressValue }} %
                  </p>
                </div>
              </template>
            </v-progress-circular>
          </div>
          <h6 
            class="text-h6 my-5 text-center" 
            v-html="task.status === 'completed' ? $t('settingsPage.steps.migrationCompleteMessage') : migratingMessage"
          />
          <div
            v-if="task.status === 'running'"
            class="d-flex justify-center align-center bg-theme-base rounded-lg pt-5 ga-2"
          >
            <Mascot3 class="mb-4 mr-4" />
            <p 
              class="text-body"
              style="width: 66%;"
            >
              {{ $t('settingsPage.steps.migrationRunningMessage') }}
            </p>
          </div>
        </div>        
      </div>
      <div class="px-6 py-8">
        <v-row>
          <!-- Step 1: Show Start and Cancel Buttons -->
          <template v-if="currentStep === 1">
            <v-col cols="6">
              <v-btn
                color="#F2F4F7"
                width="100%"
                height="40"
                :depressed="true"
                class="text-capitalize btn-theme"
                elevation="0"
                @click="onCancel"
              >
                {{ $t('cancel') }}
              </v-btn>
            </v-col>
            <v-col cols="6">
              <v-btn
                width="100%"
                class="btn-theme"
                height="40"
                color="primary"
                :depressed="true"
                elevation="0"
                @click="executeStorageMigration"
              >
                {{ $t('Start') }}
              </v-btn>
            </v-col>
          </template>
          <template v-if="currentStep === 3 && task.status === 'running'">
            <v-col cols="6">
              <v-btn
                color="#F2F4F7"
                width="100%"
                height="40"
                :depressed="true"
                class="text-capitalize btn-theme"
                elevation="0"
                @click="onCancel"
              >
                {{ $t('cancel') }}
              </v-btn>
            </v-col>
            <v-col cols="6">
              <v-btn
                class="btn-theme"
                width="100%"
                height="40"
                color="primary"
                :depressed="true"
                elevation="0"
                @click="$emit('close-dialog')"
              >
                {{ $t('Check Later') }}
              </v-btn>
            </v-col>
          </template>
          <template v-if="currentStep === 3 && task.status === 'completed'">
            <v-col cols="12">
              <v-btn
                class="btn-theme"
                width="100%"
                height="40"
                color="primary"
                :depressed="true"
                elevation="0"
                @click="onCancel"
              >
                {{ $t('Close') }}
              </v-btn>
            </v-col>
          </template>
          <template v-if="currentStep === 0">
            <v-col cols="6">
              <v-btn
                color="#F2F4F7"
                width="100%"
                height="40"
                :depressed="true"
                class="text-capitalize btn-theme"
                elevation="0"
                @click="onCancel"
              >
                {{ $t('cancel') }}
              </v-btn>
            </v-col>
            <v-col cols="6">
              <v-btn
                class="btn-theme"
                width="100%"
                height="40"
                color="primary"
                :depressed="true"
                elevation="0"
                @click="onCreate"
              >
                {{ isEditMode ? $t('update') : $t('save') }}
              </v-btn>
            </v-col>
          </template>
        </v-row>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import LogoIcon from '@/assets/svg/logo-medium.svg';
import Mascot1 from '@/assets/svg/mascot1.svg';
import Mascot2 from '@/assets/svg/mascot2.svg';
import Mascot3 from '@/assets/svg/mascot3.svg';
import UploadCloud from '@/assets/svg/upload-cloud.svg';
import { startPolling, stopPolling } from '@/utils/util';
import { showErrorToast } from '@/utils/toast';

export default {
  name: 'StorageConfigDialog',

  components: {
    LogoIcon,
    Mascot1,
    Mascot2,
    Mascot3,
    UploadCloud,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    file: {
      type: Object,
      default: () => ({}),
    },
    taskId: {
      type: String,
      default: ''
    },
    regions: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      validForm: false,
      requiredRules: [
        (v) => !!v || this.$t('error.requiredField'),
      ],
      storageTypes: [
        { text: 'Amazon', value: 'aws' },
        { text: 'Google Cloud', value: 'gcp' }
      ],
      storageConfig: this.getDefaultConfig('aws'),
      pollingInterval: null,
    };
  },

  computed: {
    ...mapState('storage', ['task']),
    ...mapState('user', ['currentAccount']),
    showDialog: {
      get() {
        return this.value;
      },
      set(v) {
        this.$emit('input', v);
      },
    },
    currentStep() {
      if (this.task?.status) {
        const stepMap = {
          not_started: 2,
          running: 3,
          failed: 4,
          error: 4,
          completed: 3,
        };
        return stepMap[this.task.status] || 0;
      }

      if (!Object.keys(this.file || {}).length) return 0;
      return this.file.isOverSized ? 4 : 1;
    },
    progressValue() {
      return this.task?.percentage || 0;
    },
    migratingMessage() {
      return this.$t('settingsPage.steps.migratingMessage').replace(/\n/g, '<br>');
    },
    analyzingMessage() {
      return this.$t('settingsPage.steps.analyzingMessage').replace(/\n/g, '<br>');
    },
    migratingWarning() {
      return this.$t('settingsPage.steps.migratingWarning').replace(/\n/g, '<br>');
    },
    errorWarning() {
      return this.$t('settingsPage.steps.errorWarning').replace(/\n/g, '<br>');
    },
    isEditMode() {
      return !!this.storageConfig.uid;
    },
    visibleFields() {
      return this.getFieldsByType(this.storageConfig?.provider);
    },
    textFields() {
      return Object.fromEntries(Object.entries(this.visibleFields).filter(([, field]) => field.type !== 'checkbox'));
    },
    checkboxFields() {
      return Object.fromEntries(Object.entries(this.visibleFields).filter(([, field]) => field.type === 'checkbox'));
    },
    cloudRegions() {
      if (this.storageConfig.provider === 'aws')  return this.regions.awsRegions || [];
      if (this.storageConfig.provider === 'gcp')  return this.regions.gcpRegions || [];
      return [];
    },
  },

  watch: {
    showDialog(value) {
      if (value) {
        this.storageConfig = {
          ...this.getDefaultConfig(this.data?.provider),
          ...this.data,
        };
      }
    },
    data: {
      handler(newVal, oldVal) {
      if (this.showDialog && newVal?.provider !== oldVal?.provider) {
        this.storageConfig = {
          ...this.getDefaultConfig(newVal?.provider),
          ...newVal,
        };
      }
    },
      deep: true,
      immediate: true,
    },
    task: {
      handler(newVal) {
        if (!newVal) return;
        this.progressValue = newVal.percentage || 0;
        const stepMap = {
          not_started: 2,
          running: 3,
          failed: 4,
          error: 4,
          completed: 3,
        };
        this.currentStep = stepMap[newVal.status] || 0;

        if (['failed', 'completed', 'error'].includes(newVal.status.toLowerCase())) {
          this.endPollingProcess();
        }
      },
      immediate: true,
      deep: true,
    },
  },

  methods: {
    ...mapActions('storage', ['get','fetchAndStoreTask', 'fetchAndStoreTaskRun']),
    getDefaultConfig(key) {
      const configs = {
        "aws": {
          name: '',
          provider: 'aws',
          credentials: {
            access_key_id: '',
            secret_access_key: '',
            region: '', 
            bucket: '',
          },
        },
        "gcp": {
          name: '',
          provider: 'gcp',
          credentials: {
            bucket: '',
            type: "service_account",
            project_id: '',
            private_key_id: '',
            private_key: '',
            region: '',
            client_email: '',
            client_id: '',
            token_uri: '',
          },
        },
      };
      return configs[key] || configs['aws'];
    },
    getFieldsByType(key) {
      const fieldConfigs = {
        "aws": {
          name: {type: 'text'},
          'credentials.access_key_id': { type: 'text' },
          'credentials.secret_access_key': { type: 'text' },
          'credentials.region': { type: 'select', options: this.cloudRegions },
          'credentials.bucket': { type: 'text' },
        },
        "gcp": {
          name: { type: 'text' },
          'credentials.type': { type: 'text' },
          'credentials.region': { type: 'select', options: this.cloudRegions },
          'credentials.bucket': { type: 'text' },
          'credentials.project_id': { type: 'text' },
          'credentials.private_key_id': { type: 'text' },
          'credentials.private_key': { type: 'text' },
          'credentials.client_email': { type: 'text' },
          'credentials.client_id': { type: 'text' },
          'credentials.token_uri': { type: 'url' },
        },
      };
      return fieldConfigs[key] || {};
    },
    getFieldValue(key) {
      if (key.startsWith('credentials.')) {
        const metadataKey = key.split('.')[1];
        return this.storageConfig.credentials?.[metadataKey] ?? '';
      }
      return this.storageConfig[key];
    },
    setFieldValue(key, value) {
      if (key.startsWith('credentials.')) {
        const metadataKey = key.split('.')[1];
        if (!this.storageConfig.credentials) this.$set(this.storageConfig, 'credentials', {});
        this.$set(this.storageConfig.credentials, metadataKey, value);
      } else {
        this.$set(this.storageConfig, key, value);
      }
    },
    onStorageTypeChange() {
      this.storageConfig = this.getDefaultConfig(this.storageConfig.provider);
      this.$emit('type-change', this.storageConfig);
    },
    onCreate() {
      const isValidForm = this.$refs.form.validate();
      if (!isValidForm) return;

      this.$emit(this.isEditMode ? 'update-storage-field' : 'create-storage-field', this.storageConfig);
    }, 
    async executeStorageMigration() {
      if (!this.taskId) return;
      try {
        await this.fetchAndStoreTaskRun({handle: this.currentAccount.handle, id: this.taskId});
        this.beginPollingProcess();
      } catch (error) {
        showErrorToast(this.$swal, error.response.data.message);
        return;
      }
    },

    async pollTaskUpdates() {
      try {
        await this.fetchAndStoreTask({ handle: this.currentAccount.handle, id: this.taskId });
      } catch (error) {
        showErrorToast(this.$swal, error.response.data.message);
        this.endPollingProcess();
      }
    },

    beginPollingProcess() {
      if (!this.pollingInterval) {
        this.pollingInterval = startPolling(this.pollTaskUpdates, 3000);
      }
    },

    endPollingProcess() {
      stopPolling(this.pollingInterval);
      this.pollingInterval = null;
    },
    onCancel() {
      this.endPollingProcess();
      this.$store.commit('storage/SET_STORAGE_TASK', {});
      this.$store.commit('storage/SET_STORAGE_file', {});
      this.$emit('close-dialog');
    },
    async fetchStorages() {
      await this.get(this.currentAccount.handle);
    },
  },
};
</script>
<style>
.progress{
  color: #101828;
  font-weight: 500;
  font-size: 24px;
  font-weight: 600;
}
</style>