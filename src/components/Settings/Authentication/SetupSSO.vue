<template>
  <v-dialog
    v-model="drawer"
    class="test-cases-filter-drawer dialog-theme"
    transition="slide-x-transition"
    attach
    fullscreen
    width="485px"
    @click:outside="$emit('modalClosed', false)"
  >
    <v-card class="d-flex flex-column justify-space-between overflow-hidden">
      <v-card-text class="black--text mt-8">
        <div class="d-flex align-center justify-space-between">
          <h2 class="black--text">
            {{ $t('authentication.singleSignOn') }}
          </h2>
          <v-btn
            icon
            @click="drawer = false"
          >
            <v-icon color="black">
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <v-form
          ref="form"
          v-model="validForm"
          lazy-validation
          class="mt-10"
        >
          <v-col
            cols="12"
            class="pb-0"
          >
            <p class="font-weight-medium body-2 text-left mb-1">
              {{ $t('authentication.sso.protocol') }} <strong class="red--text">*</strong>
            </p>
            <v-select
              v-model="protocol"
              :items="protocols"
              item-text="text"
              item-value="value"
              background-color="#F9F9FB"
              height="38"
              class="field-theme"
              :rules="protocolRules"
              :hint="$t('authentication.sso.protocolHint')"
              persistent-hint
            />
          </v-col>

          <v-col
            cols="12"
            class="pb-0"
          >
            <p class="font-weight-medium body-2 text-left mb-1">
              {{ $t('authentication.sso.clientId') }} <strong class="red--text">*</strong>
            </p>
            <v-text-field
              v-model="clientId"
              type="text"
              background-color="#F9F9FB"
              height="38"
              class="field-theme"
              :rules="clientIdRules"
            />
          </v-col>

          <v-col
            cols="12"
            class="pb-0"
          >
            <p class="font-weight-medium body-2 text-left mb-1">
              {{ $t('authentication.sso.clientSecret') }} <strong class="red--text">*</strong>
            </p>
            <v-text-field
              v-model="clientSecret"
              background-color="#F9F9FB"
              height="38"
              class="field-theme"
              :rules="clientSecretRules"
              :type="visibleClientSecret ? 'text' : 'password'"
              :append-icon="visibleClientSecret ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
              @click:append="visibleClientSecret = !visibleClientSecret"
            />
          </v-col>

          <v-col
            cols="12"
            class="pb-0"
          >
            <p class="font-weight-medium body-2 text-left mb-1">
              {{ $t('authentication.sso.issuerUrl') }} <strong class="red--text">*</strong>
            </p>
            <v-text-field
              v-model="issuerUrl"
              type="text"
              background-color="#F9F9FB"
              height="38"
              class="field-theme"
              :rules="issuerUrlRules"
            />
          </v-col>

          <v-col
            cols="12"
            class="pb-0"
          >
            <p class="font-weight-medium body-2 text-left mb-1">
              {{ $t('authentication.sso.accessControl') }}
            </p>
            <v-switch
              v-model="useGroupMapping"
              :label="$t('authentication.sso.useGroupMapping')"
              color="blue"
              hide-details
              inset
              class="mt-0 mb-4"
              :disabled="onlyAllowInvited"
              @change="handleGroupMappingChange"
            />
            <v-switch
              v-model="onlyAllowInvited"
              :label="$t('authentication.sso.onlyAllowInvited')"
              color="blue"
              hide-details
              inset
              class="mt-0 mb-2"
              :disabled="useGroupMapping"
              @change="handleInviteOnlyChange"
            />
          </v-col>

          <template v-if="useGroupMapping">
            <v-col
              cols="12"
              class="pb-0"
            >
              <p class="font-weight-medium body-2 text-left mb-1">
                {{ $t('authentication.sso.groupMappings') }} <strong class="red--text">*</strong>
              </p>
              <v-alert
                type="info"
                text
                dense
                class="mb-4 text-caption"
              >
                {{ $t('authentication.sso.groupMappingsInfo') }}
              </v-alert>
              <div
                v-for="(mapping, index) in groupMappings"
                :key="index"
                class="mb-3 pa-2 group-mapping-item"
              >
                <div class="d-flex align-center justify-space-between mb-2">
                  <span class="caption grey--text text--darken-1">{{ $t('authentication.sso.mapping') }} {{ index + 1 }}</span>
                  <v-btn
                    icon
                    x-small
                    class="mt-n1"
                    @click="removeGroupMapping(index)"
                  >
                    <v-icon
                      small
                    >
                      mdi-close
                    </v-icon>
                  </v-btn>
                </div>
                <div class="d-flex align-center gap-2">
                  <v-text-field
                    v-model="mapping.group"
                    :label="$t('authentication.sso.groupName')"
                    background-color="#F9F9FB"
                    dense
                    hide-details="auto"
                    class="field-theme flex-grow-1"
                    :rules="groupNameRules"
                  />
                  <v-select
                    v-model="mapping.roleId"
                    :items="formattedRoles"
                    item-text="name"
                    item-value="uid"
                    :label="$t('authentication.sso.role')"
                    background-color="#F9F9FB"
                    dense
                    hide-details="auto"
                    class="field-theme flex-grow-1"
                    :rules="groupRoleRules"
                  />
                </div>
              </div>
              <v-btn
                color="primary"
                text
                x-small
                class="mt-1 px-0"
                @click="addGroupMapping"
              >
                <v-icon
                  x-small
                  left
                >
                  mdi-plus
                </v-icon>
                {{ $t('authentication.sso.addMapping') }}
              </v-btn>
            </v-col>
          </template>
          <template v-else>
            <v-col
              cols="12"
              class="pb-0"
            >
              <p
                class="font-weight-medium body-2 text-left mb-1"
                :class="{ 'grey--text': useGroupMapping || onlyAllowInvited }"
              >
                {{ $t('authentication.sso.defaultRole') }}
                <strong
                  v-if="!useGroupMapping && !onlyAllowInvited"
                  class="red--text"
                >*</strong>
              </p>
              <v-select
                v-model="defaultRole"
                :items="formattedRoles"
                item-text="name"
                item-value="uid"
                :disabled="useGroupMapping || onlyAllowInvited"
                background-color="#F9F9FB"
                height="38"
                class="field-theme"
                :rules="!useGroupMapping && !onlyAllowInvited ? defaultRoleRules : []"
              />
            </v-col>
          </template>
        </v-form>
      </v-card-text>
    </v-card>
    <div class="actions-container d-flex justify-space-between">
      <v-btn
        v-if="!isEditMode"
        width="204.5px"
        color="#F2F4F7"
        height="40"
        :depressed="true"
        class="text-capitalize btn-theme"
        elevation="0"
        @click="drawer = false"
      >
        {{ $t('cancel') }}
      </v-btn>
      <v-btn
        v-if="isEditMode"
        color="error"
        width="200px"
        :depressed="true"
        :loading="isDeleting"
        :disabled="isDeleting"
        height="40"
        elevation="0"
        class="white--text text-capitalize"
        @click="deleteSSOConfig()"
      >
        {{ $t('delete') }}
      </v-btn>
      <v-btn
        width="204.5px"
        class="btn-theme"
        :disabled="loading"
        :loading="loading"
        height="40"
        color="primary"
        :depressed="true"
        elevation="0"
        @click="save()"
      >
        {{ $t('save') }}
      </v-btn>
    </div>
  </v-dialog>
</template>
  
<script>
 import { mapGetters, mapActions } from 'vuex';
import makeOrgService from '@/services/api/org';
import makeRoleService from '@/services/api/role';
import { encryptObject } from '@/utils/encryption';
import { showErrorToast, showSuccessToast } from '@/utils/toast';
import { createNamespacedHelpers } from 'vuex';

const { mapState } = createNamespacedHelpers('user');

let orgService;
let roleService;

export default {
  name: 'SetupSSO',
  components: {},

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    ssoConfig: {
      type: Object,
      default: () => null,
    },
    enableSingleSignOn: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['modalClosed', 'setup-success'],
  data() {
    return {
      validForm: false,
      protocols: [
        {
          text: 'OpenID Connect',
          value: 'oidc',
        },
      ],
      protocol: '',
      clientId: '',
      clientSecret: '',
      issuerUrl: '',
      whitelistDomains: '',
      visibleClientSecret: false,
      useGroupMapping: false,
      loading: false,
      onlyAllowInvited: false,
      defaultRole: null,
      availableRoles: [],
      groupMappings: [],
      protocolRules: [(v) => !!v || this.$t('fieldRequired')],
      clientIdRules: [(v) => !!v || this.$t('fieldRequired')],
      clientSecretRules: [(v) => !!v || this.$t('fieldRequired')],
      issuerUrlRules: [
        (v) => !!v || this.$t('fieldRequired'),
        (v) => this.isValidUrl(v) || this.$t('invalidUrl'),
      ],
      defaultRoleRules: [(v) => {
        if (!this.useGroupMapping && !this.onlyAllowInvited) {
          return !!v || this.$t('fieldRequired');
        }
        return true;
      }],
      groupNameRules: [(v) => !!v || this.$t('fieldRequired')],
      groupRoleRules: [(v) => !!v || this.$t('fieldRequired')],
      isEditMode: false,
      isDeleting: false,
      isSaving: false,
      currentConfig: null,
    }
  },
  computed: {
    ...mapState(['currentAccount']),
    ...mapGetters({
      getPublicKey: 'encryption/getPublicKey',
    }),
    latestPublicKey() {
      return this.getPublicKey('auth');
    },
    formattedRoles() {
      return this.availableRoles.map(role => ({
        ...role,
        text: `${role.name} ${role.isDefault ? '(Default)' : ''}`
      }));
    },
    drawer: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('input', v)
      }
    },
  },

  watch: {
    drawer(value) {
      if (!value) {
        this.$emit('modalClosed', false)
      }

      if(this.ssoConfig?.uid && value) {

        const protocolConfig = this.ssoConfig.config?.[this.ssoConfig.protocol];

        this.protocol = this.ssoConfig.protocol;
        this.issuerUrl = protocolConfig.url;
        this.clientId = protocolConfig.key;
        this.clientSecret = protocolConfig.secret;
        this.whitelistDomains = (protocolConfig.allowedOrigins || []).join('\n');
        this.onlyAllowInvited = !!protocolConfig.allowOnlyInvitedAccounts;
        this.isEditMode = true;

        if (protocolConfig.groupMappings && Object.keys(protocolConfig.groupMappings).length > 0) {
          this.useGroupMapping = true;
          this.groupMappings = Object.entries(protocolConfig.groupMappings).map(([group, roleId]) => ({
            group, roleId,
          }));
          this.defaultRole = null;
        } 
        else {
          this.useGroupMapping = false;
          this.defaultRole = protocolConfig.defaultRole || null;
        }
      }
    },
  },
  created() {
    orgService = makeOrgService(this.$api);
    roleService = makeRoleService(this.$api);
    this.fetchPublicKey({ service: 'auth' });
  },
  async mounted() {
    try {
      const response = await roleService.getRoles(this.currentAccount.handle);
      this.availableRoles = response.data.items || [];
    } catch (error) {
      console.log(error)
      showErrorToast(this.$swal, 'error', { item: 'Authentication' }, error?.response?.data)
    }
  },
  methods: {
    ...mapActions('encryption', ['fetchPublicKey']),
    addGroupMapping() {
      this.groupMappings.push({
        group: '',
        roleId: null
      });
    },

    removeGroupMapping(index) {
      this.groupMappings.splice(index, 1);
    },
    handleGroupMappingChange(value) {
      if (value) {
        this.defaultRole = null;
        this.groupMappings = [{ group: '', roleId: null }];
      } else {
        this.groupMappings = [];
        this.defaultRole = null;
      }
    },
    handleInviteOnlyChange(value) {
      if (value && !this.useGroupMapping) {
        this.defaultRole = null;
      }
    },
    isValidUrl(url) {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    },
    async save() {
      const isValidForm = this.$refs.form.validate()

      if (!isValidForm) {
        return
      }

      const allowedOrigins = this.whitelistDomains ? this.whitelistDomains
        .split('\n')
        .map(domain => domain.trim())
        .filter(domain => domain.length > 0) : [];

      const config = {
        url: this.issuerUrl,
        allowedOrigins,
        key: this.clientId,
        secret: this.clientSecret,
        allowOnlyInvitedAccounts: this.onlyAllowInvited,
        groupMappings: {},
      }

      if (this.useGroupMapping && this.groupMappings.length > 0) {
        config.groupMappings = this.groupMappings.reduce((acc, mapping) => {
          if (mapping.group && mapping.roleId) {
            acc[mapping.group] = mapping.roleId;
          }
          return acc;
        }, {});
      }
      
      if (!this.useGroupMapping && !this.onlyAllowInvited) {
        config.defaultRole = this.defaultRole;
      }

      const { encryptedFields, symmetricKeyData } = await encryptObject(
        { key: config.key, secret: config.secret },
        this.latestPublicKey,
      );

      config.key = encryptedFields.key;
      config.secret = encryptedFields.secret;

      const payload = {
        config: {[this.protocol]: {...config }},
        isActive: this.enableSingleSignOn,
        symmetricKeyData,
      }

      if(this.currentAccount.type !== 'org') {
        showErrorToast(this.$swal, 'error', { item: 'Authentication' }, this.$t('invalidOrgHandle'))
        return;
      }

      try {
        this.loading = true
        let response;
        if(this.isEditMode) {
          response = await orgService.updateSSOConfig(this.currentAccount.handle, this.ssoConfig.uid, payload)
         this.isEditMode = false;
        } else {
          response = await orgService.setupSSOConfig(this.currentAccount.handle, payload)
        }
        this.clientId = '';
        this.clientSecret = '';
        this.issuerUrl = '';
        this.whitelistDomains = '';
        this.$emit('setup-success', {...payload, protocol: response.data.protocol, uid: response.data.uid });
        this.drawer = false
        showSuccessToast(this.$swal, this.$t('authentication.sso.ssoSetupSuccess'), { item: 'Authentication' })
      } catch (error) {
        showErrorToast(this.$swal, 'error', { item: 'Authentication' }, error?.response?.data)
      } finally {
        this.loading = false
      }
    },
    async deleteSSOConfig() {
      if(this.currentAccount.type !== 'org') {
        showErrorToast(this.$swal, 'error', { item: 'Authentication' }, this.$t('invalidOrgHandle'))
        return;
      }

      try {
        this.loading = true
        await orgService.deleteSSOConfig(this.currentAccount.handle, this.ssoConfig.uid)
        this.$emit('setup-success', null);
        this.drawer = false
        this.isEditMode = false
        this.clientId = '';
        this.clientSecret = '';
        this.issuerUrl = '';
        this.whitelistDomains = '';
        this.useGroupMapping = false;
        showSuccessToast(this.$swal, this.$t('authentication.sso.deleteSuccess'), { item: 'Authentication' })
      } catch (error) {
        showErrorToast(this.$swal, 'error', { item: 'Authentication' }, error?.response?.data)
      } finally {
        this.isDeleting = false
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.group-mapping-item {
  border: 1px solid #E0E0E0;
  border-radius: 4px;
  background-color: #FAFAFA;

  .gap-2 {
    gap: 8px;
  }
}
</style>
