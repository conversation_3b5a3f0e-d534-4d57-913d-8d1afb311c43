<template>
  <v-container
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        md="auto"
        class="d-flex align-center"
      >
        <SearchComponent
          :search="searchByName"
          :placeholder="$t('placeHolder.searchByName')"
          @update:search="searchByName = $event"
        />
      </v-col>
    </v-row>

    <v-data-table
      v-if="!skeletonLoaderState"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="custom-table mt-6"
      :headers="headers"
      :items="filteredHistories"
      item-key="id"
    >
      <template #[`item.amount`]="{ item }">
        <span>{{ `$${item.amount_paid}` }}</span>
      </template>

      <template #[`item.created_at`]="{ item }">
        <span>{{ formatCreatedAt(item.created_at) }}</span>
      </template>

      <template #[`item.status`]="{ item }">
        <span
          class="font-weight-bold text-capitalize"
          :class="item.status === 'paid' ? 'success--text' : 'error--text'"
        >
          {{ item.status }}
        </span>
      </template>

      <template #[`item.id`]="{ item }">
        <div class="d-flex justify-space-between">
          <v-btn
            icon
            color="#000"
            class="btn-plain-them"
            type="text"
            plain
            :ripple="false"
            @click="onDownloadHistory(item)"
          >
            <div class="d-flex align-center">
              <DownloadIcon />
            </div>
          </v-btn>
        </div>
      </template>
    </v-data-table>
    <template v-else>
      <HistoryTableSkeleton class="mt-6" />
    </template>
  </v-container>
</template>

<script>
import DownloadIcon from '@/assets/svg/download20px.svg';
import {  createNamespacedHelpers } from 'vuex';
import * as _ from 'lodash'
import handleLoading from '@/mixins/loader.js'
import makeUserService from '@/services/api/user';
import makeOrgService from '@/services/api/org';
import HistoryTableSkeleton from '@/components/Skeletons/Settings/Billing/HistoryTableSkeleton.vue';

import { formatDate } from '@/utils/util';
import { showErrorToast } from '@/utils/toast';

import SearchComponent from '@/components/Project/SearchComponent.vue';

const { mapState } = createNamespacedHelpers('user');

export default {
  components: {
    SearchComponent,
    HistoryTableSkeleton,
    DownloadIcon
  },
  mixins: [handleLoading],

  props: {
    isOrg: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      searchByName: '',
      headers: [
        {
          text: this.$t('name'),
          align: 'start',
          sortable: true,
          value: 'planName',
          class: 'elevation-0 rounded-l-lg',
        },
        {
          text: this.$t('common.createdAt'),
          value: 'created_at',
          sortable: true,
        },
        {
          text: this.$t('billing.amount'),
          value: 'amount',
          sortable: true,
        },
        {
          text: this.$t('status'),
          value: 'status',
          sortable: true,
        },
        {
          text: '',
          value: 'id',
          sortable: false,
          class: 'rounded-r-lg',
          width: 130,
        },
      ],
      originalHistories: [],
      filteredHistories: [],
      loading: false,
    }
  },

  computed: {
    ...mapState(['user', 'currentAccount']),
  },

  watch: {
    searchByName: {
      handler: _.debounce(function () {
        this.filterHistories()
      }, 500),
    },
  },

  mounted() {
    this.init([this.loadHistories()])
  },

  methods: {
    formatCreatedAt(createdAt) {
      return formatDate(createdAt, 'MM/dd/yy')
    },

    async loadHistories() {
      this.loading = true;

      try {
        const response = this.isOrg
          ? await makeOrgService(this.$api).getSubscriptionHistory(this.currentAccount.uid)
          : await makeUserService(this.$api).getSubscriptionHistory()
        this.originalHistories = response.data.items.map((item) => ({
          ...item,
          planName: item.price_details?.metadata?.title || this.$t('billing.unknownPlan'),
          created_at: new Date(item.date_opened * 1000),
        }))
      } catch (err) {
        showErrorToast(this.$swal, err.response?.data?.message || this.$t('error.internalServerError'))
      } finally {
        this.loading = false;
      }

      this.filterHistories()
    },

    filterHistories() {
      let filteredHistories = _.cloneDeep(this.originalHistories)

      if (this.searchByName) {
        filteredHistories = filteredHistories.filter(item => item.planName.toLowerCase().includes(this.searchByName.toLowerCase()))
      }

      this.filteredHistories = _.cloneDeep(filteredHistories)
    },

    onDownloadHistory(history) {
      if (!history.pdf_url) {
        return
      }

      window.open(history.pdf_url, '_blank')
    }
  }
}
</script>
