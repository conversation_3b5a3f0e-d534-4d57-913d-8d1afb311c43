<template>
  <div>
    <section class="mb-5">
      <div
        class="card"
        :class="{ 
          'cursor-pointer': !isDisabled, 
          'cursor-not-allowed': isDisabled,
          'card-disabled': isDisabled,
          'card-selected': plan.isSelected
        }"
        :style="{ 
          backgroundColor: plan.cardColor, 
          color: plan.cardTextColor,
          opacity: isDisabled ? 1: 1
        }"
        @click="handleCardClick"
      >
        <span class="fs-14px font-weight-medium">
          {{ plan.tier }}:
        </span>
        <p 
          class="mb-0"
          :style="{ color: isDisabled ? 'inherit' : '#667085' }"
        >
          <span
            v-if="plan.name !== 'FREE'"
            class="fs-32px font-weight-extrabold"
          >${{ plan.name }}</span>
          <span
            v-if="plan.period==='Monthly'"
            class="fs-14px font-weight-medium ms-1"
          >{{ $t("organization.perUserPerMonth") }}</span>
          <span
            v-else
          >
            <span class="fs-32px font-weight-extrabold">{{ plan.period }} </span>
            <span class="fs-14px font-weight-medium">{{ $t("organization.days") }}</span>
          </span>
        </p>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  props: {
    plan: {
      type: Object,
      required: true,
    },
    switchPaymentLoading: {
      type: Boolean,
      default: false,
    },
  },
  
  computed: {
    isDisabled() {
      return this.plan.isSelected || this.switchPaymentLoading;
    }
  },
  
  methods: {
    handleCardClick() {
      if (!this.isDisabled) {
        this.$emit('select', this.plan);
      }
    }
  }
};
</script>

<style scoped>
.card {
  width: 215px;
  height: 112px;
  border-radius: 8px;
  padding: 24px !important;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
}
.card-disabled {
  pointer-events: none;
}
.card-sub-title {
  font-weight: 500;
  font-size: 14px;
  line-height: 28px;
  letter-spacing: 0%;
}
.card-title {
  font-weight: 500;
  font-size: 24px;
  line-height: 20px;
  letter-spacing: 0%;
  margin-top: 12px;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
</style>