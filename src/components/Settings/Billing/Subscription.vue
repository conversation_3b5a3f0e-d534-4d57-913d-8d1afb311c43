<template>
  <v-card
    flat
    max-width="438"
    width="100%"
    class="pb-5"
  >
    <div
      v-if="currentView === 'subscription'"
      class="d-flex flex-column gap-4"
    >
      <h3
        v-if="!skeletonLoaderState"
        class="font-weight-semibold fs-16px mb-6"
      >
        {{ $t('billing.yourCurrentPlan') }}
      </h3>
      <v-skeleton-loader
        v-else
        class="mb-6"
        height="24"
        type="text"
      />

      <div class="d-flex justify-space-between align-center">
        <span
          v-if="!skeletonLoaderState"
          class="uppercase fw-bold"
        >{{ subscriptionInfo?.subscription?.currentPlan?.name || $t('billing.basic') }}</span>
        <v-skeleton-loader
          v-else
          class="mb-6"
          height="24"
          width="100"
          type="text"
        />

        <v-btn
          v-if="!skeletonLoaderState"
          width="138px"
          color="#F2F4F7"
          full-width
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme font-weight-semibold"
          elevation="0"
          @click="onUpgradePlan"
        >
          {{ $t('changePlan') }}
        </v-btn>
        <v-skeleton-loader
          v-else
          class="mb-6"
          height="40"
          width="138"
          type="button"
        />
      </div>

      <div v-if="!skeletonLoaderState">
        <v-label class="text-left fs-14px text-theme-label font-weight-medium">
          {{ $t('organizationName') }}
        </v-label>
        <v-hover v-slot="{ hover }">
          <v-text-field
            v-model="orgName"
            :placeholder="$t('organizationName')"
            class="round-8 field-theme"
            background-color="#F9F9FB"
            dense
            height="38px"
            hide-details
            :readonly="!isEditingOrgName"
            @input="onEditOrgName"
          >
            <template #append>
              <v-btn
                v-show="hover"
                icon
                :ripple="false"
                plain
                class="btn-plain-theme"
                @click="toggleEditOrgName"
              >
                <PencilIcon />
              </v-btn>
            </template>
          </v-text-field>
        </v-hover>
      </div>

      <v-skeleton-loader
        v-else
        class="mb-3"
        height="38"
        width="100%"
        type="text"
      />

      <div class="d-flex justify-end align-center">
        <v-btn
          v-if="!skeletonLoaderState"
          width="188px"
          color="#F2284E"
          full-width
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme font-weight-semibold white--text"
          elevation="0"
          @click="showCancelSubscriptionDialog = true"
        >
          {{ $t('cancelSubscription') }}
        </v-btn>
        <v-skeleton-loader
          v-else
          class="danger"
          height="40"
          width="188"
          type="button"
        />
      </div>

      <div
        v-if="!skeletonLoaderState"
        class="mb-3"
      >
        <v-label class="text-left fs-14px text-theme-label font-weight-medium">
          {{ $t('organizationHandle') }}
        </v-label>
        <v-text-field
          v-model="orgHandle"
          :placeholder="$t('organizationHandle')"
          class="round-8 field-theme"
          background-color="#F9F9FB"
          dense
          height="38px"
          hide-details
          disabled
        />
        <div class="d-flex align-center justify-end gap-1">
          <OutlineIcon />
          <span class="fs-14px text-theme-secondary">{{ $t('allYourDataIsStored') }}</span>
        </div>
      </div>
      <v-skeleton-loader
        v-else
        class="mb-3"
        height="38"
        width="100%"
        type="text"
      />

      <div class="d-flex flex-column gap-3">
        <div
          v-if="!skeletonLoaderState && nextPaymentMaxActiveUsers"
          class="d-flex justify-space-between align-center bg-grey rounded-lg py-2 px-3"
        >
          <span class="fs-14px text-gray-light">{{ $t('billing.currentUserCount') }}</span>
          <span
            v-if="nextPaymentMaxActiveUsers"
            class="fs-14px fw-semibold"
          >{{ nextPaymentMaxActiveUsers }}</span>
          <span
            v-else
            class="fs-14px fw-semibold"
          >
            <v-icon>mdi-minus</v-icon>
          </span>
        </div>
        <v-skeleton-loader
          v-else
          height="36"
          width="100%"
          type="text"
        />

        <div
          v-if="!skeletonLoaderState && subscriptionInfo?.subscription?.nextPaymentDate"
          class="d-flex justify-space-between align-center bg-grey rounded-lg py-2 px-3"
        >
          <span class="fs-14px text-gray-light">{{ $t('billing.nextPaymentDate') }}</span>
          <span
            v-if="subscriptionInfo?.subscription?.nextPaymentDate"
            class="fs-14px fw-semibold"
          >{{ formatDate(subscriptionInfo?.subscription?.nextPaymentDate) }}</span>
          <span
            v-else
            class="fs-14px fw-semibold"
          >
            <v-icon>mdi-minus</v-icon>
          </span>
        </div>
        <v-skeleton-loader
          v-else
          height="36"
          width="100%"
          type="text"
        />
        <div
          v-if="!skeletonLoaderState && nextPaymentCurrency"
          class="d-flex justify-space-between align-center bg-grey rounded-lg py-2 px-3"
        >
          <span class="fs-14px text-gray-light">{{ $t('billing.nextPayment') }}</span>
          <span
            v-if="nextPaymentCurrency"
            class="fs-14px fw-semibold"
          ><span class="text-uppercase">{{ `${nextPaymentCurrency}` }}</span>{{ ` ${numberLocaleFormat(nextPaymentEstimate)}` }}</span>
          <span
            v-else
            class="fs-14px fw-semibold"
          >
            <v-icon>mdi-minus</v-icon>
          </span>
        </div>
        <v-skeleton-loader
          v-else
          height="36"
          width="100%"
          type="text"
        />
      </div>

      <div class="d-flex justify-space-between align-center mt-8">
        <span
          v-if="!skeletonLoaderState"
          class="font-weight-bold fs-16px"
        >{{ $t('billing.paymentMethod') }}</span>
        <v-skeleton-loader
          v-else
          height="24"
          width="150"
          type="text"
        />
        <button
          v-if="!skeletonLoaderState"
          class=" d-flex justify-space-between align-center gap-2 text-primary"
          @click="onAddPaymentMethod"
        >
          <span class="fs-14px">{{ $t('billing.addPaymentMethod') }}</span>
          <v-icon
            size="20"
            color="primary"
            class="ml-2"
          >
            mdi-plus
          </v-icon>
        </button>
        <v-skeleton-loader
          v-else
          height="24"
          width="185"
          type="button"
          class="primary"
        />
      </div>
      <div 
        v-if="!skeletonLoaderState"
        class="mt-6 d-flex flex-column gap-4"
      >
        <div
          v-for="paymentMethod in paymentMethods"
          :key="paymentMethod.id"
          class="d-flex justify-space-between align-center"
        >
          <span class="fs-14px font-weight-medium uppercase">{{ paymentMethod.brand }} ****{{ paymentMethod.last4 }}</span> 
          <div class="d-flex align-center gap-2">
            <span
              v-if="paymentMethod.isDefault"
              class="primary-badge"
            >Primary</span>
            <v-menu offset-y>
              <template #activator="{ on }">
                <v-btn
                  icon
                  v-on="on"
                >
                  <v-icon>mdi-dots-vertical</v-icon>
                </v-btn>
              </template>
              <v-list
                dense
                flat
                class="text-left"
              >
                <v-list-item @click="onSetAsPrimary(paymentMethod.id)">
                  <FlagIcon />
                  <v-list-item-content class="ml-2 fs-14px text-theme-label">
                    <span class=""> {{ $t('billing.setAsPrimary') }}</span>
                  </v-list-item-content>
                </v-list-item>
                <v-list-item @click="showDeleteDialog(paymentMethod)">
                  <DeleteIcon />
                  <v-list-item-content class="ml-2 fs-14px text-theme-label">
                    <span class=" text-danger"> {{ $t('delete') }}</span>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
      </div>
      <div 
        v-else 
        class="mt-6 d-flex flex-column gap-4"
      >
        <v-skeleton-loader
          v-for="i in 3"
          :key="i"
          class="mb-4"
          height="36"
          width="100%"
          type="text"
        />
      </div>
    </div>
    <div v-else-if="currentView === 'changePlan'">
      <div class="d-flex align-center">
        <button
          plain
          class="btn-nav-back font-inter mb-3"
          @click="goBackToSubscription"
        >
          <v-icon color="blue">
            mdi-chevron-left
          </v-icon>
          <span class="d-flex-inline justify-center align-center ma-0 blue--text">
            {{ $t('backToCurrentPlan') }}
          </span>
        </button>
      </div>
      <BillingPlans
        :plans="subscriptionPlans"
        :current-plan="subscriptionInfo"
        :switch-payment-loading="switchPaymentLoading"
        @choose-plan="choosePlan"
        @back="goBackToSubscription"
      />
    </div>
    <choose-plan-dialog
      v-model="showChoosePlanDialog"
      :plans="subscriptionPlans"
      :current-plan="subscriptionInfo"
      :switch-payment-loading="switchPaymentLoading"
      @choose-plan="choosePlan"
    />

    <add-payment-method
      v-model="showAddPaymentMethodDialog"
      :model="isOrg ? 'org' : 'user'"
      :uid="isOrg ? currentAccount.uid : user.uid"
      :client-secret="clientSecret"
      @payment-method-added="addPaymentMethod"
      @close-dialog="showAddPaymentMethodDialog = false"
      @close="closePaymentForm"
    />

    <discard-payment-method-dialog
      v-model="showDiscardDialog"
      :title="$t('billing.deletePaymentMethodConfirm', { name: selectedPaymentMethodName })"
      :description="$t('billing.deletePaymentMethodDescription')"
      :confirm-text="$t('delete')"
      @close="handleCloseDiscardDialog"
      @handleConfirmClick="onDeletePaymentMethod"
    />

    <cancel-subscription-dialog
      v-model="showCancelSubscriptionDialog"
      :title="$t('billing.cancelSubscriptionConfirm')"
      :description="$t('billing.cancelSubscriptionDescription')"
      :confirm-text="$t('integrations.close_dialog.confirm_button')"
      @close="handleCloseCancelSubscriptionDialog"
      @handleConfirmClick="onCancelSubscription"
    />
  </v-card>
</template>

<script>
import {  createNamespacedHelpers, mapActions } from 'vuex';
import makeOrgService from '@/services/api/org';
import { showErrorToast, showSuccessToast } from '@/utils/toast';
import DeleteIcon from '@/assets/svg/delete.svg';
import FlagIcon from '@/assets/svg/milestone.svg';
import PencilIcon from '@/assets/svg/pencil20px.svg';
import OutlineIcon from '@/assets/svg/outline.svg';
import { userPlans, orgPlans } from '@/constants/plans';
import ChoosePlanDialog from '@/components/Settings/Billing/ChoosePlanDialog.vue';
import AddPaymentMethod from '@/components/Settings/Billing/AddPaymentMethod.vue';
import BillingPlans from './BillingPlans.vue';
import makeBillingService from '@/services/api/billing';
import { formatDate } from '@/utils/util';
import handleLoading from '@/mixins/loader.js'
import { debounce } from 'lodash';
import DiscardPaymentMethodDialog from '@/components/base/DiscardDialog.vue';
import CancelSubscriptionDialog from '@/components/base/DiscardDialog.vue';

const {  mapState } = createNamespacedHelpers('user');
export default {
  name: 'Subscription',
  components: {
    DiscardPaymentMethodDialog,
    DeleteIcon,
    FlagIcon,
    ChoosePlanDialog,
    AddPaymentMethod,
    PencilIcon,
    OutlineIcon,
    CancelSubscriptionDialog,
    BillingPlans
  },
  mixins: [handleLoading],
  props: {
    isOrg: {
      type: Boolean,
      default: false,
    }
  },

  data()
  {
    return {
      userService: null,
      orgService: null,
      billingService: null,
      showChoosePlanDialog: false,
      showAddPaymentMethodDialog: false,
      paymentRef: null,
      subscriptionInfo: null,
      paymentMethods: [],
      subscriptionPlans: [],
      switchPaymentLoading: false,
      clientSecret: null,
      currentView: 'subscription',
      orgName: '',
      orgHandle: '',
      isEditingOrgName: false,
      showDiscardDialog: false,
      showCancelSubscriptionDialog: false,
      selectedPaymentMethod: null,
      nextPayment: null,
    }
  },

  computed: {
    ...mapState(['user', 'currentAccount', 'orgs']),
    selectedPaymentMethodName() {
      const brand = this.selectedPaymentMethod?.brand?.toUpperCase() || '';
      const last4 = this.selectedPaymentMethod?.last4 || '';
      return `${brand} ****${last4}`;
    },
    nextPaymentEstimate() {
      return this.nextPayment?.estimated_amount || 0;
    },
    nextPaymentMaxActiveUsers() {
      return this.nextPayment?.max_active_users || 0;
    },
    nextPaymentCurrency() {
      return this.nextPayment?.currency || '';
    },
  },
  created() {
    this.debouncedUpdateOrganization = debounce(this.updateOrganization, 1000);
  },
  async mounted()
  {
    try {
      this.showSkeletonLoader();
      this.planList = this.isOrg ? orgPlans : userPlans
      this.orgService = makeOrgService(this.$api);
      this.billingService = makeBillingService(this.$api);
      this.orgName = this.currentAccount ? this.currentAccount.name : '';
      this.orgHandle = this.currentAccount ? this.currentAccount.handle : '';

      await this.getSubscriptionInfo();
      await this.getPaymentMethods();
      this.getNextPaymentEstimate();
      this.getSubscriptionPlans();
    } catch (error) {
      showErrorToast(this.$swal, 'fetchError', { item: 'subscription info' }, error?.response?.data);
    } finally {
      this.hideSkeletonLoader();
    }
    
  },

  methods: {
    toggleEditOrgName() {
      this.isEditingOrgName = !this.isEditingOrgName;
    },
    ...mapActions({
      setCurrentAccount: 'user/setCurrentAccount',
      setOrgs: 'user/setOrgs',
    }),
    onEditOrgName() {
      if (this.isEditingOrgName) {
        this.debouncedUpdateOrganization.cancel();
        this.debouncedUpdateOrganization();
      }
    },
    async updateOrganization() {
      try {
        const data = {
          orgName: this.orgName,
          handle: this.currentAccount.handle,
        }
        await this.orgService.updateOrg(data)
        const updatedOrgs = this.orgs.map((org) => {
          if (org.uid !== this.currentAccount.uid) {
            return org
          }

          org.name = this.orgName

          return org
        })
        this.setOrgs(updatedOrgs)

        showSuccessToast(this.$swal, this.$t('organization.organizationUpdated'))

        this.setCurrentAccount({
          ...this.currentAccount,
          name: this.orgName,
        })
      } catch (error) {
        showErrorToast(this.$swal, 'updateError', { item: 'organization name' }, error?.response?.data);
      } 
    },
    async onSetAsPrimary(paymentMethodId){
      try {
        await this.billingService.setDefaultPaymentMethod({
          model: this.isOrg ? 'org' : 'user',
          uid: this.isOrg ? this.currentAccount.uid : this.user.uid,
          paymentMethodId
        });
        await this.getPaymentMethods();
        showSuccessToast(this.$swal, 'updateSuccess', { item: 'set as primary' });
      } catch (error) {
        showErrorToast(this.$swal, 'updateError', { item: 'set as primary' }, error?.response?.data);
      }
    },
    showDeleteDialog(data){
      this.selectedPaymentMethod = data;
      this.showDiscardDialog = true;
    },
    handleCloseDiscardDialog() {
      this.showDiscardDialog = false;
    },
    handleCloseCancelSubscriptionDialog() {
      this.showCancelSubscriptionDialog = false;
    },
    async onDeletePaymentMethod() {
      const paymentMethodId = this.selectedPaymentMethod?.id;
      try {
        await this.billingService.deletePaymentMethod({
          model: this.isOrg ? 'org' : 'user',
          uid: this.isOrg ? this.currentAccount.uid : this.user.uid,
          paymentMethodId
        });
        await this.getPaymentMethods();
        showSuccessToast(this.$swal, 'deleteSuccess', { item: 'Payment method' });
      } catch (error) {
        showErrorToast(this.$swal, 'deleteError', { item: 'Payment method' }, error?.response?.data);
      } finally {
        this.showDiscardDialog = false;
        this.selectedPaymentMethod = null;
      }
    },
    numberLocaleFormat(number) {
      const numberValue = number || 0;
      return numberValue.toLocaleString();
    },
    formatDate(date) {
      if (!date) return '--';
      return formatDate(date, 'MM/dd/yy');
    },
    async getSubscriptionInfo(){
      try {
        const response = await this.billingService.getCurrentSubscription({
          model: this.isOrg ? 'org' : 'user',
          uid: this.isOrg ? this.currentAccount.uid : this.user.uid
        });
        this.subscriptionInfo = response.data;
        
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'subscription info' }, error?.response?.data);
      }
    },
    async onCancelSubscription(){
      try {
        await this.billingService.cancelSubscriptionPlan({
          model: this.isOrg ? 'org' : 'user',
          uid: this.isOrg ? this.currentAccount.uid : this.user.uid,
        });
        this.showCancelSubscriptionDialog = false;
      } catch (error) {
        showErrorToast(this.$swal, 'createError', { item: this.$t('cancelSubscription') }, error?.response?.data);
      }
    },
    async getNextPaymentEstimate() {
      try {
        const response = await this.billingService.nextPaymentEstimate({
          model: this.isOrg ? 'org' : 'user',
          uid: this.isOrg ? this.currentAccount.uid : this.user.uid
        });
        this.nextPayment = response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'next payment estimate' }, error?.response?.data);
      }
    },
    async getPaymentMethods() {
      try {
        const response = await this.billingService.getPaymentMethods({
          model: this.isOrg ? 'org' : 'user',
            uid: this.isOrg ? this.currentAccount.uid : this.user.uid
        });
        this.paymentMethods = response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'payment methods' }, error?.response?.data);
      }
    },

    onUpgradePlan() {
      this.currentView = 'changePlan';
    },
    goBackToSubscription() {
      this.currentView = 'subscription';
    },
    async choosePlan(plan)
    {
      try {
        this.switchPaymentLoading = true;
        const {data: {client_secret}} = await this.billingService.switchPlan({
          model: this.isOrg ? 'org' : 'user',
          uid: this.isOrg ? this.currentAccount.uid : this.user.uid,
          planId: plan.id
        });
        
        if (!client_secret) {
          this.showChoosePlanDialog = false;
          this.switchPaymentLoading = false;
          await this.getSubscriptionInfo();
          await this.getSubscriptionPlans();
          showSuccessToast(this.$swal, 'updateSuccess', { item: 'Plan' });
        } else { 
          this.onAddPaymentMethod();
          this.clientSecret = client_secret;
        }
      } catch (error) {
        showErrorToast(this.$swal, 'updateError', { item: 'change plan' }, error?.response?.data);
        this.switchPaymentLoading = false;
      }
    },



    onAddPaymentMethod()
    {
      this.showAddPaymentMethodDialog = true
    },

    async addPaymentMethod(paymentMethodData) {
      try {
        await this.billingService.addPaymentMethod({
          model: this.isOrg ? 'org' : 'user',
            uid: this.isOrg ? this.currentAccount.uid : this.user.uid,
          paymentMethodData
        });
        this.showAddPaymentMethodDialog = false;
        await this.getPaymentMethods();
        this.closePaymentForm()
        this.showChoosePlanDialog = true;
      } catch (error) {
        showErrorToast(this.$swal, 'createError', { item: 'add payment method' }, error?.response?.data);
      }
    },

    async getSubscriptionPlans() {
      try {
        const response = await this.billingService.getSubscriptionPlans({
          model: this.isOrg ? 'org' : 'user',
            uid: this.isOrg ? this.currentAccount.uid : this.user.uid
        });
        this.subscriptionPlans = response.data;        
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'subscription plans' }, error?.response?.data);
      }
    },

    closePaymentForm() {
      this.clientSecret = null;
      this.showAddPaymentMethodDialog = false;
      this.switchPaymentLoading = false;
    }


  },
  beforeDestroy() {
    if (this.debouncedUpdateOrganization) {
      this.debouncedUpdateOrganization.cancel();
    }
  }
}
</script>

<style scoped>
.primary-badge{
  color: #66BB6A;
  font-weight: 600;
  font-size: 15px;
  background-color: transparent !important;
}
.uppercase{
  text-transform: uppercase;
}
.bg-grey {
  background-color: #F9FAFB;
}

.gap-4 {
  gap: 16px;
}

.text-primary {
  color: #0C2FF3;
  font-weight: 600;
  font-size: 15px;
}
.text-gray-light {
  color: #667085;
}
.text-danger {
  color: #FF5656;
}
</style>