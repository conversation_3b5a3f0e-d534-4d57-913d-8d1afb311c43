<template>
  <v-dialog
    v-model="showDialog"
    max-width="485"
    persistent
  >
    <v-card class="">
      <div class="pa-6 black--text">
        <div class="d-flex align-center justify-space-between">
          <h2 class="black--text">
            {{ $t('billing.availablePlans') }}
          </h2>
          <v-btn
            icon
            @click="showDialog = false"
          >
            <v-icon color="black">
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <div
          v-if="!skeletonLoaderState"
          class="d-flex justify-space-between gap-2 mt-5"
        >
          <PlanCardItem
            v-for="plan in planList"
            :key="plan.name"
            :plan="plan"
            :switch-payment-loading="switchPaymentLoading"
            @select="selectPlan"
          />
        </div>
        <div
          v-else
          class="d-flex gap-2 mt-5"
        >
          <v-skeleton-loader
            v-for="i in 2"
            :key="i"
            type="card"
            width="100%"
            height="112"
            :loading="true"
            :loading-text="$t('loading')"
          />
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
import PlanCardItem from '@/components/Settings/Billing/PlanCardItem.vue';
import handleLoading from '@/mixins/loader.js';

export default {
  components: {
    PlanCardItem,
  },
  mixins: [handleLoading],

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    plans: {
      type: Array,
      default: () => [],
    },
    currentPlan: {
      type: Object,
      default: () => {},
    },
    isOrg: {
      type: Boolean,
      default: false,
    },
    switchPaymentLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },

  computed: {
    showDialog: {
      get() {
        return this.value;
      },
      set(v) {
        this.$emit('input', v);
      },
    },

    rawPlans() {
      return this.plans && this.plans.length > 0 ? this.plans : []
    },
    currentPlanPriceId() {
      return this.currentPlan?.subscription?.stripePriceId || 
             this.currentPlan?.stripePriceId || 
             null;
    },

    planList() {

      return this.rawPlans.map((plan) => {
        const isSelected = plan.stripePriceId === this.currentPlanPriceId;
        
        return {
          id: plan.stripePriceId,
          tier: plan.amount === 0 ? 'Free Trial' : 'After Trial',
          name: plan.amount === 0 ? 'FREE' : `${plan.amount / 100}`,
          price: parseFloat(plan.amount),
          period: plan.amount === 0 ? '14' : 'Monthly',
          isSelected: isSelected,
          cardColor: isSelected ? '#0C2FF3' : '#F9F9FB',
          cardTextColor: isSelected ? '#FFFFFF' : '#667085',
        };
      });
    },
  },

  methods: {
    selectPlan(plan) {
      this.$emit('choose-plan', plan);
    },

    getPlanFeatures(plan) {
      return [
        { text: `Users: ${plan.features.users}`, enabled: true },
        { text: `Test Cases: ${plan.features.testCases}`, enabled: true },
      ];
    },
  },
};
</script>
