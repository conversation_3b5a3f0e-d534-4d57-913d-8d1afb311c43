<template>
  <div>
    <div class="black--text">
      <div class="d-flex align-center justify-space-between pt-6">
        <h2 class="black--text fs-24px fw-semibold">
          {{ $t('billing.availablePlans') }}
        </h2>
      </div>

      <div
        class="d-flex justify-space-between gap-2 my-5"
      >
        <PlanCardItem
          v-for="plan in planList"
          :key="plan.name"
          :plan="plan"
          :switch-payment-loading="switchPaymentLoading"
          @select="selectPlan"
        />
      </div>
    </div>
  </div>
</template>

<script>
import PlanCardItem from '@/components/Settings/Billing/PlanCardItem.vue';

export default {
  components: {
    PlanCardItem,
  },

  props: {
    plans: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Boolean,
      default: false,
    },
    currentPlan: {
      type: Object,
      default: () => ({}),
    },
    isOrg: {
      type: Boolean,
      default: false,
    },
    switchPaymentLoading: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
    };
  },

  computed: {
    showDialog: {
      get() {
        return this.value;
      },
      set(v) {
        this.$emit('input', v);
      },
    },
    rawPlans() {
      return this.plans && this.plans.length > 0 ? this.plans : []
    },

    currentPlanPriceId() {
      return this.currentPlan?.subscription?.stripePriceId || 
             this.currentPlan?.stripePriceId || 
             null;
    },

    planList() {

      return this.rawPlans.map((plan) => {
        const isSelected = plan.stripePriceId === this.currentPlanPriceId;
        
        return {
          id: plan.stripePriceId,
          tier: plan.amount === 0 ? 'Free Trial' : 'After Trial',
          name: plan.amount === 0 ? 'FREE' : `${plan.amount / 100}`,
          price: parseFloat(plan.amount),
          period: plan.amount === 0 ? '14' : 'Monthly',
          isSelected: isSelected,
          cardColor: isSelected ? '#0C2FF3' : '#F9F9FB',
          cardTextColor: isSelected ? '#FFFFFF' : '#667085',
        };
      });
    },
  },

  methods: {
    selectPlan(plan) {
      this.$emit('choose-plan', plan);
    },

    getPlanFeatures(plan) {
      return [
        { text: `Users: ${plan.features.users}`, enabled: true },
        { text: `Test Cases: ${plan.features.testCases}`, enabled: true },
      ];
    },
  },
};
</script>
