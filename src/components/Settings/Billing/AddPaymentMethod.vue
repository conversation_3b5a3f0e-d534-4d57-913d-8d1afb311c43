<template>
  <v-dialog
    v-model="showDialog"
    class="dialog-theme"
    transition="slide-x-transition"
    attach
    fullscreen
    width="485px"
    persistent
  >
    <v-card>
      <v-card-text class="black--text">
        <div class="d-flex align-center justify-space-between pt-6">
          <h2 class="black--text">
            {{ $t('billing.addPaymentMethod') }}
          </h2>
          <v-btn
            icon
            @click="$emit('close-dialog')"
          >
            <v-icon color="black">
              mdi-close
            </v-icon>
          </v-btn>
        </div>
        <v-form
          ref="form"
          v-model="validForm"
          lazy-validation
          class="mt-5"
        >
          <v-row>
            <v-col
              cols="12"
              class="pb-0"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('billing.card') }}
              </v-label>
              <div
                id="card-element"
                class="ElementsApp"
              />
            </v-col>

            <v-col
              cols="12"
              class="pb-0 mt-3"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('firstName') }} <strong class="red--text">*</strong>
              </v-label>
              <v-text-field
                v-model="paymentInfo.firstName"
                type="text"
                dense
                filled
                :rules="requiredRules"
              />
            </v-col>

            <v-col
              cols="12"
              class="py-0"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('lastName') }} <strong class="red--text">*</strong>
              </v-label>
              <v-text-field
                v-model="paymentInfo.lastName"
                type="text"
                dense
                filled
                :rules="requiredRules"
              />
            </v-col>

        

            <v-col
              cols="12"
              class="pb-0 mt-3"
            >
              <v-label class="text-left fs-14px text-theme-label fw-semibold">
                {{ $t('billing.billingAddress') }}
              </v-label>
            </v-col>

            <v-col
              cols="12"
              class="pb-0 pt-1 mt-2"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('address.country') }} <strong class="red--text">*</strong>
              </v-label>
              <v-select
                v-model="paymentInfo.country"
                :items="countryList"
                dense
                filled
                item-text="name"
                item-value="code"
                append-icon="mdi-chevron-down"
                :rules="requiredRules"
              />
            </v-col>

            <v-col
              cols="12"
              class="pb-0 pt-1"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('address.addressLine', { index: 1 }) }} <strong class="red--text">*</strong>
              </v-label>
              <v-text-field
                v-model="paymentInfo.streetAddress"
                type="text"
                dense
                filled
                :rules="requiredRules"
              />
            </v-col>

            <v-col
              cols="12"
              class="pb-0 pt-1"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('address.addressLine', { index: 2 }) }}
              </v-label>
              <v-text-field
                v-model="paymentInfo.streetAddress2"
                type="text"
                dense
                filled
              />
            </v-col>

            <v-col
              cols="12"
              class="pb-0 pt-1"
            >
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('address.city') }} <strong class="red--text">*</strong>
              </v-label>
              
              <v-text-field
                v-model="paymentInfo.city"
                type="text"
                dense
                filled
                :rules="requiredRules"
              />
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </v-card>
    <div class="actions-container d-flex justify-space-between">
      <v-btn
        width="204.5px"
        color="#F2F4F7"
        height="40"
        :depressed="true"
        class="text-capitalize btn-theme"
        elevation="0"
        @click="$emit('close-dialog')"
      >
        {{ $t('cancel') }}
      </v-btn>
      <v-btn
        width="204.5px"
        class="btn-theme"
        height="40"
        color="primary"
        :depressed="true"
        elevation="0"
        :disabled="!validForm || loading"
        :loading="loading"
        @click="handleSubmit"
      >
        {{ $t('save') }}
      </v-btn>
    </div>
  </v-dialog>
</template>

<script>
import { countryList } from '@/constants/address';
import makeBillingService from '@/services/api/billing';
import { showSuccessToast, showErrorToast } from '@/utils/toast';

export default {
  name: 'AddPaymentMethod',

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    model: {
      type: String,
      default: 'user',
    },
    uid: {
      type: String,
      default: '',
    },
    clientSecret: {
      type: String,
      default: '',
    },
  },

  data () {
    return {
    requiredRules: [(value) => !!value || this.$t('error.requiredField')],
      validForm: false,
      paymentInfo: {
        firstName: '',
        lastName: '',
        country: '',
        streetAddress: '',
        streetAddress2: '',
        city: '',
      },
      countryList: countryList,
      cardElement: null,
      billingService: null,
      loading: false,
      error: null,
      clientSecretData: null,
    }
  },

  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('input', v)
      }
    },
  },

  watch: {
    showDialog(value) {
      if (!value) {
        this.resetForm();
        this.$emit('close');
        return;
      }

      if (this.cardElement) {
        return;
      }

      setTimeout(() => {
        const style = {
          base: {
            backgroundColor: '#f9f9fb',
            padding: '11px 10px',
            borderRadius: '4px',
            fontFamily: 'Avenir, "Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
              color: '#9f9f9f',
            },
          },
        };

        this.cardElement = this.$stripe.elements().create('card', { style });
        this.cardElement.mount('#card-element');
      }, 100)
    },
  },

  beforeDestroy () {
    this.cardElement?.destroy();
  },

  mounted() {
    this.billingService = makeBillingService(this.$api);
  },

  methods: {
    resetForm() {
      this.paymentInfo = {
        firstName: '',
        lastName: '',
        country: '',
        streetAddress: '',
        streetAddress2: '',
        city: '',
      };
      this.$refs.form?.reset();
      this.cardElement?.clear();
    },

    async handleSubmit() {
      const isValidForm = this.$refs.form.validate();
      if (!isValidForm) return;
      
      try {
        this.loading = true;
        this.error = null;

        if (!this.clientSecret) {
          const { data: { client_secret } } = await this.billingService.addPaymentMethod({
            model: this.model,
            uid: this.uid,
          });
          this.clientSecretData = client_secret;
        } else {
          this.clientSecretData = this.clientSecret;
        }

        const { error: submitError } = await this.$stripe.elements().submit();
        if (submitError) {
          throw new Error(submitError.message);
        }


        const payment_method = {
          card: this.cardElement,
            billing_details: {
              name: `${this.paymentInfo.firstName} ${this.paymentInfo.lastName}`,
              address: {
                line1: this.paymentInfo.streetAddress,
                line2: this.paymentInfo.streetAddress2,
                city: this.paymentInfo.city,
                postal_code: this.paymentInfo.postalCode,
                country: this.paymentInfo.country,
              },
            },
        }

        const { setupIntent, error } = await this.$stripe.confirmCardSetup(this.clientSecretData,  { payment_method });

        if (error) {
          throw new Error(error.message);
        }

        showSuccessToast(this.$swal, 'createSuccess', { item: 'Payment method' });
        this.showDialog = false;
        this.$emit('payment-method-added', setupIntent.payment_method);
      } catch (err) {
        showErrorToast(this.$swal, 'createError', { item: 'Payment method', message: err.message }, err?.response?.data);
      } finally {
        this.loading = false;
      }
    },
  },
}
</script>

<style lang="scss" scoped>

.ElementsApp{

    background-color: #f9f9fb;
    padding: 11px 10px;
    border-radius: 4px;

}
.payment-method {
  #card-number,
  #card-cvc,
  #card-expiry {
    padding: 11px 10px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .v-text-field.v-text-field--enclosed .v-text-field__details {
    margin-bottom: 0;
  }
}

.dialog-theme {
  .btn-theme {
    font-weight: 600;
    border-radius: 8px;
    text-transform: none !important;
  }
}
</style>