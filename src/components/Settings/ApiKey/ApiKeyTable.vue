<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="custom-table mt-6 data-table-style"
      :headers="headers"
      :items="items"
      :item-key="itemKey"
      hide-default-footer
      disable-pagination
    >
      <template #[`item.expiresAt`]="{ item }">
        <span>{{ formatExpirationDate(item.expiresAt) }}</span>
      </template>

      <template #[`item.uid`]="{ item }">
        <div class="d-flex justify-space-between">
          <v-btn
            icon
            color="primary"
            @click="$emit('edit', item)"
          >
            <EditIcon />
          </v-btn>
          <v-tooltip 
            bottom
            :disabled="deleteEntity" 
          >
            <template #activator="{ on, attrs }">
              <div 
                v-bind="attrs" 
                v-on="on"
              >
                <v-btn
                  :disabled="!deleteEntity"
                  icon
                  color="primary"
                  @click="$emit('delete', item)"
                >
                  <DeleteIcon />
                </v-btn>
              </div>
            </template>
            <span>
              {{ $t('settingsPage.noPermissionToDo', { action: $t('delete').toLowerCase(), type: $t('api key') }) }}
            </span>
          </v-tooltip>
        </div>
      </template>
    </v-data-table>
    <template v-else>
      <ApiKeyTableSkeleton class="mt-6" />
    </template>
  </div>
</template>

<script>
import { formatDate } from '@/utils/util';
import handleLoading from '@/mixins/loader.js';
import ApiKeyTableSkeleton from '@/components/Skeletons/Settings/ApiKey/ApiKeyTableSkeleton.vue';
import EditIcon from '@/assets/svg/edit.svg';
import DeleteIcon from '@/assets/svg/delete.svg';

export default {
  name: 'APIKeyTable',
  components: {
    EditIcon,
    DeleteIcon,
    ApiKeyTableSkeleton
  },
  mixins: [handleLoading],
  props: {
    headers: Array,
    itemKey: String,
    items: Array,
    deleteEntity: {
      type: Boolean,
      default: true,
    },
  },

  methods: {
    formatExpirationDate(expiresAt) {
      return formatDate(expiresAt, 'MM/dd/yy')
    }
  },
}
</script>
