<template>
  <v-data-table
    :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
    :headers="headers"
    :items="colorItems"
    :item-key="itemKey"
    class="data-table-style personalization-table mt-6"
    hide-default-footer
    disable-pagination
  >
    <template #[`item.color`]="{ item }">
      <div class="color-column-wrapper">
        <ColorItem
          :value="item.color"
          default-color="#000"
          :disabled="false"
          @input="handleColorChange(item, $event)"
        />
        <v-icon 
          class="refresh-icon-display"
          size="18"
        >
          mdi-refresh
        </v-icon>
      </div>
    </template>

    <template #[`item.testCase`]="{ item }">
      <div 
        class="text-center entity-cell"
        @click="handleCellClick(item, 'testCase')"
      >
        <span
          v-if="getStatusForEntityType(item, 'testCase') !== '-'"
          class="status-text"
        >
          {{ getStatusForEntityType(item, 'testCase') }}
          <v-icon 
            class="edit-icon"
            size="16"
            @click.stop="handleEditStatus(item, 'testCase')"
          >
            mdi-pencil
          </v-icon>
        </span>
        <span
          v-else
          class="empty-cell"
        >
          <span class="dash-text">-</span>
          <v-icon 
            class="add-icon"
            size="16"
            @click.stop="handleAddStatus(item, 'testCase')"
          >
            mdi-plus
          </v-icon>
        </span>
      </div>
    </template>

    <template #[`item.testExecution`]="{ item }">
      <div 
        class="text-center entity-cell"
        @click="handleCellClick(item, 'testExecution')"
      >
        <span
          v-if="getStatusForEntityType(item, 'testExecution') !== '-'"
          class="status-text"
        >
          {{ getStatusForEntityType(item, 'testExecution') }}
          <v-icon 
            class="edit-icon"
            size="16"
            @click.stop="handleEditStatus(item, 'testExecution')"
          >
            mdi-pencil
          </v-icon>
        </span>
        <span
          v-else
          class="empty-cell"
        >
          <span class="dash-text">-</span>
          <v-icon 
            class="add-icon"
            size="16"
            @click.stop="handleAddStatus(item, 'testExecution')"
          >
            mdi-plus
          </v-icon>
        </span>
      </div>
    </template>

    <template #[`item.testRun`]="{ item }">
      <div 
        class="text-center entity-cell"
        @click="handleCellClick(item, 'testRun')"
      >
        <span
          v-if="getStatusForEntityType(item, 'testRun') !== '-'"
          class="status-text"
        >
          {{ getStatusForEntityType(item, 'testRun') }}
          <v-icon 
            class="edit-icon"
            size="16"
            @click.stop="handleEditStatus(item, 'testRun')"
          >
            mdi-pencil
          </v-icon>
        </span>
        <span
          v-else
          class="empty-cell"
        >
          <span class="dash-text">-</span>
          <v-icon 
            class="add-icon"
            size="16"
            @click.stop="handleAddStatus(item, 'testRun')"
          >
            mdi-plus
          </v-icon>
        </span>
      </div>
    </template>

    <template #[`item.testPlan`]="{ item }">
      <div 
        class="text-center entity-cell"
        @click="handleCellClick(item, 'testPlan')"
      >
        <span
          v-if="getStatusForEntityType(item, 'testPlan') !== '-'"
          class="status-text"
        >
          {{ getStatusForEntityType(item, 'testPlan') }}
          <v-icon 
            class="edit-icon"
            size="16"
            @click.stop="handleEditStatus(item, 'testPlan')"
          >
            mdi-pencil
          </v-icon>
        </span>
        <span
          v-else
          class="empty-cell"
        >
          <span class="dash-text">-</span>
          <v-icon 
            class="add-icon"
            size="16"
            @click.stop="handleAddStatus(item, 'testPlan')"
          >
            mdi-plus
          </v-icon>
        </span>
      </div>
    </template>

    <template #[`item.milestone`]="{ item }">
      <div 
        class="text-center entity-cell"
        @click="handleCellClick(item, 'milestone')"
      >
        <span
          v-if="getStatusForEntityType(item, 'milestone') !== '-'"
          class="status-text"
        >
          {{ getStatusForEntityType(item, 'milestone') }}
          <v-icon 
            class="edit-icon"
            size="16"
            @click.stop="handleEditStatus(item, 'milestone')"
          >
            mdi-pencil
          </v-icon>
        </span>
        <span
          v-else
          class="empty-cell"
        >
          <span class="dash-text">-</span>
          <v-icon 
            class="add-icon"
            size="16"
            @click.stop="handleAddStatus(item, 'milestone')"
          >
            mdi-plus
          </v-icon>
        </span>
      </div>
    </template>
  </v-data-table>
</template>

<script>
import ColorItem from '@/components/Settings/DataColors/ColorItem.vue';

export default {
  name: 'ColorsTable',

  components: {
    ColorItem,
  },

  props: {
    headers: Array,
    itemKey: String,
    items: Array,
    type: String,
    onEdit: Function,
    onAddStatus: Function,
    onColorChange: Function,
    allStatuses: {
      type: Array,
      default: () => []
    },
    allPriorities: {
      type: Array,
      default: () => []
    },
    filterType: {
      type: String,
      default: 'statusColors'
    }
  },
  computed: {
    colorItems() {
      return this.items || [];
    },
  },
  methods: {
    getStatusForEntityType(item, entityType) {
      // For priorities, milestones don't have priorities
      if (this.filterType === 'priorityColors' && entityType === 'milestone') {
        return '-';
      }
      
      // Find the status/priority for this entity type
      let allItems = [];
      if (this.filterType === 'statusColors') {
        allItems = this.allStatuses;
      } else if (this.filterType === 'priorityColors') {
        allItems = this.allPriorities;
      }
      
      const matchingItem = allItems.find(status => 
        status.entityType === entityType && 
        status.color === item.color &&
        status.name
      );
      
      return matchingItem ? matchingItem.name : '-';
    },
    
    handleAddStatus(item, entityType) {
      if (this.onAddStatus) {
        this.onAddStatus(item, entityType);
      }
    },
    
    handleCellClick(item, entityType) {
      const statusName = this.getStatusForEntityType(item, entityType);
      if (statusName !== '-') {
        // If there's a status, trigger edit
        this.handleEditStatus(item, entityType);
      } else {
        // If empty, trigger add
        this.handleAddStatus(item, entityType);
      }
    },
    
    handleEditStatus(item, entityType) {
      if (this.onEdit) {
        // Find the actual status object to edit
        let allItems = [];
        if (this.filterType === 'statusColors') {
          allItems = this.allStatuses;
        } else if (this.filterType === 'priorityColors') {
          allItems = this.allPriorities;
        }
        
        const statusToEdit = allItems.find(status => 
          status.entityType === entityType && 
          status.color === item.color &&
          status.name
        );
        
        if (statusToEdit) {
          this.onEdit('Edit', statusToEdit.id, null);
        }
      }
    },
    
    handleColorChange(item, newColor) {
      // Update all statuses/priorities that use this color (entire row)
      let allItems = [];
      if (this.filterType === 'statusColors') {
        allItems = this.allStatuses;
      } else if (this.filterType === 'priorityColors') {
        allItems = this.allPriorities;
      }
      
      // Update each item with the new color (this affects the entire row)
      const updatedItems = allItems.map(status => 
        status.color === item.color ? { ...status, color: newColor } : status
      );
      
      // Emit the change to parent component
      if (this.onColorChange) {
        this.onColorChange(updatedItems);
      }
      
      // Also emit the reset-color event for backward compatibility
      this.$emit('reset-color', item, newColor);
    }
  }
}
</script>

<style lang="scss">
  .no-pointer-events {
    pointer-events: none;
  }

  .justify-content-right {
    justify-content: right;
  }

  .refresh-icon-display {
    color: #666;
    opacity: 0.7;
    cursor: default;
    pointer-events: none;
  }

  .color-column-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    min-width: 0;
  }

  .color-column-wrapper .d-flex {
    flex-shrink: 0;
    min-width: 0;
  }

  // Ensure the color column has enough space
  .v-data-table td:first-child {
    text-align: left;
    padding-left: 16px;
    padding-right: 16px;
    min-width: 200px;
  }

  .entity-cell {
    cursor: pointer;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .empty-cell {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .dash-text {
    transition: opacity 0.2s ease;
  }

  .add-icon {
    position: absolute;
    color: #666;
    opacity: 0;
    transition: opacity 0.2s ease;
    
    &:hover {
      color: #0c2ff3;
    }
  }

  .entity-cell:hover .dash-text {
    opacity: 0;
  }

  .status-text {
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }

  .edit-icon {
    color: #666;
    opacity: 0;
    transition: opacity 0.2s ease;
    
    &:hover {
      color: #0c2ff3;
    }
  }

  .entity-cell:hover .add-icon {
    opacity: 1;
  }

  .entity-cell:hover .edit-icon {
    opacity: 1;
  }

  .v-data-table__selected, .v-data-table-header {
    .v-icon {
      color: #0c2ff3 !important;
      caret-color: #0c2ff3 !important;
    }
  }

  // Personalization table specific styles
  .personalization-table {
    table-layout: fixed;
    width: 100%;
    min-width: 800px; // Ensure minimum width for smaller screens
  }

  .personalization-table .v-data-table__wrapper {
    overflow-x: auto;
    min-width: 100%;
  }

  // Responsive adjustments for smaller screens
  @media (max-width: 1200px) {
    .personalization-table {
      min-width: 700px;
    }
    
    .color-column-wrapper {
      gap: 8px;
      padding: 4px 0;
    }
    
    .color-column-wrapper .d-flex {
      font-size: 12px;
    }
  }

  @media (max-width: 768px) {
    .personalization-table {
      min-width: 600px;
    }
    
    .color-column-wrapper {
      gap: 6px;
      padding: 2px 0;
    }
  }

  // Ensure proper column alignment
  .v-data-table td {
    vertical-align: middle;
  }

  // Color column (first column) should be left-aligned
  .v-data-table td:first-child {
    text-align: left;
  }

  // Entity type columns should be center-aligned
  .v-data-table td:not(:first-child) {
    text-align: center;
  }
</style>
