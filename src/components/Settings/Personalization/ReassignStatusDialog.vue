<template>
  <v-dialog
    v-model="showDialog"
    max-width="500"
    persistent
  >
    <v-card class="pa-2">
      <v-card-text class="black--text">
        <div class="d-flex align-center justify-space-between pt-6">
          <h2 class="black--text">
            {{ $t( 'reAssignStatus' ) }}
          </h2>
          <v-btn
            icon
            @click="handleCancel"
          >
            <v-icon color="black">
              mdi-close
            </v-icon>
          </v-btn>
        </div>
        <v-text-field
          v-model="search"
          type="text"
          value="dfs"
          dense
          background-color="#F9F9FB"
          height="38"
          prepend-inner-icon="mdi-magnify"
          class="field-theme mt-4"
          :placeholder="$t('Search')"
        />
        <div class="status-list">
          <div 
            v-for="item in colorItems" 
            :key="item.value"
            class="status-item pa-2 text-body-2 cursor-pointer"
            :class="{ 
              'grey lighten-4': selectedStatus === item.name,
              'grey--text text--darken-3': selectedStatus !== item.name
            }"
            @click="selectStatus(item.name)"
          >
            {{ item.name }}
          </div>
        </div>
      </v-card-text>

      <v-card-actions class="pb-3">
        <v-row>
          <v-col cols="6">
            <v-btn
              color="gray-100"
              width="100%"
              class="text-capitalize"
              elevation="0"
              @click="handleCancel"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>

          <v-col cols="6">
            <v-btn
              color="primary"
              width="100%"
              elevation="0"
              class="white--text text-capitalize"
              @click="handleConfirm"
            >
              {{ $t('confirm') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
<script>
import { createNamespacedHelpers } from 'vuex';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import { showErrorToast } from '@/utils/toast';

const { mapState, mapGetters: mapUserGetters } = createNamespacedHelpers('user');

export default {
  name: 'ReassignStatusDialog',
  
  mixins: [colorPreferencesMixin],

  props: {
    value: {
      type: Boolean,
      default: false,
    },

    item: {
      type: Object,
      default: () => ({})
    },
    currentStatus: {
      type: Object,
      default: () => ({})
    },
    
    personalizationType: {
      type: String,
      default: 'Status'
    },
    
    totalCount: {
      type: Number,
      default: 0
    }
  },

  data(){
    return {
      search: "",
      selectedStatus: null,
    }
  },

  computed: {
    ...mapState(["currentAccount"]),
    ...mapUserGetters({
      getUserPreferences: "getUserPreferences",
    }),
    
    showDialog: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('input', v)
      }
    },
    
    allStatusColors() {
      if (this.currentAccount?.type === 'org') {
        return this.getOrgPreferences(this.currentAccount.handle)?.statusColors || [];
      } else {
        return this.getUserPreferences?.statusColors || [];
      }
    },

    colorItems() {
      const currentEntityType = this.currentStatus.entityType?.value || this.currentStatus.entityType;
      
      let filtered = this.allStatusColors.filter(color => {
        return color.entityType === currentEntityType && 
               !color.archived && 
               color.id !== this.currentStatus.id;
      });

      if (this.search) {
        filtered = filtered.filter(color =>
          color.name.toLowerCase().includes(this.search.toLowerCase())
        );
      }
      
      return filtered;
    },
  },
  
  methods: {
    handleCancel() {
      this.selectedStatus = null;
      this.$emit('cancel')
      this.$emit('input', false)
    },

    handleConfirm() {
      if (!this.selectedStatus) {
        showErrorToast(this.$swal, this.$t('pleaseSelectStatus'));
        return;
      }
      
      const selectedStatusObj = this.colorItems.find(item => item.name === this.selectedStatus);
      
      if (!selectedStatusObj) {
        showErrorToast(this.$swal, this.$t('invalidSelection'));
        return;
      }
      
      this.$emit('confirm', selectedStatusObj);
      this.selectedStatus = null;
    },
    
    selectStatus(value) {
      this.selectedStatus = value;
    },
  }
}
</script>

