<template>
  <div>
    <v-btn
      color="blue"
      depressed
      height="40px"
      class="btn-theme rounded-lg white--text"
      @click="onAdd('Add')"
    >
      {{ $t('dataColors.addStatus') }}
    </v-btn>

    <v-dialog
      v-model="localShowDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card class="flex-column justify-space-between">
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ action + ' ' + $t('status') }}
            </h2>
            <v-btn
              icon
              @click="modalOpen(false)"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>

          <v-form
            ref="form"
            v-model="validForm"
            lazy-validation
            class="mt-10"
          >
            <v-row>
              <v-col
                cols="12"
                class="pb-0"
              >
                <p class="font-weight-medium body-2 text-left mb-1">
                  {{ $t('dataColors.name') }} <strong class="red--text">*</strong>
                </p>
                <v-text-field
                  v-model="status.name"
                  type="text"
                  dense
                  background-color="#F9F9FB"
                  height="38"
                  class="field-theme"
                  :placeholder="$t('name')"
                  :rules="requiredRules"
                />
              </v-col>
              <v-col
                cols="12"
                class="pb-0"
              >
                <p class="font-weight-medium body-2 text-left mb-0">
                  {{ $t('dataColors.aliases') }}
                </p>
                <v-combobox
                  v-model="status.aliases"
                  multiple
                  chips
                  small-chips
                  deletable-chips
                  clearable
                  hide-selected
                  hide-details
                  background-color="#F9F9FB"
                  class="rounded-lg pt-0 field-theme custom-prepend mh-38px"
                  @keydown.enter.native.stop
                  @change="onAliasesChange"
                />
                <p 
                  v-if="aliasesError" 
                  class="red--text mb-0 caption font-weight-normal"
                >
                  {{ aliasesError }}
                </p>
                <p class="font-weight-normal caption text-left mb-1">
                  {{ $t('dataColors.aliasesDescription') }}
                </p>
              </v-col>

              <v-col cols="12">
                <p class="font-weight-medium body-2 text-left mb-1">
                  {{ $t('dataColors.category') }} <strong class="red--text">*</strong>
                </p>
                <v-select
                  v-model="status.entityType"
                  :items="entityTypes"
                  class="rounded-lg field-theme custom-prepend"
                  dense
                  background-color="#F9F9FB"
                  item-text="name"
                  item-value="value"
                  :placeholder="$t('dataColors.category')"
                  height="38px"
                  append-icon="mdi-chevron-down"
                  persistent-placeholder
                  :rules="requiredRulesForArrayType"
                  return-object
                />
              </v-col>

              <v-col cols="12">
                <p class="font-weight-medium body-2 text-left mb-1">
                  {{ $t('dataColors.color') }}
                </p>
                <template>
                  <ColorItem
                    :value="status.color"
                    default-color="#000"
                    class="color-picker"
                    @input="changeColor('chart', 0, $event)"
                  />
                </template>
              </v-col>

              <!-- Test Case Specific Options -->
              <v-col
                cols="12"
                class="pb-0"
              >
                <!-- Is considered approved -->
                <div class="d-flex justify-space-between align-center mb-2">
                  <div class="d-flex align-center">
                    <span
                      class="fs-14"
                      :class="{'text-theme-label': !status.isApproved, 'text-disabled': status.isApproved}"
                    >
                      {{ $t('dataColors.isApproved') }}
                    </span>
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-icon
                          small
                          class="ml-1"
                          v-bind="attrs"
                          v-on="on"
                        >
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span>{{ $t('dataColors.isApprovedTooltip') }}</span>
                    </v-tooltip>
                  </div>
                  <v-switch
                    v-model="status.isApproved"
                    inset
                    hide-details
                    class="custom-switch mt-0"   
                    @click="handleSwitchToggle('approved', 'isApproved', $event)"
                  />
                </div>

                <!-- Is considered declined -->
                <div class="d-flex justify-space-between align-center mb-2">
                  <div class="d-flex align-center">
                    <span
                      class="fs-14"
                      :class="{'text-theme-label': !status.isDeclined, 'text-disabled': status.isDeclined}"
                    >
                      {{ $t('dataColors.isDeclined') }}
                    </span>
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-icon
                          small
                          class="ml-1"
                          v-bind="attrs"
                          v-on="on"
                        >
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span>{{ $t('dataColors.isDeclinedTooltip') }}</span>
                    </v-tooltip>
                  </div>
                  <v-switch
                    v-model="status.isDeclined"
                    inset
                    hide-details
                    class="custom-switch mt-0"
                    @click="handleSwitchToggle('declined', 'isDeclined', $event)"
                  />
                </div>

                <!-- Assign as a default -->
                <div class="d-flex justify-space-between align-center mb-2">
                  <div class="d-flex align-center">
                    <span
                      class="fs-14"
                      :class="{'text-theme-label': !defaultChanged || !status.isDefault, 'text-disabled': defaultChanged && status.isDefault}"
                    >
                      {{ $t('dataColors.assignDefault') }}
                    </span>
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-icon
                          small
                          class="ml-1"
                          v-bind="attrs"
                          v-on="on"
                        >
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span>{{ $t('newStatusTooltip') }} {{ status.entityType?.name || findEntityNameByValue(status.entityType) }}</span>
                    </v-tooltip>
                  </div>
  
                  <div class="d-flex align-center">
                    <v-menu
                      v-if="status.isDefault && defaultChanged && action === 'Edit'"
                      v-model="showTooltip"
                      :close-on-content-click="false"
                      :open-on-hover="true"
                      :close-delay="200"
                      :open-delay="0"
                      left
                      offset-x
                      class="custom-tooltip-menu"
                      :nudge-left="10"
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-switch
                            v-model="status.isDefault"
                            inset
                            hide-details
                            class="custom-switch mt-0"
                            :disabled="defaultChanged && status.isDefault"
                            @click="handleSwitchToggle('default', 'isDefault', $event)"
                          />
                        </div>
                      </template>

                      <v-card
                        class="reassign-tooltip pa-3"
                        max-width="280"
                      >
                        <span class="body-2">
                          {{ $t('reassignDefaultTooltipStart') }}
                          <span 
                            class="primary--text underline cursor-pointer"
                            @click="openReassignDialog"
                          >
                            {{ $t('reassignLinkText') }}
                          </span> 
                          {{ $t('reassignDefaultTooltipEnd') }}
                        </span>
                      </v-card>
                    </v-menu>

                    <v-switch
                      v-else
                      v-model="status.isDefault"
                      inset
                      hide-details
                      class="custom-switch mt-0"
                      :disabled="defaultChanged && status.isDefault"
                      @click="handleSwitchToggle('default', 'isDefault', $event)"
                    />
                  </div>
                </div>
              </v-col>

              <v-col class="flex justify-end">
                <v-btn
                  v-if="action === 'Edit'"
                  color="red"
                  height="40"
                  depressed
                  elevation="0"
                  class="white--text text-capitalize btn-theme rounded-lg"
                  @click="onDeleteClick()"
                >
                  {{ $t('delete') }}
                </v-btn>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="modalOpen(false)"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          :disabled="!validForm"
          @click="onCreateUpdateDelete(action, status.name, status.entityType)"
        >
          {{ action + ' ' + 'status' }}
        </v-btn>
      </div>
    </v-dialog>

    <DeleteConfirmDialog
      v-model="showDeleteConfirmDialog"
      :item="{name: status.name, type: 'Status'}"
      @delete="deleteColor"
    />
    <ConfirmationDialog
      v-model="showSuccessDialog" 
      :item="{name: status.name, type: 'Status'}"
      :switch-type="currentSwitchType"
      @confirm="confirmToggle"
      @cancel="revertSwitch"
    />

    <ReassignStatusDialog
      v-model="showReassignDialog"
      :item="status"
      :current-status="status"
      :personalization-type="'Status'"
      :total-count="totalCount"
      @confirm="onReassignConfirm"
      @cancel="onReassignCancel"
    />
  </div>
</template>

<script>
import _ from 'lodash';
import { createNamespacedHelpers, mapGetters } from 'vuex';
import { entityTypes } from '@/constants/colors.js';
import { mapActions } from 'vuex';
import ColorItem from '@/components/Settings/DataColors/ColorItem.vue';
import DeleteConfirmDialog from '@/components/Settings/Personalization/DeleteConfirmDialog'
import makeHandleService from '@/services/api/handle';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import { requiredFieldValidationRules, requiredFieldValidationRulesForArray } from "@/utils/validation";
import ConfirmationDialog from './ConfirmationDialog.vue';
import ReassignStatusDialog from './ReassignStatusDialog.vue';

const { mapState } = createNamespacedHelpers('user');
let handleService;

export default {
  name: "AddTestCaseStatus",

  components: {
    ColorItem,
    DeleteConfirmDialog,
    ConfirmationDialog,
    ReassignStatusDialog
  },
  mixins: [colorPreferencesMixin],
  props: {
    action: String,
    totalCount: Number,
    currentOrg: Object,
    currentUser: Object,
    value: String,
    type: String,
    singleItemIndex: String,
    showDialog: Boolean,
    modalOpen: Function,
    onAdd: Function,
    selectedItem: [Object, Array],
    selectedCounts: Number,
  },

  data() {
    return {
      requiredRules: requiredFieldValidationRules(this),
      requiredRulesForArrayType: requiredFieldValidationRulesForArray(this),
      showPickColorDialog: false,
      status: {
        name: '',
        aliases: [],
        entityType: '',
        color: null,
        id: '',
        isDefault: false,
        isApproved: false,
        isDeclined: false,
      },
      aliasesError: '',
      validForm: false,
      showDeleteConfirmDialog: false,
      defaultChanged: false,
      statusDefaultCounted: false,
      showToggleConfirmDialog: false,
      currentToggleType: '',
      pendingToggleValue: null,
      currentDefaultStatus: null,
      showTooltip: false,
      showReassignDialog: false,
      currentSwitchType: '',
      currentSwitchProperty: '',
      showSuccessDialog: false,
      originalValue: false,
    }
  },
  computed: {
    ...mapState(['currentAccount']),
    ...mapGetters({
      user: 'user/user',
      orgs: 'user/orgs',
    }),
    localShowDialog: {
      get() {
        return this.showDialog;
      },
      set(value) {
        this.$emit('update:showDialog', value);
      }
    },
    entityTypes(){
      // Only show testCase entity type for test case status management
      return entityTypes.filter(element => element.value === 'testCase')
    },
    getToggleDialogItem() {
      return {
       name: this.currentDefaultStatus || this.status.name,
       type: this.status.entityType?.name || this.findEntityNameByValue(this.status.entityType)
    };
  }
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newItem) {
        if (newItem) {
          this.status.name = newItem.name || '';
          this.status.aliases = newItem.aliases || [];
          this.status.entityType = newItem.entityType || '';
          this.status.color = newItem.color || this.generateRandomHexColor();
          this.status.id = newItem.id || '';
          this.status.isDefault = newItem.isDefault || false;
          this.status.isApproved = newItem.isApproved || false;
          this.status.isDeclined = newItem.isDeclined || false;
          this.defaultChanged = newItem.isDefault || false;
          this.statusDefaultCounted = newItem.isApproved || newItem.isDeclined || false;
        }
      },
    },
    localShowDialog(value) {
      if (value && this.action === 'Add' && !this.selectedItem?.color) {
        this.status.color = this.generateRandomHexColor();
      }
    },
    status: {
      handler() {
        this.aliasesError = this.checkDuplicateAliases()
          ? '' : 'Duplicate aliases found within the same entity type.'
      },
      deep: true,
    },
  },
  created() {
    handleService = makeHandleService(this.$api);
  },
  methods: {
    ...mapActions({
      setUser: 'user/setUser',
    }),
    findEntityNameByValue(value) {
      if(typeof value === 'string'){
        const entity = entityTypes.find((item) => item?.value?.toLowerCase() === value?.toLowerCase());
        return entity ? entity?.name : null;
      }
    },
    checkDuplicateAliases() {
      const selectedType = this.status.entityType?.value || this.status.entityType;

      const statusColors = this.currentAccount.type === 'org'
        ? this.orgs.find(org => org.handle === this.currentAccount.handle)?.preferences?.statusColors || []
        : this.user?.preferences?.statusColors || [];

      const allAliasesForType = statusColors
        .filter(item => item.entityType === selectedType && item.id !== this.status.id)
        .flatMap(item => item.aliases || []);

      const hasDuplicate = this.status.aliases.some(alias => allAliasesForType.includes(alias));

      if (hasDuplicate) {
        this.aliasesError = 'Duplicate aliases found within the same entity type.';
        return false;
      }

      this.aliasesError = '';
      return true;
    },
    onAliasesChange() {
      this.aliasesError = this.checkDuplicateAliases()
        ? '' : 'Duplicate aliases found within the same entity type.';
    },
    openDialog() {
      this.localShowDialog = true;
    },
    closeDialog() {
      this.localShowDialog = false;
    },
    onDeleteClick() {
      this.showDeleteConfirmDialog = true;
    },
    deleteColor() {
      this.onCreateUpdateDelete('delete', this.status.name, this.status.entityType);
    },
    async onCreateUpdateDelete(action, name , status) {
      const isValidAliases = this.checkDuplicateAliases();
      if (!isValidAliases) return;
      const isValidForm = this.$refs.form.validate();
      if (action !== 'delete' && !isValidForm) {
        return;
      }

      if(action == 'delete' && this.status.isDefault)
        return showErrorToast(this.$swal, this.$t('defaultValueError'));

      if(action == 'delete' && this.selectedCounts == 1 && this.statusDefaultCounted )
        return showErrorToast(this.$swal, this.$t('atLeastOneStatusCounted'));

      if(this.defaultChanged && !this.status.isDefault)
        return showErrorToast(this.$swal, this.$t('defaultValueError'));

      if(this.selectedCounts == 1 && this.statusDefaultCounted && !this.status.isApproved && !this.status.isDeclined)
        return showErrorToast(this.$swal, this.$t('atLeastOneStatusCounted'));

      const dataColor = {
        name: this.status.name,
        entityType: this.status.entityType.value || this.status.entityType,
        aliases: this.status.aliases,
        color: this.status.color || this.generateRandomHexColor(),
        isDefault: this.status.isDefault,
        isApproved: this.status.isApproved,
        isDeclined: this.status.isDeclined,
      };

      let payload = {};

      const updatedColor = dataColor;

      let statusColors = [];
      let priorityColors = [];
      if(this.currentAccount.type == 'org'){
        statusColors = this.getOrgPreferences(this.currentAccount.handle)?.statusColors || [];
        priorityColors = this.getOrgPreferences(this.currentAccount.handle)?.priorityColors || [];
      } else {
        statusColors = this.getUserPreferences?.statusColors || [];
        priorityColors = this.getUserPreferences?.priorityColors || [];
      }
      // Deep clone the original arrays to avoid directly mutating them.
      // This ensures that the original data remains intact in case of errors or failed API calls.
      let newStatusColors = _.cloneDeep(statusColors);
      let newPriorityColors = _.cloneDeep(priorityColors);

      if (action === 'delete' && this.status.id) {
        this.showDeleteConfirmDialog = false;

        const index = newStatusColors.findIndex(color => color.id === this.status.id);
        if (index !== -1) {
          newStatusColors.splice(index, 1, { ...newStatusColors[index], archived: true });
        }
      } else {
        if (this.status.id) {
          const index = newStatusColors.findIndex(color => color.id === this.status.id);
          if (index !== -1) {
            // Only reset the default flag for the same entity type
            if(updatedColor.isDefault) {
              newStatusColors = newStatusColors.map((color) => {
                if (color.entityType === updatedColor.entityType && color.id !== this.status.id) {
                  return { ...color, isDefault: false };
                }
                return color;
              });
            }
            // Only make approved and declined mutually exclusive
            if (updatedColor.isApproved) {
              updatedColor.isDeclined = false;
            } else if (updatedColor.isDeclined) {
              updatedColor.isApproved = false;
            }
            newStatusColors[index] = { ...updatedColor, id: this.status.id };
          }
        } else {
          // Only reset the default flag for the same entity type
          if(updatedColor.isDefault) {
            newStatusColors = newStatusColors.map((color) => {
              if (color.entityType === updatedColor.entityType) {
                return { ...color, isDefault: false };
              }
              return color;
            });
          }
          // Only make approved and declined mutually exclusive
          if (updatedColor.isApproved) {
            updatedColor.isDeclined = false;
          } else if (updatedColor.isDeclined) {
            updatedColor.isApproved = false;
          }
          const colorIndex = newStatusColors.findIndex(element => element.entityType == updatedColor.entityType && element.name?.toLowerCase() == updatedColor.name.toLowerCase());
          
          if(colorIndex > 0 && newStatusColors[colorIndex].archived){
            newStatusColors[colorIndex].archived = false;
            newStatusColors[colorIndex].color = updatedColor.color;
          }else{
            newStatusColors.push({ ...updatedColor, id: this.totalCount + 1 });
          }
        }
      }

      payload = {
        preferences: {
          statusColors: newStatusColors,
          priorityColors: newPriorityColors,
        },
      };

      try {
        const response = await handleService.updatePreferences(this.currentAccount.handle, payload);
        if(response.status === 200) {
          if(this.currentAccount.type == 'org'){
            this.setOrgPreferences({
              handle: this.currentAccount.handle,
              preferences: payload.preferences,
            });
          } else {
            this.setUserPreferences(payload.preferences);
          }
        
         this.$emit("update:colors", payload.preferences);
         let addOrUpdate;

         if (action === 'Edit') {
           addOrUpdate = 'updated';
          } else if (action === 'delete') {
           addOrUpdate = 'deleted';
          } else {
           addOrUpdate = 'added';
         }

         const statusName = status?.name ?? this.camelCaseToSpaced(status);
        

         const successMessage = this.$t('dataColors.colorUpdatedNewMessage', {
           name: statusName,
           status: name,
           personalizationType: 'Status',
           add: addOrUpdate
         });

         showSuccessToast(this.$swal, successMessage);
        }
      } catch (err) {
        const errorMessage = err.response?.data?.data["preferences.statusColors"] || err.response?.data?.message || 'Internal server error';
        showErrorToast(this.$swal, errorMessage);
        return;
      } finally {
        this.modalOpen(false);
      }
    },
    camelCaseToSpaced(str){
     return str.replace(/([a-z])([A-Z])/g, '$1 $2').toLowerCase();
    },
    changeColor(_, __, color) {
    this.status.color = color;
    },
    onDefaultStatusChange(value) {
     if(value) {
      this._handleExclusiveStatusFlags('isDefault');
     }
    },
    onApprovedStatusChange(value) {
     if (value) {
      this._handleExclusiveStatusFlags('isApproved');
     }
    },
    onDeclinedStatusChange(value) {
     if (value) {
      this._handleExclusiveStatusFlags('isDeclined');
     }
    },
   _handleExclusiveStatusFlags(changedFlag) {
     if (this.status[changedFlag]) {
      if (changedFlag === 'isApproved') {
        this.status.isDeclined = false;
      } else if (changedFlag === 'isDeclined') {
        this.status.isApproved = false;
      }
     }
    },
    handleSwitchToggle(switchType, property, event) {
     const newValue = this.status[property];
      if (newValue) {
       this.originalValue = !this.status[property]; 
       this.currentSwitchType = switchType;
       this.currentSwitchProperty = property;
       this.showSuccessDialog = true;
       event.preventDefault();
      } 
    },
    revertSwitch() {
     this.status[this.currentSwitchProperty] = this.originalValue;
     this.showSuccessDialog = false;
    },
    confirmToggle() {
     this.showSuccessDialog = false;
     this.status[this.currentSwitchProperty] = !this.originalValue;
     switch(this.currentSwitchProperty) {
      case 'isApproved':
       this.onApprovedStatusChange(this.status.isApproved);
      break;
      case 'isDeclined':
       this.onDeclinedStatusChange(this.status.isDeclined);
      break;
      case 'isDefault':
       this.onDefaultStatusChange(this.status.isDefault);
      break;
     }
    },
    openReassignDialog() {
     this.showTooltip = false;
     this.showReassignDialog = true;
    } ,
    onReassignConfirm(selectedStatus) {
     this.showReassignDialog = false;
     this.updateDefaultStatusAssignment(selectedStatus);
    },
    onReassignCancel() {
     this.showReassignDialog = false;
    },
    async updateDefaultStatusAssignment(newDefaultStatus) {
     try {
      let statusColors = [];
      let priorityColors = [];
      
      if (this.currentAccount.type === 'org') {
        statusColors = this.getOrgPreferences(this.currentAccount.handle)?.statusColors || [];
        priorityColors = this.getOrgPreferences(this.currentAccount.handle)?.priorityColors || [];
      } else {
        statusColors = this.getUserPreferences?.statusColors || [];
        priorityColors = this.getUserPreferences?.priorityColors || [];
      }

      const updatedStatusColors = _.cloneDeep(statusColors);
      const updatedPriorityColors = _.cloneDeep(priorityColors);

      updatedStatusColors.forEach(color => {
        if (color.entityType === (this.status.entityType?.value || this.status.entityType)) {
          color.isDefault = false;
        }
      });

      const index = updatedStatusColors.findIndex(color => color.id === newDefaultStatus.id);
      if (index !== -1) {
        updatedStatusColors[index] = { ...updatedStatusColors[index], isDefault: true };
      }

      const payload = {
        preferences: {
          statusColors: updatedStatusColors,
          priorityColors: updatedPriorityColors,
        },
      };

      const response = await handleService.updatePreferences(this.currentAccount.handle, payload);

      if (response.status === 200) {
        if (this.currentAccount.type === 'org') {
          this.setOrgPreferences({
            handle: this.currentAccount.handle,
            preferences: payload.preferences,
          });
        } else {
          this.setUserPreferences(payload.preferences);
        }

        this.$emit('update:colors', payload.preferences);
        showSuccessToast(this.$swal, this.$t('statusSetAsDefault', { statusName: newDefaultStatus.name }))
        
        this.status.isDefault = false;
        this.defaultChanged = false;
      }
     } catch (err) {
      showErrorToast(this.$swal, err.response?.data?.message );
     }
    },
   },
};
</script>

<style scoped>
.v-dialog--fullscreen {
  max-height: 100vh !important;
  width: 485px !important;
  right: 0 !important;
  left: auto !important;
}

.v-expansion-panel-content__wrap {
  padding: 0 !important;
}

.color-picker {
  padding: 5px 10px;
  background: lightgray;
  margin-left: 0 !important;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

.text-disabled {
  opacity: 0.6;
}
.underline {
  text-decoration: underline;
}

.cursor-pointer {
  cursor: pointer;
}
</style> 