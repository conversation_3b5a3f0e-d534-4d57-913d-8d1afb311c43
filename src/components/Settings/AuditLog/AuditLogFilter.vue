<template>
  <div>
    <v-sheet
      v-if="!skeletonLoaderState"
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40px"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-3 py-2 d-flex flex-row">
        {{ $t('filters') }}
        <v-icon 
          size="16px" 
          class="ml-2"
        >
          mdi-filter-variant
        </v-icon>
      </span>
    </v-sheet>
    <v-dialog
      v-model="showDialog"
      class="audit-log-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 
              class="black--text"
            >
              {{ $t('filters') }}
            </h2>
            <v-btn 
              icon 
              @click="showDialog = false"
            >
              <v-icon 
                color="black"
              >
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <v-expansion-panels 
            v-model="datePanel" 
            class="mb-5"
            flat 
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('Date') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content class="no-wrap-panel-content panel-content-theme">
                <div class="d-flex align-center">
                  <v-menu
                    v-model="isStartDatePickerOpen"
                    :close-on-content-click="false"
                    max-width="290"
                  >
                    <template #activator="{ on }">
                      <v-text-field
                        dense
                        single-line
                        class="text-field field-theme mt-0 pa-0 rounded-lg custom-prepend"
                        :value="startDate"
                        background-color="#F9F9FB"
                        readonly
                        height="38"
                        hide-details
                        v-on="on"
                      >
                        <template #prepend-inner>
                          <calendarBlueIcon />
                        </template>
                      </v-text-field>
                    </template>
                    <v-date-picker
                      v-model="startDate"
                      @change="isStartDatePickerOpen = false"
                    />
                  </v-menu>
                  <div class="mx-4 font-weight-bold text-h6">
                    {{ $t('-') }}
                  </div>
                  <v-menu
                    v-model="isEndDatePickerOpen"
                    :close-on-content-click="false"
                    max-width="290"
                  >
                    <template #activator="{ on }">
                      <v-text-field
                        background-color="#F9F9FB"
                        class="text-field mt-0 field-theme pa-0 rounded-lg custom-prepend"
                        :value="endDate"
                        readonly
                        height="40"
                        hide-details
                        v-on="on"
                      >
                        <template #prepend-inner>
                          <calendarBlueIcon />
                        </template>
                      </v-text-field>
                    </template>
                    <v-date-picker
                      v-model="endDate"
                      @change="isEndDatePickerOpen = false"
                    />
                  </v-menu>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
          <!-- Filters -->
          <v-expansion-panels 
            v-for="(filter, index) in filters" 
            :key="index" 
            v-model="filter.panel" 
            flat 
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium text-capitalize">
                    {{ $t(filter.label) }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content class="no-wrap-panel-content panel-content-theme">
                <v-text-field
                  v-if="filter.searchable"
                  v-model="filter.search"
                  :placeholder="$t('search')"
                  background-color="#F9F9FB"
                  class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
                  height="38"
                  dense
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchIcon />
                  </template>
                </v-text-field>
                <div 
                  class="scrollable-container"
                  :style="{ overflow: filter.items.length > 5 ? 'auto' : 'hidden' }"
                >
                  <v-checkbox
                    v-for="(item, idx) in filteredItems(filter)"
                    :key="idx"
                    :input-value="filter.selected.includes(item.value)"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                    @change="toggleSelection(filter, item.value)"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ item.label }}</span>
                    </template>
                  </v-checkbox>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
        <div class="actions-container d-flex justify-space-between">
          <v-btn
            width="204.5px"
            color="#F2F4F7"
            height="40"
            :depressed="true"
            class="text-capitalize btn-theme"
            elevation="0"
            @click="clearAll"
          >
            {{ $t('clearAll') }}
          </v-btn>
          <v-btn
            width="204.5px"
            class="btn-theme"
            height="40"
            color="primary"
            :depressed="true"
            elevation="0"
            @click="apply"
          >
            {{ $t('apply') }}
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import SearchIcon from '@/assets/svg/search-icon.svg';
import calendarBlueIcon from '@/assets/svg/calendar-blue.svg';

export default {
  name: 'AuditLogFilter',
  components: {
    SearchIcon,
    calendarBlueIcon,
  },
  props: {
    filterOptions: {
      type: Object,
      required: true,
      default: () => ({
        projectNames: [],
        actors: [],
        actions: [],
        entityNames: [],
      }),
    },
    currentFilters: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      showDialog: false,
      skeletonLoaderState: false,
      filters: this.initializeFilters(),
      datePanel: 0,
      isStartDatePickerOpen: false,
      isEndDatePickerOpen: false,
      startDate: null,
      endDate: null,
    };
  },
  watch: {
    filterOptions: {
      deep: true,
      handler(newOptions) {
        const keys = ['projectNames', 'entityNames', 'actions', 'actors'];
        keys.forEach((key, index) => {
          this.filters[index].items = newOptions[key];
        });
      },
    },
    filters: {
      deep: true,
      handler(newFilters, oldFilters) {
        if (JSON.stringify(newFilters) === JSON.stringify(oldFilters)) {
          return;
        }
        const updatedFilters = newFilters.map(filter => {
          if (filter.search) {
            const filteredItems = filter.items.filter(item =>
              item.label.toLowerCase().includes(filter.search.toLowerCase())
            );
            filter.selected = filter.selected.filter(uid =>
              filteredItems.some(item => item.value === uid)
            );
          }
          return filter;
        });

        this.filters = [...updatedFilters];
      },
    },
    showDialog(newVal) {
      if (newVal) {
        if (this.currentFilters) {
          this.filters.forEach(filter => {
            const currentFilter = this.currentFilters[filter.label.toLowerCase().replace(/\s+/g, '_')];
            filter.selected = currentFilter?.value?.map(item => item.uid) || [];
            filter.search = currentFilter?.search || null;
          });
          const dateRange = this.currentFilters.date?.value || {};
          this.startDate = dateRange.start || null;
          this.endDate = dateRange.end || null;
        } else {
          this.resetFilters();
        }
      }
    },
  },
  methods: {
    initializeFilters() {
      return [
        { label: this.$t('auditLog.projectName'), panel: 0, search: null, items: this.filterOptions.projectNames, selected: [], searchable: true },
        { label: this.$t('auditLog.entityName'), panel: 0, search: null, items: this.filterOptions.entityNames, selected: [], searchable: true },
        { label: this.$t('auditLog.action'), panel: 0, search: null, items: this.filterOptions.actions, selected: [], searchable: true },
        { label: this.$t('auditLog.actor'), panel: 0, search: null, items: this.filterOptions.actors, selected: [], searchable: true },
      ];
    },
    filteredItems(filter) {
      return filter.items.filter(item =>
        !filter.search || item.label.toLowerCase().includes(filter.search.toLowerCase())
      );
    },
    toggleSelection(filter, value) {
      const index = filter.selected.indexOf(value);
      if (index === -1) {
        filter.selected.push(value);
      } else {
        filter.selected.splice(index, 1);
      }
    },
    resetFilters() {
      this.startDate = null;
      this.endDate = null;
      this.filters.forEach(filter => {
        filter.selected = [];
        filter.search = null;
      });
    },
    clearAll() {
      this.$emit('clear-filters');
      this.showDialog = false;
    },
    toCamelCase(str) {
      return str
        .replace(/\s(.)/g, (_, group1) => group1.toUpperCase()).replace(/\s+/g, '')
        .replace(/^(.)/, (_, group1) => group1.toLowerCase());
    },
    apply() {
      if (!this.hasActiveFilters()) {
        this.$emit('applyFilters', null);
        this.showDialog = false;
        return;
      }

      const enhancedFilters = this.filters.reduce((acc, filter) => {
        acc[filter.label.toLowerCase().replace(/\s+/g, '_')] = {
          type: 'array',
          label: this.$t(`auditLog.${this.toCamelCase(filter.label)}`),
          value: filter.selected.map((uid) => {
            const item = filter.items.find((item) => item.value === uid);
            return {
              name: item?.label || null,
              uid: item?.value || null,
            };
          }),
        };
        return acc;
      }, {});

      enhancedFilters.date = {
        type: 'dateRange',
        label: this.$t('auditLog.date'),
        value: {
          start: this.startDate,
          end: this.endDate,
        },
      };

      const apiFilters = this.filters.reduce((acc, filter) => {
        if (filter.selected.length > 0) {
          acc[filter.label.toLowerCase().replace(/\s+/g, '_')] = filter.selected.map(uid => {
            const item = filter.items.find(item => item.value === uid);
            return item?.value || null;
          });
        }
        if (filter.search) {
          acc[`${filter.label.toLowerCase().replace(/\s+/g, '_')}_search`] = filter.search;
        }
        return acc;
      }, {});

      if (this.startDate || this.endDate) {
        apiFilters.date_range = {
          from: this.startDate,
          to: this.endDate,
        };
      }

      this.$emit('applyFilters', { ui: enhancedFilters, api: apiFilters });
      this.showDialog = false;
    },
    hasActiveFilters() {
      return (
        this.filters.some(filter => filter.selected.length > 0 || filter.search) ||
        (this.startDate && this.endDate)
      )
    },
  },
};
</script>

<style scoped>
.v-dialog--fullscreen {
  max-height: 100vh !important;
  width: 485px !important;
  right: 0 !important;
  left: auto !important;
}

.scrollable-container {
  max-height: 200px;
  padding-right: 8px;
}
</style>