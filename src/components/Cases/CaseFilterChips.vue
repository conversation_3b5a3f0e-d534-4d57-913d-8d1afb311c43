<template>
  <div
    v-if="hasActiveFilters"
  >
    <div class="d-flex align-center flex-wrap">
      <span
        class="fw-bold font-weight-medium no-bg pr-2"
        width="300px"
      >{{ $t('results') }}: {{ resultsCount }}</span>
  
      <template v-if="filters.priorities && filters.priorities.length > 0">
        <v-chip
          v-for="priorityId in filters.priorities"
          :key="`priority-${priorityId}`"
          class="chips-style ma-2"
          close
          @click:close="removeFilter('priorities', priorityId)"
        >
          {{ $t('priority') }}: {{ getPriorityName(priorityId) }}
        </v-chip>
      </template>
  
      <template v-if="filters.tagObjects && filters.tagObjects.length > 0">
        <v-chip
          v-for="tag in filters.tagObjects"
          :key="`tag-${tag.uid}`"
          class="chips-style ma-2"
          close
          @click:close="removeFilter('tagObjects', tag)"
        >
          {{ $t('tag') }}: #{{ tag.name }}
        </v-chip>
      </template>

  
      <v-btn
        class="blue--text text-capitalize fw-semibold font-weight-medium"
        width="100px"
        text
        elevation="0"
        style="text-transform: none; font-weight: 600 !important"
        @click="clearAllFilters"
      >
        <div class="p-2">
          {{ $t('clearAll') }}
        </div>
      </v-btn>
    </div>
  </div>
</template>
  
  <script>
  import colorPreferencesMixin from '@/mixins/colorPreferences';
  
  export default {
    name: 'CaseFilterChips',
    mixins: [colorPreferencesMixin],
    props: {
      filters: {
        type: Object,
        required: true,
      },
      resultsCount: {
        type: Number,
        default: 0,
      },
      priorities: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      hasActiveFilters() {
        return (
          (this.filters.priorities && this.filters.priorities.length > 0) ||
          (this.filters.tagObjects && this.filters.tagObjects.length > 0) ||
          (this.filters.tags && this.filters.tags.length > 0)
        );
      },
    },
    methods: {
      getPriorityName(priorityId) {
        const priority = this.priorities.find(p => p.id === priorityId);
        return priority ? priority.name : priorityId;
      },
      removeFilter(filterType, value) {
        const updatedFilters = { ...this.filters };
        
        if (filterType === 'priorities') {
          updatedFilters.priorities = this.filters.priorities.filter(id => id !== value);
        } else if (filterType === 'tagObjects') {
          // Remove from tagObjects array
          updatedFilters.tagObjects = this.filters.tagObjects.filter(tag => tag.uid !== value.uid);
          // Update the tags array (names only)
          updatedFilters.tags = updatedFilters.tagObjects.map(tag => tag.name);
          // Update the tagUids array
          updatedFilters.tagUids = updatedFilters.tagObjects.map(tag => tag.uid);
        } else if (filterType === 'tags') {
          // Legacy fallback for tags array
          updatedFilters.tags = this.filters.tags.filter(tag => tag !== value);
          // Clear tagUids when using legacy tags (since we can't match them precisely)
          if (this.filters.tagUids && Array.isArray(this.filters.tagUids)) {
            updatedFilters.tagUids = [];
          }
        }
        
        this.$emit('update-filters', updatedFilters);
      },
      clearAllFilters() {
        this.$emit('clear-filters');
      }
    }
  };
  </script>
  
  <style scoped>
 .chips-style {
  background-color: #f9fafb !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
}
  </style>