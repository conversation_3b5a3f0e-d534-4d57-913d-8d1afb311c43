<template>
  <v-row
    align-content="start"
    justify="start"
    dense
    class="case-management-layout"
  >
    <CaseConfirmDialog
      v-model="dialog"
      @edit="openDetailView"
    />
    <div
      v-if="showTestFolder"
      :style="{ width: isTreeViewCollapsed ? '5%' : treeViewWidth }"
      class="position-relative mt-4 case-tree-section"
    >
      <div
        class="d-flex flex-column white card rounded-lg ml-1 mr-2 position-sticky	sticky-on-scroll"
        :class="{ 'is-collapsed-menu': isTreeViewCollapsed }"
      >
        <TreeViewFolder
          :items="folders"
          :selected-folder-uid="selectedFolderUid"
          :collapsed="isTreeViewCollapsed ? 0 : 1"
          :write-entity="writeEntity"
          @folder-selected="selectFolder"
          @folder-delete="deleteFolder"
          @reload-cases="reloadCases"
          @refresh-folders="$emit('refresh-folders')"
          @save-expanded-tree="$emit('save-expanded-tree', $event)"
        />
        <div
          v-if="showCollapse"
          class="collapse-btn"
          @click="toggleMenu"
        >
          <v-icon
            color="#0C2FF3"
          >
            {{ isTreeViewCollapsed ? 'mdi-arrow-right-bottom' : 'mdi-arrow-left-bottom'
            }}
          </v-icon>
          <span
            v-if="!isTreeViewCollapsed"
            class="collapse-text"
          >{{ $t('collapse') }}
          </span>
        </div>
      </div>
      <div
        v-if="!isTreeViewCollapsed"
        class="resizer position-absolute top-0 right-0 curser-resize h-full"
        @mousedown="startDragging"
      />
    </div>
    <v-col
      class="case-main-section"
      :style="{ width: `calc(100% - ${treeViewWidth})` }"
    >
      <v-row
        align-content="start"
        justify="start"
        dense
        class="h-100"
      >
        <div
          :class="[isDetailViewCollapsed ? 'col-7' : 'col-12']"
          class="col mt-3 case-list-container white rounded-lg"
        >
          <cases-list
            id="cases-list"
            :case-items="filterCases"
            :table-filter="tableFilter"
            :initial-selected="runCases"
            :bread-crumbs="breadCrumbs"
            :allow-action="writeEntity && allowAction"
            :write-entity="writeEntity"
            :delete-entity="deleteEntity"
            :is-repository="!quickCreate"
            :from-run="fromRun"
            :selected-case.sync="selectedCase"
            :pagination="pagination"
            :current-page="currentPage"
            :items-per-page="itemsPerPage"
            :update-pagination="updatePagination"
            :relations-loading="relationsLoading"
            :cases-loading="casesLoading"
            :total-items="totalItems"
            :sort-by="sortBy"
            :sort-desc="sortDesc"
            :show-pagination="showPagination"
            :show-test-folder="showTestFolder"
            @update:options="updatePagination"
            @expandDetail="confirmEditing"
            @createCase="onCreateCase"
            @updateSelectedCases="updateSelectedCases"
            @selectedCases="handleCases"
            @updateCasesHistoryData="updateCasesHistoryData"
            @folder-selected="selectFolder"
            @caseRemove="onDeleteCase"
            @bulkCaseRemove="onBulkDeleteCases"
            @reload-cases="reloadCases"
            @applySearch="$emit('applySearch', $event)"
            @applyFilters="$emit('applyFilters', $event)"
            @clearFilters="$emit('clearFilters')"
          />
          <slot name="control-area" />
        </div>
        <div
          v-if="isDetailViewCollapsed"
          class="col-5 mt-2"
        >
          <CaseDetailView
            :case-item="selectedCase"
            :is-selected-case-first-index="isCurrentSelectedCasesFirstIndex"
            :is-selected-case-last-index="isCurrentSelectedCasesLastIndex"
            :selected-folder="breadCrumbs"
            :from-run="fromRun"
            :case-defects="caseDefects"
            :is-test-case-loading="isTestCaseLoading"
            @closeDetail="closeDetailView"
            @viewPreviousCase="viewPreviousCase"
            @viewNextCase="viewNextCase"
            @removeFile="removeFile"
            @updateAttachments="reloadCase"
            @updateCase="updateCase"
          />
        </div>
      </v-row>
    </v-col>
  </v-row>
</template>

<script>
import { ref } from 'vue';
import CasesList from '@/components/Cases/CaseList.vue';
import TreeViewFolder from '@/views/Tests/Case/Tree/Index.vue';
import CaseDetailView from '@/views/Tests/Case/Detail/Index.vue';
import CaseConfirmDialog from '@/components/TestRuns/ConfirmDialog.vue'
import { useCaseManagement } from '@/composables/modules/cases/management';


export default {
  name: 'CaseManagement',
  components: {
      CasesList,
      TreeViewFolder,
      CaseDetailView,
      CaseConfirmDialog,
  },
  props:{
    /*
      showCollapse: Hides the collapse folder option.
      selectOption: Returns selected cases only when set to true.
      allowAction: Activates the action button for selected cases.
    */
    fromRun:{
      type: Boolean,
      default: false,
    },
    fromTestCase:{
      type: Boolean,
      default: false,
    },
    showCollapse:{
      type: Boolean,
      default: true
    },
    selectOption:{
      type: Boolean,
      default: false,
    },
    allowAction:{
      type: Boolean,
      default: true
    },
    quickCreate:{
      type: Boolean,
      default: false
    },
    cases:{
      type: Array,
      default: () => {
        return []
      },
      required: true
    },
    runCases:{
      type: Array,
      default: () => {
        return []
      }
    },
    folders:{
      type: Array,
      default: () =>{
        return []
      },
      required: true,
    },
    writeEntity: {
      type: Boolean,
      default: false
    },
    deleteEntity: {
      type: Boolean,
      default: false
    },
    setSelectedFolderUid:{
      type: [Number, String],
      default: null
    },
    pagination: {
      type: Object,
      default: () => ({})
    },
    currentPage: {
      type: Number,
      default: 1
    },
    itemsPerPage: {
      type: Number,
      default: 10
    },
    updatePagination: {
      type: Function,
      default: () => {}
    },
    relationsLoading: {
      type: Boolean,
      default: false
    },
    casesLoading: {
      type: Boolean,
      default: false
    },
    totalItems: {
      type: Number,
      default: 0
    },
    sortBy: {
      type: Array,
      default: () => []
    },
    sortDesc: {
      type: Array,
      default: () => []
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    showTestFolder: {
      type: Boolean,
      default: true
    }
  },
  setup(props, context) {
    const treeViewWidth = ref('15%');

    const startDragging = (e) => {
      const startX = e.clientX;
      const startWidth = parseFloat(treeViewWidth.value) / 100 * window.innerWidth;
      const onMouseMove = (e) => {
        const delta = e.clientX - startX;
        const newWidth = Math.min(Math.max(startWidth + delta, 0.15 * window.innerWidth), 0.5 * window.innerWidth);
        treeViewWidth.value = `${(newWidth / window.innerWidth) * 100}%`;
      };

      const stopDragging = () => {
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', stopDragging);
      };
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', stopDragging);
    };
    
    const {
      isTreeViewCollapsed,
      isDetailViewCollapsed,
      selectedItem,
      breadCrumbs,
      selectedCase,
      selectedCases,
      selectedFolder,
      dialog,
      handleSelected,
      tableFilter,
      isTestCaseLoading,
      caseDefects,

      // Computed
      currentAccount,
      selectedFolderUid,
      filterCases,
      isCurrentSelectedCasesFirstIndex,
      isCurrentSelectedCasesLastIndex,
      nextCase,
      previousCase,
      
      // Methods
      confirmEditing,
      toggleMenu,
      changeExpansion,
      reloadCases,
      updateCasesHistoryData,
      closeDetailView,
      handleCases,
      viewPreviousCase,
      viewNextCase,
      buildBreadCrumbs,
      selectFolder,
      onCreateCase,
      openDetailView,
      deleteFolder,
      updateSelectedCases,
      getCase,
      onDeleteCase,
      onBulkDeleteCases,
      removeFile,
      reloadCase,
      updateCase,
    } = useCaseManagement(props, context);
    return {
      treeViewWidth,
      startDragging,
      isTreeViewCollapsed,
      isDetailViewCollapsed,
      selectedItem,
      breadCrumbs,
      selectedCase,
      selectedCases,
      selectedFolder,
      dialog,
      handleSelected,
      tableFilter,
      currentAccount,
      selectedFolderUid,
      filterCases,
      isCurrentSelectedCasesFirstIndex,
      isCurrentSelectedCasesLastIndex,
      nextCase,
      previousCase,
      confirmEditing,
      toggleMenu,
      changeExpansion,
      reloadCases,
      updateCasesHistoryData,
      closeDetailView,
      handleCases,
      viewPreviousCase,
      viewNextCase,
      buildBreadCrumbs,
      selectFolder,
      onCreateCase,
      openDetailView,
      deleteFolder,
      updateSelectedCases,
      getCase,
      onDeleteCase,
      onBulkDeleteCases,
      removeFile,
      reloadCase,
      updateCase,
      isTestCaseLoading,
      caseDefects,
    };
  }
}
</script>
<style scoped>
.sticky-on-scroll {
  position: -webkit-sticky;
  top: 12px;
  height: calc(100vh - 24px);
}

.resizer {
  width: 3px;
}

/* Case Management Layout Improvements */
.case-management-layout {
  height: 100%;
  margin-bottom: 10px;
  align-items: stretch !important;
}

.case-tree-section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.case-tree-section .sticky-on-scroll {
  flex: 1;
  height: calc(100vh - 140px);
  overflow-y: auto;
  scrollbar-width: thin;
}

.case-main-section {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: visible;
}

.case-main-section .v-row {
  height: 100%;
  overflow: hidden;
}

.case-list-container {
  scrollbar-width: thin;
  display: flex;
  flex-direction: column;
}

/* Ensure the cases list fills available height */
.case-list-container #cases-list {
  overflow-y: auto;
}

/* Detail view adjustments when expanded */
.case-main-section .col-5 {
  height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
}
</style>