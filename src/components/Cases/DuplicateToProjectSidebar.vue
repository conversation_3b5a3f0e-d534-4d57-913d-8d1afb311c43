<template>
  <v-dialog
    v-model="showDrawer"
    class="test-cases-filter-drawer dialog-theme"
    transition="slide-x-transition"
    attach
    fullscreen
    width="485px"
  >
    <v-card class="d-flex flex-column fill-height">
      <v-card-text class="flex-grow-1 pa-0">
        <!-- Header -->
        <div class="px-4 pt-6 pb-1">
          <div class="d-flex align-center justify-space-between">
            <h2 class="black--text">
              {{ $t('duplicateToProject') }}
            </h2>
            <v-btn
              icon
              @click="close"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
        </div>

        <!-- Form Content -->
        <div class="form-wrapper">
          <v-form class="px-4">
            <div>
              <!-- Project Selection -->
              <div class="d-flex flex-column mb-6">
                <div class="text-left">
                  <v-label class="fs-14px text-theme-label font-weight-medium">
                    {{ $t('project') }} <span class="required-asterisk">*</span>
                  </v-label>
                </div>
                <v-select
                  v-model="selectedProject"
                  :items="activeProjects"
                  item-text="name"
                  item-value="uid"
                  :placeholder="$t('select')"
                  :loading="loadingProjects"
                  :disabled="loadingProjects"
                  dense
                  background-color="#F9F9FB"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  append-icon="mdi-chevron-down"
                  :menu-props="{ offsetY: true }"
                  @change="onProjectSelect"
                />
              </div>

              <!-- Folder Selection -->
              <div class="d-flex flex-column mb-6">
                <div class="text-left">
                  <v-label class="fs-14px text-theme-label font-weight-medium">
                    {{ $t('testFolder') }} <span class="required-asterisk">*</span>
                  </v-label>
                </div>
                <FolderAutocomplete
                  v-if="selectedProject"
                  v-model="selectedFolderUID"
                  :handle="currentHandle"
                  :project-key="selectedProjectKey"
                  :placeholder="$t('selectFolder')"
                  dense
                  background-color="#F9F9FB"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                />
                <v-text-field
                  v-else
                  :placeholder="$t('selectProjectFirst')"
                  disabled
                  dense
                  background-color="#F9F9FB"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                />
              </div>
            </div>
          </v-form>
        </div>
      </v-card-text>

      <!-- Action Buttons -->
      <div class="actions-container">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          full-width
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="close"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme text-capitalize"
          height="40"
          color="primary"
          :depressed="true"
          full-width
          elevation="0"
          :loading="duplicateLoading"
          :disabled="!canDuplicate"
          @click="handleDuplicate"
        >
          <span>{{ $t('duplicate') }}</span>
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
</template>

<script>
import FolderAutocomplete from '@/components/base/FolderAutocomplete.vue';
import makeProjectsService from '@/services/api/project';
import makeCasesService from '@/services/api/case';
import { showErrorToast, showSuccessToast } from '@/utils/toast';

export default {
  name: 'DuplicateToProjectSidebar',
  
  components: {
    FolderAutocomplete
  },

  props: {
    value: {
      type: Boolean,
      default: false
    },
    selectedCases: {
      type: Array,
      default: () => []
    },
    currentHandle: {
      type: String,
      required: true
    },
    currentProjectKey: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      selectedProject: null,
      selectedFolderUID: '',
      activeProjects: [],
      loadingProjects: false,
      duplicateLoading: false
    }
  },

  computed: {
    showDrawer: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    },
    selectedProjectKey() {
      if (!this.selectedProject) return this.currentProjectKey;
      const selectedProjectObj = this.activeProjects.find(p => p.uid === this.selectedProject);
      return selectedProjectObj?.key || this.currentProjectKey;
    },
    canDuplicate() {
      return this.selectedProject && this.selectedFolderUID && !this.duplicateLoading;
    }
  },

  watch: {
    showDrawer(newVal) {
      if (newVal) {
        this.fetchActiveProjects();
      }
    },
    selectedProject() {
      this.selectedFolderUID = '';
    }
  },

  methods: {
    async fetchActiveProjects() {
      try {
        this.loadingProjects = true;
        const projectsService = makeProjectsService(this.$api);
        
        // Get all projects for the current user
        const searchParams = new URLSearchParams();
        searchParams.set('status', 'active');
        searchParams.set('includeCount', true);
        
        const response = await projectsService.getProjects(this.currentHandle, searchParams.toString());
        
        // Filter out the current project from the list
        const currentProjectKey = this.currentProjectKey;
        this.activeProjects = (response.data.items || []).filter(project => project.key !== currentProjectKey);
      } catch (error) {
        console.error('Error fetching active projects:', error);
        this.activeProjects = [];
      } finally {
        this.loadingProjects = false;
      }
    },

    onProjectSelect() {
      this.selectedFolderUID = '';
    },

    async handleDuplicate() {
      if (!this.canDuplicate) return;

      try {
        this.duplicateLoading = true;
        const casesService = makeCasesService(this.$api);
        const caseUids = this.selectedCases.map(caseItem => caseItem.uid);
        
        const payload = {
          caseUids: caseUids,
          projectUid: this.selectedProject,
          targetFolderUid: this.selectedFolderUID
        };

        await casesService.duplicateTestCases(this.currentHandle, this.currentProjectKey, payload);
        
        showSuccessToast(this.$swal, this.$t('duplicateSuccess', { item: 'test cases' }));
        this.$emit('duplicate-success');
        this.close();
      } catch (error) {
        console.error('Error duplicating cases:', error);
        showErrorToast(this.$swal, error.response?.data?.message || this.$t('duplicateError', { item: 'test cases' }));
      } finally {
        this.duplicateLoading = false;
      }
    },

    close() {
      this.selectedProject = null;
      this.selectedFolderUID = '';
      this.$emit('input', false);
    }
  }
}
</script>

<style scoped>
.dialog-theme {
  background-color: white;
}

.form-wrapper {
  flex-grow: 1;
  overflow-y: auto;
}

.actions-container {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  background-color: white;
}

.field-theme {
  background-color: #F9F9FB;
}

.custom-prepend {
  border-radius: 8px;
}

.required-asterisk {
  color: #ef4444;
}

.btn-theme {
  border-radius: 8px;
}
</style> 