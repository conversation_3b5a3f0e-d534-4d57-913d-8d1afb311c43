<template>
  <div>
    <div
      v-for="category in tools"
      :key="category.category"
    >
      <div class="bg-gray-theme py-4 px-6 my-4 text-left">
        <h3 class="fs-20 fw-semibold">
          {{ category.category }}
        </h3>
      </div>
      <v-row>
        <v-col
          v-for="tool in category.items"
          :key="tool.name"
          cols="4"
        >
          <v-sheet
            class="project-item pa-4 bg-gray-theme"
            rounded="lg"
          >
            <div class="d-flex justify-between align-center">
              <!-- Left: Avatar + Name -->
              <div class="d-flex align-center">
                <Avatar
                  class="mr-2"
                  :size="40"
                  :avatar-src="integrationImages[tool.image] || require(`@/assets/png/${tool.image}`)"
                />
                <div class="fw-semibold fs-14px tool-name">
                  {{ tool.name }}
                </div>
              </div>

              <!-- Right: Add Button -->
              <v-btn
                color="blue"
                depressed
                height="40px"
                class="text-capitalize rounded-lg btn-theme white--text px-4"
                @click="addIntegration(tool)"
              >
                {{ $t('Add') }}
                <v-icon
                  class="ml-1"
                  size="16px"
                >
                  mdi-plus
                </v-icon>
              </v-btn>
            </div>

            <!-- Description Below -->
            <div class="text-start pt-2 pb-2 description fs-12px">
              {{ tool.description }}
            </div>
          </v-sheet>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script>
import jiraIcon from '@/assets/png/jira.png';
import githubIcon from '@/assets/png/github.png';
import testrailIcon from '@/assets/png/testrail.png';
import Avatar from "@/components/base/Avatar.vue"
export default {
  name: 'IntegrationCardAll',
  components:{
    Avatar
  },
  props: {
    tools: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      integrationImages: {
        'jira.png': jiraIcon,
        'github.png': githubIcon,
        'testrail.png': testrailIcon,
      },
    };
  },
  methods: {
    async addIntegration(integration) {
      this.$emit('select-integration', integration);
    },
  },
};
</script>

<style scoped>
/* Ensures proper spacing */
.tool-name {
  margin-left: 8px;
}

/* Align button without breaking layout */
.d-flex.justify-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* Prevent overlapping */
.description {
  margin-top: 6px;
}
</style>
