<template>
  <v-navigation-drawer
    v-model="showDrawer"
    fixed
    right
    temporary
    width="485"
    class="error-details-sidebar"
  >
    <div>
      <div class="header-section d-flex justify-space-between align-center px-6">
        <h3 class="integration-title">
          {{ $t('Details') }}
        </h3>
        <v-btn
          icon
          @click="close"
        >
          <v-icon color="black">
            mdi-close
          </v-icon>
        </v-btn>
      </div>

      <div class="details-message px-6 mt-2 mb-4">
        {{ $t('integrations.error.resolveErrors') }}
      </div>

      <div
        v-if="errors && errors.length"
        class="error-list px-6"
      >
        <div 
          v-for="(error, index) in errors" 
          :key="index"
          class="error-item pa-0 mb-6"
        >
          <div class="d-flex justify-space-between align-center mb-2">
            <div>
              <div class="error-timestamp">
                {{ new Date(error.timestamp).toLocaleString() }}
              </div>
              <div class="error-title">
                {{ getErrorTitle(error.statusCode) }}
              </div>
            </div>
            <div class="d-flex gap-2">
              <v-btn
                color="primary"
                depressed
                height="40"
                class="btn-theme rounded-lg fw-semibold white--text"
                :loading="loading"
                @click="resolveError(index)"
              >
                {{ $t('resolve') }}
              </v-btn>
            </div>
          </div>
          <div class="error-message">
            {{ error.message }}
          </div>
        </div>
      </div>
      
      <div
        v-else
        class="text-center pa-4"
      >
        <p>{{ $t('integrations.error.noErrorsToDisplay') }}</p>
      </div>
    </div>


    <div class="bottom-actions">
      <v-btn
        color="primary"
        depressed
        height="48"
        :loading="loading"
        style="flex: 1;"
        class="btn-theme rounded-lg fw-semibold white--text"
        @click="resolveAllErrors"
      >
        {{ $t('integrations.resolveAll') }}
      </v-btn>
    </div>
  </v-navigation-drawer>
</template>

<script>
import makeIntegrationsService from '@/services/api/integrations';
import { showErrorToast, showSuccessToast } from '@/utils/toast';

export default {
  name: 'ErrorDetailsSidebar',
  
  props: {
    value: {
      type: Boolean,
      default: false
    },
    errors: {
      type: Array,
      default: () => []
    },
    integrationUid: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: false,
      errorMap: {
        400: 'Bad Request',
        401: 'Integration Authentication Failure',
        403: 'Access Denied',
        404: 'Resource Not Found',
        408: 'Request Timeout',
        422: 'Invalid Integration Configuration',
        500: 'Internal Server Error',
        503: 'Service Unavailable',
        505: 'Unsupported Integration API Version',
        default: 'Unknown Error'
      }
    }
  },

  computed: {
    showDrawer: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },

  methods: {
    getErrorTitle(statusCode) {
      return this.errorMap[statusCode] || this.errorMap.default;
    },
    close() {
      this.showDrawer = false;
    },
    async resolveError(index) {
      try {
        this.loading = true;
        const handle = this.$route.params.handle;
        const integrationsService = makeIntegrationsService(this.$api);
        await integrationsService.removeIntegrationErrors(handle, this.integrationUid, { index });
        this.errors.splice(index, 1);
        showSuccessToast(this.$swal, this.$t('integrations.success.error_resolved'));
        this.$emit('error-resolved', index);
        if (this.errors.length === 0) {
          this.$emit('all-errors-resolved');
          this.close();
        }
      } catch (error) {
        console.error('Error resolving single error:', error);
        showErrorToast(this.$swal, error.response?.data?.message || this.$t('integrations.error.failed_to_resolve'));
      } finally {
        this.loading = false;
      }
    },
    async resolveAllErrors() {
      try {
        this.loading = true;
        const handle = this.$route.params.handle;
        const integrationsService = makeIntegrationsService(this.$api);
        await integrationsService.removeIntegrationErrors(handle, this.integrationUid, { removeAll: true });
        this.errors = [];
        showSuccessToast(this.$swal, this.$t('integrations.success.all_errors_resolved'));
        this.$emit('all-errors-resolved');
        this.close();
      } catch (error) {
        console.error('Error resolving all errors:', error);
        showErrorToast(this.$swal, error.response?.data?.message || this.$t('integrations.error.failed_to_resolve_all'));
      } finally {
        this.loading = false;
      }
    }
  }
}
</script>

<style scoped>
.error-details-sidebar {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px 0 0 12px;
  background-color: white;
}
.header-section {
  padding-top: 24px;
  padding-bottom: 12px;
}
.integration-title {
  font-family: Inter, sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 28px;
  text-align: left;
  margin: 0;
}
.error-item {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0;
  padding: 0;
}
.gap-2 {
  gap: 8px;
}
.error-timestamp {
  font-family: Inter, sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
}
.error-title {
  font-family: Inter, sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
}
.error-message {
  font-family: Inter, sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0;
  margin-bottom: 10px;
  margin-top: 2px;
}
.details-message {
  font-family: Inter, sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #222;
  margin-bottom: 65px;
  margin-top: 8px;
}
.error-list {
  margin-top: 40px;
}
.bottom-actions {
  position: absolute;
  bottom: 24px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 24px;
  background: transparent;
  gap: 16px;
}
</style>
