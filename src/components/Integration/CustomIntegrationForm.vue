<template>
  <div class="custom-integration-form">
    <v-card
      class="mt-n4 elevation-0 auth-card text-center"
      width="450px"
    >
      <div>
        <h3 class="font-weight-bold fs-24px text-left">
          {{ $t('integrations.create_intergrations.custom_integration_title') }}
        </h3>
        <p class="text-grey my-1 text-left fs-14px">
          {{ $t('integrations.create_intergrations.custom_integration_description') }}
        </p>
      </div>
      
      <!-- Custom Integration Form -->
      <div class="mt-6">
        <div class="mb-4">
          <p class="d-flex fs-14px text-theme-label mb-1 font-weight-medium">
            {{ $t('integrations.create_intergrations.name') }}
          </p>
          <v-text-field
            v-model="formData.name"
            type="text"
            class="rounded-lg field-theme pt-0"
            background-color="#F9F9FB"
            height="38px"
            :placeholder="$t('integrations.create_intergrations.name')"
            :rules="[rules.required]"
            clear-icon="body-2"
            @input="validateForm"
          />
        </div>
        
        <div class="mb-4">
          <p class="d-flex fs-14px text-theme-label mb-1 font-weight-medium">
            {{ $t('integrations.create_intergrations.description') }}
          </p>
          <v-textarea
            v-model="formData.description"
            class="rounded-lg field-theme pt-0"
            background-color="#F9F9FB"
            :placeholder="$t('integrations.create_intergrations.description')"
            rows="3"
            auto-grow
            @input="validateForm"
          />
        </div>
        
        <div class="mb-4">
          <p class="d-flex fs-14px text-theme-label mb-1 font-weight-medium">
            {{ $t('integrations.create_intergrations.add_url') }}
          </p>
          <v-text-field
            v-model="formData.addUrl"
            type="url"
            class="rounded-lg field-theme pt-0"
            background-color="#F9F9FB"
            height="38px"
            :placeholder="$t('integrations.create_intergrations.add_url_placeholder')"
            :rules="[rules.required, rules.url]"
            clear-icon="body-2"
            @input="validateForm"
          />
        </div>
        
        <div class="mb-4">
          <p class="d-flex fs-14px text-theme-label mb-1 font-weight-medium">
            {{ $t('integrations.create_intergrations.view_url') }}
          </p>
          <v-text-field
            v-model="formData.viewUrl"
            type="url"
            class="rounded-lg field-theme pt-0"
            background-color="#F9F9FB"
            height="38px"
            :placeholder="$t('integrations.create_intergrations.view_url_placeholder')"
            :rules="[rules.required, rules.url]"
            clear-icon="body-2"
            @input="validateForm"
          />
          <p class="text-grey-darken-1 fs-12px mt-1 mb-0 text-left">
            {{ $t('integrations.create_intergrations.view_url_instruction') }}
          </p>
        </div>
        
        <div class="mb-4">
          <p class="d-flex fs-14px text-theme-label mb-1 font-weight-medium">
            {{ $t('integrations.create_intergrations.testfiesta_projects') }}
          </p>
          <v-select
            v-model="formData.selectedProjects"
            :items="filteredProjects"
            item-text="name"
            item-value="uid"
            multiple
            chips
            deletable-chips
            background-color="#F9F9FB"
            class="rounded-lg field-theme pt-0"
            height="38px"
            :placeholder="$t('integrations.create_intergrations.select_testfiesta_projects')"
            :rules="[rules.required]"
            clear-icon="body-2"
            @change="validateForm"
          >
            <template #prepend-item>
              <v-list-item
                class="px-4"
                @click.stop="toggleSelectAll"
              >
                <v-checkbox
                  :input-value="isAllSelected"
                  :indeterminate="isIndeterminate"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :hide-details="true"
                  dense
                >
                  <template #label>
                    <span class="select-text">{{ $t('selectAll') }}</span>
                  </template>
                </v-checkbox>
              </v-list-item>
              <v-divider />
              <v-text-field
                v-model="searchTerm"
                class="mx-4 mt-2"
                dense
                clearable
                hide-details
                :placeholder="$t('search')"
                prepend-icon="mdi-magnify"
              />
            </template>

            <template #selection="{ item, index }">
              <template v-if="index < calculateVisibleChips(formData.selectedProjects)">
                <v-chip
                  small
                  class="custom-chip-theme ma-1"
                  close
                  @click:close="removeProject(item.uid)"
                >
                  <span
                    class="text-theme-label text-truncate"
                    style="max-width: 150px"
                  >{{ item.name }}</span>
                </v-chip>
              </template>
              <span
                v-else-if="index === calculateVisibleChips(formData.selectedProjects)"
                class="text-caption grey--text text-truncate"
              >
                (+{{ formData.selectedProjects.length - calculateVisibleChips(formData.selectedProjects) }} {{ $t('more') }})
              </span>
            </template>

            <template #item="{ item, on }">
              <v-list-item
                class="px-4"
                v-on="on"
              >
                <v-checkbox
                  :input-value="isItemSelected(item)"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :hide-details="true"
                  dense
                >
                  <template #label>
                    <span class="select-text">{{ item.name }}</span>
                  </template>
                </v-checkbox>
              </v-list-item>
            </template>
          </v-select>
        </div>
      </div>
    </v-card>
  </div>
</template>

<script>
export default {
  name: 'CustomIntegrationForm',
  
  props: {
    projects: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      formData: {
        name: '',
        description: '',
        addUrl: '',
        viewUrl: '',
        selectedProjects: []
      },
      searchTerm: '',
      rules: {
        required: (v) => !!v || this.$t('integrations.create_intergrations.this_field_is_required'),
        url: (v) => {
          if (!v) return true; // Let required rule handle empty values
          try {
            new URL(v);
            return true;
          } catch {
            return this.$t('integrations.create_intergrations.invalid_url');
          }
        }
      },
      isValid: false
    };
  },
  
  computed: {
    filteredProjects() {
      if (!this.searchTerm) return this.projects;
      return this.projects.filter(project => 
        project.name.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    },
    
    isAllSelected() {
      return this.filteredProjects.length > 0 && 
             this.formData.selectedProjects.length === this.filteredProjects.length;
    },
    
    isIndeterminate() {
      return this.formData.selectedProjects.length > 0 && 
             this.formData.selectedProjects.length < this.filteredProjects.length;
    }
  },
  
  watch: {
    formData: {
      handler() {
        this.validateForm();
      },
      deep: true
    }
  },
  
  methods: {
    validateForm() {
      const { name, addUrl, viewUrl, selectedProjects } = this.formData;
      
      this.isValid = !!(
        name && 
        addUrl && 
        viewUrl && 
        selectedProjects.length > 0 &&
        this.isValidUrl(addUrl) &&
        this.isValidUrl(viewUrl)
      );
      
      this.$emit('form-change', {
        isValid: this.isValid,
        data: this.formData
      });
    },
    
    isValidUrl(url) {
      if (!url) return false;
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    },
    
    getFormData() {
      return {
        service: 'custom',
        name: this.formData.name,
        description: this.formData.description,
        configuration: {
          addUrl: this.formData.addUrl,
          viewUrl: this.formData.viewUrl,
          projectUids: this.formData.selectedProjects,
        },
        entityTypes: ['defects'],
        projectUids: this.formData.selectedProjects,
        authType: 'none',
      };
    },
    
    resetForm() {
      this.formData = {
        name: '',
        description: '',
        addUrl: '',
        viewUrl: '',
        selectedProjects: []
      };
      this.searchTerm = '';
      this.isValid = false;
    },
    
    // Project selection methods
    toggleSelectAll() {
      if (this.isAllSelected) {
        this.formData.selectedProjects = [];
      } else {
        this.formData.selectedProjects = this.filteredProjects.map(p => p.uid);
      }
      this.validateForm();
    },
    
    isItemSelected(item) {
      return this.formData.selectedProjects.includes(item.uid);
    },
    
    removeProject(projectUid) {
      this.formData.selectedProjects = this.formData.selectedProjects.filter(id => id !== projectUid);
      this.validateForm();
    },
    
    calculateVisibleChips(selectedItems) {
      return Math.min(selectedItems.length, 3);
    }
  }
};
</script>

<style scoped>
.custom-integration-form {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.custom-chip-theme {
  background-color: #E5E7EB !important;
  color: #374151 !important;
}

.select-text {
  font-size: 14px;
  color: #374151;
}
</style> 