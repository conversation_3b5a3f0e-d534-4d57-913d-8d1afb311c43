<template>
  <div>
    <v-dialog
      :value="true"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
      @input="$emit('close-dialog')"
    >
      <!-- Loading Overlay -->
      <v-overlay
        :value="isLoading"
        absolute
      >
        <v-progress-circular
          indeterminate
          size="64"
        />
      </v-overlay>

      <!-- Dialog Content -->
      <v-card
        :disabled="isLoading"
        class="fill-height d-flex flex-column"
      >
        <!-- Header -->
        <div class="header-section d-flex justify-space-between align-center px-6">
          <h3 class="integration-title">
            {{ $t('integrations.edit_integration.edit') }} {{ editedData.name }}
          </h3>
          <v-btn
            icon
            @click="handleClose"
          >
            <v-icon color="black">
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <!-- Navigation Chips -->
        <div class="mt-4 d-flex px-6 mb-6">
          <v-chip
            label
            width="200px"
            :class="{ 
              'blue--text': currentStep === 1,
              'fw-semibold': currentStep === 1,
              'font-weight-medium': currentStep !== 1,
              'chip-theme': true
            }"
            :color="currentStep === 1 ? '#e6ecff' : '#f9fafb'"
            @click="currentStep = 1"
          >
            <div class="px-2">
              {{ $t('integrations.edit_integration.details') }}
            </div>
          </v-chip>

          <v-chip
            class="ml-2"
            label
            width="200px"
            :class="{ 
              'blue--text': currentStep === 2,
              'fw-semibold': currentStep === 2,
              'font-weight-medium': currentStep !== 2,
              'chip-theme': true
            }"
            :color="currentStep === 2 ? '#e6ecff' : '#f9fafb'"
            @click="currentStep = 2"
          >
            <div class="px-2">
              {{ $t('integrations.edit_integration.configurations') }}
            </div>
          </v-chip>
        </div>

        <!-- Main Content -->
        <v-card-text class="flex-grow-1 overflow-y-auto px-6">
          <!-- Step 1: Basic Info -->
          <div v-if="currentStep === 1">
            <!-- Image Upload Section -->
            <div class="mt-2">
              <p class="field-label mb-2 text-left">
                {{ $t('integrations.create_intergrations.thumbnail') }}
              </p>
              <div class="create-project-layout">
                <v-hover v-slot="{ hover }">
                  <div
                    class="project-logo"
                    :style="{ ...imageStyle }"
                    :class="{ hovering: hover }"
                    @click="openFileDialog"
                    @drop.prevent="handleDrop"
                    @dragover.prevent
                    @dragenter.prevent
                  >
                    <input
                      ref="fileInput"
                      type="file"
                      :accept="`${projectImageTypes.join(', ')}`"
                      style="display: none"
                      @change="handleFileChange"
                    >
                    <template v-if="!imageSrc">
                      <p class="ma-0 fs-14px">
                        {{ $t('drag_and_drop.part1') }}
                      </p>
                      <p class="ma-0 fw-semibold mt-2 blue--text fs-14px">
                        {{ $t('drag_and_drop.part3') }}
                      </p>
                    </template>
                    <template v-if="hover && imageSrc">
                      <v-icon
                        class="edit-icon"
                        @click.stop="openFileDialog"
                      >
                        mdi-pencil
                      </v-icon>
                    </template>
                  </div>
                </v-hover>
              </div>
            </div>

            <!-- Name Field -->
            <div class="mb-4">
              <p class="field-label mb-2 text-left">
                {{ $t('name') }} <strong class="red--text text--lighten-1">*</strong>
              </p>
              <v-text-field
                v-model="form.name"
                type="text"
                class="rounded-lg field-theme"
                background-color="#F9F9FB"
                height="38px"
                :placeholder="$t('name')"
                :rules="[rules.required]"
                hide-details="auto"
              />
            </div>

            <!-- Description Field -->
            <div class="mb-6">
              <p class="field-label mb-2 text-left">
                {{ $t('integrations.edit_integration.description') }}
                <strong class="red--text text--lighten-1">*</strong>
              </p>
              <v-textarea
                v-model="form.description"
                class="rounded-lg field-theme"
                background-color="#F9F9FB"
                :placeholder="$t('integrations.edit_integration.description')"
                :rules="[rules.required]"
                rows="3"
                hide-details="auto"
                auto-grow
              />
            </div>
          </div>

          <!-- Step 2: Configurations -->
          <div v-else-if="currentStep === 2">
            <!-- Custom Service Configuration -->
            <div v-if="isCustomService">
              <!-- Testfiesta Projects Selection -->
              <div class="mb-4">
                <p class="field-label text-left">
                  {{ $t('integrations.create_intergrations.testfiesta_projects') }}
                </p>
                <v-select
                  v-model="selectedCustomProjects"
                  :items="filteredCustomProjects"
                  item-text="name"
                  item-value="uid"
                  multiple
                  chips
                  deletable-chips
                  background-color="#F9F9FB"
                  class="rounded-lg field-theme"
                  height="38px"
                  :placeholder="$t('integrations.create_intergrations.select_testfiesta_projects')"
                >
                  <template #prepend-item>
                    <v-list-item
                      class="px-4"
                      @click.stop="toggleSelectAllCustom"
                    >
                      <v-checkbox
                        :input-value="isAllCustomSelected"
                        :indeterminate="isIndeterminateCustom"
                        class="field-theme"
                        :ripple="false"
                        off-icon="icon-checkbox-off"
                        on-icon="icon-checkbox-on"
                        :hide-details="true"
                        dense
                      >
                        <template #label>
                          <span class="select-text">{{ $t('selectAll') }}</span>
                        </template>
                      </v-checkbox>
                    </v-list-item>
                    <v-divider />
                    <v-text-field
                      v-model="searchCustom"
                      class="mx-4 mt-2"
                      dense
                      clearable
                      hide-details
                      :placeholder="$t('search')"
                      prepend-icon="mdi-magnify"
                    />
                  </template>

                  <template #selection="{ item, index }">
                    <template v-if="index < calculateVisibleChips(selectedCustomProjects)">
                      <v-chip
                        small
                        class="custom-chip-theme ma-1"
                        close
                        @click:close="selectedCustomProjects = selectedCustomProjects.filter(id => id !== item.uid)"
                      >
                        <span
                          class="text-theme-label text-truncate"
                          style="max-width: 150px"
                        >{{ item.name }}</span>
                      </v-chip>
                    </template>
                    <span
                      v-else-if="index === calculateVisibleChips(selectedCustomProjects)"
                      class="text-caption grey--text text-truncate"
                    >
                      (+{{ selectedCustomProjects.length - calculateVisibleChips(selectedCustomProjects) }} {{ $t('more') }})
                    </span>
                  </template>

                  <template #item="{ item, on }">
                    <v-list-item
                      class="px-4"
                      v-on="on"
                    >
                      <v-checkbox
                        :input-value="isCustomItemSelected(item)"
                        class="field-theme"
                        :ripple="false"
                        off-icon="icon-checkbox-off"
                        on-icon="icon-checkbox-on"
                        :hide-details="true"
                        dense
                      >
                        <template #label>
                          <span class="select-text">{{ item.name }}</span>
                        </template>
                      </v-checkbox>
                    </v-list-item>
                  </template>
                </v-select>
              </div>



              <!-- Add URL Input -->
              <div class="mb-4">
                <p class="field-label text-left">
                  {{ $t('integrations.edit_integration.add_url') }}
                </p>
                <p
                  class="field-instruction text-left mb-2"
                  style="color: #667085; font-size: 12px;"
                >
                  {{ $t('integrations.edit_integration.add_url_instruction') }}
                </p>
                <v-text-field
                  v-model="addUrl"
                  class="rounded-lg field-theme pt-0"
                  background-color="#F9F9FB"
                  height="38px"
                  :placeholder="$t('integrations.edit_integration.enter_add_url')"
                />
              </div>

              <!-- View URL Input -->
              <div class="mb-4">
                <p class="field-label text-left">
                  {{ $t('integrations.edit_integration.view_url') }}
                </p>
                <p
                  class="field-instruction text-left mb-2"
                  style="color: #667085; font-size: 12px;"
                >
                  {{ $t('integrations.edit_integration.view_url_instruction') }}
                </p>
                <v-text-field
                  v-model="viewUrl"
                  class="rounded-lg field-theme pt-0"
                  background-color="#F9F9FB"
                  height="38px"
                  :placeholder="$t('integrations.edit_integration.enter_view_url')"
                />
              </div>
            </div>

            <!-- Standard Service Configuration -->
            <div v-else>
              <div
                v-if="configurations.length > 0 && editConfigIndex === null"
                ref="configList"
                class="mb-6 configurations-list"
                style="height: 200px; overflow-y: auto;"
              >
                <div
                  v-for="(config, index) in configurations"
                  :key="index"
                  class="configuration-item"
                >
                  <div
                    class="d-flex justify-space-between align-center"
                    style="margin-bottom: 12px"
                  >
                    <p
                      class="configuration-name body-2 fs-14px font-weight-bold ma-0"
                      style="color: #0C111D; text-transform: capitalize"
                    >
                      {{ config.name }}
                    </p>
                    <div class="action-icons">
                      <v-btn
                        icon
                        small
                        @click="editConfiguration(index)"
                      >
                        <EditIcon />
                      </v-btn>
                      <v-btn
                        icon
                        small
                        @click="removeConfiguration(index)"
                      >
                        <DeleteIcon />
                      </v-btn>
                    </div>
                  </div>

                  <p
                    class="configuration-name fs-14px text-left body-2 font-weight-medium ma-0"
                    style="color: #667085"
                  >
                    {{ $t('integrations.create_intergrations.testfiesta_projects') }}
                  </p>
                  <div class="d-flex flex-wrap">
                    <v-chip
                      v-for="(project, i) in getTestFiestaProjects(config).slice(0, 4)"
                      :key="i"
                      class="ma-2"
                      style="background-color: #F9F9FB; border: 1px solid #D0D5DD; border-radius: 8px"
                      small
                    >
                      {{ project }}
                    </v-chip>
                    <v-chip
                      v-if="getTestFiestaProjects(config).length > 4"
                      class="ma-2"
                      style="background-color: #F9F9FB; border: 1px solid #D0D5DD; border-radius: 8px"
                      small
                    >
                      +{{ getTestFiestaProjects(config).length - 4 }} {{ $t('more') }}
                    </v-chip>
                  </div>

                  <p
                    class="configuration-name fs-14px text-left body-2 font-weight-medium ma-0 text-capitalize"
                    style="color: #667085; margin-top: 12px"
                  >
                    <span class="text-capitalize mr-1">{{
                      $t('integrations.edit_integration.service_projects', {
                        serviceName: serviceName,
                        entityName: serviceEntityName(),
                      })
                    }}</span>
                  </p>
                  <div class="d-flex flex-wrap">
                    <v-chip
                      v-for="(project, i) in getServiceProjects(config).slice(0, 4)"
                      :key="i"
                      class="ma-2"
                      style="background-color: #F9F9FB; border: 1px solid #D0D5DD; border-radius: 8px"
                      small
                    >
                      {{ project }}
                    </v-chip>
                    <v-chip
                      v-if="getServiceProjects(config).length > 4"
                      class="ma-2"
                      style="background-color: #F9F9FB; border: 1px solid #D0D5DD; border-radius: 8px"
                      small
                    >
                      +{{ getServiceProjects(config).length - 4 }} {{ $t('more') }}
                    </v-chip>
                  </div>
                </div>
              </div>

              <!-- Configuration Dropdown -->
              <div class="mt-2">
                <IntegrationConfigurationDropdown
                  :resources="resources"
                  :testfiesta-projects="testFiestaProjects"
                  :service-projects="serviceProjects"
                  :integration-uid="String(integrationUid)"
                  :service-name="serviceName.toLowerCase()"
                  :edit-config-data="editConfigData"
                  @add-configuration="handleAddConfiguration"
                  @edit-configuration="handleEditConfiguration"
                />
              </div>
            </div>
          </div>
        </v-card-text>

        <!-- Footer Actions -->
        <div class="d-flex justify-space-between px-6 py-4 bg-white">
          <v-btn
            width="204.5px"
            :color="'#F2F4F7'"
            height="40"
            class="text-capitalize"
            @click="handleClose()"
          >
            {{ $t('cancel') }}
          </v-btn>
          <v-btn
            width="204.5px"
            color="primary"
            height="40"
            class="text-capitalize"
            :disabled="!isStepValid"
            @click="handleSave()"
          >
            {{ $t('save') }}
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import fileValidator from '@/mixins/fileValidator.js';
import { projectImageTypes } from '@/constants/fileTypes.js';
import { showErrorToast, showSuccessToast } from '@/utils/toast';
import IntegrationConfigurationDropdown from '@/components/Integration/IntegrationConfigurationDropdown.vue';
import makeProjectsService from '@/services/api/project';
import makeIntegrationsService from '@/services/api/integrations';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'EditIntegration',
  components: {
    EditIcon,
    DeleteIcon,
    IntegrationConfigurationDropdown,
  },
  mixins: [fileValidator],
  props: {
    editedData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      currentStep: 1,
      isLoading: false,
      isDataInitialized: false,
      serviceProjects: [],
      testFiestaProjects: [],
      resources: [],
      imageSrc: '',
      file: null,
      projectImageTypes,
      configurations: [],
      editConfigData: {},
      editConfigIndex: null,
      serviceEnities: {
        jira: 'Projects',
        github: 'Repositories',
      },
      form: {
        name: '',
        description: '',
      },
      addUrl: '',
      viewUrl: '',
      selectedCustomProjects: [], // For custom service project selection
      searchCustom: '', // For custom service project search
      rules: {
        required: (value) => !!value || this.$t('required'),
      },
      attachments: [],
      avatarRemoved: false,
    };
  },

  computed: {
    imageStyle() {
      return this.imageSrc ? { backgroundImage: `url(${this.imageSrc})` } : {};
    },
    isValidForSave() {
      return this.form.name && this.form.name.trim() !== '' && this.configurations.length > 0;
    },
    integrationUid() {
      return this.editedData?.uid;
    },
    serviceName() {
      return this.editedData?.service || '';
    },
    showOrgSelection() {
      return this.serviceName?.toLowerCase() === 'jira';
    },
    // Custom service detection
    isCustomService() {
      return this.serviceName?.toLowerCase() === 'custom';
    },
    progressPercentage() {
      return this.currentStep * 50; // 2 steps, 50% each
    },
    isStepValid() {
      if (this.currentStep === 1) {
        return this.form.name.trim() !== '' && this.form.description.trim() !== '';
      }
      // For step 2, if we're editing a configuration, disable the save button
      if (this.editConfigIndex !== null) {
        return false;
      }
      // For custom services, we need at least one configuration
      if (this.isCustomService) {
        return true;
      }
      // For standard services, use the existing logic
      return this.configurations.length > 0;
    },

    // Custom service project filtering and selection
    filteredCustomProjects() {
      if (!this.searchCustom) return this.testFiestaProjects;
      const searchTerm = this.searchCustom.toLowerCase();
      return this.testFiestaProjects.filter((project) => project.name.toLowerCase().includes(searchTerm));
    },

    isAllCustomSelected() {
      return this.selectedCustomProjects.length === this.filteredCustomProjects.length;
    },

    isIndeterminateCustom() {
      return (
        this.selectedCustomProjects.length > 0 &&
        this.selectedCustomProjects.length < this.testFiestaProjects.length
      );
    },

    ...mapGetters({
      uploadedFiles: 'attachment/uploadedFiles',
    }),
  },

  watch: {
    editedData: {
      immediate: true,
      handler(newVal) {
        if (newVal?.uid && !this.isDataInitialized) {
          this.initializeData();
        }
      },
    },

    // Watch for service name changes to handle Jira case
    serviceName: {
      immediate: true,
      handler(newVal) {
        if (newVal?.toLowerCase() === 'jira' && this.integrationUid) {
          this.fetchResources();
        }
      },
    },
  },

  created() {
    // Initialize form data when component is created
    this.initializeFormData();
    if (this.showOrgSelection) {
      this.fetchResources();
    }
  },

  methods: {
    ...mapActions({
      uploadToServer: 'attachment/uploadToServer',
    }),
    serviceEntityName() {
      return this.serviceEnities[this.serviceName?.toLowerCase()] || 'Projects';
    },
    async initializeData() {
      if (this.isDataInitialized) return;

      this.isLoading = true;
      try {
        // Reset form data
        this.resetForm();

        // Load initial data from editedData
        if (this.editedData) {
          this.form.name = this.editedData.name || '';
          this.form.description = this.editedData.description || '';
          this.imageSrc = this.editedData.avatarUrl || '';
          
          if (this.isCustomService) {
            // For custom services, load external URLs and selected projects
            this.addUrl = this.editedData.configuration?.addUrl || this.editedData.configuration?.externalUrl || '';
            this.viewUrl = this.editedData.configuration?.viewUrl || '';
            // Convert projectUids to integers for v-select comparison
            this.selectedCustomProjects = this.editedData.projectUids?.map(uid => parseInt(uid)) || [];
          } else {
            // For standard services, load configurations
            this.configurations = [...(this.editedData.configurations || [])];
          }
        }

        // Fetch required data in parallel
        await Promise.all([
          this.fetchTestfiestaProjects(),
          this.showOrgSelection ? this.fetchResources() : Promise.resolve(),
        ]);

        this.isDataInitialized = true;
      } catch (error) {
        showErrorToast(this.$swal, 'Failed to load integration data', {}, error?.response?.data);
      } finally {
        this.isLoading = false;
      }
    },

    async fetchTestfiestaProjects() {
      try {
        const projectsService = makeProjectsService(this.$api);
        const params = 'status=active&includeCount=true';
        const res = await projectsService.getProjects(this.$route.params.handle, params);
        this.testFiestaProjects = res.data.items || [];
      } catch (err) {
        throw new Error('Failed to fetch Testfiesta projects');
      }
    },

    async fetchResources() {
      if (!this.integrationUid) return;

      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const res = await integrationsService.getOrganizations(this.$route.params.handle, this.integrationUid);
        this.resources = res.data;
      } catch (err) {
        showErrorToast(this.$swal, 'Failed to fetch resources', {}, err?.response?.data);
      }
    },

    openFileDialog() {
      this.$refs.fileInput.click();
    },

    handleFileChange(event) {
      const files = Array.from(event.target.files);
      const validationResult = this.validateMimeTypes(files, this.projectImageTypes);

      if (!validationResult.valid) {
        showErrorToast(this.$swal, this.$t('integrations.error.fileFormatNotSupported'));
      } else {
        const file = event.target.files[0];
        this.previewImage(file);
      }
    },

    previewImage(file) {
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          this.imageSrc = e.target.result;
        };
        reader.readAsDataURL(file);
        this.file = file;
      }
    },

    handleDrop(event) {
      const file = event.dataTransfer.files[0];
      if (file) {
        const validationResult = this.validateMimeTypes([file], this.projectImageTypes);
        if (validationResult.valid) {
          this.previewImage(file);
        } else {
          showErrorToast(this.$swal, this.$t('integrations.error.fileFormatNotSupported'));
        }
      }
    },

    async uploadLogo(file) {
      const handle = this.$route.params.handle;
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const params = {
          handle,
          integrationUid: this.editedData.uid,
          relatedTo: 'integration',
        };

        const response = await this.uploadToServer({
          handle,
          mediaType: 'attachment',
          file,
          apiService: integrationsService,
          params,
        });

        if (response?.objectUrl) {
          this.imageSrc = response.objectUrl;
          // No need for additional update call since backend handles it
        }
      } catch (error) {
        if (error?.status == 507)
          showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, 'limitReached', handle)
        else
          showErrorToast(this.$swal, this.$t('integrations.error.logoUploadFailed'), {}, error?.response?.data);
      }
    },

    removeImage() {
      this.imageSrc = '';
      this.avatarRemoved = true;
    },

    closeDrawer() {
      // Emit event to update drawer state in parent
      this.$emit('update:drawer', false);
    },

    editConfiguration(index) {
      this.editConfigData = { ...this.configurations[index] };
      this.editConfigIndex = index;
    },

    handleAddConfiguration(config) {
      this.configurations.push(config);
      this.scrollToBottom();
    },

    handleEditConfiguration(config) {
      if (this.editConfigIndex !== null) {
        this.configurations.splice(this.editConfigIndex, 1, config);
        this.editConfigData = {};
        this.editConfigIndex = null;
        this.scrollToBottom();
      }
    },

    removeConfiguration(index) {
      this.configurations.splice(index, 1);
    },

    async handleSave() {
      if (!this.isStepValid) return;
      this.isLoading = true;

      try {
        let integrationData;

        if (this.isCustomService) {
          // For custom services, use selected projects and update external URLs
          integrationData = {
            name: this.form.name.trim(),
            description: this.form.description,
            service: this.serviceName,
            projectUids: this.selectedCustomProjects || [], // Use selected projects
            configuration: {
              addUrl: this.addUrl,
              viewUrl: this.viewUrl,
            },
          };
        } else {
          // For standard services (Jira, GitHub), use the existing logic
          const projectUids = this.configurations.reduce((keys, config) => {
            if (config.projects) {
              Object.keys(config.projects).forEach((key) => keys.add(key));
            }
            return keys;
          }, new Set());

          integrationData = {
            name: this.form.name.trim(),
            description: this.form.description,
            service: this.serviceName,
            projectUids: Array.from(projectUids),
            configuration: {
              projectConfigurations: this.configurations,
            },
          };
        }

        const integrationsService = makeIntegrationsService(this.$api);
        await integrationsService.updateIntegration(this.$route.params.handle, this.editedData.uid, integrationData);

        // Handle logo changes after successful integration update
        if (this.file) {
          await this.uploadLogo(this.file);
        }

        showSuccessToast(this.$swal, this.$t('integrations.success.saved'));
        this.$emit('refresh');
        this.$emit('close-dialog');
      } catch (error) {
        showErrorToast(this.$swal, this.$t('integrations.error.saveFailed'), {}, error?.response?.data);
      } finally {
        this.isLoading = false;
      }
    },
    async uploadIntegrationAttachments(integrationUid) {
      const handle = this.$route.params.handle;
      const integrationsService = makeIntegrationsService(this.$api);
      const mediaType = 'attachment';

      const uploadPromises = this.attachments.map(async (attachment) => {
        try {
          const params = {
            handle,
            integrationUid,
            relatedTo: 'integration',
          };
          await this.uploadToServer({
            handle,
            mediaType,
            file: attachment.file,
            apiService: integrationsService,
            params,
          });
        } catch (error) {
          if (error?.status == 507)
            showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, 'limitReached', handle)
          else
            showErrorToast(this.$swal, this.$t('error.uploadedAttachments'), {}, error?.response?.data);
        }
      });

      // Let uploads happen in parallel but don't wait for them
      return Promise.allSettled(uploadPromises);
    },

    resetForm() {
      this.isDataInitialized = false;
      // Reset all form data to initial state
      this.form.name = this.editedData.name || '';
      this.imageSrc = this.editedData.avatarUrl || '';
      
      if (this.isCustomService) {
        this.addUrl = this.editedData.configuration?.addUrl || this.editedData.configuration?.externalUrl || '';
        this.viewUrl = this.editedData.configuration?.viewUrl || '';
        this.selectedCustomProjects = this.editedData.projectUids?.map(uid => parseInt(uid)) || [];
      } else {
        this.selectedTestFiestaProjects = this.editedData.projects || [];
        this.selectedServiceProjects = this.editedData.selectedProjectIds || [];
      }
      
      this.currentFilter = 'all';
      this.search = '';

      // Reset file input if it exists
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = '';
      }
    },
    handleClose() {
      this.resetForm();
      this.$emit('close-dialog');
    },
    handleCancel() {
      this.resetForm();
      this.$emit('close-dialog');
    },
    initializeFormData() {
      if (this.editedData) {
        this.form.name = this.editedData.name || '';
        this.form.description = this.editedData.description || '';
        this.imageSrc = this.editedData.avatarUrl || '';
      }
    },
    getTestFiestaProjects(config) {
      if (!config.projects || !this.testFiestaProjects) return [];

      return Object.keys(config.projects)
        .map((key) => {
          const project = this.testFiestaProjects.find((p) => p.uid === parseInt(key));
          return project ? project.name : null;
        })
        .filter(Boolean);
    },

    getServiceProjects(config) {
      if (!config.projects) return [];

      // Flatten all project arrays from all testfiesta projects
      return Object.values(config.projects)
        .flat()
        .map((project) => project.projectName)
        .filter(Boolean);
    },

    scrollToBottom() {
      if (this.$refs.configList) {
        setTimeout(() => {
          this.$refs.configList.scrollTop = this.$refs.configList.scrollHeight;
        }, 100);
      }
    },

    // Custom service project methods
    toggleSelectAllCustom() {
      if (this.isAllCustomSelected) {
        this.selectedCustomProjects = [];
      } else {
        this.selectedCustomProjects = this.filteredCustomProjects.map((p) => p.uid);
      }
    },

    isCustomItemSelected(item) {
      return this.selectedCustomProjects.includes(item.uid);
    },

    calculateVisibleChips(items) {
      let totalWidth = 0;
      const containerWidth = 400; // Approximate width of the select field
      const chipPadding = 32; // Approximate padding and margins
      const moreChipWidth = 80; // Width reserved for "+X more"
      
      return items.reduce((count, uid) => {
        const project = this.testFiestaProjects.find(p => p.uid === uid);
        const projectName = project ? project.name : '';
        const textWidth = this.getTextWidth(projectName);
        const chipWidth = textWidth + chipPadding;
        
        totalWidth += chipWidth;
        if (totalWidth + moreChipWidth <= containerWidth) {
          return count + 1;
        }
        return count;
      }, 0);
    },
    
    getTextWidth(text) {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      context.font = '14px Roboto'; // Match your app's font
      return context.measureText(text).width;
    },

  },
};
</script>

<style scoped>
.v-dialog--fullscreen {
  max-height: 100vh !important;
  width: 485px !important;
  right: 0 !important;
  left: auto !important;
}

.v-expansion-panel-content__wrap {
  padding: 0 !important;
}
</style>

<style lang="scss" scoped>
.dialog-theme {
  .v-dialog {
    max-height: 100vh !important;
    width: 485px !important;
    right: 0 !important;
    left: auto !important;
  }
}

.d-flex.justify-space-between.align-center {
  padding: 16px 24px;
  margin-bottom: 8px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #344054;
  margin-bottom: 8px;
  margin-top: 24px;
}

.field-theme {
  :deep(.v-input__slot) {
    min-height: 38px !important;
    box-shadow: none !important;
  }
}

.select-text {
  font-size: 14px;
  color: #344054;
}

::v-deep .v-list-item {
  padding-left: 16px !important;
  padding-right: 16px !important;
}



.project-logo {
  border-radius: 50%;
  border: 2px dashed #d0d5dd;
  width: 136px;
  height: 136px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  box-sizing: border-box;
  cursor: pointer;
  transition: border-color 0.3s;
  background-size: cover;
  background-position: center;
  position: relative;
  background-color: #f9fafb;
  margin-left: 24px;

  .upload-content {
    padding: 0 16px;
    width: 100%;

    .drag-drop-text {
      font-size: 12px;
      color: #667085;
      margin-bottom: 4px;
      white-space: normal;
      line-height: 1.2;
    }

    .browse-text {
      font-size: 12px;
      color: #0c2ff3;
      font-weight: 500;
      cursor: pointer;
    }
  }

  .edit-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: none;
    cursor: pointer;
    left: 50%;
    transform: translate(-50%, -50%);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }

  &:hover {
    border-color: #2196f3;

    .edit-icon {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.create-project-layout {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 16px;
}

.hovering .edit-icon {
  display: flex;
}

.attachment-upload-area {
  border: 1px dashed #d0d5dd;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  background-color: #f9fafb;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: #2196f3;
  }
}

.attachment-list {
  .attachment-item {
    background-color: #f9fafb;
    border-radius: 6px;
    padding: 0 12px;
    margin-bottom: 4px;

    .filename {
      font-size: 14px;
      color: #344054;
    }

    &:hover {
      background-color: #f2f4f7;
    }
  }
}

.header-section {
  padding-top: 24px;
  padding-bottom: 12px;
}

.integration-title {
  font-family: Inter, sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 28px;
  text-align: left;
  margin: 0;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.configurations-list {
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: thin;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: #F9FAFB;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #D0D5DD;
    border-radius: 4px;
  }
}

.configuration-item {
  background-color: #F9FAFB;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;

  .v-chip {
    &.selected {
      background-color: #EEF4FF !important;
      border-color: #D1E0FF !important;
      color: #1D2939 !important;
    }

    &:not(.selected) {
      background-color: #F9FAFB !important;
      border: 1px solid #D0D5DD !important;
      color: #344054 !important;
    }
  }

  .configuration-name {
    font-size: 14px;
    color: #0C111D;
    font-weight: 700;
  }

  &:hover {
    background-color: #F2F4F7;
  }
}

.chip-theme {
  height: 32px !important;
  font-size: 14px !important;
  font-weight: 500;
  border-radius: 6px !important;
}

.fw-semibold {
  font-weight: 600;
}

.font-weight-medium {
  font-weight: 500;
}
</style>