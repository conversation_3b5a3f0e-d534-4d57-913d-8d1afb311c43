<template>
  <div>
    <v-data-table
      v-if="paginatedItems && paginatedItems.length > 0"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="custom-table data-table-style mt-6"
      :headers="filteredHeaders || []"
      :items="paginatedItems"
      :item-key="itemKey"
      :item-class="() => 'integration-row'"
      :page="page"
      :items-per-page="itemsPerPage"
      :total-items-length="totalItems"
      hide-default-footer
      disable-pagination
      @update:options="$emit('update:options', $event)"
      @click:row="onRowClick"
    >
      <!-- Integration Name Header -->
      <template #[`header.name`]="{ header }">
        <span class="header_text ml-3">
          {{ header.text }}
        </span>
      </template>

      <!-- Projects Header -->
      <template #[`header.projects`]="{ header }">
        <div class="text-center w-100">
          <span class="header_text">
            {{ header.text }}
          </span>
        </div>
      </template>

      <!-- Integration Name Slot -->
      <template #[`item.name`]="{ item }">
        <div class="d-flex align-center">
          <Avatar 
            :size="40"
            :avatar-src="item.picUrl || require('@/assets/png/project.png')"
            class="mr-2"
          />
          <div class="text-start">
            <div class="fw-semibold fs-14px">
              <v-tooltip
                bottom
                max-width="485px"
                :disabled="!isTruncated"
                content-class="tooltip-theme"
              >
                <template #activator="{ on, attrs }">
                  <div
                    :ref="'integrationName_' + item.uid"
                    class="custom-attribute text-truncate cursor-pointer"
                    v-bind="attrs"
                    v-on="on"
                    @mouseover="checkTruncate(item.uid, 'integrationName')"
                  >
                    {{ item.name }}
                  </div>
                </template>
                <span>{{ item.name }}</span>
              </v-tooltip>
            </div>
            <div class="text-theme-secondary fs-12px text-truncate mw-100px font-weight-regular">
              {{ item.service }}
            </div>
          </div>
        </div>
      </template>

      <!-- Project Avatars Slot -->
      <template #[`item.projects`]="{ item }">
        <div class="d-flex justify-center w-100">
          <div class="avatar-group">
            <div
              v-for="(logo, index) in item.projectDetails.slice(0, 4)"
              :key="index"
            >
              <v-tooltip
                top
                content-class="project-tooltip"
              >
                <template #activator="{ on, attrs }">
                  <Avatar 
                    v-bind="attrs"
                    :size="40"
                    :avatar-src="item.logoUrl || require('@/assets/png/project.png')"
                    class="mr-2"
                    v-on="on"
                  />
                </template>
                <div class="project-tooltip-content">
                  {{ logo.name }}
                </div>
              </v-tooltip>
            </div>
            <v-avatar
              v-if="item.projectUids?.length > 4"
              class="font-weight-bold gray-ish--text ml-n2"
              color="#ebecf0"
              size="36"
            >
              +{{ item.projectUids?.length - 4 }}
            </v-avatar>
          </div>
        </div>
      </template>

      <!-- Actions Column -->
      <template #[`item.actions`]="{ item }">
        <div class="d-flex align-center justify-end">
          <!-- Error Status -->
          <div
            v-if="item.status === 'error'"
            class="d-flex align-center mr-2 error-container"
          >
            <span class="text-error fs-14px d-flex align-center">
              {{ $t('integrations.error.integrationError') }}

              <v-menu
                bottom
                offset-y
                :close-delay="0"
                :open-delay="0"
                :open-on-hover="true"
                :close-on-content-click="false"
                :nudge-left="115"
                max-width="none"
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    v-bind="attrs"
                    icon
                    class="warning-icon ml-2"
                    v-on="on"
                  >
                    <WarningIcon />
                  </v-btn>
                </template>
                <div
                  class="error-tooltip"
                  @mouseenter="() => ($refs.menu.isActive = true)"
                  @mouseleave="() => ($refs.menu.isActive = false)"
                >
                  <div class="d-flex align-center">
                    <span class="error-count">{{ item.externalErrors?.length || 0 }} {{ $t('integrations.error.apiIssues') }}</span>
                    <v-btn
                      text
                      small
                      color="primary"
                      class="see-details-btn"
                      @click.stop.prevent="showErrorDetails(item)"
                    >
                      {{ $t('integrations.error.seeDetails') }}
                    </v-btn>
                  </div>
                </div>
              </v-menu>
            </span>
          </div>

          <!-- Inactive Status -->
          <div
            v-if="item.status === 'inactive'"
            class="d-flex align-center mr-2"
          >
            <span class="text-error fs-14px">{{ $t('integrations.please_reauth') }}</span>
            <v-btn
              icon
              class="ml-1"
              @click.stop="handleReauth(item)"
            >
              <RefreshIcon />
            </v-btn>
          </div>

          <!-- Personal Token Link (NEW) -->
          <div
            v-if="item.service === 'github' && item.status !== 'inactive'"
            class="personal-token-link d-flex align-center mr-2"
            style="cursor:pointer;"
            @click="handlePersonalToken(item)"
          >
            <LinkedIcon
              class="mr-1"
              style="width: 16px; height: 16px;"
            />
            <span class="text-primary fs-14px fw-semibold">{{ $t('Personal token') }}</span>
          </div>


          <!-- Action Buttons -->
          <v-tooltip
            bottom
            :disabled="_writeIntegration" 
          >
            <template #activator="{ on, attrs }">
              <div 
                v-bind="attrs" 
                v-on="on"
              >
                <v-btn
                  :disabled="!_writeIntegration"
                  icon
                  plain
                  small
                  class="btn-plain-theme"
                  :ripple="false"
                  @click.stop="onEdit(item)"
                >
                  <EditIcon />
                </v-btn>
              </div>
            </template>
            <span>
              {{ $t('integrations.noPermissionToDo', { action: $t('edit').toLowerCase() }) }}
            </span>
          </v-tooltip>


          <!-- Delete Button -->
          <v-tooltip
            bottom
            :disabled="_deleteIntegration" 
          >
            <template #activator="{ on, attrs }">
              <div 
                v-bind="attrs" 
                v-on="on"
              >
                <v-btn
                  :disabled="!_deleteIntegration"
                  icon
                  plain
                  small
                  @click.stop="onDelete(item)"
                >
                  <DeleteIcon />
                </v-btn>
              </div>
            </template>
            <span>
              {{ $t('integrations.noPermissionToDo', { action: $t('delete').toLowerCase() }) }}
            </span>
          </v-tooltip>
        </div>
      </template>
    </v-data-table>
    <Pagination
      v-if="!skeletonLoaderState && totalItems > 0"
      :page="currentPage"
      :total-pages="totalPages"
      :items-per-page="perPage"
      :total-items="totalItems"
      @update:pagination="onUpdatePagination"
    />

    <!-- Empty state -->
    <div
      v-else
      class="d-flex align-center justify-center py-8"
    >
      <p class="text-subtitle-1">
        {{ $t('integrations.no_data') }}
      </p>
    </div>

    <!-- Edit Integration Component -->
    <EditIntegration
      v-if="showEditDialog"
      :edited-data="editedData"
      @integration-updated="$emit('integration-updated')"
      @refresh="$emit('refresh')"
      @close-dialog="showEditDialog = false"
    />

    <!-- Add the token dialog -->
    <PersonalTokenConfirmDialog
      v-model="showTokenDialog"
      :integration-name="selectedIntegration?.name"
      :repos="repos"
      @confirm="handleTokenConfirm"
      @close="showTokenDialog = false"
    />
  </div>
</template>

<script>
import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import RefreshIcon from '@/assets/svg/reauth.svg';
import EditIntegration from '@/components/Integration/EditIntegration.vue';
import { mapGetters, mapActions } from 'vuex';
import WarningIcon from '@/assets/svg/warning.svg';
import LinkedIcon from '@/assets/svg/linked.svg';
import PersonalTokenConfirmDialog from './integrationTokenDialog.vue';
import Avatar from "@/components/base/Avatar.vue";
import Pagination from '@/components/base/Pagination.vue';

export default {
  components: {
    DeleteIcon,
    EditIcon,
    RefreshIcon,
    EditIntegration,
    WarningIcon,
    LinkedIcon,
    PersonalTokenConfirmDialog,
    Avatar,
    Pagination
  },
  props: {
    filteredHeaders: {
      type: Array,
      default: () => [],
    },
    filteredItems: {
      type: Array,
      default: () => [],
    },
    itemKey: {
      type: String,
      default: 'id',
    },
    rowClass: {
      type: String,
      default: '',
    },
    totalItems: {
      type: Number,
      default: 0,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    itemsPerPage: {
      type: Number,
      default: 10,
    },
    skeletonLoaderState: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      editedData: null,
      showEditDialog: false,
      showTokenDialog: false,
      selectedIntegration: null,
      repos: [],
      isTruncated: false,
    };
  },
  computed: {
    ...mapGetters({
      dynamicHeaders: 'headers/dynamicHeaders',
    }),
    _deleteIntegration() {
      return this.authorityTo('delete_integration');
    },
    _writeIntegration() {
      return this.authorityTo('write_integration');
    },
    perPage() {
      return this.itemsPerPage;
    },
    totalPages() {
      return Math.ceil(this.totalItems / this.perPage);
    },
    paginatedItems() {
      return this.filteredItems;
    }
  },
  watch: {
    'dynamicHeaders.integration': {
      handler(newHeaders) {
        if (newHeaders) {
          this.$emit(
            'headers-updated',
            newHeaders.filter((header) => header.checked)
          );
        }
      },
      deep: true,
    },
  },
  methods: {
    ...mapActions('headers', ['initializeHeaders']),
    onRowClick(item) {
      this.$emit('select-item', item);
    },
    onEdit(item) {
      this.editedData = {
        uid: item.uid,
        name: item.name,
        description: item.description,
        avatarUrl: item.picUrl,
        avatarAttachmentUid: item.avatarAttachmentUid,
        service: item.service,
        projectUids: item.projectUids || [],
        configurations: item.configuration.projectConfigurations,
        configuration: item.configuration,
      };
      this.showEditDialog = true;
    },
    onDelete(item) {
      this.$emit('delete-item', item);
    },
    handleReauth(item) {
      this.$emit('reauth', item.uid, item.service);
    },
    showErrorDetails(item) {
      this.$emit('show-error-details', item);
    },
    handlePersonalToken(item) {
      this.selectedIntegration = item;
      this.repos = [];
      item.configuration.projectConfigurations.forEach((config) => {
        const projects = config.projects;
        const projectNames = Object.values(projects).flatMap((project) => {
          return project.map((p) => p.projectName);
        });
        this.repos = [...this.repos, ...projectNames];
      });
      this.showTokenDialog = true;
    },
    handleTokenConfirm() {
      this.$emit('personal-token', this.selectedIntegration);
      this.showTokenDialog = false;
    },
    onUpdatePagination(options) {
      this.$emit('update-pagination', {
        page: options.page,
        itemsPerPage: options.itemsPerPage,
      });
    },
    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
  },
};
</script>

<style scoped>
.avatar-group {
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-group v-avatar:first-child {
  margin-left: 0;
}

.extra-avatar {
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #555;
}
.v-data-table .v-data-table__wrapper tbody tr:nth-child(odd) {
  background-color: #ffffff;
}

.v-data-table-header__icon {
  opacity: 1 !important;
}

.v-data-table .v-data-table__wrapper tbody tr:nth-child(even) {
  background-color: #f9fafb;
}

.pointer {
  cursor: pointer;
}

.header_text {
  color: #475467;
  font-weight: 700;
}

.custom_border {
  border: 2px solid #ffffff;
}

.v-data-table table {
  border-collapse: collapse;
}

.v-data-table th {
  border: none !important;
}

.v-data-table td {
  border: none !important;
}

.v-data-table .v-data-table__wrapper tbody tr {
  height: auto !important;
  min-height: 80px;
}

.v-data-table .v-data-table__wrapper tbody tr td {
  height: auto !important;
  min-height: 80px;
  padding: 16px 0;
  white-space: normal;
}

.v-data-table tbody tr:hover:not(.v-data-table__expanded__content) {
  background-color: transparent !important;
}

.w-100 {
  width: 100%;
}

.reauth-btn {
  background: #e6ecff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.d-flex.align-center.justify-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  max-width: 100%;
}

.d-flex.align-center.mr-2 {
  margin-right: 20px;
  white-space: nowrap;
}

.text-error {
  color: #f4284e !important;
  display: flex;
  align-items: center;
}

.fs-14px {
  font-size: 14px;
  line-height: 20px;
}

.warning-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 16px;
  width: 16px;
  cursor: pointer;
  flex-shrink: 0;
  vertical-align: middle;
}

.warning-icon :deep(svg) {
  color: #f4284e;
}

.error-count {
  color: #344054;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
}

.see-details-btn {
  text-transform: none !important;
  padding: 0 !important;
  height: auto !important;
  font-weight: 600 !important;
  min-width: 0 !important;
  letter-spacing: 0 !important;
  margin-left: 8px !important;
}

.gap-2 {
  gap: 4px;
}

.error-container {
  position: relative;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.error-menu-wrapper {
  margin-top: 20px !important;
  padding-top: 8px !important;
  z-index: 100;
  width: auto !important;
}

.error-tooltip {
  position: relative;
  background: white;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08);
  white-space: nowrap;
  cursor: default;
  margin-top: 8px;
}

.error-tooltip::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  filter: drop-shadow(0px -1px 1px rgba(16, 24, 40, 0.03));
  z-index: 101;
}

.text-error {
  display: flex;
  align-items: center;
  color: #f4284e !important;
  font-size: 14px;
  line-height: 20px;
  vertical-align: middle;
}

.project-tooltip {
  background: white;
  border-radius: 8px;
  padding: 6px 10px;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08);
  margin-bottom: 8px;
}

.project-tooltip.v-tooltip__content::before {
  display: none !important;
}

.project-tooltip::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

.project-tooltip-content {
  font-size: 14px;
  line-height: 16px;
  color: #344054;
  white-space: nowrap;
}

.pagination-select ::v-deep .v-input__slot {
  min-height: 32px !important;
}

.v-data-table ::v-deep .v-data-footer {
  border-top: thin solid rgba(0, 0, 0, 0.12);
}

.personal-token-link {
  color: #0C2FF3 !important;
  text-transform: none;
  min-width: 0;
  padding: 0 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.personal-token-link :deep(svg) {
  width: 16px;
  height: 16px;
}
</style>