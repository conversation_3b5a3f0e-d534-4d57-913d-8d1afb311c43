<template>
  <div>
    <div
      v-for="category in tools"
      :key="category.category"
      class="mb-6"
    >
      <!-- Category Header -->
      <div class="bg-gray-theme py-4 px-6 mb-2 text-left">
        <h3 class="fs-20 fw-semibold">
          {{ category.category }}
        </h3>
      </div>

      <!-- Tool Items -->
      <v-row no-gutters>
        <v-col
          v-for="(tool, index) in category.items"
          :key="tool.name"
          cols="12"
        >
          <!-- Apply a background color conditionally based on index -->
          <v-sheet
            class="pa-3"
            :class="index % 2 === 0 ? 'bg-gray-light' : 'bg-theme-base'"
            rounded="lg"
            style="margin-bottom: 0"
          >
            <div class="d-flex flex-column">
              <div class="d-flex align-center">
                <div class="d-flex align-center pb-2 flex-grow-1">
                  <Avatar
                    class="mr-2"
                    :size="40"
                    :avatar-src="integrationImages[tool.image] || require(`@/assets/png/${tool.image}`)"
                  />
                 

                  <div class="d-flex flex-column ml-3">
                    <div class="text-start fw-semibold fs-16px">
                      {{ tool.name }}
                    </div>
                    <div class="text-start text-grey-dark fs-14px">
                      {{ tool.description }}
                    </div>
                  </div>
                </div>

                <!-- Move button to the right -->
                <v-tooltip 
                  bottom
                  :disabled="_writeIntegration" 
                >
                  <template #activator="{ on, attrs }">
                    <div 
                      v-bind="attrs" 
                      v-on="on"
                    >
                      <v-btn
                        :disabled="!_writeIntegration"
                        color="#0C2FF3"
                        depressed
                        height="40px"
                        class="text-capitalize rounded-lg btn-theme white--text px-4 ml-auto"
                        @click="addIntegration(tool)"
                      >
                        {{ $t('Add') }}
                        <v-icon
                          class="ml-1"
                          size="16px"
                        >
                          mdi-plus
                        </v-icon>
                      </v-btn>
                    </div>
                  </template>
                  <span>
                    {{
                      $t("integrations.noPermissionToDo", { action: $t("Add").toLowerCase() })
                    }}
                  </span>
                </v-tooltip>
              </div>
            </div>
          </v-sheet>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script>
import jiraIcon from '@/assets/png/jira.png';
import githubIcon from '@/assets/png/github.png';
import testrailIcon from '@/assets/png/testrail.png';
import Avatar from "@/components/base/Avatar.vue"
export default {
  name: 'IntegrationListAll',
  components:{
    Avatar
  },
  props: {
    tools: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      integrationImages: {
        'jira.png': jiraIcon,
        'github.png': githubIcon,
        'testrail.png': testrailIcon,
      },
    };
  },
  computed: {
    _writeIntegration() {
      return this.authorityTo('write_integration');
    },
  },
  methods: {
    async addIntegration(integration) {
      this.$emit('select-integration', integration);
    },
  },
};
</script>

<style scoped>
.bg-gray-theme {
  background-color: #f9fafb;
}
.bg-gray-light {
  background-color: #f3f4f7;
}
.bg-light-gray {
  background-color: #e0e0e0; /* Light gray for alternate rows */
}
.text-grey-dark {
  color: #8e8f96;
}
.fs-20 {
  font-size: 20px;
}
.fs-16px {
  font-size: 16px;
}
.fs-14px {
  font-size: 14px;
}
.fw-semibold {
  font-weight: 600;
}
</style>
