<template>
  <div>
    <v-sheet
      class="integration-item"
      rounded="lg"
      p="4"
      color="#f9fafb"
    >
      <div class="d-flex align-center justify-space-between py-4 px-2">
        <div
          v-if="isHeaderVisible('name')"
          class="d-flex gap-4"
        >
          <Avatar 
            :size="40"
            :avatar-src="item.picUrl || require('@/assets/png/project.png')"
            class="mr-2"
          />
          <div class="text-start">
            <div class="fw-semibold fs-12px">
              {{ item.name || $t('integrations.integration_card.name') }}
            </div>
            <div class="text-theme-secondary fs-12px text-truncate mw-100px font-weight-regular">
              {{ item.service || $t('integrations.integration_card.description') }}
            </div>
          </div>
        </div>
        <div class="d-flex align-center">
          <div
            v-if="item.service === 'github' && item.status !== 'inactive'"
            class="personal-token-link d-flex align-center mr-2"
            style="cursor:pointer;"
            @click="handlePersonalToken"
          >
            <LinkedIcon class="mr-1" />
            <span class="text-primary fw-semibold fs-14px">{{ $t('Personal token') }}</span>
          </div>
          <div class="d-flex align-center justify-end">
            <v-btn
              icon
              @click.stop="onEdit(item)"
            >
              <v-sheet
                color="#F2F4F7"
                class="d-flex align-center justify-center pointer"
                height="40px"
                rounded="lg"
              >
                <EditIcon />
              </v-sheet>
            </v-btn>
            <v-btn
              icon
              @click.stop="onDelete(item)"
            >
              <DeleteIcon />
            </v-btn>
          </div>
        </div>
      </div>

      <div
        v-if="isHeaderVisible('projects')"
        class="pl-3 pb-5"
      >
        <div class="d-flex flex-column">
          <div class="d-flex align-center justify-space-between w-100">
            <div>
              <div class="d-flex">
                <span class="font-weight-regular fs-12px">{{ $t('projectsName') }}:</span>
              </div>
              <div class="avatar-group">
                <div
                  v-for="(logo, index) in item.projectDetails"
                  :key="index"
                >
                  <v-tooltip
                    top
                    content-class="project-tooltip"
                  >
                    <template #activator="{ on, attrs }">
                      <Avatar 
                        v-bind="attrs"
                        :size="40"
                        :avatar-src="item.logoUrl || require('@/assets/png/project.png')"
                        class="mr-2"
                        v-on="on"
                      />
                    </template>
                    <div class="project-tooltip-content">
                      {{ logo.name }}
                    </div>
                  </v-tooltip>
                </div>
                <v-avatar
                  v-if="item.projectUids?.length > 4"
                  class="font-weight-bold gray-ish--text ml-n2"
                  color="#ebecf0"
                  size="36"
                >
                  +{{ item.projectUids?.length - 4 }}
                </v-avatar>
              </div>
            </div>
            <div
              v-if="item.status === 'error'"
              class="d-flex align-center reauth-container"
            >
              <span class="text-error fs-14px d-flex align-center">
                {{ $t('integrations.error.integrationError') }}

                <v-menu
                  ref="menu"
                  bottom
                  offset-y
                  :close-delay="0"
                  :open-delay="0"
                  :open-on-hover="true"
                  :close-on-content-click="false"
                  :nudge-left="115"
                  max-width="none"
                >
                  <template #activator="{ on, attrs }">
                    <v-btn
                      v-bind="attrs"
                      icon
                      class="warning-icon ml-2"
                      v-on="on"
                    >
                      <WarningIcon />
                    </v-btn>
                  </template>
                  <div
                    class="error-tooltip"
                    @mouseenter="() => ($refs.menu.isActive = true)"
                    @mouseleave="() => ($refs.menu.isActive = false)"
                  >
                    <div class="d-flex align-center">
                      <span class="error-count">{{ item.externalErrors?.length || 0 }} {{ $t('integrations.error.apiIssues') }}</span>
                      <v-btn
                        text
                        small
                        color="primary"
                        class="see-details-btn"
                        @click.stop.prevent="showErrorDetails"
                      >
                        {{ $t('integrations.error.seeDetails') }}
                      </v-btn>
                    </div>
                  </div>
                </v-menu>
              </span>
            </div>

            <div
              v-if="item.status === 'inactive'"
              class="d-flex align-center reauth-container"
            >
              <span class="text-error fs-14px">{{ $t('integrations.please_reauth') }}</span>
              <v-btn
                icon
                class="ml-1"
                @click="handleReauth"
              >
                <RefreshIcon />
              </v-btn>
            </div>
          </div>
        </div>
      </div>
    </v-sheet>

    <EditIntegration
      v-if="showEditDialog"
      :edited-data="editedData"
      @integration-updated="$emit('integration-updated')"
      @refresh="$emit('refresh')"
      @close-dialog="showEditDialog = false"
    />

    <PersonalTokenConfirmDialog
      v-model="showTokenDialog"
      :integration-name="selectedIntegration?.name"
      :repos="repos"
      @confirm="handleTokenConfirm"
      @close="showTokenDialog = false"
    />
  </div>
</template>

<script>
import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import RefreshIcon from '@/assets/svg/reauth.svg';
import EditIntegration from '@/components/Integration/EditIntegration.vue';
import WarningIcon from '@/assets/svg/warning.svg';
import LinkedIcon from '@/assets/svg/linked.svg';
import PersonalTokenConfirmDialog from './integrationTokenDialog.vue';
import Avatar from '../base/Avatar.vue';
export default {
  components: {
    DeleteIcon,
    EditIcon,
    RefreshIcon,
    EditIntegration,
    WarningIcon,
    LinkedIcon,
    PersonalTokenConfirmDialog,
    Avatar
  },
  props: {
    item: {
      type: Object,
      required: true,
    },
    filteredHeaders: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      editedData: null,
      showEditDialog: false,
      showTokenDialog: false,
      selectedIntegration: null,
      repos: [],
    };
  },
  methods: {
    onEdit(item) {
      this.editedData = {
        uid: item.uid,
        name: item.name,
        description: item.description,
        avatarUrl: item.picUrl,
        service: item.service,
        avatarAttachmentUid: item.avatarAttachmentUid,
        projectUids: item.projectUids || [],
        configurations: item.configuration.projectConfigurations,
        configuration: item.configuration,
      };
      this.showEditDialog = true;
    },
    onDelete(item) {
      this.$emit('delete-item', item);
    },
    isHeaderVisible(headerValue) {
      return this.filteredHeaders.some((header) => header.value === headerValue && header.checked);
    },
    handleEditSuccess() {
      this.$emit('refresh');
    },
    handleReauth() {
      this.$emit('reauth', this.item.uid, this.item.service);
    },
    showErrorDetails() {
      this.$emit('show-error-details', this.item);
    },
    handlePersonalToken() {
      this.selectedIntegration = this.item;
      this.repos = [];
      this.item.configuration.projectConfigurations.forEach((config) => {
        const projects = config.projects;
        const projectNames = Object.values(projects).flatMap((project) => {
          return project.map((p) => p.projectName);
        });
        this.repos = [...this.repos, ...projectNames];
      });
      this.showTokenDialog = true;
    },
    handleTokenConfirm() {
      this.$emit('personal-token', this.selectedIntegration);
      this.showTokenDialog = false;
    },
  },
};
</script>

<style scoped>
.integration-item:hover {
  cursor: pointer;
}
.custom_border {
  border: 2px solid #ffffff;
}
.custom_color {
  color: #667085;
}
.avatar-group {
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-group v-avatar:first-child {
  margin-left: 0;
}

.extra-avatar {
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #555;
}

.text-theme-secondary {
  color: #667085;
}

.fs-12px {
  font-size: 12px;
  line-height: 18px;
}

.text-error {
  display: flex;
  align-items: center;
  color: #f4284e !important;
  font-size: 14px;
  line-height: 20px;
  vertical-align: middle;
}

.fs-14px {
  font-size: 14px;
  line-height: 20px;
}

.reauth-container {
  margin-right: 16px;
}

.warning-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 16px;
  width: 16px;
  cursor: pointer;
  flex-shrink: 0;
  vertical-align: middle;
}

.warning-icon :deep(svg) {
  color: #f4284e;
}

.project-name {
  color: #344054;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
}

.error-menu-wrapper {
  margin-top: 20px !important;
  padding-top: 8px !important;
  z-index: 100;
  width: auto !important;
  background: transparent !important;
  box-shadow: none !important;
}

.error-tooltip {
  position: relative;
  background: white;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08);
  white-space: nowrap;
  cursor: default;
  margin-top: 8px;
  min-width: 200px;
}

.error-tooltip::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  filter: drop-shadow(0px -1px 1px rgba(16, 24, 40, 0.03));
  z-index: 101;
}

.error-count {
  color: #344054;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
}

.see-details-btn {
  text-transform: none !important;
  padding: 0 !important;
  height: auto !important;
  font-weight: 600 !important;
  min-width: 0 !important;
  letter-spacing: 0 !important;
  margin-left: 8px !important;
}

.reauth-container {
  position: relative;
}

.project-tooltip {
  background: white;
  border-radius: 8px;
  padding: 6px 10px;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08);
  margin-bottom: 8px;
}

/* Override Vuetify's default tooltip arrow */
.project-tooltip.v-tooltip__content::before {
  display: none !important;
}

.project-tooltip::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
}

.project-tooltip-content {
  font-size: 14px;
  line-height: 16px;
  color: #344054;
  white-space: nowrap;
}

.personal-token-link {
  color: var(--v-primary-base, #0C2FF3);
  font-size: 14px;
  font-weight: 600;
  transition: text-decoration 0.2s;
  margin-right: 8px;
}
.personal-token-link:hover {
  text-decoration: underline;
}
.text-primary {
  color: var(--v-primary-base, #0C2FF3) !important;
}
</style>