<template>
  <BaseDialog
    v-model="dialog"
    max-width="500px"
  >
    <v-card class="bg-white">
      <v-card-text class="py-8 px-sm-10">
        <v-flex class="d-flex align-start">
          <p class="ma-0 font-weight-bold text-h6 text-sm-h5 text-start black--text">
            {{ $t('integrations.personal_token_title') }}
          </p>
          <v-icon
            class="mt-1 ml-4 pointer"
            @click="$emit('close')"
          >
            mdi-close
          </v-icon>
        </v-flex>
        <slot name="content">
          <v-flex class="mt-4">
            <p class="text-start">
              {{ $t('integrations.personal_token_confirmation_message', { integrationName: integrationName }) }}
            </p>
            <div class="mt-4 mb-6">
              <span class="font-weight-bold">NOTE:</span>
              <span>
                {{ $t('integrations.personal_token_note_message') }}
              </span>
              <ul class="mt-2">
                <li
                  v-for="repo in repos"
                  :key="repo"
                >
                  <span class="font-weight-bold">{{ repo }}</span>
                </li>
              </ul>
            </div>
          </v-flex>
        </slot>
        <slot name="footer">
          <v-row>
            <v-col cols="6">
              <v-btn
                height="40"
                width="100%"
                class="text-capitalize fw-semibold black--text btn-theme"
                color="#F2F4F7"
                depressed
                @click="$emit('close')"
              >
                {{ $t('cancel') }}
              </v-btn>
            </v-col>
            <v-col cols="6">
              <v-btn
                depressed
                height="40"
                width="100%"
                class="text-capitalize rounded-lg fw-semibold white--text btn-theme"
                color="primary"
                @click="$emit('confirm')"
              >
                {{ $t('confirm') }}
              </v-btn>
            </v-col>
          </v-row>
        </slot>
      </v-card-text>
    </v-card>
  </BaseDialog>
</template>
  
<script>
import BaseDialog from '@/components/base/BaseDialog.vue';

export default {
  name: 'PersonalTokenConfirmDialog',
  components: {
    BaseDialog,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    integrationName: {
      type: String,
      required: true,
    },
    repos: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    dialog: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('input', value);
      },
    },
  },
};
</script>
  
<style scoped></style>
  