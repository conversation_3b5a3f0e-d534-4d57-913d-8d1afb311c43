<template>
  <div>
    <!-- This template is rendered, but typically "invisible" if parent does `v-show="false" -->
    <template>
      <div>
        <v-form class="form-style">
          <div>
            <!-- JIRA ORG SELECT -->
            <div
              v-if="showJiraOrgs && (actionSelected === 'Create new defect' || actionSelected === 'Link existing defect')"
              class="d-flex flex-column mb-6"
            >
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('defect.createNewDefectDialog.jiraOrgLabel') }} <span class="required-asterisk">*</span>
                </v-label>
              </div>
              <v-select
                v-model="resourceId"
                :items="jiraOrganizations"
                type="text"
                dense
                background-color="#F9F9FB"
                :placeholder="$t('defect.createNewDefectDialog.selectJiraOrg')"
                class="rounded-lg field-theme custom-prepend mh-38px"
                hide-details
                append-icon="mdi-chevron-down"
                item-text="resourceName"
                item-value="resourceId"
                :menu-props="{ offsetY: true }"
                :loading="isLoadingResources"
                :disabled="isLoadingResources"
                @change="onJiraOrgSelected"
              />
            </div>

            <!-- PROJECT/REPO SELECT (for Create and Link) -->
            <div
              v-if="actionSelected === 'Create new defect' || actionSelected === 'Link existing defect'"
              class="d-flex flex-column mb-6"
            >
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('defect.createNewDefectDialog.itemLabel', { item: currentServiceEntity }) }} <span class="required-asterisk">*</span>
                </v-label>
              </div>
              <v-select
                v-model="externalProjectUid"
                :items="serviceProjects"
                type="text"
                dense
                background-color="#F9F9FB"
                :placeholder="$t('defect.createNewDefectDialog.itemLabel', { item: currentServiceEntity })"
                class="rounded-lg field-theme custom-prepend mh-38px"
                hide-details
                append-icon="mdi-chevron-down"
                item-text="title"
                item-value="value"
                :menu-props="{ offsetY: true }"
                :loading="isLoadingMetadata"
                :disabled="isLoadingMetadata"
                chips
                deletable-chips
                @change="onProjectSelect"
              >
                <!-- Selection template for displaying selected tags as chips -->
                <template #selection="{ item, index }">
                  <v-chip
                    v-if="index < 3"
                    small
                    class="ma-1"
                    close
                    @click:close="removeTag(item.title)"
                  >
                    <span class="black--text">{{ item.title }}</span>
                  </v-chip>
                </template>
              </v-select>
            </div>

            <!-- TYPE (for Jira) -->
            <div
              v-if="showJiraOrgs && actionSelected === 'Create new defect'"
              class="d-flex flex-column mb-6"
            >
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('defect.createNewDefectDialog.typeLabel') }} <span class="required-asterisk">*</span>
                </v-label>
              </div>
              <v-select
                v-model="typeId"
                type="text"
                dense
                background-color="#F9F9FB"
                :placeholder="$t('defect.createNewDefectDialog.selectType')"
                class="rounded-lg field-theme custom-prepend mh-38px"
                hide-details
                append-icon="mdi-chevron-down"
                :items="issueTypes"
                item-text="title"
                item-value="value"
                :menu-props="{ offsetY: true }"
                :loading="isLoadingTypes"
                :disabled="isLoadingTypes"
                @change="onTypeIdChange"
              />
            </div>
          </div>
          <!-- Dynamic Fields Section (Jira special case) -->
          <div v-if="showDynamicFields">
            <div
              v-if="fieldData.length > 0"
              class="d-flex flex-column mb-6"
            >
              <div
                v-for="field in fieldData"
                :key="field.key"
                class="d-flex flex-column mb-6"
              >
                <!-- Field Label Above -->
                <div class="text-left">
                  <v-label
                    v-if="field.name !== 'Sprint'"
                    class="fs-14px text-theme-label font-weight-medium"
                  >
                    {{ field.name }}
                    <span
                      v-if="field.required"
                      class="red--text"
                    >*</span>
                  </v-label>
                </div>

                <!-- String Fields -->
                <v-text-field
                  v-if="field.schema.type === 'string'"
                  v-model="blankIssue.fields[field.key]"
                  dense
                  background-color="#F9F9FB"
                  :placeholder="field.name"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  :rules="field.required ? [(v) => !!v || $t('defect.createNewDefectDialog.fieldRequired')] : []"
                />

                <!-- Add this new condition for Sprint field -->
                <template v-else-if="field.schema.type === 'array' && field.name === 'Sprint'">
                  <!-- Board ID Input -->
                  <div class="text-left mb-4">
                    <v-label class="fs-14px text-theme-label font-weight-medium">
                      {{ $t('defect.createNewDefectDialog.boardIdLabel') }}
                    </v-label>
                    <v-select
                      v-model="selectedBoard"
                      :items="boards"
                      item-text="name"
                      item-value="id"
                      dense
                      background-color="#F9F9FB"
                      :placeholder="$t('defect.createNewDefectDialog.selectBoard')"
                      class="rounded-lg field-theme custom-prepend mh-38px"
                      hide-details
                      :menu-props="{ offsetY: true }"
                      :loading="isLoadingBoards"
                      :disabled="isLoadingBoards"
                      @change="onBoardSelect"
                    />
                  </div>

                  <!-- Sprint Selection (modified to handle array properly) -->
                  <div class="text-left">
                    <v-label class="fs-14px text-theme-label font-weight-medium">
                      {{ field.name }}
                      <span
                        v-if="field.required"
                        class="error--text"
                      >*</span>
                    </v-label>
                  </div>
                  <v-select
                    v-model="selectedSprint"
                    :items="sprints"
                    item-text="name"
                    item-value="id"
                    dense
                    background-color="#F9F9FB"
                    :placeholder="$t('defect.createNewDefectDialog.selectSprint')"
                    class="rounded-lg field-theme custom-prepend mh-38px"
                    hide-details
                    :loading="isLoadingSprints"
                    :disabled="isLoadingSprints || !selectedBoard"
                    :menu-props="{ offsetY: true }"
                    @change="onSprintSelect(field.key)"
                  />
                </template>

                <!-- Free Text Array Fields (like labels) -->
                <v-combobox
                  v-else-if="field.schema.type === 'array' && !field.allowedValues && field.schema.items === 'string'"
                  v-model="blankIssue.fields[field.key]"
                  multiple
                  small-chips
                  deletable-chips
                  dense
                  background-color="#F9F9FB"
                  :placeholder="field.name"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  append-icon=""
                  no-filter
                  :rules="
                    field.required ? [(v) => v.length > 0 || $t('defect.createNewDefectDialog.fieldRequired')] : []
                  "
                >
                  <template #selection="{ item, index }">
                    <v-chip
                      small
                      close
                      @click:close="removeItem(field.key, index)"
                    >
                      {{ item }}
                    </v-chip>
                  </template>
                </v-combobox>

                <!-- Priority Fields -->
                <v-select
                  v-else-if="field.schema.type === 'priority'"
                  v-model="blankIssue.fields[field.key]"
                  :items="field.allowedValues"
                  item-value="id"
                  item-text="name"
                  dense
                  background-color="#F9F9FB"
                  :placeholder="field.name"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  :menu-props="{ offsetY: true }"
                  :rules="field.required ? [(v) => !!v || $t('defect.createNewDefectDialog.fieldRequired')] : []"
                >
                  <template #selection="{ item }">
                    <span :style="{ color: item.statusColor }">{{ item.name }}</span>
                  </template>
                  <template #item="{ item }">
                    <span :style="{ color: item.statusColor }">{{ item.name }}</span>
                  </template>
                </v-select>

                <!-- Array Fields with Allowed Values -->
                <v-select
                  v-else-if="field.schema.type === 'array' && field.allowedValues"
                  v-model="blankIssue.fields[field.key]"
                  :items="field.allowedValues"
                  item-text="value"
                  item-value="value"
                  multiple
                  dense
                  background-color="#F9F9FB"
                  :placeholder="field.name"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  :menu-props="{ offsetY: true }"
                  chips
                  deletable-chips
                >
                  <!-- Item slot for individual items -->
                  <template #item="{ item }">
                    <v-list-item @click.stop="toggleFieldItem(field.key, item.value)">
                      <v-list-item-action>
                        <v-checkbox
                          :input-value="(blankIssue.fields[field.key] || []).includes(item.value)"
                          class="field-theme"
                          :ripple="false"
                          off-icon="icon-checkbox-off"
                          on-icon="icon-checkbox-on"
                          :hide-details="true"
                          dense
                        />
                      </v-list-item-action>
                      <v-list-item-content>
                        <v-list-item-title>
                          <span class="fs-14px text-theme-label">{{ item.value }}</span>
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>

                  <!-- Selection slot for chips display -->
                  <template #selection="{ item, index }">
                    <v-chip
                      v-if="index < 3"
                      small
                      class="custom-chip-theme ma-1"
                      close
                      @click:close="removeFieldItem(field.key, item.value)"
                    >
                      <span class="text-theme-label">{{ item.value }}</span>
                    </v-chip>
                    <span
                      v-if="index === 3"
                      class="text-caption grey--text text-truncate"
                    >
                      (+{{ blankIssue.fields[field.key].length - 3 }} {{ $t('more') }})
                    </span>
                  </template>
                </v-select>

                <!-- Option Fields -->
                <v-select
                  v-else-if="field.schema.type === 'option'"
                  v-model="blankIssue.fields[field.key]"
                  :items="field.allowedValues"
                  item-value="value"
                  item-text="value"
                  dense
                  background-color="#F9F9FB"
                  :placeholder="field.name"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  :rules="field.required ? [(v) => !!v || $t('defect.createNewDefectDialog.fieldRequired')] : []"
                />

                <!-- Number Field -->
                <v-text-field
                  v-else-if="field.schema.type === 'number'"
                  v-model.number="blankIssue.fields[field.key]"
                  type="number"
                  dense
                  background-color="#F9F9FB"
                  :placeholder="field.name"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  :rules="field.required ? [(v) => !!v || $t('defect.createNewDefectDialog.fieldRequired')] : []"
                />

                <!-- Date Field -->
                <v-menu
                  v-else-if="field.schema.type === 'date' || field.schema.type === 'datetime'"
                  v-model="dateMenus[field.key]"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="290px"
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="blankIssue.fields[field.key]"
                      readonly
                      dense
                      background-color="#F9F9FB"
                      :placeholder="field.name"
                      class="rounded-lg field-theme custom-prepend mh-38px"
                      hide-details
                      v-bind="attrs"
                      :rules="field.required ? [(v) => !!v || $t('defect.createNewDefectDialog.fieldRequired')] : []"
                      v-on="on"
                    />
                  </template>
                  <v-date-picker
                    v-model="blankIssue.fields[field.key]"
                    @input="dateMenus[field.key] = false"
                  />
                </v-menu>

                <!-- Boolean Field -->
                <v-switch
                  v-else-if="field.schema.type === 'boolean'"
                  v-model="blankIssue.fields[field.key]"
                  dense
                  hide-details
                  class="mt-0"
                  :label="field.name"
                  :rules="field.required ? [(v) => v !== null || $t('defect.createNewDefectDialog.fieldRequired')] : []"
                />
                <!-- User Field -->
                <template v-else-if="field.schema.type === 'user'">
                  <v-select
                    v-model="blankIssue.fields[field.key]"
                    :items="field.key === 'assignee' ? assignees : reporters"
                    item-text="name"
                    item-value="id"
                    dense
                    background-color="#F9F9FB"
                    :placeholder="field.name"
                    class="rounded-lg field-theme custom-prepend mh-38px"
                    hide-details
                    :menu-props="{ offsetY: true }"
                    :rules="field.required ? [(v) => !!v || $t('defect.createNewDefectDialog.fieldRequired')] : []"
                  >
                    <!-- Add item template for consistent list items -->
                    <template #item="{ item }">
                      <v-list-item-content>
                        <v-list-item-title>
                          <span class="fs-14px text-theme-label">{{ item.name }}</span>
                        </v-list-item-title>
                      </v-list-item-content>
                    </template>
                  </v-select>
                </template>

                <!-- Array User Field -->
                <v-select
                  v-else-if="field.schema.type === 'array' && field.schema.items === 'user'"
                  v-model="blankIssue.fields[field.key]"
                  :items="reporters"
                  item-text="name"
                  item-value="id"
                  multiple
                  dense
                  background-color="#F9F9FB"
                  :placeholder="field.name"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  :menu-props="{
                    offsetY: true,
                    closeOnClick: false,
                    closeOnContentClick: false,
                  }"
                  chips
                  deletable-chips
                >
                  <!-- Item template with themed checkbox -->
                  <template #item="{ item, attrs, on }">
                    <v-list-item
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-list-item-action>
                        <v-checkbox
                          :input-value="blankIssue.fields[field.key].includes(item.id)"
                          color="primary"
                          class="field-theme mt-0"
                          :ripple="false"
                          off-icon="icon-checkbox-off"
                          on-icon="icon-checkbox-on"
                          hide-details
                          dense
                        />
                      </v-list-item-action>
                      <v-list-item-content>
                        <v-list-item-title>
                          <span class="fs-14px text-theme-label">{{ item.name }}</span>
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>

                  <!-- Selection template for chips -->
                  <template #selection="{ item, index }">
                    <v-chip
                      v-if="index < 3"
                      small
                      class="custom-chip-theme ma-1"
                      close
                      @click:close="removeSprintItem(item, field.key)"
                    >
                      <span class="text-theme-label">{{ item.name }}</span>
                    </v-chip>
                    <span
                      v-if="index === 3"
                      class="text-caption grey--text text-truncate"
                    >
                      (+{{ blankIssue.fields[field.key].length - 3 }} {{ $t('more') }})
                    </span>
                  </template>
                </v-select>

                <!-- Issue Link Field -->
                <template v-else-if="field.schema.type === 'issuelink'">
                  <v-select
                    v-model="blankIssue.fields[field.key]"
                    :items="filteredResults"
                    item-text="name"
                    item-value="externalId"
                    dense
                    background-color="#F9F9FB"
                    :placeholder="$t('defect.createNewDefectDialog.selectDefect')"
                    class="rounded-lg field-theme custom-prepend mh-38px"
                    hide-details
                    :menu-props="{ offsetY: true }"
                    :rules="field.required ? [(v) => !!v || $t('defect.createNewDefectDialog.fieldRequired')] : []"
                  >
                    <template #prepend-item>
                      <v-text-field
                        v-model="searchQueries[field.key]"
                        dense
                        background-color="#F9F9FB"
                        :placeholder="$t('defect.createNewDefectDialog.searchPlaceholder')"
                        class="mx-2 mt-2"
                        hide-details
                        clearable
                        @input="filterDefects(field.key)"
                      >
                        <template #prepend-inner>
                          <v-icon size="18">
                            mdi-magnify
                          </v-icon>
                        </template>
                      </v-text-field>
                    </template>
                  </v-select>
                </template>

                <!-- Linked Issues Field (multi-select version) -->
                <v-select
                  v-else-if="field.schema.type === 'array' && field.schema.items === 'issuelinks'"
                  v-model="blankIssue.fields[field.key]"
                  :items="filteredResults"
                  item-text="name"
                  item-value="externalId"
                  multiple
                  chips
                  deletable-chips
                  dense
                  background-color="#F9F9FB"
                  :placeholder="$t('defect.createNewDefectDialog.selectDefect')"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  :menu-props="{ offsetY: true }"
                  :rules="
                    field.required ? [(v) => v.length > 0 || $t('defect.createNewDefectDialog.fieldRequired')] : []
                  "
                >
                  <template #prepend-item>
                    <v-text-field
                      v-model="searchQueries[field.key]"
                      dense
                      background-color="#F9F9FB"
                      :placeholder="$t('defect.createNewDefectDialog.searchPlaceholder')"
                      class="mx-2 mt-2"
                      hide-details
                      clearable
                      @input="filterDefects(field.key)"
                    >
                      <template #prepend-inner>
                        <v-icon size="18">
                          mdi-magnify
                        </v-icon>
                      </template>
                    </v-text-field>
                  </template>

                  <!-- Selection slot for chips display -->
                  <template #selection="{ item, index }">
                    <v-chip
                      v-if="index < 3"
                      small
                      class="custom-chip-theme ma-1"
                      close
                      @click:close="removeDefectLink(field.key, item.id)"
                    >
                      <span class="text-theme-label">{{ item.name }}</span>
                    </v-chip>
                    <span
                      v-if="index === 3"
                      class="text-caption grey--text text-truncate"
                    >
                      (+{{ blankIssue.fields[field.key].length - 3 }} {{ $t('more') }})
                    </span>
                  </template>
                </v-select>

                <!-- Any/Generic Field -->
                <v-text-field
                  v-else
                  v-model="blankIssue.fields[field.key]"
                  dense
                  background-color="#F9F9FB"
                  :placeholder="field.name"
                  class="rounded-lg field-theme custom-prepend mh-38px"
                  hide-details
                  :rules="field.required ? [(v) => !!v || $t('defect.createNewDefectDialog.fieldRequired')] : []"
                />
              </div>
            </div>
          </div>
        </v-form>
      </div>
    </template>
  </div>
</template>

<script>
import makeIntegrationsService from '@/services/api/integrations';
import { showErrorToast } from '@/utils/toast';

export default {
  name: 'IntegrationDefect',
  props: {
    actionSelected: {
      type: String,
      required: true,
    },
    defects: {
      type: Array,
      required: true,
    },
    currentIntegration: {
      type: Object,
      required: true,
    },
    testfiestaProjectUid: {
      type: String,
      required: true,
    },
    selectedService: {
      type: String,
      required: true,
    },
    integrationState: {
      type: Object,
      default: () => ({}),
    },
    saveRepoProjectPreference: {
      type: Function,
      default: null,
    },
    restoreRepoProjectPreference: {
      type: Function,
      default: null,
    },
    autoSelectSingleRepoProject: {
      type: Function,
      default: null,
    },
    getSavedJiraOrgPreference: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      jiraOrganizations: [],
      serviceProjects: [],
      integrationUid: null,
      resourceId: null,
      externalProjectUid: null,
      externalProjectName: null,
      typeId: null,
      service: null,
      tags: [],
      serviceEntities: {
        jira: 'projects',
        github: 'repositories',
      },
      issueTypes: [],
      isLoadingIssueTypes: false,
      isLoadingResources: false,
      isLoadingTypes: false,
      saveLoading: false,
      jiraMetadata: null,
      isLoadingMetadata: false,
      githubTags: [],
      isLoadingTags: false,
      fieldData: [],
      blankIssue: {
        fields: {},
      },
      processedJiraFields: {},
      dateMenus: {}, // for date pickers
      fieldRules: {
        required: (v) => !!v || this.$t('defect.createNewDefectDialog.fieldRequired'),
      },
      assignees: [],
      reporters: [],
      sprints: [],
      boards: [],
      selectedBoard: null,
      isLoadingSprints: false,
      selectedSprint: null,
      isLoadingBoards: false,
      searchQueries: {},
      filteredResults: [],
    };
  },
  computed: {
    showJiraOrgs() {
      return this.selectedService === 'jira';
    },
    currentServiceEntity() {
      return this.serviceEntities[this.selectedService] || 'projects';
    },
    currentProjectMetadata() {
      if (!this.jiraMetadata || !this.externalProjectUid) {
        return null;
      }
      return this.jiraMetadata[this.externalProjectUid] || null;
    },
    isJiraDefectCreation() {
      return this.selectedService === 'jira' && this.actionSelected === 'Create new defect';
    },
    showDynamicFields() {
      return this.isJiraDefectCreation;
    },
  },
  watch: {
    selectedService: {
      handler(newService) {
        if (newService) {
          this.initializeFromParentState();
        }
      },
    },
    currentIntegration: {
      handler(newIntegration) {
        if (!newIntegration) {
          this.integrationUid = null;
          this.serviceProjects = [];
          this.jiraOrganizations = [];
          return;
        }
        
        this.integrationUid = newIntegration.uid;
        if (this.selectedService === 'jira') {
          this.loadJiraOrganizations(newIntegration);
        } else {
          this.loadNonJiraProjects(newIntegration);
        }
      },
    },
    defects: {
      handler() {
        this.initializeFiltering();
      },
      immediate: true,
    },

  },
  created() {
    if (this.integrationState && this.integrationState.resourceId) {
      this.integrationUid = this.currentIntegration.uid;
      this.onJiraOrgSelected(this.integrationState.resourceId);
    }
    this.initializeFromParentState();
  },
  methods: {
    initializeFromParentState() {
      if (this.integrationState) {
        this.jiraOrganizations = this.integrationState.jiraOrganizations;
        this.serviceProjects = this.integrationState.serviceProjects;
        this.resourceId = this.integrationState.resourceId;
        this.externalProjectUid = this.integrationState.externalProjectUid;
        this.externalProjectName = this.integrationState.externalProjectName;
        this.typeId = this.integrationState.typeId;
        this.issueTypes = this.integrationState.issueTypes;
        this.fieldData = this.integrationState.fieldData;
        this.blankIssue = JSON.parse(JSON.stringify(this.integrationState.blankIssue));
        this.assignees = this.integrationState.assignees;
        this.reporters = this.integrationState.reporters;
        this.sprints = this.integrationState.sprints;
        this.boards = this.integrationState.boards;
        this.selectedBoard = this.integrationState.selectedBoard;
        this.selectedSprint = this.integrationState.selectedSprint;
        this.filteredResults = this.integrationState.filteredResults || [];
      }
      
      // Set integrationUid from currentIntegration if available
      if (this.currentIntegration && this.currentIntegration.uid) {
        this.integrationUid = this.currentIntegration.uid;
      }
    },
    syncStateToParent() {
      const stateUpdate = {
        jiraOrganizations: this.jiraOrganizations,
        serviceProjects: this.serviceProjects,
        resourceId: this.resourceId,
        externalProjectUid: this.externalProjectUid,
        externalProjectName: this.externalProjectName,
        typeId: this.typeId,
        issueTypes: this.issueTypes,
        fieldData: this.fieldData,
        blankIssue: this.blankIssue,
        assignees: this.assignees,
        reporters: this.reporters,
        sprints: this.sprints,
        boards: this.boards,
        selectedBoard: this.selectedBoard,
        selectedSprint: this.selectedSprint,
        integrationUid: this.integrationUid,
        filteredResults: this.filteredResults,
      };
      Object.entries(stateUpdate).forEach(([key, value]) => {
        this.$emit('update:state', { key, value });
      });
    },
    resetSelections() {
      this.jiraOrganizations = [];
      this.serviceProjects = [];
      this.resourceId = null;
      this.externalProjectUid = null;
      this.integrationUid = null;
      if (this.selectedService !== 'jira') {
        this.typeId = null;
      }
    },
    makeDefectData() {
      this.processJiraFields();
      this.syncStateToParent();
      const defectData = {
        fields: this.processedJiraFields,
        externalProjectUid: this.externalProjectUid,
        externalProjectName: this.externalProjectName,
        typeId: this.typeId,
        resourceId: this.resourceId,
      };
      return defectData;
    },
    async loadJiraOrganizations(integrationObj) {
      if (!integrationObj || !integrationObj.configuration) {
        this.jiraOrganizations = [];
        return;
      }
      
      this.isLoadingResources = true;
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const handle = this.$route.params.handle;
        const resp = await integrationsService.getOrganizations(handle, this.integrationUid);
        const usedResourceIds = new Set(
          (integrationObj?.configuration?.projectConfigurations || []).map((cfg) => cfg.resourceId).filter(Boolean)
        );
        const relevantOrgs = (resp?.data || []).filter((org) => usedResourceIds.has(org.resourceId));
        this.jiraOrganizations = relevantOrgs;
        
        // First try to restore saved Jira org preference
        if (this.getSavedJiraOrgPreference) {
          const savedResourceId = this.getSavedJiraOrgPreference();
          if (savedResourceId) {
            const availableOrg = relevantOrgs.find(org => org.resourceId === savedResourceId);
            if (availableOrg) {
              this.resourceId = availableOrg.resourceId;
              this.onJiraOrgSelected(this.resourceId);
              return; // Don't auto-select if we restored a preference
            }
          }
        }
        
        // Auto-select single organization if only one is available and no saved preference
        if (relevantOrgs.length === 1) {
          this.resourceId = relevantOrgs[0].resourceId;
          this.onJiraOrgSelected(this.resourceId);
        }
      } catch (err) {
        console.error('Failed to fetch Jira orgs for integration', this.integrationUid, err);
        this.jiraOrganizations = [];
      } finally {
        this.isLoadingResources = false;
      }
    },
    loadNonJiraProjects(integrationObj) {
      if (!integrationObj || !integrationObj.configuration) {
        this.serviceProjects = [];
        return;
      }
      
      const configs = integrationObj.configuration.projectConfigurations || [];
      const allProjects = [];

      configs.forEach((cfg) => {
        const arr = cfg.projects?.[this.testfiestaProjectUid] || [];
        arr.forEach((p) => {
          allProjects.push({
            title: p.projectName,
            value: p.projectId,
          });
        });
      });
      this.serviceProjects = allProjects;
      
      // First try to restore saved preference
      if (this.restoreRepoProjectPreference) {
        const savedPreference = this.restoreRepoProjectPreference();
        if (savedPreference && savedPreference.repoProjectUid) {
          // Check if the saved project is still available
          const availableProject = allProjects.find(p => p.value === savedPreference.repoProjectUid);
          if (availableProject) {
            this.externalProjectUid = availableProject.value;
            this.externalProjectName = availableProject.title;
            
            // Emit restored project to parent
            this.$emit('project-changed', {
              externalProjectUid: availableProject.value,
              externalProjectName: availableProject.title,
              resourceId: savedPreference.resourceId || null
            });
            
            // For non-Jira services, we don't have resourceId, so we can return here
            return; // Don't auto-select if we restored a preference
          }
        }
      }
      
      // Auto-select single project if available and no saved preference
      if (this.autoSelectSingleRepoProject && allProjects.length === 1) {
        const autoSelection = this.autoSelectSingleRepoProject(allProjects);
        if (autoSelection) {
          this.externalProjectUid = autoSelection.repoProjectUid;
          this.externalProjectName = autoSelection.repoProjectName;
          
          // Emit auto-selected project to parent
          this.$emit('project-changed', {
            externalProjectUid: autoSelection.repoProjectUid,
            externalProjectName: autoSelection.repoProjectName,
            resourceId: null
          });
        }
      }
      

    },
    async onJiraOrgSelected(resourceId) {
      try {
        this.externalProjectUid = null;
        this.serviceProjects = [];
        this.issueTypes = [];
        this.jiraMetadata = null;

        if (!this.currentIntegration || !resourceId) return;

        const configs = this.currentIntegration?.configuration?.projectConfigurations || [];
        const finalProjects = [];

        configs.forEach((cfg) => {
          if (cfg.resourceId === resourceId) {
            const arr = cfg.projects?.[this.testfiestaProjectUid] || [];
            arr.forEach((p) => {
              finalProjects.push({
                title: p.projectName,
                value: p.projectId,
              });
            });
          }
        });

        this.serviceProjects = finalProjects;
        
        // Emit organization change to parent
        this.$emit('project-changed', {
          externalProjectUid: null,
          externalProjectName: null,
          resourceId: resourceId
        });
        
        // First try to restore saved preference
        if (this.restoreRepoProjectPreference) {
          const savedPreference = this.restoreRepoProjectPreference();
          if (savedPreference && savedPreference.repoProjectUid) {
            // Check if the saved project is still available
            const availableProject = finalProjects.find(p => p.value === savedPreference.repoProjectUid);
            if (availableProject) {
              this.externalProjectUid = availableProject.value;
              this.externalProjectName = availableProject.title;
              // If we have a saved resourceId and it matches the current resourceId, use it
              if (savedPreference.resourceId && savedPreference.resourceId === resourceId) {
                // Continue with metadata fetch but don't auto-select
              }
            }
          }
        }
        
        // Auto-select single project if available and no saved preference
        if (!this.externalProjectUid && this.autoSelectSingleRepoProject && finalProjects.length === 1) {
          const autoSelection = this.autoSelectSingleRepoProject(finalProjects);
          if (autoSelection) {
            this.externalProjectUid = autoSelection.repoProjectUid;
            this.externalProjectName = autoSelection.repoProjectName;
          }
        }
        // Now fetch metadata
        const integrationsService = makeIntegrationsService(this.$api);
        const handle = this.$route.params.handle;
        this.isLoadingMetadata = true;
        const response = await integrationsService.getIntegrationData(handle, this.integrationUid, {
          type: 'createMetadata',
          resourceId,
        });
        if (response?.data) {
          this.jiraMetadata = response.data;
        }
        this.isLoadingMetadata = false;
        this.fetchReporters();
        this.fetchBoards();
        

      } catch (err) {
        console.error('Failed to fetch Jira metadata:', err);
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.failedToFetchMetadata'),
          {},
          err?.response?.data
        );
        this.isLoadingMetadata = false;
      }
    },
    async onTypeIdChange(newTypeId) {
      if (newTypeId) {
        this.typeId = newTypeId;
        await this.getIssueTypeDataFromMetadata();
      }
    },
    async onProjectSelect(project) {
      if (!project) {
        this.externalProjectUid = null;
        this.externalProjectName = null;
        this.issueTypes = [];
        this.typeId = null;
      } else {
        const selectedProject = this.serviceProjects.find((p) => p.value === project);
        if (selectedProject) {
          this.externalProjectUid = selectedProject.value;
          this.externalProjectName = selectedProject.title;
          
          // Save the project preference
          if (this.saveRepoProjectPreference) {
            this.saveRepoProjectPreference(selectedProject.value, selectedProject.title, this.resourceId);
          }
          
          // Emit project change to parent
          this.$emit('project-changed', {
            externalProjectUid: selectedProject.value,
            externalProjectName: selectedProject.title,
            resourceId: this.resourceId
          });
        }
      }
      if (
        this.selectedService === 'jira' &&
        this.resourceId &&
        this.jiraMetadata &&
        this.jiraMetadata[this.externalProjectUid]
      ) {
        await this.fetchIssueTypes(this.resourceId, this.externalProjectUid);
        this.fetchAssignees();
      }

    },
    async getIssueTypeDataFromMetadata() {
      try {
        if (!this.externalProjectUid || !this.typeId || !this.jiraMetadata) {
          showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.failedToFetchIssueTypes'));
          return;
        }
        const projectData = this.jiraMetadata[this.externalProjectUid];
        if (!projectData) {
          showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.failedToFetchIssueTypes'));
          return;
        }

        const issueTypeData = projectData[this.typeId];
        if (!issueTypeData) {
          showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.failedToFetchIssueTypes'));
          return;
        }
        const typeName = this.issueTypes.find((type) => String(type.value) === String(this.typeId))?.title;
        const excludedFields = ['issuetype', 'project', 'summary', 'description', 'issuelinks', 'attachment'];
        this.fieldData = [];
        this.blankIssue.fields = {};

        for (const [, field] of Object.entries(issueTypeData)) {
          if (!field) continue;
          if (!excludedFields.includes(field.key)) {
            if (typeName === 'Subtask') {
              if (field.name === 'Sprint') {
                continue;
              }
            }
            let processedField = { ...field };

            // Priority transform
            if (field.key === 'priority' && field.allowedValues) {
              processedField.allowedValues = field.allowedValues.map((value) => ({
                id: value.id,
                name: value.name,
                iconUrl: value.iconUrl,
              }));
            }

            this.fieldData.push(processedField);

            // Initialize the blank fields
            if (field.key === 'labels' || field.schema.type === 'array') {
              this.blankIssue.fields[field.key] = [];
            } else if (field.key === 'priority' && field.defaultValue) {
              this.blankIssue.fields[field.key] = field.defaultValue.id;
            } else if (field.key === 'reporter' || field.key === 'assignee') {
              this.blankIssue.fields[field.key] = { id: '' };
            } else {
              this.blankIssue.fields[field.key] = '';
            }

            // Array of users?
            if (field.schema.type === 'array' && field.schema.items === 'user') {
              this.blankIssue.fields[field.key] = [];
              if (!this.reporters.length) {
                this.fetchReporters();
              }
            }
          }
        }

        // Handle possible sprint field
        const sprintField = this.fieldData.find((f) => f.schema.type === 'array' && f.name === 'Sprint');
        if (sprintField) {
          this.$set(this.blankIssue.fields, sprintField.key, []);
        }
      } catch (error) {
        console.error('Error fetching issue type data:', error);
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.failedToFetchIssueTypes'),
          {},
          error?.response?.data
        );
      }
    },

    async fetchIssueTypes(resourceId, externalProjectUid) {
      this.isLoadingTypes = true;
      this.issueTypes = [];
      this.typeId = null;

      try {
        // First check if we have metadata for this project
        if (this.jiraMetadata && this.jiraMetadata[externalProjectUid]) {
          const projectMetadata = this.jiraMetadata[externalProjectUid];
          const availableTypeIds = Object.keys(projectMetadata);

          // Get the full list of issue types from API
          const integrationsService = makeIntegrationsService(this.$api);
          const handle = this.$route.params.handle;

          const response = await integrationsService.getIntegrationData(handle, this.integrationUid, {
            type: 'issueTypes',
            resourceId,
            externalProjectUid,
          });

          if (response?.data) {
            // Filter issue types based on available type IDs from metadata
            this.issueTypes = response.data
              .filter((type) => availableTypeIds.includes(type.id.toString()))
              .map((type) => ({
                title: type.name,
                value: type.id,
              }));
          }
        }
      } catch (err) {
        console.error('Failed to fetch issue types:', err);
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.failedToFetchIssueTypes'),
          {},
          err?.response?.data
        );
      } finally {
        this.isLoadingTypes = false;
      }
    },
    removeItem(fieldKey, index) {
      this.blankIssue.fields[fieldKey].splice(index, 1);
    },
    removeTag(itemName) {
      const itemToRemove = this.serviceProjects.find(project => project.title === itemName);
      if (itemToRemove) {
        if (this.externalProjectUid === itemToRemove.value) {
          this.externalProjectUid = null;
          this.externalProjectName = null;
        if (this.selectedService === 'jira') {
          this.issueTypes = [];
          this.typeId = null;
          this.fieldData = [];
          this.blankIssue.fields = {};
        }
      }
      if (Array.isArray(this.externalProjectUid)) {
         const index = this.externalProjectUid.indexOf(itemToRemove.value);
        if (index >= 0) {
           this.externalProjectUid.splice(index, 1);
        }
       }
     }
    },
    validateStep(step) {
      if (step === 1) {
        if (!this.externalProjectUid) {
          showErrorToast(
            this.$swal,
            this.$t('defect.createNewDefectDialog.itemSelectError', { item: this.currentServiceEntity })
          );
          return false;
        }
        if (this.selectedService === 'jira') {
          if (!this.resourceId) {
            showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.jiraOrgSelectError'));
            return false;
          }
          if (!this.typeId) {
            showErrorToast(this.$swal, this.$t('defect.createNewDefectDialog.typeSelectError'));
            return false;
          }
        }
        return true;
      }
      if (step === 2) {
        const missingFields = this.fieldData
          .filter(
            (field) =>
              field.required &&
              (!this.blankIssue.fields[field.key] ||
                (Array.isArray(this.blankIssue.fields[field.key]) && this.blankIssue.fields[field.key].length === 0))
          )
          .map((field) => field.name);

        if (missingFields.length > 0) {
          showErrorToast(
            this.$swal,
            this.$t('defect.createNewDefectDialog.requiredFieldsError', {
              fields: missingFields.join(', '),
            })
          );
          return false;
        }
        return true;
      }
      return true;
    },
    processJiraFields() {
      const processedFields = {};
      for (const [key, value] of Object.entries(this.blankIssue.fields)) {
        // Skip empty values, empty strings, empty arrays, and objects with empty values
        if (
          value === null ||
          value === undefined ||
          value === '' ||
          (Array.isArray(value) && value.length === 0) ||
          (typeof value === 'object' && value?.id === '')
        )
          continue;

        const field = this.fieldData.find((f) => f.key === key);
        if (!field) continue;
        switch (field.schema.type) {
          case 'string':
          case 'text':
          case 'any':
            if (value) processedFields[key] = value;
            break;

          case 'number':
            if (value) processedFields[key] = Number(value);
            break;

          case 'boolean':
            if (value !== null) processedFields[key] = Boolean(value);
            break;

          case 'date':
          case 'datetime':
            if (value) processedFields[key] = value;
            break;

          case 'array':
            if (field.name === 'Sprint') {
              processedFields[key] = value;
            } else if (Array.isArray(value) && value.length > 0) {
              if (field.schema.items === 'string' || key === 'labels') {
                processedFields[key] = value;
              } else if (field.schema.items === 'user') {
                const validUsers = value.filter((id) => id);
                if (validUsers.length > 0) {
                  processedFields[key] = validUsers.map((id) => ({ id }));
                }
              } else if (field.schema.items === 'issuelinks') {
                const validLinks = value.filter((link) => link);
                if (validLinks.length > 0) {
                  processedFields[key] = validLinks.map((link) => ({ id: link }));
                }
              } else if (field.allowedValues) {
                const validValues = value.filter((v) => v);
                if (validValues.length > 0) {
                  processedFields[key] = validValues.map((v) => ({ value: v }));
                }
              }
            }
            break;

          case 'user':
            if (value && typeof value === 'string') {
              processedFields[key] = { id: value };
            }
            break;

          case 'option':
            if (value && field.allowedValues) {
              processedFields[key] = { value };
            }
            break;

          case 'priority':
            if (value) {
              processedFields[key] = { id: value };
            }
            break;

          case 'issuelink':
            if (value) {
              processedFields[key] = { id: value };
            }
            break;

          default:
            processedFields[key] = value;
        }
      }

      this.processedJiraFields = processedFields;
    },
    isItemSelected(item) {
      return this.tags.includes(item.name);
    },
    toggleFieldItem(fieldKey, value) {
      if (!this.blankIssue.fields[fieldKey]) {
        this.$set(this.blankIssue.fields, fieldKey, []);
      }
      const index = this.blankIssue.fields[fieldKey].indexOf(value);
      if (index === -1) {
        this.blankIssue.fields[fieldKey].push(value);
      } else {
        this.blankIssue.fields[fieldKey].splice(index, 1);
      }
    },
    removeFieldItem(fieldKey, value) {
      if (!this.blankIssue.fields[fieldKey]) return;
      const index = this.blankIssue.fields[fieldKey].indexOf(value);
      if (index >= 0) {
        this.blankIssue.fields[fieldKey].splice(index, 1);
      }
    },
    async fetchAssignees() {
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const response = await integrationsService.getIntegrationData(this.$route.params.handle, this.integrationUid, {
          type: 'assignees',
          externalProjectUid: this.externalProjectUid,
          resourceId: this.resourceId,
        });
        this.assignees = response.data;
      } catch (error) {
        console.error('Error fetching assignees:', error);
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.failedToFetchAssignees'),
          {},
          error?.response?.data
        );
      }
    },
    async fetchReporters() {
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const response = await integrationsService.getIntegrationData(this.$route.params.handle, this.integrationUid, {
          type: 'reporters',
          resourceId: this.resourceId,
        });
        this.reporters = response.data;
      } catch (error) {
        console.error('Error fetching reporters:', error);
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.failedToFetchReporters'),
          {},
          error?.response?.data
        );
      }
    },
    async fetchBoards() {
      this.isLoadingBoards = true;
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const response = await integrationsService.getIntegrationData(this.$route.params.handle, this.integrationUid, {
          type: 'boards',
          resourceId: this.resourceId,
        });
        this.boards = response.data;
      } catch (error) {
        console.error('Error fetching boards:', error);
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.failedToFetchBoards'),
          {},
          error?.response?.data
        );
        this.boards = [];
      } finally {
        this.isLoadingBoards = false;
      }
    },
    async onBoardSelect(boardId) {
      this.sprints = [];
      if (this.blankIssue.fields.customfield_10020) {
        this.blankIssue.fields.customfield_10020 = [];
      }
      if (!boardId) return;
      await this.fetchSprints(boardId);
    },
    async fetchSprints(boardId) {
      if (this.isLoadingSprints) return;
      this.isLoadingSprints = true;
      try {
        const integrationsService = makeIntegrationsService(this.$api);
        const response = await integrationsService.getIntegrationData(this.$route.params.handle, this.integrationUid, {
          type: 'sprints',
          resourceId: this.resourceId,
          boardId,
        });
        this.sprints = response.data;
      } catch (error) {
        console.error('Error fetching sprints:', error);
        showErrorToast(
          this.$swal,
          this.$t('defect.createNewDefectDialog.failedToFetchSprints'),
          {},
          error?.response?.data
        );
        this.sprints = [];
      } finally {
        this.isLoadingSprints = false;
      }
    },
    onSprintSelect(fieldKey) {
      if (this.selectedSprint) {
        this.$set(this.blankIssue.fields, fieldKey, this.selectedSprint);
      } else {
        this.$set(this.blankIssue.fields, fieldKey, null);
      }
    },
    async filterDefects(fieldKey = null) {
      // This method is only used for field-specific filtering (like issuelink fields)
      // The main defect filtering is now handled in the parent component
      const searchQuery = fieldKey ? this.searchQueries[fieldKey]?.toLowerCase() : null;
      this.filteredResults = [...this.defects];
      
      // Only apply search filter for field-specific searches
      if (searchQuery) {
        this.filteredResults = this.filteredResults.filter((defect) => defect.name.toLowerCase().includes(searchQuery));
      }
    },
    removeDefectLink(fieldKey, defectId) {
      if (!this.blankIssue.fields[fieldKey]) return;
      const index = this.blankIssue.fields[fieldKey].indexOf(defectId);
      if (index >= 0) {
        this.blankIssue.fields[fieldKey].splice(index, 1);
      }
    },
    initializeFiltering() {
      this.fieldData.forEach((field) => {
        if (
          field.schema.type === 'issuelink' ||
          (field.schema.type === 'array' && field.schema.items === 'issuelinks')
        ) {
          this.$set(this.searchQueries, field.key, '');
          this.filterDefects(field.key);
        }
      });
    },
  },
};
</script>

<style scoped>
:deep(.actions-container) {
  position: fixed;
  bottom: 0;
  width: 100%;
  max-width: 485px;
  padding: 16px 24px;
  background-color: white;
  z-index: 1;
  border-top: 1px solid #f2f4f7;
  display: flex;
  justify-content: space-between;
  gap: 16px;
  box-sizing: border-box;
}

:deep(.actions-container .v-btn) {
  flex: 1;
  min-width: 0 !important;
}

:deep(.v-text-field.custom-prepend .v-input__prepend-inner) {
  margin-top: 10px !important;
  margin-right: 8px !important;
}

.attachment-list {
  border: 1px solid #eaecf0;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
}
.attachment-item {
  border-bottom: 1px solid #eaecf0;
  background-color: #f9f9fb;
}
.attachment-item:last-child {
  border-bottom: none;
}
.upload-zone {
  background-color: #f9f9fb;
  border: 1px dashed #e4e7ec;
  border-radius: 8px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
}
.upload-zone:hover {
  border-color: #b2b7c2;
  background-color: #f5f5f7;
}
.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.upload-text {
  color: #667085;
  font-size: 14px;
  margin: 0;
}
.selected-files {
  border: 1px solid #e4e7ec;
  border-radius: 8px;
  overflow: hidden;
}
.file-item {
  background-color: #f9fafb;
  border-bottom: 1px solid #e4e7ec;
}
.file-item:last-child {
  border-bottom: none;
}
.file-name {
  font-size: 14px;
  color: #344054;
}
.upload__attachment {
  background-color: #fff;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #eaecf0;
  width: 100%;
  gap: 12px;
  margin-bottom: 8px;
}
.attachment_file-type {
  position: relative;
  display: flex;
  align-items: center;
}
.attachment_file-type .v-icon {
  z-index: 5;
  font-size: 55px !important;
}
.attachment_file-type span {
  position: absolute;
  font-size: 10px;
  padding: 1px 5px;
  border-radius: 2px;
  color: #fff;
  z-index: 6;
}
.attachment_file-details {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}
.attachment_file-details p {
  font-size: 14px;
  font-family: 'Inter', sans-serif;
  color: #344054;
}
.attachment_file-details #fileName {
  white-space: nowrap;
  width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
}
.attachment_file-details .attachment_progress {
  gap: 5px;
}
.toggle-btn {
  box-shadow: none !important;
}
.defect-attachment-section {
  margin-bottom: 32px;
}
.section-label {
  font-size: 14px;
  font-weight: 500;
  color: #344054;
  display: block;
}
.upload-container-wrapper {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 10px 8px;
  width: 100%;
  max-width: 421px;
}
.upload-container {
  border: 1px dashed #d0d5dd;
  border-radius: 8px;
  padding: 24px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  background-color: #f9fafb;
  min-height: 120px;
}
.upload-container:hover {
  border-color: #b2b7c2;
  background-color: #f5f5f7;
}
.upload-text {
  color: #667085;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  margin: 0;
  max-width: 300px;
}
.browse-files-btn {
  color: #0c2ff3;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}
.arrow-icon {
  transform: rotate(-45deg);
  margin-right: 4px;
}
.file-list {
  border: 1px solid #eaecf0;
  border-radius: 8px;
  overflow: hidden;
}
.file-item {
  background-color: #f9fafb;
  border-bottom: 1px solid #e4e7ec;
}
.file-item:last-child {
  border-bottom: none;
}
.file-name {
  font-size: 14px;
  color: #344054;
}
:deep(.custom-chip-theme) {
  background-color: #f2f4f7 !important;
  color: #344054 !important;
  height: 24px !important;
}
:deep(.custom-chip-theme .v-chip__close) {
  opacity: 1;
  font-size: 16px;
}
:deep(.v-select.v-text-field input) {
  max-height: none;
  height: auto;
}
:deep(.v-select__selections) {
  padding: 0 8px;
  flex-wrap: wrap;
}

.form-style {
  width: 100%;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.v-menu__content) {
  max-height: none !important;
  height: auto !important;
}

:deep(.v-select-list) {
  max-height: 300px !important;
}

:deep(.v-list) {
  padding: 0;
}

:deep(.v-menu__content .v-list) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08);
}

:deep(.v-menu__content) {
  border-radius: 8px;
  box-shadow: 0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08);
}

:deep(.custom-menu-content) {
  position: absolute;
  width: 100%;
  z-index: 100;
}

:deep(.v-select.v-text-field) {
  position: relative;
}

:deep(.v-menu) {
  display: block;
}

:deep(.v-list) {
  padding: 0;
  border-radius: 8px;
  background: white;
}

:deep(.v-list-item) {
  min-height: 40px;
  padding: 0 16px;
}

/* Add position relative to the field container */
.d-flex.flex-column.mb-6 {
  position: relative;
}

.required-asterisk {
  color: #FF0000;
  margin-left: 2px;
}
</style>
