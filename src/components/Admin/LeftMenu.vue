<template>
  <v-card
    class="white mt-3 left-menu-card sticky-on-scroll pt-4"
    rounded="lg"
    elevation="0"
    :class="{ 'is-collapsed-menu': isMenuCollapsed }"
  >
    <v-list 
      :class="isMenuCollapsed ? 'px-3' : 'px-5'"
      nav
    >
      <v-list-item-group
        v-model="selectedItem"
        color="primary"
      >
        <v-menu
          v-for="(item, index) in menuItems"
          :key="item.title"
          open-on-hover
          hoverable
          offset-y
          :close-on-content-click="false"
          :disabled="item.hasAccess"
        >
          <template #activator="{ on, attrs }"> 
            <v-list-item   
              v-bind="attrs"
              :to="item.hasAccess ? item.to : null"
              class="mh-36px"
              :class="{ 'px-3': !isMenuCollapsed, 'mb-3': isMenuCollapsed, 'opacity-md': !item.hasAccess }"
              active-class="active-menu-item"
              v-on="on"
              @click="setActive(index)"
            >
              <v-list-item-icon 
                v-if="!isMenuCollapsed"
                class="my-3 mr-2"
                :class="item.className"
              >
                <component :is="item.icon" />
              </v-list-item-icon>
              <v-list-item-content
                class="pa-0"
                :class="item.className"
              >
                <component
                  :is="item.icon"
                  v-if="isMenuCollapsed"
                />
                <v-list-item-title
                  v-if="!isMenuCollapsed"
                  class="text-left"
                >
                  <span class="fs-14px font-weight-regular text-theme-secondary">{{ item.title }}</span>
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </template>
          <v-card class="tooltip-content">
            <v-card-text>
              <div
                v-if="!item.hasAccess"
                class="text-theme-label"
              >
                <span>{{ $t('integrations.disabledMessage.prefixUser') }}</span> 
                <router-link
                  :to="{
                    name: 'Organizations'
                  }"
                >
                  {{ $t('integrations.disabledMessage.action') }}
                </router-link>
                <span>{{ $t('integrations.disabledMessage.suffix') }}</span>
              </div>
            </v-card-text>
          </v-card>
        </v-menu>
      </v-list-item-group>
    </v-list>
    <div
      class="collapse-btn"
      @click="toggleMenu"
    >
      <v-icon
        class="collapse-icon"
        color="#0C2FF3"
      >
        {{ isMenuCollapsed ? 'mdi-arrow-right-bottom' : 'mdi-arrow-left-bottom' }}
      </v-icon>
      <span
        v-if="!isMenuCollapsed"
        class="collapse-text"
      >{{ $t('collapse') }}</span>
    </div>
  </v-card> 
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';

export default {
  name: 'LeftMenuCard',

  props: {
    menuItems: {
      type: Array,
      default: () => [],
    }
  },

  data() {
    return {
      selectedItem: 0,
    };
  },

  computed: {
    ...mapGetters(['isMenuCollapsed']),
  },

  methods: {
    ...mapMutations(['toggleMenu']),
    setActive(index) {
      this.selectedItem = index;
    }
  },
  mounted() {
    this.selectedItem = this.menuItems.findIndex(item => item.isActive);
  }
};
</script>

<style scoped>
.left-menu-card {
  position: relative;
  height: calc(100vh - 100px);
  transition: width 0.3s;
}

.left-menu-card.sticky-on-scroll {
  position: -webkit-sticky;
  position: sticky;
  top: 12px;
  height: calc(100vh - 24px);
}


.text-left {
  text-align: left;
}

.left-menu-card.collapsed .collapse-btn .collapse-text {
  display: none;
}
.left-menu-card.collapsed .collapse-btn {
  justify-content: center;
}

</style>
