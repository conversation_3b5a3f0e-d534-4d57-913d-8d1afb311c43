<template>
  <v-card
    class="white py-6 px-6 mt-1"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div class="d-flex align-center justify-space-between">
      <h2
        class="text-theme-base fs-24px"
      >
        {{ $t('dataManagement.title') }}
      </h2>
    </div>
  </v-card>
</template>

<script>
export default {
  name: 'DataManagementHeader',
};
</script>

<style scoped>
h2 {
  font-weight: 700;
}
.v-list-item:hover {
  background-color: #F9FAFB;
}
.v-list a {
  text-decoration: none;
}
.v-menu__content {
  text-align: left !important;
}
.horizontal-margin {
  margin: 0px 10px;
}
.tray-btn-margin {
  min-width: 40px !important;
  width: 40px !important;
  padding: 10px 0px !important;
}
.tray-btn-outline {
  border-radius: 8px;
  height: 40px !important;
}
</style>
