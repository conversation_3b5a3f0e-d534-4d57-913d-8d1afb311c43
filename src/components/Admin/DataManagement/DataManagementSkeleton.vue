<template>
  <div class="configuration-container mt-3">
    <v-card
      class="pa-4 pb-4"
      elevation="0"
      rounded="lg"
    >
      <!-- Header Section -->
      <div class="main-header-row">
        <div class="data-header" />
        <div class="section-header-skeleton">
          <v-skeleton-loader
            type="text"
          />
        </div>
        <div class="section-header-skeleton">
          <v-skeleton-loader
            type="text"
          />
        </div>
      </div>
  
      <!-- Sub Header Section -->
      <div class="sub-header-row">
        <div class="data-subheader-skeleton rounded-lg">
          <v-skeleton-loader
            type="text"
          />
        </div>
        <div class="field-header-skeleton rounded-lg">
          <v-skeleton-loader
            type="text"
          />
        </div>
        <div class="field-header-skeleton rounded-lg">
          <v-skeleton-loader
            type="text"
          />
        </div>
        <div class="field-header-skeleton rounded-lg">
          <v-skeleton-loader
            type="text"
          />
        </div>
        <div class="field-header-skeleton rounded-lg">
          <v-skeleton-loader
            type="text"
          />
        </div>
      </div>
  
      <!-- Data Rows Skeleton -->
      <div 
        v-for="n in 4" 
        :key="n"
        class="d-flex"
      >
        <div class="data-row-skeleton">
          <!-- Data Cell -->
          <div class="data-cell-skeleton">
            <v-skeleton-loader
              type="text@2"
            />
            <v-skeleton-loader
              type="chip"
            />
          </div>
  
          <!-- Archive Section -->
          <div class="input-fields-section-skeleton">
            <v-skeleton-loader
              type="text-field"
            />
            <div class="toggle-skeleton">
              <v-skeleton-loader
                type="button"
              />
            </div>
            <v-skeleton-loader
              type="text-field"
            />
          </div>
  
          <!-- Delete Section -->
          <div class="input-fields-section-skeleton">
            <v-skeleton-loader
              type="text-field"
            />
            <div class="toggle-skeleton">
              <v-skeleton-loader
                type="button"
              />
            </div>
            <v-skeleton-loader
              type="text-field"
            />
          </div>
        </div>
  
        <!-- Action Menu -->
        <div class="actions-cell-skeleton">
          <v-skeleton-loader
            type="avatar"
          />
        </div>
      </div>
  
      <!-- Time Selector Section -->
      <div class="time-selector-container-skeleton">
        <div class="section-title-skeleton">
          <v-skeleton-loader
            type="text"
            max-width="200"
          />
        </div>
        <div class="time-row-skeleton">
          <v-skeleton-loader
            type="text-field"
            max-width="300"
          />
          <v-skeleton-loader
            type="text-field"
            max-width="150"
          />
        </div>
        <div class="button-skeleton">
          <v-skeleton-loader
            type="button"
            max-width="100"
          />
        </div>
      </div>
    </v-card>
  </div>
</template>
  
  <script>
  export default {
    name: 'DataManagementSkeleton',
    props: {
      loading: {
        type: Boolean,
        default: false
      }
    }
  };
  </script>
  
  <style scoped>
  .main-header-row {
    display: grid;
    grid-template-columns: 200px 1fr 1fr;
    gap: 2px;
    margin-bottom: 8px;
  }
  
  .data-header {
    background: transparent;
  }
  
  .section-header-skeleton {
    background: #f5f5f5;
    padding: 12px;
    text-align: center;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .sub-header-row {
    display: grid;
    grid-template-columns: 200px repeat(4, 1fr);
    gap: 2px;
    margin-bottom: 4px;
  }
  
  .data-subheader-skeleton,
  .field-header-skeleton {
    background: #eeeeee;
    padding: 10px 8px;
    text-align: center;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .data-row-skeleton {
    display: grid;
    grid-template-columns: 200px 1fr 1fr;
    gap: 4px;
    align-items: center;
    padding: 3px 0;
    width: 100%;
  }
  
  .data-cell-skeleton {
    background: #eeeeee;
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
    min-height: 40px;
  }
  
  .input-fields-section-skeleton {
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 40px;
  }
  
  .toggle-skeleton {
    flex-shrink: 0;
  }
  
  .actions-cell-skeleton {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .time-selector-container-skeleton {
    width: 40%;
    margin: 4rem auto;
  }
  
  .section-title-skeleton {
    margin-bottom: 1.5rem;
  }
  
  .time-row-skeleton {
    display: flex;
    gap: 8px;
    margin-bottom: 2rem;
  }
  
  .button-skeleton {
    display: flex;
    justify-content: end;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .main-header-row,
    .sub-header-row,
    .data-row-skeleton {
      grid-template-columns: minmax(150px, 180px) repeat(4, 1fr);
    }
    
    .time-selector-container-skeleton {
      width: 80%;
    }
  }
  
  /* Vuetify 2 skeleton loader already has built-in animations */
  .data-cell-skeleton .v-skeleton-loader {
    margin: 4px 0;
  }
  
  .input-fields-section-skeleton .v-skeleton-loader {
    margin: 2px;
  }
  
  .toggle-skeleton .v-skeleton-loader {
    min-width: 80px;
  }
  
  .section-header-skeleton .v-skeleton-loader,
  .field-header-skeleton .v-skeleton-loader,
  .data-subheader-skeleton .v-skeleton-loader {
    width: 100%;
    max-width: 150px;
  }
  
  .time-row-skeleton .v-skeleton-loader:first-child {
    flex: 2;
  }
  
  .time-row-skeleton .v-skeleton-loader:last-child {
    flex: 1;
  }
  </style>