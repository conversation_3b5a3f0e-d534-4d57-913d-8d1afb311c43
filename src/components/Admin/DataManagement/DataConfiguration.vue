<template>
  <div class="configuration-container mt-3">
    <DataManagementSkeleton v-if="loading" />
    <v-card
      v-else
      class="pa-6 app-height-global"
      elevation="0"
      rounded="lg"
    >
      <div class="main-header-row">
        <div class="data-header d-flex justify-center align-center">
          <v-switch
            v-model="globalEnabled"
            inset
            hide-details
            color="primary"
            class="ma-0 pa-0 custom-switch"
            :ripple="false"
          >
            <template #label>
              <span class="text-theme-label fs-14px font-weight-medium">
                {{ $t('integrations.header.active') }}
              </span>
            </template>
          </v-switch>
        </div>
        <div class="section-header">
          <span class="title">{{ $t('dataManagement.autoArchiveAfter') }}</span>
        </div>
        <div class="section-header">
          <span class="title">{{ $t('dataManagement.autoDeleteAfter') }}</span>
        </div>
      </div>

      <div class="sub-header-row">
        <div class="data-subheader rounded-lg">
          {{ $t('dataManagement.data') }}
        </div>
        <div class="field-header rounded-lg">
          {{ $t('dataManagement.days') }}
        </div>
        <div class="field-header rounded-lg">
          {{ $t('dataManagement.daysOfInactivity') }}
        </div>
        <div class="field-header rounded-lg">
          {{ $t('dataManagement.days') }}
        </div>
        <div class="field-header rounded-lg">
          {{ $t('dataManagement.daysOfInactivity') }}
        </div>
      </div>

      <!-- Milestones -->
      <div class="d-flex">
        <div class="data-row">
          <div class="data-cell">
            <span class="data-label">{{ $t('dataManagement.milestones') }}</span>
          </div>
          <div class="d-flex gap-1 input-fields-section">
            <v-text-field
              v-model="localSettings.milestones.archiveAfter"
              type="number"
              min="1"
              height="32px"
              hide-details
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @focus="handleFieldFocus('milestones')"
              @input="validatePositiveNumber('milestones', 'archiveAfter', $event)"
            />

            <div class="d-flex align-center">
              <div class="d-flex align-center justify-center">
                <v-btn-toggle
                  v-model="localSettings.milestones.archiveStrategy"
                  color="primary"
                  background-color="white"
                  class="custom-toggle-padding"
                >
                  <v-btn
                    value="and"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.and') }}
                  </v-btn>
                  <v-btn
                    value="or"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.or') }}
                  </v-btn>
                </v-btn-toggle>
              </div>
            </div>
            <v-text-field
              v-model="localSettings.milestones.archiveAfterInactive"
              type="number"
              hide-details
              min="1"
              height="32px"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @input="validatePositiveNumber('milestones', 'archiveAfterInactive', $event)"
            />
          </div>
          <div class="d-flex align-center justify-center gap-1 input-fields-section">
            <v-text-field
              v-model="localSettings.milestones.deleteAfter"
              type="number"
              hide-details
              min="1"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              height="32px"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @input="validatePositiveNumber('milestones', 'deleteAfter', $event)"
            />
            <div class="d-flex align-center justify-center">
              <div class="d-flex align-center justify-center">
                <v-btn-toggle
                  v-model="localSettings.milestones.deletionStrategy"
                  color="primary"
                  background-color="white"
                  class="custom-toggle-padding"
                >
                  <v-btn
                    value="and"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.and') }}
                  </v-btn>
                  <v-btn
                    value="or"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.or') }}
                  </v-btn>
                </v-btn-toggle>
              </div>
            </div>
            <v-text-field
              v-model="localSettings.milestones.deleteAfterInactive"             
              type="number"
              hide-details
              min="1"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              height="32px"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @input="validatePositiveNumber('milestones', 'deleteAfterInactive', $event)"
            />
          </div>
        </div>
      </div>
      
      <!-- Test Plans -->
      <div class="d-flex">
        <div class="data-row mt-1">
          <div class="data-cell">
            <span class="data-label">{{ $t('dataManagement.testPlans') }}</span>
          </div>
          <div class="d-flex align-center justify-center gap-1 input-fields-section">
            <v-text-field
              v-model="localSettings.testPlans.archiveAfter"
              type="number"
              hide-details
              min="1"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              height="32px"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @focus="handleFieldFocus('testPlans')"
              @input="validatePositiveNumber('testPlans', 'archiveAfter', $event)"
            />
            <div class="d-flex align-center justify-center">
              <div class="d-flex align-center justify-center">
                <v-btn-toggle
                  v-model="localSettings.testPlans.archiveStrategy"
                  color="primary"
                  background-color="white"
                  class="custom-toggle-padding"
                >
                  <v-btn
                    value="and"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.and') }}
                  </v-btn>
                  <v-btn
                    value="or"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.or') }}
                  </v-btn>
                </v-btn-toggle>
              </div>
            </div>
            <v-text-field
              v-model="localSettings.testPlans.archiveAfterInactive"
              type="number"
              hide-details
              min="1"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              height="32px"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @input="validatePositiveNumber('testPlans', 'archiveAfterInactive', $event)"
            />
          </div>
          <div class="d-flex align-center justify-center gap-1 input-fields-section">
            <v-text-field
              v-model="localSettings.testPlans.deleteAfter"
              type="number"
              hide-details
              min="1"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              height="32px"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @input="validatePositiveNumber('testPlans', 'deleteAfter', $event)"
            />
            <div class="d-flex align-center justify-center">
              <div class="d-flex align-center justify-center">
                <v-btn-toggle
                  v-model="localSettings.testPlans.deletionStrategy"
                  color="primary"
                  background-color="white"
                  class="custom-toggle-padding"
                >
                  <v-btn
                    value="and"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.and') }}
                  </v-btn>
                  <v-btn
                    value="or"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.or') }}
                  </v-btn>
                </v-btn-toggle>
              </div>
            </div>
            <v-text-field
              v-model="localSettings.testPlans.deleteAfterInactive"
              type="number"
              hide-details
              min="1"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              height="32px"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @input="validatePositiveNumber('testPlans', 'deleteAfterInactive', $event)"
            />
          </div>
        </div>
      </div>
      
      <!-- Test Runs -->
      <div class="d-flex justify-center align-center">
        <div class="data-row mt-1">
          <div class="data-cell">
            <span class="data-label">{{ $t('dataManagement.testRuns') }}</span>
          </div>
          <div class="d-flex align-center justify-center gap-1 input-fields-section">
            <v-text-field
              v-model="localSettings.testRuns.archiveAfter"
              type="number"
              hide-details
              min="1"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              height="32px"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @focus="handleFieldFocus('testRuns')"
              @input="validatePositiveNumber('testRuns', 'archiveAfter', $event)"
            />
            <div class="d-flex align-center justify-center">
              <div class="d-flex align-center justify-center">
                <v-btn-toggle
                  v-model="localSettings.testRuns.archiveStrategy"
                  color="primary"
                  background-color="white"
                  class="custom-toggle-padding"
                >
                  <v-btn
                    value="and"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.and') }}
                  </v-btn>
                  <v-btn
                    value="or"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.or') }}
                  </v-btn>
                </v-btn-toggle>
              </div>
            </div>
            <v-text-field
              v-model="localSettings.testRuns.archiveAfterInactive"
              type="number"
              hide-details
              min="1"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              height="32px"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @input="validatePositiveNumber('testRuns', 'archiveAfterInactive', $event)"
            />
          </div>
          <div class="d-flex align-center justify-center gap-1 input-fields-section">
            <v-text-field
              v-model="localSettings.testRuns.deleteAfter"
              type="number"
              hide-details
              min="1"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              height="32px"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @input="validatePositiveNumber('testRuns', 'deleteAfter', $event)"
            />
            <div class="d-flex align-center justify-center">
              <div class="d-flex align-center justify-center">
                <v-btn-toggle
                  v-model="localSettings.testRuns.deletionStrategy"
                  color="primary"
                  background-color="white"
                  class="custom-toggle-padding"
                >
                  <v-btn
                    value="and"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.and') }}
                  </v-btn>
                  <v-btn
                    value="or"
                    small
                    class="text-capitalize font-weight-medium fs-14px"
                    height="28px"
                  >
                    {{ $t('dataManagement.or') }}
                  </v-btn>
                </v-btn-toggle>
              </div>
            </div>
            <v-text-field
              v-model="localSettings.testRuns.deleteAfterInactive"
              type="number"
              hide-details
              min="1"
              class="rounded-lg field-theme input-field-width-class"
              background-color="white"
              height="32px"
              :placeholder="$t('dataManagement.enterNumberOfDays')"
              @input="validatePositiveNumber('testRuns', 'deleteAfterInactive', $event)"
            />
          </div>
        </div>
      </div>
      
      <div class="time-selector-container">
        <div class="section-title">
          {{ $t('dataManagement.selectConvenientTime') }}
        </div>
        <div>
          <v-label class="text-left fs-14px text-theme-label font-weight-medium">
            {{ $t('dataManagement.timezone') }}<strong class="red--text text--lighten-1">*</strong>
          </v-label>
          <v-select
            v-model="selectedTimezone"
            :items="timezones"
            item-text="name"
            item-value="value"
            class="rounded-lg pt-0 field-theme custom-prepend mh-38px mb-4"
            background-color="#F9F9FB"
            append-icon="mdi-chevron-down"
            :menu-props="{ offsetY: true }"
            height="32px"
            hide-details
          />
        </div>
        <div class="d-flex">
          <div class="w-full">
            <v-label class="text-left fs-14px text-theme-label font-weight-medium">
              {{ $t('dataManagement.time') }}<strong class="red--text text--lighten-1">*</strong>
            </v-label>
            <div>
              <date-picker
                v-model="selectedHour"
                type="time"
                format="HH:mm"
                value-type="format"
                :default-value="getCurrentTime()"
                class="rounded-lg field-theme mh-38px flex-grow-1 mt-1 borderless-datepicker w-full"
              />
            </div>
          </div>
        </div>
      </div>
      
      <div class="d-flex justify-end pb-5">
        <v-btn
          v-if="!jobId"
          class="mt-4"
          color="primary"
          :loading="saving"
          :disabled="isSaveDisabled"
          elevation="0"
          @click="handleSave"
        >
          {{ $t('integrations.close_dialog.save_button') }}
        </v-btn>
        <div
          v-else
          class="d-flex"
        >
          <v-btn
            depressed
            class="text-capitalize rounded-lg white--text mr-3"
            color="primary"
            height="40"
            elevation="0"
            :loading="saving"
            :disabled="isSaveDisabled"
            @click="handleEdit"
          >
            {{ $t('dataManagement.update') }}
          </v-btn>
          <v-btn
            depressed
            class="text-capitalize rounded-lg white--text"
            color="danger"
            height="40"
            elevation="0"
            :loading="deleting"
            @click="openDeleteDialog"
          >
            {{ $t('dataManagement.delete') }}
          </v-btn>
        </div>
      </div>
    </v-card>

    <!-- Delete Confirmation Dialog -->
    <confirm-delete-dialog
      v-model="deleteDialogVisible"
      :header="$t('dataManagement.dataDelete')"
      @respond="handleDeleteResponse"
    />
  </div>
</template>

<script>
import makeDataManagementService from '@/services/api/dataManagement.js';
import { showErrorToast, showSuccessToast } from '@/utils/toast';
import ConfirmDeleteDialog from './DeleteConfirmDialog.vue';
import DataManagementSkeleton from './DataManagementSkeleton.vue';
import DatePicker from 'vue2-datepicker';
import 'vue2-datepicker/index.css';
import { format } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

export default {
  name: 'DataConfiguration',
  components: {
    ConfirmDeleteDialog,
    DatePicker,
    DataManagementSkeleton
  },
  data() {
    return {
      loading: false,
      saving: false,
      deleting: false,
      dataManagementService: null,
      
      // Set proper defaults
      selectedTimezone: null,
      frequency: 'daily',
      selectedHour: '09:00',
      globalEnabled: false,
      jobId: null,
      activeEntityTypes: [],
      
      frequencyOptions: [
        { name: 'Daily', value: 'daily' },
        { name: 'Monthly', value: 'monthly' }
      ],
      timezones: [],
      
      // Simplified settings structure matching API
      localSettings: {
        milestones: {
          entity: 'milestone',
          archiveAfter: '',
          archiveAfterInactive: '',
          archiveStrategy: 'and',
          deleteAfter: '',
          deleteAfterInactive: '',
          deletionStrategy: 'or',
        },
        testPlans: {
          entity: 'testPlan',
          archiveAfter: '',
          archiveAfterInactive: '',
          archiveStrategy: 'and',
          deleteAfter: '',
          deleteAfterInactive: '',
          deletionStrategy: 'or',
        },
        testRuns: {
          entity: 'testRun',
          archiveAfter: '',
          archiveAfterInactive: '',
          archiveStrategy: 'and',
          deleteAfter: '',
          deleteAfterInactive: '',
          deletionStrategy: 'or',
        },
        testCases: {
          entity: 'testCase',
          archiveAfter: '',
          archiveAfterInactive: '',
          archiveStrategy: 'and',
          deleteAfter: '',
          deleteAfterInactive: '',
          deletionStrategy: 'or',
        }
      },
      
      deleteDialogVisible: false
    };
  },
  
  computed: {
    hasApiService() {
      return this.$api && this.dataManagementService;
    },
    _readActivity() {
      return this.authorityTo('read_activity');
    },
    _writeActivity() {
      return this.authorityTo('write_activity');
    },
    deleteActivity() {
      return this.authorityTo('delete_activity');
    },
    hasAnyConfigurationData() {
      // Check if any entity has any field with data
      return Object.values(this.localSettings).some(entity => {
        return entity.archiveAfter !== '' ||
               entity.archiveAfterInactive !== '' ||
               entity.deleteAfter !== '' ||
               entity.deleteAfterInactive !== '';
      });
    },
    isRequiredFieldsFilled() {
      return this.frequency && this.selectedHour && this.selectedTimezone;
    },
    isSaveDisabled() {
      // For create mode: require both configurations and required fields
      if (!this.jobId) {
        return !this.hasAnyConfigurationData || !this.isRequiredFieldsFilled;
      }
      // For edit mode: only require required fields (allow updating just schedule)
      return !this.isRequiredFieldsFilled;
    }
  },
  
  mounted() {
    this.initializeApiService();
    this.generateTimezones();
    this.setDefaultTimezoneFromIP();
    this.loadSettings();
    this.selectedHour = this.getCurrentTime();
  },
  
  methods: {
    generateTimezones() {
      try {
        let allTimezones;
        if (Intl.supportedValuesOf) {
          allTimezones = Intl.supportedValuesOf('timeZone');
        }
        this.timezones = allTimezones
          .map(timezone => {
            try {
              const now = new Date();
              const zonedTime = utcToZonedTime(now, timezone);
              const offset = this.getTimezoneOffset(timezone);
              const currentTime = format(zonedTime, 'HH:mm');
              
              return {
                name: this.formatTimezoneName(timezone, offset, currentTime),
                value: timezone,
                offset: offset,
              };
            } catch (error) {
              return null;
            }
          })
          .filter(Boolean)
          .sort((a, b) => {
            if (a.offset !== b.offset) {
              return a.offset.localeCompare(b.offset);
            }
            return a.name.localeCompare(b.name);
          });

      } catch (error) {
        showErrorToast(this.$swal, this.$t('dataManagement.errorTimeZone'))
      }
    },
    
    getTimezoneOffset(timezone) {
      try {
        const now = new Date();
        const formatter = new Intl.DateTimeFormat('en', {
          timeZone: timezone,
          timeZoneName: 'shortOffset'
        });
        
        const parts = formatter.formatToParts(now);
        const offsetPart = parts.find(part => part.type === 'timeZoneName');
        
        return offsetPart ? offsetPart.value : 'UTC';
      } catch (error) {
        return 'UTC';
      }
    },
    
    formatTimezoneName(timezone, offset, currentTime) {
      const parts = timezone.split('/');
      const city = parts[parts.length - 1].replace(/_/g, ' ');
      const region = parts[0].replace(/_/g, ' ');
      let displayName = '';
      
      if (timezone === 'UTC') {
        displayName = this.$t('dataManagement.utc');
      } else {
        displayName = `${city}`;
        if (region !== city) {
          displayName += ` (${region})`;
        }
      }
      return `${displayName} - ${offset} (${currentTime})`;
    },
    
    isValidTimezone(timezone) {
      try {
        Intl.DateTimeFormat(undefined, { timeZone: timezone });
        return true;
      } catch {
        return false;
      }
    },
    
    async setDefaultTimezoneFromIP() {
      try {
       const response = await fetch('http://ip-api.com/json/');
       const data = await response.json();
       const ipTimezone = data.timezone;
       if (!this.selectedTimezone && this.timezones.length > 0) {
       const match = this.timezones.find(tz => tz.value === ipTimezone);
       if (match) {
        this.selectedTimezone = match.value.replace(/\s*\(\d{1,2}:\d{2}\)$/, '');
        } 
       }
      } catch (error) {
        console.log(this.$t('dataManagement.failedToLoad'), error);
     }
    },
    
    getCurrentTime() {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    
    initializeApiService() {
      if (this.$api) {
        this.dataManagementService = makeDataManagementService(this.$api);
      }
    },
    
    async loadSettings() {
       const handle = this.$route.params.handle;
       const orgs = JSON.parse(localStorage.getItem('project'));
       const projectUid = orgs?.uid;
       this.loading = true;

       try {
        const response = await this.dataManagementService.getDataManagement(handle, projectUid);
        const data = response?.data;
    
        // Handle empty array response
        if (!data || data.length === 0) {
          this.loading = false;
          return;
        }
    
        const jobData = data[0];
    
        // Handle configurations array
        let configs = [];
        if (jobData?.configurations && Array.isArray(jobData.configurations)) {
          configs = jobData.configurations;
        }
    
        // Set job-level data with fallbacks to defaults
       this.frequency = jobData?.frequency || 'daily';
       this.selectedHour = jobData?.time || this.getCurrentTime();
       this.selectedTimezone = jobData?.timezone || this.selectedTimezone;
       this.globalEnabled = jobData?.isEnabled || false;
       this.jobId = jobData?.uid;
    
      // Reset all localSettings to default first
       Object.keys(this.localSettings).forEach(key => {
        this.localSettings[key] = {
          entity: this.localSettings[key].entity,
          archiveAfter: '',
          archiveAfterInactive: '',
          archiveStrategy: 'and',
          deleteAfter: '',
          deleteAfterInactive: '',
          deletionStrategy: 'or',
        };
      });
    
      // Map ALL configurations from the array to local settings
       configs.forEach(config => {
         const entityKey = this.getEntityKeyFromEntity(config.entity);
         if (entityKey && this.localSettings[entityKey]) {
           this.localSettings[entityKey] = {
             entity: config.entity,
             archiveAfter: config.archiveAfter || '',
             archiveAfterInactive: config.archiveAfterInactive || '',
             archiveStrategy: config.archiveStrategy?.toLowerCase() || 'and',
             deleteAfter: config.deleteAfter || '',
             deleteAfterInactive: config.deleteAfterInactive || '',
             deletionStrategy: config.deletionStrategy?.toLowerCase() || 'or',
          };
         }
       });
    
       } catch (error) {
         showErrorToast(this.$swal, this.$t('dataManagement.failedToLoad'));
       } finally {
         this.loading = false;
       }
    },
    
    // Simple mapping from API entity to local key
    getEntityKeyFromEntity(entity) {
      const mapping = {
        milestone: 'milestones',
        testPlan: 'testPlans', 
        testRun: 'testRuns',
        testCase: 'testCases'
      };
      return mapping[entity];
    },
    
    handleFieldFocus(type) {
      if (!this.activeEntityTypes.includes(type)) {
        this.activeEntityTypes.push(type);
      }
    },
    buildApiPayload() {
      const orgs = JSON.parse(localStorage.getItem('project'));
      const projectUid = orgs?.uid;
      
      // For edit mode, include all configurations that have any data
      // For create mode, only include active entities
      const entityTypesToInclude = this.jobId ? 
        Object.keys(this.localSettings) : 
        this.activeEntityTypes;
      
      const configurations = entityTypesToInclude.map(type => {
        const entityData = this.localSettings[type];
        if (!entityData) return null;
        
        const {
          entity,
          deleteAfter,
          deleteAfterInactive,
          deletionStrategy,
          archiveAfter,
          archiveAfterInactive,
          archiveStrategy
        } = entityData;
        
        // Parse values
        const parsedDeleteAfter = deleteAfter !== '' ? parseInt(deleteAfter) : null;
        const parsedDeleteAfterInactive = deleteAfterInactive !== '' ? parseInt(deleteAfterInactive) : null;
        const parsedArchiveAfter = archiveAfter !== '' ? parseInt(archiveAfter) : null;
        const parsedArchiveAfterInactive = archiveAfterInactive !== '' ? parseInt(archiveAfterInactive) : null;
        
        // Check if at least one field has data
        const hasValidData = 
          parsedDeleteAfter !== null ||
          parsedDeleteAfterInactive !== null ||
          parsedArchiveAfter !== null ||
          parsedArchiveAfterInactive !== null;
          
        if (!hasValidData) return null;
        
        return {
          entity,
          deleteAfter: parsedDeleteAfter,
          deleteAfterInactive: parsedDeleteAfterInactive,
          deletionStrategy: deletionStrategy ? deletionStrategy.toUpperCase() : null,
          archiveAfter: parsedArchiveAfter,
          archiveAfterInactive: parsedArchiveAfterInactive,
          archiveStrategy: archiveStrategy ? archiveStrategy.toUpperCase() : null
        };
      }).filter(Boolean);
      
      return {
        projectUid: projectUid,
        isEnabled: this.globalEnabled,
        type: 'MAINTENANCE',
        configurations,
        frequency: this.frequency,
        time: this.selectedHour,
        timezone: this.selectedTimezone
      };
    },
    
    validateRequiredFields() {
      // Check required fields
      if (!this.frequency || !this.selectedHour || !this.selectedTimezone) {
        showErrorToast(this.$swal, this.$t('dataManagement.frequencyAndTimeRequired'));
        return false;
      }
      
      return true;
    },
    
    async handleSave() {
      if (!this.validateRequiredFields()) return;
      
      this.saving = true;
      try {
        const handle = this.$route.params.handle;
        const payload = this.buildApiPayload();
        
        const response = await this.dataManagementService.createDataManagement(handle, payload);
        
        if (response.status === 200) {
          showSuccessToast(this.$swal, this.$t('integrations.success.data_management_success'));
          this.loadSettings();
          this.activeEntityTypes = [];
        }
      } catch (error) {
        showErrorToast(this.$swal, error.response?.data?.message || this.$t('integrations.success.data_management_error'));
      } finally {
        this.saving = false;
      }
    },
    
    async handleEdit() {
      if (!this.validateRequiredFields()) return;
      if (!this.jobId) {
        showErrorToast(this.$swal, this.$t('dataManagement.noJobFound'));
        return;
      }
      
      this.saving = true;
      try {
        const handle = this.$route.params.handle;
        const payload = this.buildApiPayload();
        
        const response = await this.dataManagementService.updateDataManagement(
          handle,
          this.jobId,
          payload
        );
        
        if (response.status === 200) {
          showSuccessToast(this.$swal, this.$t('integrations.success.data_management_updated'));
          this.loadSettings();
          this.activeEntityTypes = [];
        }
      } catch (error) {
        showErrorToast(this.$swal, error.response?.data?.message || this.$t('dataManagement.updateFailed'));
      } finally {
        this.saving = false;
      }
    },
    
    openDeleteDialog() {
      this.deleteDialogVisible = true;
    },
    
    async handleDeleteResponse(confirm) {
      if (confirm) {
        this.deleting = true;
        await this.deleteEntity();
        this.deleting = false;
      }
      this.deleteDialogVisible = false;
    },
    
    async deleteEntity() {
      if (!this.hasApiService || !this.jobId) return;
      
      try {
        const handle = this.$route.params.handle;
        await this.dataManagementService.deleteDataManagement(handle, this.jobId);
        
        // Reset all settings
        Object.keys(this.localSettings).forEach(key => {
          this.localSettings[key] = {
            entity: this.localSettings[key].entity,
            archiveAfter: '',
            archiveAfterInactive: '',
            archiveStrategy: 'and',
            deleteAfter: '',
            deleteAfterInactive: '',
            deletionStrategy: 'or',
          };
        });
        
        this.jobId = null;
        this.globalEnabled = false;
        this.activeEntityTypes = [];
        this.loadSettings();
        showSuccessToast(this.$swal, this.$t('dataManagement.entitySuccessfulDelete'));
      } catch (error) {
        showErrorToast(this.$swal, this.$t('dataManagement.deleteFailed'));
      }
    },
    validatePositiveNumber(entityType, field, value) {
      // Convert to string and remove any non-digits
      let numericValue = value.toString().replace(/\D/g, '');

      console.log("numericValue", numericValue)
    
      // If empty or starts with 0, clear it
      if (!numericValue || numericValue === '0' || numericValue.startsWith('0')) {
        this.$nextTick(() => {
          this.localSettings[entityType][field] = '';
        });
       return;
      }
    
      // Convert to number and ensure it's positive
      const num = parseInt(numericValue);
      if (num > 0) {
        this.$nextTick(() => {
          this.localSettings[entityType][field] = num.toString();
        });
      } else {
        this.$nextTick(() => {
         this.localSettings[entityType][field] = '';
        });
      }
    },
  }
};
</script>

<style scoped>
.main-header-row {
  width: 100%;
  display: grid;
  grid-template-columns: 126px 1fr 1fr;
  gap: 4px;
  margin-bottom: 4px;
}

.data-header {
  background: transparent;
}

.section-header {
  background: #F2F4F7;
  border-radius: 8px;
  border-radius: 8px;
}
.section-header .title {
  font-size: 12px !important;
  font-weight: 600;
  padding: 7px 10px;
  font-family: 'Inter', sans-serif !important;
}
.sub-header-row {
  width: 100%;
  display: grid;
  grid-template-columns: 126px repeat(4, 1fr);
  gap: 4px;
  margin-bottom: 4px;
}
.data-subheader,
.field-header {
  background: #E3E8EE;
  padding: 7px 10px;
  font-weight: 600;
  border-radius: 8px;
  font-size: 12px;
  color: #000;
}
.data-row {
  width: 100%;
  display: grid;
  grid-template-columns: 126px repeat(2, 2fr);
  gap: 4px;
}
.data-cell {
  background: #F2F4F7;
  border-radius: 8px;
  display: flex;
  align-items: center;
}
.data-label {
  color: #000000;
  font-size: 12px;
  font-weight: 600;
  padding: 11px 10px;
}
.input-fields-section {
  width: 100% !important;
  background-color: #F2F4F7;
  border-radius: 8px;
  padding: 4px;
  display: flex;
  gap: 4px;
}
.custom-toggle-padding {
  border-radius: 8px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4px;
  padding: 2px;
}
.input-field-width-class {
  width: 100% !important;
  flex: 1 !important;
  min-width: 0 !important;
  padding-top: 0px;
  margin-top: 0px;
}
.custom-toggle-padding .v-btn {
  min-width: auto !important;
  border: none !important;
  border-radius: 6px !important;
}
.custom-toggle-padding .v-item--active:before {
  background-color: #E6ECFF;
  color: #0C2FF3;
  opacity: 1;
}
.custom-toggle-no-border .v-btn--active {
  margin: 8px 6px !important;
  border-radius: 6px !important;
}
.custom-switch {
  ::v-deep .v-input--switch__thumb {
    width: 24px;
    height: 20px;
    top: 0;
    right: 2px;
  }

  ::v-deep .primary--text {
    background-color: #ffffff !important;
  }

  ::v-deep .primary--text.v-input--switch__track {
    background-color: #0000ff !important;
    opacity: 1;
  }
}
.time-selector-container{
  width: 100;
  max-width: 448px;
  margin: 2rem auto;
}
.section-title{
  font-size: 14px;
  margin-bottom: 1.5rem;
  font-weight: 500;
  color: #0C111D;
}

.borderless-datepicker ::v-deep .mx-input,
.borderless-datepicker ::v-deep input[type="text"] {
  border: none !important;
  box-shadow: none !important;
  background-color: #F9F9FB !important;
  outline: none !important;
  height: 38px;
  padding: 6px;
  border-radius: 4px;
}

.borderless-datepicker ::v-deep .mx-input:focus,
.borderless-datepicker ::v-deep input[type="text"]:focus {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  border-radius: 4px;
}

.borderless-datepicker ::v-deep .mx-datepicker {
  border: none !important;
  border-radius: 4px;
}

.borderless-datepicker ::v-deep .mx-input-wrapper {
  border: none !important;
   background-color: #F9F9FB !important;
   border-radius: 4px;
}
</style>