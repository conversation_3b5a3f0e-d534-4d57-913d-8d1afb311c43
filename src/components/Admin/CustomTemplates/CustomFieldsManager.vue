<template>
  <div>
    <div v-if="localFieldsHasItems">
      <div
        v-for="(item, index) in localFields"
        :key="`field-${item.id || index}`"
        class="px-0 w-full"
      >
        <CustomFieldForm
          ref="customFieldForms"
          :custom-fields.sync="customFields"
          :field-index="index"
          :field-item="item"
          @add-option="onAddOption"
          @remove-option="onRemoveOption"
          @remove-field="removeItem"
        />
      </div>
    </div>
    <div
      v-else
      class="pa-6 text-center"
    >
      <span class="fs-14 text-theme-secondary">{{ $t('templatesPage.noCustomFields') }}</span>
    </div>
    
    <v-row>
      <v-col class="d-flex flex-row justify-end">
        <v-menu
          offset-y
          left
          :nudge-bottom="4"
        >
          <template #activator="{ attrs, on }">
            <v-btn
              v-bind="attrs"
              color="primary"
              height="40"
              depressed
              width="137px"
              class="text-capitalize white--text btn-theme rounded-lg"
              v-on="on"
            >
              <div class="d-flex align-center gap-2">
                <span>
                  {{ $t('templatesPage.add_field') }}
                </span>
                <v-icon
                  color="white"
                  size="20"
                >
                  mdi-plus
                </v-icon>
              </div>
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              :key="1"
              @click="handleAddExistingFieldClick"
            >
              {{ $t('templatesPage.add_exist_fields') }}
            </v-list-item>
            <v-list-item
              :key="2"
              @click="handleAddCustomFieldClick"
            >
              {{ $t('templatesPage.add_new_custom_field') }}
            </v-list-item>
          </v-list>
        </v-menu>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import CustomFieldForm from '@/components/Admin/CustomTemplates/CustomFieldForm.vue';
import { uuid } from 'vue-uuid';

export default {
  name: 'CustomFieldsManager',
  
  components: {
    CustomFieldForm,
  },
  
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    customFields: {
      type: Array,
      default: () => [],
    },
  },

  computed: {
    localFields: {
      get() {
        return this.value;
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    },
    localFieldsHasItems() {
      return this.localFields && this.localFields.length > 0;
    },
  },
  
  methods: {
    handleAddCustomFieldClick() {
      const newItem = {
        id: uuid.v4(),
        name: '',
        dataType: 'text',
        defaultValue: '',
        options: [],
        required: false,
        default_date: new Date().toISOString().substr(0, 10),
        description: '', 
      };
      this.localFields = [...this.localFields, newItem];
    },
    
    handleAddExistingFieldClick() {
      const newItem = {
        id: uuid.v4(), // Add ID for consistency
        required: false,
        name: '',
        dataType: '',
        importFrom: 'existingField',
        defaultValue: [],
        default_date: new Date().toISOString().substr(0, 10),
        description: '', 
      };
      this.localFields = [...this.localFields, newItem];
    },
    
    onAddOption(field) {
      const index = this.localFields.indexOf(field);
      if (index === -1) return;

      const options = [...field.options, ''];
      const newField = {
        ...field,
        options,
      };

      const newFields = [...this.localFields];
      newFields.splice(index, 1, newField);
      this.localFields = newFields;
    },

    onRemoveOption(field, removingIndex) {
      const index = this.localFields.indexOf(field);
      if (index === -1) return;

      const options = [...field.options];
      options.splice(removingIndex, 1);

      const newField = {
        ...field,
        options,
      };

      const newFields = [...this.localFields];
      newFields.splice(index, 1, newField);
      this.localFields = newFields;
    },

    removeItem(selectedItem) {
      this.localFields = this.localFields.filter((item) => item !== selectedItem);
    },
    
    // Expose validation method for parent component
    async validate() {
      let isValid = true;
      let forms = this.$refs.customFieldForms;

      if (forms) {
        if (!Array.isArray(forms)) {
          forms = [forms];
        }

        for (let form of forms) {
          if (!(await form.validate())) {
            isValid = false;
          }
        }
      }
      
      return isValid;
    },
  },
};
</script>