<template>
  <v-dialog
    v-model="showDialog"
    max-width="500"
    persistent
  >
    <v-card class="pa-2">
      <v-card-text class="black--text">
        <div class="d-flex align-center justify-space-between pt-6">
          <h2 class="black--text">
            {{ $t('templatesPage.reassignDefaultTitle') }}
          </h2>
          <v-btn
            icon
            @click="showDialog = false"
          >
            <v-icon color="black">
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <div class="mt-4">
          <div class="text-left mb-1">
            <span class="font-weight-medium">{{ $t('templatesPage.template') }}</span>
          </div>
          <v-select
            v-model="selectedTemplate"
            :items="filteredTemplates"
            item-text="name"
            item-value="uid"
            class="rounded-lg field-theme"
            background-color="#F9F9FB"
            dense
            height="40px"
            hide-details
            :disabled="filteredTemplates.length === 0"
            @change="onTemplateChange"
          />
        </div>
      </v-card-text>

      <v-card-actions class="pb-3">
        <v-row>
          <v-col cols="6">
            <v-btn
              color="gray-100"
              width="100%"
              class="text-capitalize"
              elevation="0"
              height="40"
              @click="showDialog = false"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>

          <v-col cols="6">
            <v-btn
              color="primary"
              width="100%"
              elevation="0"
              height="40"
              class="white--text text-capitalize"
              :disabled="!selectedTemplateObject"
              @click="reassignDefault"
            >
              {{ $t('templatesPage.reassign') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'TemplateAssignDefaultDialog',

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    templates: {
      type: Array,
      default: () => []
    },
    currentTemplate: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      selectedTemplate: null,
      selectedTemplateObject: null
    };
  },

  computed: {
    showDialog: {
      get() {
        return this.value;
      },
      set(v) {
        this.$emit('input', v);
      }
    },
    filteredTemplates() {
      return this.templates.filter(template => 
        template.uid !== this.currentTemplate.uid && !template.isDefault
      );
    }
  },

  watch: {
    value(newVal) {
      if (newVal) {
        this.selectedTemplate = null;
        this.selectedTemplateObject = null;
        if (this.filteredTemplates && this.filteredTemplates.length > 0) {
          this.selectedTemplate = this.filteredTemplates[0].uid;
          this.selectedTemplateObject = this.filteredTemplates[0];
        }
      }
    }
  },

  methods: {
    onTemplateChange(uid) {
      this.selectedTemplateObject = this.filteredTemplates.find(t => t.uid === uid) || null;
    },
    
    reassignDefault() {
      if (!this.selectedTemplateObject) return;
      const updatedTemplate = {
        uid: this.selectedTemplateObject.uid,
        name: this.selectedTemplateObject.name,
        templateFields: this.selectedTemplateObject.customFields?.templateFields || [],
        isDefault: true
      };
      
      this.$emit('reassign-default', updatedTemplate);
      this.showDialog = false;
    }
  }
}
</script>

<style lang="scss" scoped>
.field-theme {
  border-radius: 8px;
}
</style>
