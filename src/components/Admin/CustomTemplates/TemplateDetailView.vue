<template>
  <div
    class="bg-white rounded-lg pa-6 app-height-global mt-3"
  >
    <div
      class="w-100"
    >
      <div
        v-if="showActions"
        class="d-flex justify-space-between align-center mb-6"
      >
        <div>
          <v-btn
            class="text-capitalize bg-white f-color-blue px-0 toggle-btn btn-plain-theme"
            depressed
            plain
            :ripple="false"
            @click="$emit('editTemplate', data)"
          >
            <div class="d-flex align-center">
              <EditBlueIcon />
              <span class="text-theme-primary fs-16px ml-2">{{ $t('edit') }}</span>
            </div>
          </v-btn>
        </div>
        <div class="d-flex align-center">
          <div class="d-flex align-center mr-5">
            <v-btn 
              icon
              class="text-capitalize px-1 btn-custom bg-white f-color-blue px-0 toggle-btn mx-1"
              depressed 
              :ripple="false"
              :disabled="isFirstTemplate"
              :class="{
                'disabled-action': isFirstTemplate,
              }"
              @click="$emit('viewPreviousTemplate')"
            >
              <span class="cursor-pointer d-flex align-center">
                <ArrowLeftIcon />
              </span>
            </v-btn>

            <v-btn 
              depressed
              icon
              :ripple="false"
              class="text-capitalize px-1 btn-custom bg-white f-color-blue px-0 toggle-btn mx-1"
              :disabled="isLastTemplate"
              :class="{
                'disabled-action': isLastTemplate,
              }"
              @click="$emit('viewNextTemplate')"
            >
              <span
                class="mx-2 cursor-pointer d-flex align-center"    
              >
                <ArrowRightIcon />
              </span>
            </v-btn>
          </div>
          <span
            class="close-btn"
            @click="$emit('closeDetail')"
          ><v-icon>mdi-close</v-icon></span>
        </div>
      </div>
      <div class="mb-6">
        <h2 class="font-weight-bold fs-24px text-theme-label text-capitalize">
          {{ data?.name }}
        </h2>
      </div>

      <v-tabs
        v-model="selectedTab"
        class="tabs-theme"
        color="#667085"
        hide-slider
      >
        <v-tab
          v-for="(tab, index) in tabs"
          :key="index"
          :href="'#tab-' + (index + 1)"
          class="text-capitralize text-theme-secondary font-weight-medium fs-14px"
          :ripple="false"
          active-class="tab-label-active"
        >
          {{ tab }}
        </v-tab>
      </v-tabs>

      <v-tabs-items v-model="selectedTab">
        <v-tab-item
          v-for="(tab, index) in tabs"
          :key="index"
          :value="'tab-' + (index + 1)"
        >
          <div
            v-if="selectedTab == `tab-${ 1 }`"
            class="mt-6"
          >
            <h4 class="fw-semibold fs-16px text-theme-label text-capitalize mb-3">
              {{ tab }}
            </h4>

            <div class="d-flex justify-space-between gap-2 mb-8">
              <div class="block rounded-lg px-3 py-2 mh-56px flex-grow-1 bg-gray-theme">
                <div class="">
                  <span class="fs-12px text-theme-secondary text-capitalize">
                    {{ $t('defect.creator') }}
                  </span>
                  <div
                    v-if="data?.creator?.firstName || data?.creator?.lastName"
                    class="text-theme-label fw-semibold fs-14px"
                  >
                    {{ data?.creator?.firstName }} {{ data?.creator?.lastName }}
                  </div>
                  <div v-else>
                    <v-icon>
                      mdi-minus
                    </v-icon>
                  </div>
                </div>
              </div>
              <div class="block rounded-lg px-3 py-2 mh-56px flex-grow-1 bg-gray-theme">
                <div class="">
                  <span class="fs-12px text-theme-secondary">
                    {{ $t('creationDate') }}
                  </span>
                  <div class="text-theme-label fw-semibold fs-14px">
                    {{ createdAt }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Test Case Groups Filter -->
            <div
              v-if="isTestCaseTemplate && hasGroupsInTemplateFields"
              class="mb-4"
            >
              <div class="d-flex flex-wrap gap-2">
                <v-chip
                  v-for="group in availableGroups"
                  :key="group"
                  :ripple="false"
                  class="fs-14px radius-6px text-capitalize height-28px user-select-none position-relative mr-2 mb-2"
                  :class="{
                    'blue--text': selectedGroup === group,
                    'text-theme-secondary': selectedGroup !== group,
                    'fw-semibold': selectedGroup === group,
                    'font-weight-medium': selectedGroup !== group
                  }"
                  :color="selectedGroup === group ? '#e6ecff' : '#f9fafb'"
                  label
                  @click="onChangeGroup(group)"
                >
                  <span>
                    {{ testFormatGroupNames[group] }}
                  </span>
                </v-chip>
              </div>
            </div>

            <!-- Test Result Rules Filter -->
            <div
              v-if="isTestResultTemplate && hasRules"
              class="mb-4"
            >
              <div class="d-flex flex-wrap gap-2">
                <v-chip
                  v-for="(rule, index) in availableRules"
                  :key="rule.ruleId || index"
                  :ripple="false"
                  class="fs-14px radius-6px text-capitalize height-28px user-select-none position-relative mr-2"
                  :class="{
                    'blue--text': selectedRule === rule.ruleId,
                    'text-theme-secondary': selectedRule !== rule.ruleId,
                    'fw-semibold': selectedRule === rule.ruleId,
                    'font-weight-medium': selectedRule !== rule.ruleId
                  }"
                  :color="selectedRule === rule.ruleId ? '#e6ecff' : '#f9fafb'"
                  label
                  @click="onChangeRule(rule.ruleId)"
                >
                  <span>
                    {{ rule.name }}
                  </span>
                </v-chip>
              </div>
            </div>

            <!-- Rules Display for Test Results -->
            <div
              v-if="isTestResultTemplate && hasRules && selectedRule"
              class="my-6"
            >
              <div class="block rounded-lg px-3 py-2 flex-grow-1 bg-gray-theme mb-2">
                <div class="">
                  <span class="fs-12px text-theme-secondary text-capitalize">
                    {{ $t('templatesPage.appliesToStatus') }}
                  </span>
                  <div
                    v-if="selectedRuleData.statusIds && selectedRuleData.statusIds.length > 0"
                    class="text-theme-label fw-semibold fs-14px"
                  >
                    <span
                      v-for="(statusId, index) in selectedRuleData.statusIds"
                      :key="index"
                    >
                      <span :style="`color: ${getStatusColor(statusId, statuses)}`">{{ getStatusName(statusId, statuses) }}</span>
                      <span v-if="index < selectedRuleData.statusIds.length - 1">, </span>
                    </span>
                  </div>
                  <div v-else>
                    <v-icon>
                      mdi-minus
                    </v-icon>
                  </div>
                </div>
              </div>

              <div class="d-flex justify-space-between gap-2">
                <div class="block rounded-lg px-3 py-2 mh-56px flex-grow-1 bg-gray-theme">
                  <div class="">
                    <span class="fs-12px text-theme-secondary text-capitalize">
                      {{ $t('templatesPage.manageTags') }}
                    </span>
                    <div class="text-theme-label fw-semibold fs-14px">
                      {{ selectedRuleData.manageTags ? $t('templatesPage.included') : $t('templatesPage.excluded') }}
                    </div>
                  </div>
                </div>
                <div class="block rounded-lg px-3 py-2 mh-56px flex-grow-1 bg-gray-theme">
                  <div class="">
                    <span class="fs-12px text-theme-secondary">
                      {{ $t('templatesPage.linkDefect') }}
                    </span>
                    <div class="text-theme-label fw-semibold fs-14px">
                      {{ selectedRuleData.createDefects ? $t('templatesPage.included') : $t('templatesPage.excluded') }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="selectedTab == `tab-${ 2 }`"
            class="mt-6"
          >
            <h4 class="fw-semibold fs-16px text-theme-label text-capitalize mb-3">
              {{ tab }}
            </h4>
            <div
              v-for="(item, index) in filteredTemplateFields"
              :key="index"
              class="bg-gray-theme rounded-lg pa-4 mb-2"
            >
              <div class="fs-14px fw-semibold text-theme-label mb-2">
                {{ `${ $t('customField') } ${ getOriginalFieldIndex(item) + 1 }` }}
              </div>
              <div class="d-flex flex-column gap-1">
                <div class="d-flex flex-column gap-1">
                  <span class="text-capitalize fs-14px fw-semibold text-theme-secondary">{{ $t('name') }}</span>
                  <span
                    v-if="item?.name"
                    class="fs-14px text-theme-label"
                  >{{ item?.name }}</span>
                  <div v-else>
                    <v-icon>mdi-minus</v-icon>
                  </div>
                </div>
                <div class="d-flex flex-column gap-1">
                  <span class="text-capitalize fs-14px fw-semibold text-theme-secondary">{{ $t('testruns.create_testrun.description') }}</span>
                  <span
                    v-if="item?.description"
                    class="fs-14px text-theme-label"
                  >{{ item?.description }}</span>
                  <div v-else>
                    <v-icon>mdi-minus</v-icon>
                  </div>
                </div>
                <div class="d-flex flex-column gap-1">
                  <span class="text-capitalize fs-14px fw-semibold text-theme-secondary">{{ $t('dataType') }}</span>
                  <span
                    v-if="item?.dataType"
                    class="fs-14px text-theme-label"
                  >{{ item?.dataType }}</span>
                  <div v-else>
                    <v-icon>mdi-minus</v-icon>
                  </div>
                </div>
                <div
                  v-if="item.options?.length > 0"
                  class="d-flex flex-column gap-1"
                >
                  <span class="text-capitalize fs-14px fw-semibold text-theme-secondary">{{ $t('values') }}</span>
                  <div
                    v-if="item.options.length > 0"
                    class="d-flex flex-column align-start justify-start"
                  >
                    <div
                      v-for="(option, optionIndex) in item.options"
                      :key="optionIndex"
                      class="d-flex align-center gap-3 py-2"
                    >
                      <CheckedBlueIcon />
                      <span class="fs-14px text-theme-label text-capitalize">
                        {{ option }}
                      </span>
                    </div>
                  </div>
                  <div v-else>
                    <v-icon>mdi-minus</v-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </v-tab-item>
      </v-tabs-items>
    </div>
  </div>
</template>

<script>
import ArrowRightIcon from '@/assets/svg/arrow-right.svg';
import ArrowLeftIcon from '@/assets/svg/arrow-left.svg';
import EditBlueIcon from '@/assets/svg/edit-blue.svg';
import CheckedBlueIcon from '@/assets/svg/checked-blue.svg';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import { testFormatGroupNames } from '@/constants/templates';
import colorPreferencesMixin from '@/mixins/colorPreferences';

export default {
  name: 'TemplateDetailView',
  components: {
    ArrowRightIcon,
    ArrowLeftIcon,
    EditBlueIcon,
    CheckedBlueIcon,
  },
  mixins: [colorPreferencesMixin],
  props: {
    showActions: {
      type: Boolean,
      default: true,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    isFirstTemplate: {
      type: Boolean,
      default: false,
    },
    isLastTemplate: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },
  data() {
    return {
      selectedGroup: 'generalInfo',
      selectedRule: null,
      testFormatGroupNames: testFormatGroupNames,
      statuses: null,
      selectedTab: null,
      tabs: [this.$t('overview'), this.$t('customFields')],
    }
  },
  computed: {
    isTestCaseTemplate() {
      return this.data?.entityType === 'testCase';
    },
    isTestResultTemplate() {
      return this.data?.entityType === 'testResult';
    },
    createdAt() {
      return this.data?.createdAt ? this.formatDate(this.data.createdAt) : '';
    },
    hasGroupsInTemplateFields() {
      return Boolean(this.data?.customFields?.templateFields?.some(field => field.group));
    },
    availableGroups() {
      if (!this.data?.customFields?.templateFields) return [];

      const groups = this.data?.customFields?.templateFields
        .filter(field => field.group)
        .map(field => field.group);
      
      return [...new Set(groups)];
    },
    hasRules() {
      return this.data?.rules?.length > 0 || false;
    },
    availableRules() {
      return this.data?.rules || [];
    },
    firstAvailableRule() {
      return this.availableRules.length > 0 ? this.availableRules[0].ruleId : null;
    },
    filteredTemplateFields() {
      const fields = this.data?.customFields?.templateFields || [];
      
      if (this.isTestCaseTemplate && this.hasGroupsInTemplateFields && this.selectedGroup) {
        return fields.filter(field => field.group === this.selectedGroup);
      }
      
      return fields;
    },
    filteredRules() {
      if (!this.isTestResultTemplate || !this.hasRules) return [];
      
      if (this.selectedRule) {
        return this.availableRules.filter(rule => rule.ruleId === this.selectedRule);
      }
      
      return this.availableRules;
    },
    selectedRuleData() {
      if (!this.selectedRule || !this.availableRules) return null;
      return this.availableRules.find(rule => rule.ruleId === this.selectedRule);
    },
  },
  watch: {
    data: {
      handler() {
        // Reset filters when data changes
        this.selectedGroup = 'generalInfo';
        if (this.isTestResultTemplate && this.hasRules) {
          this.selectedRule = this.firstAvailableRule;
        } else {
          this.selectedRule = null;
        }
      },
      immediate: true
    }
  },
  created() {
    this.statuses = this.getStatuses('testCase');
  },
  methods: {
    onChangeGroup(group) {
      this.selectedGroup = this.selectedGroup === group ? null : group;
    },
    onChangeRule(ruleId) {
      this.selectedRule = this.selectedRule === ruleId ? null : ruleId;
    },
    getFieldCountByGroup(group) {
      const fields = this.data?.customFields?.templateFields || [];
      return fields.filter(field => field.group === group).length;
    },
    getOriginalFieldIndex(item) {
      const allFields = this.data?.customFields?.templateFields || [];
      return allFields.findIndex(field => 
        field.id === item.id || 
        (field.name === item.name && field.dataType === item.dataType)
      );
    }
  }
}
</script>