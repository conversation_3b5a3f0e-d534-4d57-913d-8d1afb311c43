<template>
  <div>
    <v-sheet
      v-if="!skeletonLoaderState"
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40px"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-3 py-2 d-flex flex-row">{{ $t('filters') }} <v-icon
        size="16px"
        class="ml-2"
      >mdi-filter-variant</v-icon></span>
    </v-sheet>
    <v-skeleton-loader
      v-else
      class="rounded-lg"
      height="40"
      width="95"
      type="button"
    />
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('filters') }}
            </h2>
            <v-btn
              icon
              @click="close"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>

          <!-- Creator Section -->
          <v-expansion-panels
            v-if="isOrgAccount"
            v-model="creatorPanel"
            flat
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('creator') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-text-field
                  v-model="creatorSearch"
                  single-line
                  class="text-field field-theme mt-0 pa-0 rounded-lg custom-prepend"
                  background-color="#F9F9FB"
                  clearable
                  height="38"
                  placeholder="Search"
                  prepend-inner-icon="mdi-magnify"
                  hide-details
                />
                
                <div
                  v-for="creator in filteredCreators"
                  :key="creator.id"
                >
                  <v-checkbox
                    v-model="selectedCreators"
                    :value="creator.id"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ creator.name }}</span>
                    </template>
                  </v-checkbox>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>

          <!-- Creation Date Section -->
          <v-expansion-panels
            v-model="datePanel"
            flat
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('creationDate') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content class="panel-content-theme">
                <v-row>
                  <v-col
                    cols="6"
                    class="pr-1"
                  >
                    <v-menu
                      v-model="showStartDateCalendar"
                      :close-on-content-click="false"
                      :nudge-right="40"
                      transition="scale-transition"
                      offset-y
                      min-width="300"
                    >
                      <template #activator="{ on, attrs }">
                        <v-text-field
                          v-model="startDate"
                          single-line
                          class="text-field field-theme mt-0 pa-0 rounded-lg custom-prepend"
                          readonly
                          v-bind="attrs"
                          background-color="#F9F9FB"
                          clearable
                          height="38"
                          hide-details
                          :placeholder="$t('customFieldPage.startDate')"
                          v-on="on"
                        >
                          <template #prepend-inner>
                            <calendarBlueIcon />
                          </template>
                        </v-text-field> 
                      </template>
                      <v-date-picker
                        v-model="startDate"
                        :max="today"
                        @input="showStartDateCalendar = false"
                      />
                    </v-menu>
                  </v-col>

                  <v-col
                    cols="6"
                    class="pl-1"
                  >
                    <v-menu
                      v-model="showEndDateCalendar"
                      :close-on-content-click="false"
                      :nudge-right="40"
                      transition="scale-transition"
                      offset-y
                      min-width="300"
                    >
                      <template #activator="{ on, attrs }">
                        <v-text-field
                          v-model="endDate"
                          single-line
                          class="text-field field-theme mt-0 pa-0 rounded-lg custom-prepend"
                          readonly
                          v-bind="attrs"
                          background-color="#F9F9FB"
                          clearable
                          height="38"
                          hide-details
                          :placeholder="$t('customFieldPage.endDate')"
                          v-on="on"
                        >
                          <template #prepend-inner>
                            <calendarBlueIcon />
                          </template>
                        </v-text-field>
                      </template>
                      <v-date-picker
                        v-model="endDate"
                        :max="today"
                        @input="showEndDateCalendar = false"
                      />
                    </v-menu>
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
      </v-card>
      <!-- Action Buttons -->
      <div class="actions-container d-flex justify-space-between px-6 py-4">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="clearAll"
        >
          {{ $t('clearAll') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme white--text text-capitalize"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          @click="apply"
        >
          {{ $t('apply') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import calendarBlueIcon from '@/assets/svg/calendar-blue.svg';
import dayjs from 'dayjs';
import handleLoading from '@/mixins/loader.js'

export default {
  name: 'FilterDialog',
  components: {
    calendarBlueIcon
  },
  mixins: [handleLoading],
  props: {
    creators: {
      type: Array,
      default: () => ([])
    },
    currentAccount: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({
        creators: [],
        startDate: null,
        endDate: null
      })
    }
  },

  data() {
    return {
      showDialog: false,
      creatorPanel: 0,
      datePanel: 0,
      creatorSearch: '',
      selectedCreators: this.data.creators || [],
      startDate: this.data.startDate || null,
      endDate: this.data.endDate || null,
      showStartDateCalendar: false,
      showEndDateCalendar: false
    }
  },

  computed: {
    today() {
      return dayjs().format('YYYY-MM-DD')
    },
    
    isOrgAccount() {
      return this.currentAccount?.type === 'org'
    },

    filteredCreators() {
      if (!this.creatorSearch) return this.creators.map(creator => ({
        id: creator.uid,
        name: `${creator.firstName} ${creator.lastName}`
      }))
      
      const search = this.creatorSearch.toLowerCase()
      return this.creators
        .filter(creator => 
          `${creator.firstName} ${creator.lastName}`.toLowerCase().includes(search)
        )
        .map(creator => ({
          id: creator.uid,
          name: `${creator.firstName} ${creator.lastName}`
        }))
    }
  },

  watch: {
    data: {
      handler(newVal) {
        this.selectedCreators = newVal.creators || []
        this.startDate = newVal.startDate
        this.endDate = newVal.endDate
      },
      immediate: true,
      deep: true
    },
    
    showDialog(val) {
      if (val) {
        this.selectedCreators = this.data.creators || []
        this.startDate = this.data.startDate
        this.endDate = this.data.endDate
      }
    }
  },

  methods: {
    clearAll() {
      this.selectedCreators = []
      this.startDate = null
      this.endDate = null
      this.creatorSearch = ''
      
      this.$emit('update-filter-condition', {
        creators: [],
        startDate: null,
        endDate: null
      });
      this.showDialog = false;
    },

    apply() {
      const filterData = {
        creators: this.selectedCreators,
        startDate: this.startDate,
        endDate: this.endDate
      };
      this.$emit('update-filter-condition', filterData);
      this.showDialog = false;
    },

    close() {
      this.showDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-dialog {
  .v-dialog {
    margin: 0;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
  }

  .actions-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #E4E7EC;
  }

  .v-text-field.v-text-field--enclosed {
    margin: 0;
    
    .v-input__prepend-inner {
      margin-top: 8px;
    }
  }
}
</style> 