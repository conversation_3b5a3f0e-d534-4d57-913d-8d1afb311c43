<template>
  <v-card
    class="white py-6 px-6 mt-3"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div class="d-flex align-center justify-space-between">
      <h2
        v-if="!skeletonLoaderState"
        class="text-theme-base"
      >
        {{ $t('templatesPage.templates') }}
      </h2>
      <v-skeleton-loader
        v-else
        height="36"
        width="140"
        type="heading"
      />
      <template v-if="skeletonLoaderState">
        <v-skeleton-loader
          class="rounded-lg primary"
          height="40"
          width="150"
          type="button"
        />
      </template>
      <v-tooltip 
        v-else
        bottom
        :disabled="writeTemplate" 
      >
        <template #activator="{ on, attrs }">
          <div 
            v-bind="attrs" 
            v-on="on"
          >
            <v-btn
              :disabled="!writeTemplate"
              color="primary"
              depressed
              :class="{
                'text-capitalize btn-theme rounded-lg': true,
                'disabled-action': isProjectArchived
              }"
              height="40px"
              @click="!isProjectArchived && $emit('create-custom-template')"
            >
              {{ $t('templatesPage.createTemplate') }} <v-icon
                class="ml-1" 
                size="xs"
              >
                mdi-plus
              </v-icon>
            </v-btn>
          </div>
        </template>
        <span>
          {{
            $t("templatesPage.noPermissionToDo", { action: $t("create").toLowerCase() })
          }}
        </span>
      </v-tooltip>
    </div>

    <template v-if="!skeletonLoaderState">
      <div class="d-flex mt-4">
        <v-chip 
          :class="{ 'blue--text': entityType === 'testCase',
                    'text-theme-secondary': entityType !== 'testCase'
          }" 
          width="200px" 
          :color="entityType === 'testCase' ? '#e6ecff' : '#f9fafb'"
          label 
          @click="$emit('update-entity-type', 'testCase')"
        >
          <div
            :class="{ 'font-weight-bold': entityType === 'testCase', 
                      'font-weight-medium': entityType !== 'testCase'
            }"
          >
            {{ $t('testFormats') }} <span class="ml-2">{{ testFormatsCount ?? 0 }}</span>
          </div>
        </v-chip>
        <div class="ml-2">
          <v-chip 
            :class="{ 'blue--text': entityType === 'testResult',
                      'text-theme-secondary': entityType !== 'testResult'
            }" 
            width="200px" 
            :color="entityType === 'testResult' ? '#e6ecff' : '#f9fafb'"
            label 
            @click="$emit('update-entity-type', 'testResult')"
          >
            <div
              class="px-2"
              :class="{ 'font-weight-bold': entityType === 'testResult',
                        'font-weight-medium': entityType !== 'testResult'
              }" 
            >
              {{ $t('resultFormats') }} <span class="ml-2">{{ resultFormatsCount ?? 0 }}</span>
            </div>
          </v-chip>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="mt-4">
        <v-skeleton-loader
          class="rounded-sm d-flex gap-2 chip-primary"
          height="32"
          width="270"
          type="button@2"
        />
      </div>
    </template>
  </v-card>
</template>

<script>
import projectStatus from '@/mixins/projectStatus';
import handleLoading from '@/mixins/loader.js'

export default {
  name: 'CustomTemplatesHeader',
  mixins: [projectStatus, handleLoading],
  props:{
    writeTemplate: {
      type: Boolean,
      default: false
    },
    entityType: {
      type: String,
    },
    testFormatsCount: {
      type: Number,
      default: 0
    },
    resultFormatsCount: {
      type: Number,
      default: 0
    },
  },
 
}
</script>
