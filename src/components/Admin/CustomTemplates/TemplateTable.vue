<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="custom-table mt-6 table-fixed data-table-style"
      :server-items-length="totalTemplates"
      :headers="headers"
      :items="items"
      :item-key="itemKey"
      :item-class="getRowClass"
      v-resize-columns="{ type: 'customTemplates' }"
      hide-default-footer
      disable-pagination
      @update:options="updatePaginationOptions"
      @click:row="handleClick"
    >
      <template #[`item.name`]="{ item }">
        <v-tooltip
          bottom
          left
          max-width="485px"
          :disabled="!isTruncated"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <div 
              :ref="'templateName_' + item.uid"
              class="custom-attribute text-truncate font-weight-bold cursor-pointer"
              v-bind="attrs"
              v-on="on"
              @mouseover="checkTruncate(item.uid, 'templateName')"
            >
              {{ item.name }}
              <span
                v-if="item.isDefault"
                class="text-theme-secondary fw-semibold"
              >
                ({{ $t('templatesPage.default') }})</span>
            </div>
          </template>
          <span class="cursor-pointer">{{ item.name }}</span>
        </v-tooltip>
      </template>

      <template #[`item.createdAt`]="{ item }">
        <span>{{ formatCreatedAt(item.createdAt) }}</span>
      </template>

      <template #[`item.createdBy`]="{ item }">
        <span>{{ item.creator.firstName }} {{ item.creator.lastName }}</span>
      </template>

      <template #[`item.isDefault`]="{ item }">
        <div class="d-flex justify-center position-relative">
          <div
            v-if="!item.isDefault && writeTemplate"
          >
            <v-btn
              text
              class="action-btn font-weight-medium primary--text"
              :class="{ 'disabled-action': isProjectArchived }"
              @click.stop="!isProjectArchived && $emit('make-default', item)"
            >
              <span class="override-text">{{ $t('templatesPage.makeDefault') }}</span>
            </v-btn>
          </div>
          
          <div
            v-else-if="item.isDefault && writeTemplate" 
          >
            <v-btn
              text
              class="action-btn font-weight-medium primary--text"
              :class="{ 'disabled-action': isProjectArchived }"
              @click.stop="!isProjectArchived && $emit('reassign', item)"
            >
              <span class="override-text">{{ $t('templatesPage.reassign') }}</span>
            </v-btn>
          </div>
          
          <div
            v-else-if="item.isDefault && !writeTemplate"
          >
            <v-tooltip
              bottom
              left
              max-width="485px"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <span
                  class="gray-text"
                  v-bind="attrs"
                  v-on="on"
                >{{ $t('templatesPage.reassign') }}</span>
              </template>
              <span>{{ $t('templatesPage.reassignPermission') }}</span>
            </v-tooltip>
          </div>
          
          <div
            v-else-if="!item.isDefault && !writeTemplate"
          >
            <v-tooltip
              bottom
              left
              max-width="485px"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <span
                  class="gray-text"
                  v-bind="attrs"
                  v-on="on"
                >{{ $t('templatesPage.makeDefault') }}</span>
              </template>
              <span>{{ $t('templatesPage.makeDefaultPermission') }}</span>
            </v-tooltip>
          </div>
        </div>
      </template>

      <template #[`item.uid`]="{ item }">
        <div class="d-flex justify-end">
          <v-tooltip 
            bottom
            :disabled="writeTemplate"
          >
            <template #activator="{ on, attrs }">
              <div 
                v-bind="attrs" 
                v-on="on"
                @click.stop="writeTemplate && !isProjectArchived"
              >
                <v-btn
                  icon
                  color="primary"
                  :disabled="!writeTemplate"
                  :class="{ 'disabled-action': isProjectArchived }"
                  @click="handleEdit(item)"
                >
                  <EditIcon />
                </v-btn>
              </div>
            </template>
            <span>
              {{ $t('templatesPage.noPermissionToDo', { action: $t('edit').toLowerCase() }) }}
            </span>
          </v-tooltip>
          <v-tooltip 
            bottom
            :disabled="deleteTemplate"
          >
            <template #activator="{ on, attrs }">
              <div 
                v-bind="attrs" 
                v-on="on"
                @click.stop="writeTemplate && !isProjectArchived"
              >
                <v-btn
                  icon
                  color="primary"
                  :disabled="!deleteTemplate"
                  :class="{ 'disabled-action': isProjectArchived }"
                  @click="handleDelete(item)"
                >
                  <DeleteIcon />
                </v-btn>
              </div>
            </template>
            <span>
              {{ $t('templatesPage.noPermissionToDo', { action: $t('delete').toLowerCase() }) }}
            </span>
          </v-tooltip>
        </div>
      </template>
    </v-data-table>
    <template v-else>
      <TemplateTableSkeleton class="mt-6" />
    </template>
  </div>
</template>

<script>
import { formatDate } from '@/utils/util';
import { defineComponent } from 'vue';
import handleLoading from '@/mixins/loader.js';
import TemplateTableSkeleton from '@/components/Skeletons/Admin/Template/TemplateTableSkeleton.vue';

// Change the import statements for the icons
import EditIcon from '@/assets/svg/edit.svg?component';
import DeleteIcon from '@/assets/svg/delete.svg?component';
import { dataTypes } from '@/constants/customField.js';
import projectStatus from '@/mixins/projectStatus';


export default defineComponent({
  components: {
    EditIcon,
    DeleteIcon,
    TemplateTableSkeleton,
  },
  mixins: [projectStatus, handleLoading],
  props: {
    headers: Array,
    itemKey: String,
    items: Array,
    totalTemplates: Number,
    writeTemplate: {
      type: Boolean,
      default: false
    },
    deleteTemplate: {
      type: Boolean,
      default: false
    },
    activeRowUid: {
      type: [String, Number],
      default: null
    },
  },
  data() {
    return {
      isTruncated: false,
    };
  },
  methods: {
    getTypeLabel(type) {
      return dataTypes.find(item => item.type === type)?.name || '';
    },
    formatCreatedAt(createdAt) {
      return formatDate(createdAt, 'MM/dd/yy');
    },
    updatePaginationOptions(options) {
      this.$emit('update-pagination', options);
    },
    handleClick(row) {
      this.$emit('viewDetail', row);
    },
    getRowClass(item) {
      const activeClass = this.activeRowUid === item.uid ? 'active-row' : '';
      return `${activeClass}`.trim();
    },
    handleEdit(item) {
      if (!this.writeTemplate || this.isProjectArchived) return;
      this.$emit('edit', item);
    },
    handleDelete(item) {
      if (!this.deleteTemplate || this.isProjectArchived) return;
      this.$emit('delete', item);
    },
    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
  }
})
</script>

<style lang="scss" scoped>
.disabled-action {
  opacity: 0.6;
  cursor: not-allowed;
}

.position-relative {
  position: relative;
}

::v-deep .action-btn {
  text-transform: none !important;
  letter-spacing: normal !important;
  padding: 0 8px !important;
}

::v-deep .action-btn .v-btn__content {
  text-transform: none !important;
}

.override-text {
  text-transform: none !important;
}

.primary--text {
  color: #0c2ff3  !important;
}

.gray-text {
  color: rgba(0, 0, 0, 0.38);
  cursor: pointer;
}

.action-container {
  position: relative;
  display: inline-block;
}
</style>

