<template>
  <div>
    <div
      class="d-flex flex-flow-wrap align-center mb-6"
      :class="{
        'gap-2': isRulesNotEmpty
      }"
    >
      <div
        v-for="(rule, index) in localRules"
        :key="index"
        class="cursor-pointer rule-chip-container"
        @mouseenter="hoveredRuleIndex = index"
        @mouseleave="hoveredRuleIndex = -1"
      >
        <v-chip
          :ripple="false"
          class="fs-14px radius-6px text-capitalize height-28px user-select-none position-relative chip-with-actions"
          :class="{
            'blue--text': activeRuleIndex === index,
            'text-theme-secondary': activeRuleIndex !== index,
            'fw-semibold': activeRuleIndex === index,
            'font-weight-medium': activeRuleIndex !== index
          }"
          :color="activeRuleIndex === index ? '#e6ecff' : '#f9fafb'"
          label
          @click="editingRuleIndex !== index ? onChangeRule(index) : null"
        >
          <div class="chip-content d-flex align-center justify-space-between w-100">
            <div class="rule-content flex-grow-1">
              <v-text-field
                v-if="editingRuleIndex === index"
                ref="ruleNameInput"
                v-model="editingRuleName"
                dense
                hide-details
                class="inline-edit-field"
                @blur="onSaveRuleName(index)"
                @keyup.enter="onSaveRuleName(index)"
                @keyup.esc="onCancelEdit()"
                @click.stop
              />

              <div
                v-else
                class="rule-name text-truncate mw-108px"
              >
                {{ rule.name }}
              </div>
            </div>

            <div
              class="d-flex align-center"
              :class="{
                'action-icons-container': hoveredRuleIndex === index,
              }"
            >
              <div
                v-if="hoveredRuleIndex === index"
                class="hover-icons d-flex align-center gap-2"
                @click.stop
              >
                <div
                  class="icon-wrapper d-flex align-center cursor-pointer"
                  @click="onStartEditRuleName(index)"
                >
                  <PencilIcon />
                </div>
                <div
                  class="icon-wrapper d-flex align-center cursor-pointer"
                  @click="onRemoveRule(index)"
                >
                  <TrashIcon />
                </div>
              </div>
            </div>
          </div>
        </v-chip>
      </div>
      <v-btn
        depressed
        plain
        :ripple="false"
        class="f-color-white btn-theme text-capitalize rounded-lg btn-plain-theme px-0"
        color="primary"
        height="40px"
        @click="onCreateRule()"
      >
        <div class="d-flex align-center">
          <PlusBlueIcon />
          <span class="ml-2 fw-semibold fs-14px">{{ $t('addRule') }}</span>
        </div>
      </v-btn>
    </div>
    
    <template v-if="isRulesNotEmpty">
      <div class="d-flex gap-1 mb-2">
        <v-label class="fs-14px text-theme-label font-weight-medium">
          {{ $t('templatesPage.appliesToStatus') }}
        </v-label>
        <v-tooltip 
          bottom
          max-width="316"
        >
          <template #activator="{ on, attrs }">
            <div
              v-bind="attrs"
              class="cursor-pointer"
              v-on="on"
            >
              <QuestionIcon />
            </div>
          </template>
          <span>{{ $t('templatesPage.appliesToStatusDescription') }}</span>
        </v-tooltip>
      </div>
      
      <v-autocomplete
        :value="currentRule.statusIds"
        type="text"
        dense
        single-line
        filled
        :placeholder="$t('templatesPage.appliesToStatus')"
        hide-details
        small-chips
        deletable-chips
        background-color="#F9F9FB"
        append-icon="mdi-chevron-down"
        multiple
        :items="statuses"
        item-text="name"
        item-value="id"
        class="rounded-lg mh-32 mb-4"
        clear-icon="body-2"
        @change="onChangestatusIds"
      >
        <template #selection="{ item }">
          <div class="d-flex align-center custom-chip-theme mr-1 mb-1">
            <div
              class="fs-14px fw-semibold text-truncate mr-1"
              :style="`color: ${item.color}`"
            >
              {{ item.name }}
            </div>
            <v-icon
              size="16px"
              @click="onRemovestatusIds(item.id)"
            >
              mdi-close
            </v-icon>
          </div>
        </template>
        
        <template #item="{ item, on }">
          <v-list-item
            v-if="!isStatusUsedInOtherRules(item.id) && !selectedStatusIsFailure"
            :ripple="false"
            v-on="on"
          >
            <v-list-item-action>
              <v-checkbox
                hide-details
                :input-value="statusIdsSelection(item.id)"
                class="field-theme mt-0 pt-0"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
              >
                <template #label>
                  <span
                    class="fs-14px fw-semibold"
                    :style="`color: ${item.color}`"
                  >{{ `${item.name}` }}</span>
                </template>
              </v-checkbox>
            </v-list-item-action>
          </v-list-item>

          <v-tooltip
            v-else-if="selectedStatusIsFailure"
            nudge-top="8"
            bottom
            max-width="316"
          >
            <template #activator="{ on: tooltipOn }">
              <v-list-item
                :ripple="false"
                class="px-0"
                v-on="tooltipOn"
                @click.stop.prevent
              >
                <v-list-item-action>
                  <v-checkbox
                    hide-details
                    class="field-theme mt-0 pt-0 disabled-action"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    disabled
                    readonly
                    @click.stop.prevent
                  >
                    <template #label>
                      <span
                        class="fs-14px fw-semibold"
                        :style="`color: ${item.color}`"
                      >{{ `${item.name}` }}</span>
                    </template>
                  </v-checkbox>
                </v-list-item-action>
              </v-list-item>
            </template>
            <span>{{ $t('templatesPage.failureStatusRestriction') }}</span>
          </v-tooltip>

          <v-tooltip
            v-else
            nudge-top="8"
            bottom
            max-width="316"
          >
            <template #activator="{ on: tooltipOn }">
              <v-list-item
                :ripple="false"
                class="px-0"
                v-on="tooltipOn"
                @click.stop.prevent
              >
                <v-list-item-action>
                  <v-checkbox
                    hide-details
                    class="field-theme mt-0 pt-0 disabled-action"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    disabled
                    readonly
                    @click.stop.prevent
                  >
                    <template #label>
                      <span
                        class="fs-14px fw-semibold"
                        :style="`color: ${item.color}`"
                      >{{ `${item.name}` }}</span>
                    </template>
                  </v-checkbox>
                </v-list-item-action>
              </v-list-item>
            </template>
            <span>{{ $t('templatesPage.statusDisabledDescription') }}</span>
          </v-tooltip>
        </template>
      </v-autocomplete>
      
      <div class="d-flex align-center justify-space-between mb-4">
        <span class="font-weight-medium">{{ $t('templatesPage.linkDefect') }}</span>
        <v-switch
          :input-value="currentRule.createDefects"
          inset
          hide-details
          :ripple="false"
          class="custom-switch mt-0"
          @change="onUpdateRuleProperty('createDefects', $event)"
        />
      </div>
      
      <div class="d-flex align-center justify-space-between mb-4">
        <span class="font-weight-medium">{{ $t('templatesPage.manageTags') }}</span>
        <v-switch
          :input-value="currentRule.manageTags"
          inset
          :ripple="false"
          hide-details
          class="custom-switch mt-0"
          @change="onUpdateRuleProperty('manageTags', $event)"
        />
      </div>
    </template>
  </div>
</template>

<script>
import PlusBlueIcon from '@/assets/svg/plus-blue.svg';
import PencilIcon from '@/assets/svg/pencil-16.svg';
import TrashIcon from '@/assets/svg/trash-outline.svg';
import QuestionIcon from '@/assets/svg/question-mark.svg';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import { uuid } from 'vue-uuid';

export default {
  name: 'TemplateRulesManager',

  components: {
    PlusBlueIcon,
    PencilIcon,
    TrashIcon,
    QuestionIcon,
  },

  mixins: [colorPreferencesMixin],

  props: {
    value: {
      type: Array,
      default: () => []
    },
    customFieldsResult: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      activeRuleIndex: 0,
      editingRuleIndex: -1,
      hoveredRuleIndex: -1,
      editingRuleName: '',
      statuses: []
    };
  },

  computed: {
    localRules: {
      get() {
        return this.value || [];
      },
      set(newValue) {
        this.$emit('input', newValue);
      }
    },

    isRulesNotEmpty() {
      return this.localRules.length > 0;
    },
    
    currentRule() {
      return this.localRules[this.activeRuleIndex] || {};
    },
    
    selectedStatusIsFailure() {
      if (!this.currentRule.statusIds || this.currentRule.statusIds.length === 0) {
        return false;
      }
      return this.currentRule.statusIds.some(status => {
        return this.statuses.find(s => s.id === status)?.isFailure || false;
      });
    },

    statusUsageMap() {
      const usageMap = new Map();
      this.localRules.forEach((rule, ruleIndex) => {
        rule.statusIds?.forEach(statusId => {
          if (!usageMap.has(statusId)) {
            usageMap.set(statusId, []);
          }
          usageMap.get(statusId).push(ruleIndex);
        });
      });
      return usageMap;
    }
  },

  mounted() {
    this.statuses = this.getStatuses('testExecution');
  },

  methods: {
    updateRules() {
      this.$emit('input', [...this.localRules]);
    },

    onCreateRule() {
      const newRule = {
        name: 'Rule ' + (this.localRules.length + 1),
        statusIds: [],
        createDefects: false,
        manageTags: false,
        ruleId : uuid.v4(),
      };

      this.localRules.push(newRule);
      this.updateRules();
    },

    onChangeRule(index) {
      this.activeRuleIndex = index;
    },

    onRemoveRule(index) {
      this.localRules.splice(index, 1);
      
      if (this.activeRuleIndex >= this.localRules.length) {
        this.activeRuleIndex = Math.max(0, this.localRules.length - 1);
      }
      if (this.activeRuleIndex > index) {
        this.activeRuleIndex--;
      }
      
      this.updateRules();
    },

    onStartEditRuleName(index) {
      this.editingRuleIndex = index;
      this.editingRuleName = this.localRules[index].name;

      this.$nextTick(() => {
        if (this.$refs.ruleNameInput && this.$refs.ruleNameInput[0]) {
          this.$refs.ruleNameInput[0].focus();
        }
      });
    },

    onSaveRuleName(index) {
      if (this.editingRuleName && this.editingRuleName.trim()) {
        this.localRules[index].name = this.editingRuleName.trim();
        this.updateRules();
      }
      this.editingRuleIndex = -1;
      this.editingRuleName = '';
    },

    onCancelEdit() {
      this.editingRuleIndex = -1;
      this.editingRuleName = '';
    },

    onChangestatusIds(selectedValues) {
      if (this.localRules[this.activeRuleIndex]) {
        const hasFailureStatus = selectedValues.some(statusId => {
          return this.statuses.find(s => s.id === statusId)?.isFailure || false;
        });
        
        if (hasFailureStatus) {
          const failureStatuses = selectedValues.filter(statusId => {
            return this.statuses.find(s => s.id === statusId)?.isFailure || false;
          });
          this.localRules[this.activeRuleIndex].statusIds = failureStatuses;
        } else {
          this.localRules[this.activeRuleIndex].statusIds = selectedValues || [];
        }
        
        this.updateRules();
      }
    },

    onRemovestatusIds(statusId) {
      if (this.localRules[this.activeRuleIndex] && this.localRules[this.activeRuleIndex].statusIds) {
        const index = this.localRules[this.activeRuleIndex].statusIds.indexOf(statusId);
        if (index > -1) {
          this.localRules[this.activeRuleIndex].statusIds.splice(index, 1);
          this.updateRules();
        }
      }
    },

    onUpdateRuleProperty(property, value) {
      if (this.localRules[this.activeRuleIndex]) {
        this.localRules[this.activeRuleIndex][property] = value;
        this.updateRules();
      }
    },

    isStatusUsedInOtherRules(statusId) {
      const usedInRules = this.statusUsageMap.get(statusId) || [];
      return usedInRules.some(ruleIndex => ruleIndex !== this.activeRuleIndex);
    },

    statusIdsSelection(statusUid) {
      if (!this.currentRule || !Array.isArray(this.currentRule.statusIds)) {
        return false;
      }
      return this.currentRule.statusIds.includes(statusUid);
    }
  }
};
</script>

<style lang="scss" scoped>
.radius-6px.v-chip--label {
  border-radius: 6px !important;
}

.height-28px {
  height: 28px;
}

.inline-edit-field {
  max-width: 100px;
}

.custom-switch {
  ::v-deep .v-input--switch__thumb {
    width: 24px;
    height: 24px;
    top: 0;
    right: 2px;
  }

  ::v-deep .primary--text {
    background-color: #ffffff !important;
    opacity: 1;
  }

  ::v-deep .primary--text.v-input--switch__track {
    background-color: #0000ff !important;
  }
}

.rule-chip-container {
  display: inline-block;

  .chip-with-actions {
    width: 100%;
    min-width: fit-content;

    .chip-content {
      min-height: 28px;
    }

    .action-icons-container {
      width: 44px;
      justify-content: flex-end;
      flex-shrink: 0;
    }
  }
}
</style>