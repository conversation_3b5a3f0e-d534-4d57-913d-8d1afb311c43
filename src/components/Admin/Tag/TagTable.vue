<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="custom-table table-fixed data-table-style mt-6"
      :headers="headers"
      :items="items"
      :item-key="itemKey"
      hide-default-footer
      disable-pagination
    >
      <template #[`item.name`]="{ item }">
        <v-tooltip
          v-if="item.name"
          bottom
          left
          max-width="485px"
          :disabled="!isTruncated"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <div
              :ref="'tagName_' + item.uid"
              class="text-truncate cursor-pointer"
              v-bind="attrs"
              v-on="on"
              @mouseover="checkTruncate(item.uid, 'tagName')"
            >
              {{ item.name }}
            </div>
          </template>
          <span>{{ item.name }}</span>
        </v-tooltip>
        <span v-else>-</span>
      </template>

      <template #[`item.description`]="{ item }">
        <v-tooltip
          v-if="item.description"
          bottom
          left
          max-width="485px"
          :disabled="!isTruncated"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <div
              :ref="'tagDescription_' + item.uid"
              class="text-truncate cursor-pointer"
              v-bind="attrs"
              v-on="on"
              @mouseover="checkTruncate(item.uid, 'tagDescription')"
            >
              {{ item.description }}
            </div>
          </template>
          <span>{{ item.description }}</span>
        </v-tooltip>
        <span v-else>
          <v-icon>
            mdi-minus
          </v-icon>
        </span>
      </template>

      <template #[`item.entityTypes`]="{ item }">
        <v-tooltip
          v-if="item.entityTypes && item.entityTypes.length"
          bottom
          left
          max-width="485px"
          :disabled="!isTruncated"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <div
              :ref="'tagEntityTypes_' + item.uid"
              class="text-truncate cursor-pointer"
              v-bind="attrs"
              v-on="on"
              @mouseover="checkTruncate(item.uid, 'tagEntityTypes')"
            >
              {{ item.entityTypes.join(",") }}
            </div>
          </template>
          <span>{{ item.entityTypes.join(",") }}</span>
        </v-tooltip>
        <span v-else>-</span>
      </template>

      <template #[`item.count`]="{ item }">
        <span>{{ item?.relationCount ?? 0 }}</span>
      </template>

      <template #[`item.uid`]="{ item }">
        <td class="d-flex justify-space-between align-center">
          <v-menu
            v-if="!item.deletedAt"
            bottom
            left
            offset-y
            class="rounded-lg"
          >
            <template #activator="{ on, attrs }">
              <v-btn
                dark
                icon
                v-bind="attrs"
                v-on="on"
              >
                <v-icon color="grey darken-2">
                  mdi-dots-vertical
                </v-icon>
              </v-btn>
            </template>

            <v-list>
              <v-tooltip
                bottom
                :disabled="writeTag"
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-list-item
                      v-if="!item.archivedAt"
                      v-bind="attrs"
                      :disabled="!writeTag"
                      v-on="on"
                      @click="$emit('edit-tag', item)"
                    >
                      <v-list-item-icon class="mr-4">
                        <EditIcon />
                      </v-list-item-icon>
                      <v-list-item-content class="text-left">
                        <v-list-item-title>
                          {{ $t("edit") }}
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </div>
                </template>
                <span>{{
                  $t("tagPage.noPermissionToDo", { action: $t("edit").toLowerCase() })
                }}</span>
              </v-tooltip>

              <v-tooltip
                bottom
                :disabled="writeTag"
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-list-item
                      :disabled="!writeTag"
                      @click="$emit('archive-tag', item)"
                    >
                      <v-list-item-icon class="mr-4">
                        <ArchiveIcon v-if="!item.archivedAt" />
                        <UnarchiveIcon v-if="item.archivedAt" />
                      </v-list-item-icon>
                      <v-list-item-content class="text-left">
                        <v-list-item-title>
                          {{ item.archivedAt ? $t("unarchive") : $t("archive") }}
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </div>
                </template>
                <span>{{
                  $t("tagPage.noPermissionToDo", {
                    action: item.archivedAt
                      ? $t("unarchive").toLowerCase()
                      : $t("archive").toLowerCase(),
                  })
                }}</span>
              </v-tooltip>

              <v-tooltip
                bottom
                :disabled="deleteTag"
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-list-item
                      :disabled="!deleteTag"
                      @click="$emit('delete-tag', item)"
                    >
                      <v-list-item-icon class="mr-4">
                        <DeleteIcon />
                      </v-list-item-icon>
                      <v-list-item-content class="text-left">
                        <v-list-item-title class="red--text">
                          {{ $t("delete") }}
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </div>
                </template>
                <span>{{
                  $t("tagPage.noPermissionToDo", { action: $t("delete").toLowerCase() })
                }}</span>
              </v-tooltip>
            </v-list>
          </v-menu>
        </td>
      </template>
    </v-data-table>
    <template v-else>
      <TagTableSkeleton class="mt-6" />
    </template>
  </div>
</template>

<script>
import { createNamespacedHelpers } from "vuex";
import handleLoading from '@/mixins/loader.js';
import TagTableSkeleton from '@/components/Skeletons/Tag/TagTableSkeleton.vue';
import EditIcon from "@/assets/svg/edit.svg";
import ArchiveIcon from "@/assets/svg/archived.svg";
import UnarchiveIcon from "@/assets/svg/unarchive24px.svg";
import DeleteIcon from "@/assets/svg/delete.svg";

const { mapState } = createNamespacedHelpers("user");

export default {

  components: {
    EditIcon,
    ArchiveIcon,
    UnarchiveIcon,
    DeleteIcon,
    TagTableSkeleton
  },
  mixins: [handleLoading],
  props: {
    headers: Array,
    itemKey: String,
    items: Array,
    writeTag: {
      type: Boolean,
      default: false
    },
    deleteTag: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      tags: this.items,
      isTruncated: false,
    };
  },

  computed: {
    ...mapState(["currentAccount"]),
  },
  methods: {
    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
  },
};
</script>
