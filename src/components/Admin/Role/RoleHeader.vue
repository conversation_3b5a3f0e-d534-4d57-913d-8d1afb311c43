<template>
  <v-card
    class="white py-6 px-6"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div class="d-flex align-center justify-space-between">
      <div>
        <h2 v-if="!skeletonLoaderState">
          {{ $t('roles') }}
        </h2>
        <v-skeleton-loader
          v-else
          height="36"
          width="140"
          type="heading"
        />
      </div>
      <template v-if="!skeletonLoaderState">
        <v-btn
          color="primary"
          height="40"
          :class="{
            'disabled-action': isProjectArchived
          }"
          :depressed="true"
          :disabled="isProjectArchived || !writeRole"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="$emit('create-new-role')"
        >
          {{ $t('createRole') }}
          <v-icon
            class="ml-1"
            size="xs"
          >
            mdi-plus
          </v-icon>
        </v-btn>
      </template>
      <v-skeleton-loader
        v-else
        class="rounded-lg primary"
        height="40"
        width="150"
        type="button"
      />
    </div>
    
    <!-- Organization/Project tabs below the title -->
    <template v-if="!isProjectRole && !skeletonLoaderState">
      <div class="d-flex mt-4">
        <v-tooltip 
          bottom
          :disabled="readRole"
        >
          <template #activator="{ on, attrs }">
            <v-chip 
              v-bind="attrs"
              :class="{ 'blue--text': rolesLevel === 'org', 'disabled-tab': !readRole }"
              width="200px" 
              :color="rolesLevel === 'org' ? '#e6ecff' : '#f9fafb'" 
              label
              :disabled="!readRole" 
              v-on="on"
              @click="readRole ? $emit('update-roles-level', 'org') : null"
            >
              <div
                :class="{ 'font-weight-bold': rolesLevel === 'org' }"
              >
                {{ $t('org') }} <span class="ml-2">{{ orgRolesCount ?? 0 }}</span>
              </div>
            </v-chip>
          </template>
          <span>
            {{ $t('rolePage.noPermissionForTab') }}
          </span>
        </v-tooltip>
        <div class="ml-2">
          <v-chip 
            :class="{ 'blue--text': rolesLevel === 'project' }" 
            width="200px" 
            :color="rolesLevel === 'project' ? '#e6ecff' : '#f9fafb'"
            label 
            @click="$emit('update-roles-level', 'project')"
          >
            <div
              class="px-2"
              :class="{ 'font-weight-bold': rolesLevel === 'project' }" 
            >
              {{ $t('project') }} <span class="ml-2">{{ projectRolesCount ?? 0 }}</span>
            </div>
          </v-chip>
        </div>
      </div>
    </template>
    <template v-else-if="!isProjectRole">
      <div class="mt-4">
        <v-skeleton-loader
          class="rounded-sm d-flex gap-2 chip-primary"
          height="32"
          width="400"
          type="button@4"
        />
      </div>
    </template>
  </v-card>
</template>
  
  <script>
  import handleLoading from '@/mixins/loader.js'
  import projectStatus from '@/mixins/projectStatus';
  import { createNamespacedHelpers } from 'vuex';
  
  const { mapState } = createNamespacedHelpers('user');
  
  export default {
    name: 'RoleHeader',
    mixins: [handleLoading, projectStatus],
    props: {
      writeRole: {
        type: Boolean,
        default: false
      },
      readRole: {
        type: Boolean,
        default: false
      },
      rolesLevel: {
        type: String,
        default: 'org'
      },
      orgRolesCount: {
        type: Number,
        default: 0
      },
      projectRolesCount: {
        type: Number,
        default: 0
      }
    },
    computed: {
      ...mapState(['currentAccount']),
      isProjectRole(){
        return this.currentAccount.type == 'org' && this.$route.params.key;
      }
    }
  }
  </script>

<style scoped>
.disabled-tab {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  pointer-events: auto !important;
}

.disabled-tab:hover {
  background-color: inherit !important;
}
</style>
  