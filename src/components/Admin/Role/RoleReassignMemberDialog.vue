<template>
  <v-dialog
    v-model="showDialog"
    max-width="500"
    persistent
  >
    <v-card class="pa-2">
      <v-card-text class="black--text">
        <div class="d-flex align-center justify-space-between pt-6">
          <h2 class="black--text">
            {{ $t('rolePage.roleReassignMembers', { count: usersCount }) }}
          </h2>
          <v-btn
            icon
            @click="showDialog = false"
          >
            <v-icon
              color="black"
              @click="$emit('close')"
            >
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <p class="text-body-1 text-left font-weight-light mt-3">
          {{ $t('rolePage.reassignMembersWarning') }}
        </p>
      </v-card-text>

      <v-card-actions class="pb-3">
        <v-row>
          <v-col cols="6">
            <v-btn
              color="#F2F4F7"
              width="100%"
              class="text-capitalize rounded-lg"
              height="40"
              elevation="0"
              @click="showDialog = false"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>

          <v-col cols="6">
            <v-menu
              v-model="menuOpen"
              :close-on-content-click="false"
              offset-y
              top
              right
              class="menu-shadow-custom"
            >
              <template #activator="{ on, attrs }">
                <v-btn
                  v-bind="attrs"
                  color="primary"
                  width="100%"
                  elevation="0"
                  height="40"
                  class="white--text text-capitalize rounded-lg"
                  v-on="on"
                >
                  {{ $t('reassign') }}
                  <v-icon color="white">
                    {{ menuOpen ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
                  </v-icon>
                </v-btn>
              </template>

              <v-list class="actions-list font-inter">
                <v-list-item
                  v-for="(role, index) in availableRoles"
                  :key="index"
                  class="actions-item text-start"
                  @click="onSelectRole(role)"
                >
                  <v-list-item-title class="font-inter actions-item-title">
                    {{ role.name }}
                  </v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'RoleReassignMemberDialog',

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    role: {
      type: Object,
      default: () => ({})
    },
    availableRoles: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      menuOpen: false
    }
  },

  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('input', v)
      }
    },
    usersCount() {
      return this.role?.assignees?.length ?? 0;
    },
  },

  methods: {
    onSelectRole(selectedRole) {
      this.menuOpen = false;
      this.$emit('reassign-role', selectedRole);
      this.showDialog = false;
    }
  }
}
</script>

 