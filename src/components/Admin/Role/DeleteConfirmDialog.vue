<template>
  <v-dialog
    v-model="showDialog"
    max-width="500"
    persistent
  >
    <v-card class="pa-2">
      <v-card-text class="black--text">
        <div class="d-flex align-center justify-space-between pt-6">
          <h2
            v-if="role?.name"
            class="black--text"
          >
            {{ $t('rolePage.deleteConfirm', { roleName: `"${role?.name}"` }) }}
          </h2>
          <h2
            v-else
            class="black--text"
          >
            {{ $t('rolePage.deleteConfirm', { roleName: rolesName , s: "s", extraRoles} ) }}
          </h2>
          <v-btn
            icon
            @click="$emit('close')"
          >
            <v-icon
              color="black"
            >
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <p class="text-body-1 text-left font-weight-light mt-3">
          <v-checkbox
            v-model="dontShowAgain"
            class="field-theme"
            :ripple="false"
            off-icon="icon-checkbox-off"
            on-icon="icon-checkbox-on"
            :hide-details="true"
          >
            <template #label>
              <span class="fs-14px text-theme-label">{{ $t('dontShowAgain') }}</span>
            </template>
          </v-checkbox>
        </p>
      </v-card-text>

      <v-card-actions class="pb-3">
        <v-row>
          <v-col cols="6">
            <v-btn
              color="#F2F4F7"
              width="100%"
              class="text-capitalize rounded-lg"
              height="40"
              elevation="0"
              @click="$emit('close')"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>

          <v-col cols="6">
            <v-btn
              color="danger"
              width="100%"
              elevation="0"
              height="40"
              class="white--text text-capitalize rounded-lg"
              @click="role?.name ? $emit('delete-role', dontShowAgain) : $emit('delete-roles', dontShowAgain)"
            >
              {{ $t('delete') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'DeleteConfirmDialog',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    role: {
      type: Object,
      default: () => ({})
    },
    roles: {
      type: Array,
      default: () => ([])
    }
  },
  data(){
    return{
      dontShowAgain: false,
    }
  },

  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('input', v)
      }
    },
    rolesName(){
      return this.roles.slice(0,3)?.map(r => `"${r.name}"`).join(',')
    },
    extraRoles() {
      const extraCount = this.roles.slice(3).length;
      return extraCount
        ? `${this.$t('and')} ${extraCount} ${extraCount === 1 ? this.$t('other') : this.$t('others')}`
        : null;
    }
  },
}
</script>