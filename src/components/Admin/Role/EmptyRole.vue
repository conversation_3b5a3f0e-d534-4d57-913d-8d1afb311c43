<template>
  <div class="empty-role-container">
    <v-row justify="center">
      <v-col
        cols="12"
        sm="10"
        md="8"
        lg="6"
        class="d-flex flex-column align-center text-center"
      >
        <div class="mb-8">
          <EmptyTagImg />
        </div>
  
        <div class="empty-role-content">
          <h2 class="text-h5 font-weight-medium mb-4 text-color-primary">
            {{ emptyRoleMessage }}
          </h2>
        </div>
  
        <v-btn
          color="primary"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="$emit('create-new-role')"
        >
          {{ $t('createRole') }}
          <v-icon
            class="ml-2"
            color="white"
            size="20"
          >
            mdi-plus
          </v-icon>
        </v-btn>
      </v-col>
    </v-row>
  </div>
</template>
  
  <script>
  import EmptyTagImg from '@/assets/svg/empty-roles.svg';
  
  export default {
    name: 'EmptyRole',
  
    components: {
      EmptyTagImg,
    },

    props: {
      roleLevel: {
        type: String,
        default: 'project'
      },
      writeRole: {
        type: Boolean,
        default: false
      },
    },

    emits: ['create-new-role'],

    computed: {
      emptyRoleMessage() {
        if (this.roleLevel === 'org') {
          return 'You don\'t have any Organization level roles yet';
        }
        return this.$t('rolePage.noProjectRolesYet');
      }
    }
  }
  </script>

<style scoped>
.empty-role-container {
  padding: 60px 20px;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-role-illustration {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.empty-role-illustration svg {
  width: 100%;
  height: auto;
  max-width: 280px;
}

.text-color-primary {
  color: #374151 !important;
}

.text-color-secondary {
  color: #6b7280 !important;
}

.create-role-btn {
  min-width: 140px;
  height: 40px;
  border-radius: 8px;
  font-weight: 600;
  box-shadow: none !important;
}
</style>