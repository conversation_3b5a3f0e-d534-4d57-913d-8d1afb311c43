<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="custom-table data-table-style mt-6"
      :headers="headers"
      :items="items"
      :item-key="itemKey"
      :value="selectedItems"
      height="100%"
      show-select
      hide-default-footer
      disable-pagination
      @input="onRowClick"
      @click:row="handleRowClick"
    >
      <template #[`header.data-table-select`]="{ props, on }">
        <div class="d-flex justify-start align-center">
          <v-checkbox
            id="remember-me-checkbox"
            class="field-theme"
            :ripple="false"
            off-icon="icon-checkbox-off"
            on-icon="icon-checkbox-on"
            indeterminate-icon="icon-indeterminate"
            :input-value="props.value"
            :indeterminate="props.indeterminate"
            @change="on.input"
          />
        </div>
      </template>

      <template #[`item.data-table-select`]="{ isSelected, select }">
        <div class="d-flex justify-start align-center">
          <v-checkbox
            id="remember-me-checkbox"
            class="field-theme"
            :ripple="false"
            off-icon="icon-checkbox-off"
            on-icon="icon-checkbox-on"
            :input-value="isSelected"
            @change="select"
            @click.stop
          />
        </div>
      </template>
      <template #[`header.actions`]="{header}">
        <div class="d-none">
          {{ header.text }}
        </div>
      </template>

      <template #[`item.name`]="{ item }">
        <div class="font-weight-bold cursor-pointer">
          {{ item.name }}
        </div>
      </template>
      <template #[`item.project`]="{item}">
        <div class="d-flex align-center">
          <v-avatar
            class="mr-2"
            size="40"
          >
            <v-img
              :src="item.projectAvatar"
              :lazy-src="require('@/assets/png/project.png')"
              width="40px"
              alt="logo"
            />
          </v-avatar>
          <div class="font-weight-bold">
            {{ item.projectName }}
          </div>
        </div>
      </template>
      <template #[`item.users`]="{ item }">
        <span class="gray-ish--text text-subtitle-1">
          <template v-if="item?.assignees?.length > 0">
            <template>
              <div class="d-flex">
                <Avatar
                  v-for="(member, index) in item?.assignees?.slice(0, 5)"
                  :key="index"
                  :avatar="member.avatar"
                  :name="`${member.firstName ?? ''} ${member.lastName ?? ''}`"
                  :size="36"
                  class="mr-n2"
                />

                <Avatar
                  v-if="item.assignees?.length > 5"
                  :name="'+' + (item.assignees.length - 5)"
                  :raw-text="true"
                  :size="36"
                  class="mr-n2 font-weight-bold fs-14px"
                />
              </div>
            </template>
          </template>
        </span>
      </template>
      <template #[`item.uid`]="{ item }">
        <div class="d-flex justify-end">
          <v-menu
            v-if="item.system !== true"
            left
            offset-y
          >
            <template #activator="{ on }">
              <v-btn
                icon
                v-on="on"
                @click.stop
              >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <v-list
              dense
              class="text-left actions-list"
            >
              <!-- Edit Option -->
              <v-tooltip
                bottom
                :disabled="item.writeAccess"
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-list-item
                      :disabled="!item.writeAccess"
                      :class="{
                        pointer: true,
                        'disabled-action': !item.writeAccess,
                      }"
                      @click="handleClickEdit(item, $event)"
                    >
                      <EditIcon />
                      <v-list-item-content class="ml-2">
                        {{ $t('edit') }}
                      </v-list-item-content>
                    </v-list-item>
                  </div>
                </template>
                <span>
                  {{ $t('rolePage.noPermissionToDo', { action: $t('edit').toLowerCase() }) }}
                </span>
              </v-tooltip>

              <!-- Delete Option -->
              <v-tooltip
                bottom
                :disabled="item.deleteAccess"
              >
                <template #activator="{ on, attrs }">
                  <div
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-list-item
                      :disabled="!item.deleteAccess"
                      :class="{
                        pointer: true,
                        'disabled-action': !item.deleteAccess,
                      }"
                      @click="handleClickDelete(item, $event)"
                    >
                      <DeleteIcon />
                      <v-list-item-content class="ml-2 error--text">
                        {{ $t('delete') }}
                      </v-list-item-content>
                    </v-list-item>
                  </div>
                </template>
                <span>
                  {{ $t('rolePage.noPermissionToDo', { action: $t('delete').toLowerCase() }) }}
                </span>
              </v-tooltip>
            </v-list>
          </v-menu>
        </div>
      </template>
    </v-data-table>
    <template v-else>
      <RoleTableSkeleton class="mt-6" />
    </template>
  </div>
</template>
  
<script>
import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import { createNamespacedHelpers  } from 'vuex';
import handleLoading from '@/mixins/loader.js';
import RoleTableSkeleton from '@/components/Skeletons/Role/RoleTableSkeleton.vue';
import Avatar from "@/components/base/Avatar.vue"
const { mapState } = createNamespacedHelpers('user');

export default {
  name: 'RoleTable',
  components: {
    RoleTableSkeleton,
    Avatar,
    EditIcon,
    DeleteIcon,
  },
  mixins: [handleLoading],
  props: {
    headers: Array,
    itemKey: String,
    items: Array,
    clearSelection: Boolean,
    writeRole: {
      type: Boolean,
      default: false
    },
    deleteRole: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      roles: this.items,
      selectedItems: [],
    }
  },

  computed: {
    ...mapState(['currentAccount']),
  },
  watch: {
      clearSelection(newVal) {
      if (newVal) {
          this.selectedItems = [];
          this.$emit('select-item', this.selectedItems);
      }
      },
  },
  methods: {
      onRowClick(item) {
      this.selectedItems = item;
      this.$emit('select-item', this.selectedItems);
      },
      handleRowClick(item) {
      this.$emit('select-row', item);
      },
      handleClickEdit(item, event) {
        if(item.slug == 'owner'){
          this.unauthorizedToast
          return;
        }

        if (event) {
          event.stopPropagation(); 
        }
        
        if (item.writeAccess) {
          this.$emit('edit', item);
        }
      },
      handleClickDelete(item, event) {
        if(item.slug == 'owner'){
          this.unauthorizedToast
          return;
        }
        
        if (event) {
          event.stopPropagation(); 
        }
        
        if (item.deleteAccess) {
          this.$emit('delete', item);
        }
      }
  }
};
</script>

<style scoped>
.pointer {
  cursor: pointer;
}

.disabled-action {
  opacity: 0.5;
  cursor: not-allowed;
}

.actions-list {
  font-family: Inter;
  padding: 0;
}
</style>
