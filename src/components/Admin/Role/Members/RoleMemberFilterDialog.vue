<template>
  <div>
    <v-sheet
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40px"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-4">{{ $t('filters') }} {{ totalSelected > 0 ? `(${ totalSelected })` : '' }} <v-icon>mdi-filter-variant</v-icon></span>
    </v-sheet>

    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card class="dialog-content">
        <v-card-text class="black--text pb-24">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('filters') }}
            </h2>
            <v-btn
              icon
              @click="close()"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>

          <v-expansion-panels
            v-model="tagsPanel"
            flat
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('tags') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <SearchComponent
                  class="mb-3"
                  :search="tagSearch"
                  :placeholder="$t('search')"
                  @update:search="tagSearch = $event"
                />
                <template v-if="tagsFiltered?.length > 0">
                  <div
                    v-for="(item, index) in tagsFiltered"
                    :key="index"
                  >
                    <v-checkbox
                      v-model="tagSelected"
                      :value="item"
                      class="field-theme"
                      :ripple="false"
                      off-icon="icon-checkbox-off"
                      on-icon="icon-checkbox-on"
                      :hide-details="true"
                    >
                      <template #label>
                        <span class="fs-14px text-theme-label">{{ item.name }}</span>
                      </template>
                    </v-checkbox>
                  </div>
                </template>
                <template v-else>
                  <div class="text-center">
                    {{ $t('noTags') }}
                  </div>
                </template>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">  
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="clearAll"
        >
          {{
            $t('clearAll') }}
        </v-btn>
  
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          @click="apply"
        >
          {{
            $t('apply') }}
        </v-btn>
      </div> 
    </v-dialog>
  </div>
</template>

<script>
import SearchComponent from '@/components/Project/SearchComponent.vue';

export default {
    name: 'RoleMemberFilterDialog',
    components: {
        SearchComponent,
    },
    props: {
        tags: {
            type: Array,
            default: () => [],
        },
        initialFilters: {
            type: Object,
            default: () => ({
                tags: []
            }),
        },
    },

    data() {
        return {
            tagsPanel: 0,
            tagSearch: '',
            showDialog: false,
            tagSelected: [],
        };
    },
    computed: {
        tagsFiltered() {
            return this.tags.filter((tag) => tag.name.toLowerCase().includes(this.tagSearch.toLowerCase()));
        },
        totalSelected() {
            return this.tagSelected?.length ?? 0;
        },
    },

    watch: {
        showDialog(value) {
            if (value) {
                this.syncWithActiveFilters();
            }
        },
        initialFilters: {
            handler() {
                this.syncWithActiveFilters();
            },
            deep: true,
        },
    },

    mounted() {
        this.syncWithActiveFilters();
    },

    methods: {
        syncWithActiveFilters() {
            this.tagSelected = [...(this.initialFilters.tags || [])];
        },

        apply() {
            this.$emit('apply', {
                tagSelected: this.tagSelected,
            });
            this.showDialog = false;
        },

        clearAll() {
            this.tagSelected = [];
            this.tagSearch = '';
        },

        close() {
            this.showDialog = false;
        },

    }
};
</script>

<style scoped>
.pointer {
  cursor: pointer;
}

.dialog-content .actions-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  z-index: 1001;
  width: 485px;
  max-width: 485px;
}

.dialog-theme .v-dialog {
  position: fixed !important;
  right: 0 !important;
  top: 0 !important;
  margin: 0 !important;
  max-width: 485px !important;
  width: 485px !important;
  height: 100vh !important;
  border-radius: 0 !important;
}

.dialog-content {
  position: relative;
  height: 100vh;
}

.pb-24 {
  padding-bottom: 96px !important;
}

.field-theme .v-input__control .v-input__slot {
  background-color: #f9f9fb !important;
}

.fs-14px {
  font-size: 14px;
}

.text-theme-label {
  color: #374151;
}
</style>