<template>
  <div class="empty-role-members">
    <div class="svg-container">
      <EmptyRoleMembersSvg />
    </div>
    <div class="message-container">
      <h3 class="message-title">
        {{ $t('rolePage.noRoleMembers') }}
      </h3>
    </div>
  </div>
</template>

<script>
import EmptyRoleMembersSvg from "@/assets/svg/empty-members.svg"

export default {
  name: 'EmptyRoleMembers',

  components: {
    EmptyRoleMembersSvg,
  },
}
</script>

<style scoped>
.empty-role-members {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  min-height: 400px;
}

.svg-container {
  max-width: 250px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
}

.svg-container svg {
  width: 100%;
  height: auto;
  max-width: 250px;
}

.message-container {
  margin-bottom: 30px;
}

.message-title {
  font-size: 18px;
  font-weight: 500;
  color: #424242;
  margin: 0;
  line-height: 1.4;
}

</style>
