<template>
  <div
    v-if="hasActiveFilters"
    class="role-filter-chips py-2"
  >
    <div class="d-flex align-center flex-wrap">
      <div class="mr-3 mb-2">
        <span class="font-weight-medium text-theme-label">{{ $t('results') }} ({{ resultsCount }})</span>
      </div>

      <template v-if="showProjectFilter && filters.projects && filters.projects.length > 0">
        <v-chip
          v-for="project in filters.projects"
          :key="`project-${project}`"
          class="mr-2 mb-2 chip-theme"
          close
          @click:close="removeFilter('projects', project)"
        >
          <span class="text-caption">{{ $t('project') }}: {{ project }}</span>
        </v-chip>
      </template>

      <template v-if="filters.tags && filters.tags.length > 0">
        <v-chip
          v-for="tag in filters.tags"
          :key="`tag-${tag.uid || tag.name}`"
          class="mr-2 mb-2 chip-theme"
          close
          @click:close="removeFilter('tags', tag)"
        >
          <span class="text-caption">{{ $t('tags') }}: {{ tag.name || tag }}</span>
        </v-chip>
      </template>

      <v-btn
        small
        text
        color="primary"
        class="ml-2 mb-2 text-capitalize"
        @click="clearAllFilters"
      >
        {{ $t('clearAll') }}
      </v-btn>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RoleMemberFilterChips',
  props: {
    filters: {
      type: Object,
      required: true,
    },
    resultsCount: {
      type: Number,
      default: 0,
    },
    showProjectFilter: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    hasActiveFilters() {
      const hasTags = this.filters.tags && this.filters.tags.length > 0;
      return hasTags;
    },
  },
  methods: {
    removeFilter(filterType, value) {
      const updatedFilters = JSON.parse(JSON.stringify(this.filters));

      if (Array.isArray(updatedFilters[filterType])) {
        if (filterType === 'tags' && typeof value === 'object') {
          // For tag objects, filter by uid or name
          updatedFilters[filterType] = updatedFilters[filterType].filter((item) => {
            if (typeof item === 'object' && typeof value === 'object') {
              return item.uid !== value.uid && item.name !== value.name;
            }
            return item !== value;
          });
        } else {
          updatedFilters[filterType] = updatedFilters[filterType].filter((item) => item !== value);
        }
      }

      this.$emit('update-filters', updatedFilters);
    },

    clearAllFilters() {
      this.$emit('clear-filters');
    },
  },
};
</script>

<style scoped>
.role-filter-chips {
  background-color: #f9f9fb;
  padding: 8px 12px;
  border-radius: 8px;
}

.chip-theme {
  background-color: #f2f4f7 !important;
  color: #344054 !important;
  font-size: 12px;
  height: 28px !important;
  border-radius: 6px !important;
  padding-left: 12px !important;
  padding-right: 12px !important;
  border: none !important;
  box-shadow: none !important;
}

.chip-theme :deep(.v-chip__close) {
  margin-left: 6px;
}
</style> 