<template>
  <v-data-table
    :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
    class="custom-table data-table-style mt-6"
    :headers="headers"
    :items="items"
    :item-key="itemKey"
    :value="selectedItems"
    height="100%"
    show-select
    @input="onRowClick"
    @click:row="handleRowClick"
  >
    <template #[`header.data-table-select`]="{ props, on }">
      <div class="d-flex justify-start align-center">
        <v-checkbox
          id="remember-me-checkbox"
          class="field-theme"
          :ripple="false"
          off-icon="icon-checkbox-off"
          on-icon="icon-checkbox-on"
          indeterminate-icon="icon-indeterminate"
          :input-value="props.value"
          :indeterminate="props.indeterminate"
          @change="on.input"
        />
      </div>
    </template>

    <template #[`item.data-table-select`]="{ isSelected, select }">
      <div class="d-flex justify-start align-center">
        <v-checkbox
          id="remember-me-checkbox"
          class="field-theme"
          :ripple="false"
          off-icon="icon-checkbox-off"
          on-icon="icon-checkbox-on"
          :input-value="isSelected"
          @change="select"
          @click.stop
        />
      </div>
    </template>

    <template #[`item.user`]="{ item }">
      <td class="d-flex align-center">
        <Avatar
          v-if="item.avatar"
          :avatar="item.avatar"
          :name="`${item.firstName ?? ''} ${item.lastName ?? ''}`"
          :size="40"
          class="mr-n2 user-role-avatar"
        />
        <div class="text-subtitle-2 font-weight-bold ml-4">
          {{ item.firstName }} {{ item.lastName }}
        </div>
      </td>
    </template>

    <template #[`item.role`]>
      <td class="text-start">
        <v-menu
          offset-y
          :position-y="10"
        >
          <template #activator="{ on }">
            <div
              role="button"
              v-on="on"
            >
              {{ role.name }}
            </div>
          </template>
        </v-menu>
      </td>
    </template>

    <template #[`item.project`]="{ item }">
      <td class="text-start">
        <span v-if="role.projectName">
          {{ role.projectName }}
        </span>
        <span v-else-if="item.projects && item.projects.length > 0">
          {{ item.projects[0].name }}
          <span
            v-if="item.projects.length > 1"
            class="text-caption grey--text"
          >
            +{{ item.projects.length - 1 }}
          </span>
        </span>
        <span
          v-else-if="item.projectUid"
          class="text-caption grey--text"
        >
          {{ item.projectUid }}
        </span>
        <span
          v-else
          class="text-caption grey--text"
        >
          -
        </span>
      </td>
    </template>

    <template #[`item.tags`]="{ item }">
      <td class="text-start">
        <div v-if="item.tags && item.tags.length > 0">
          <v-menu
            offset-y
            :position-y="10"
          >
            <template #activator="{ on }">
              <div
                role="button"
                class="d-flex align-center"
                v-on="on"
              >
                <span>{{ (item.tags[0] && item.tags[0].name) ? item.tags[0].name : item.tags[0] }}</span>
                <span
                  v-if="item.tags.length > 1"
                  class="text-caption ml-1"
                >
                  +{{ item.tags.length - 1 }}
                </span>
                <v-icon
                  class="ml-1"
                  small
                >
                  mdi-chevron-down
                </v-icon>
              </div>
            </template>
            <v-list class="pt-4">
              <v-list-item
                v-for="(tag, index) in item.tags"
                :key="index"
                class="m-0"
              >
                <div class="d-flex align-start">
                  <div class="d-flex align-center mt-2">
                    <span class="mr-2">{{ tag.name }}</span>
                  </div>
                </div>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
        <span
          v-else
          class="text-caption grey--text"
        >
          -
        </span>
      </td>
    </template>

    <template #[`item.email`]="{ item }">
      <span class="gray-ish--text text-subtitle-1">
        {{ item.email }}
      </span>
    </template>
  </v-data-table>
</template>
  
  <script>
  import { createNamespacedHelpers  } from 'vuex';
  import Avatar from "@/components/base/Avatar.vue"
  const { mapState } = createNamespacedHelpers('user');
  
  export default {
    components: {
    Avatar
  },
    props: {
      headers: Array,
      itemKey: String,
      items: Array,
      clearSelection: Boolean,
      role: Object,
    },
  
    data() {
      return {
        roleMembers: this.items,
        selectedItems: [],
      }
    },
  
    computed: {
      ...mapState(['currentAccount']),
  
      isAbleToManage() {
        return this.currentAccount.roleName === "owner" || this.currentAccount.roleName === "admin"
      }
    },
    watch: {
        clearSelection(newVal) {
        if (newVal) {
            this.selectedItems = [];
            this.$emit('select-item', this.selectedItems);
        }
        },
    },
    methods: {
        onRowClick(item) {
            this.selectedItems = item;
            this.$emit('select-item', this.selectedItems);
        },
        handleRowClick(item) {
            this.$emit('select-row', item);
        },
    }
  };
  </script>
  
<style scoped>
.user-role-avatar{
  background-color: #ebecf0;
  color: #44546f;
  font-weight: 500;
}
</style>