<template>
  <v-dialog
    v-model="showDialog"
    max-width="500"
    persistent
  >
    <v-card class="pa-2 pb-0">
      <v-card-text class="black--text">
        <div class="d-flex align-center justify-space-between pt-6">
          <h2 class="black--text">
            {{ isNewOwnerAssignment ? $t('confirmOwnerAssignment') : $t('changeYourRole') }}
          </h2>
          <v-btn
            icon
            @click="closeDialog"
          >
            <v-icon
              color="black"
            >
              mdi-close
            </v-icon>
          </v-btn>
        </div>
        <p class="text-body-1 text-left font-weight-light mt-3">
          {{ isNewOwnerAssignment ? $t('newOwnerDescription1') : $t('changeOwnerDescription1') }}
        </p>
        <p class="text-body-1 text-left font-weight-light mt-3">
          {{ isNewOwnerAssignment ? $t('newOwnerDescription2') : $t('changeOwnerDescription2') }}
        </p>
        <p class="text-body-1 text-left font-weight-light mt-3">
          {{ isNewOwnerAssignment ? $t('newOwnerDescription3') : $t('changeOwnerDescription3') }}
        </p>
        <v-form
          ref="form"
          v-model="validForm"
          lazy-validation
        >
          <p class="font-weight-medium body-2 text-left mb-1">
            {{ $t('password') }}
          </p>
          <v-text-field
            v-model="password"
            dense
            filled
            :type="visiblePassword ? 'text' : 'password'"
            :placeholder="$t('password')"
            :rules="requiredRules"
            :append-icon="visiblePassword ? 'mdi-eye-off-outline' : 'mdi-eye-outline'"
            aria-autocomplete="both"
            aria-haspopup="false"
            autocomplete="off"
            @click:append="visiblePassword = !visiblePassword"
          />
        </v-form>
      </v-card-text>

      <v-card-actions class="pb-3">
        <v-row>
          <v-col cols="6">
            <v-btn
              color="#F2F4F7"
              width="100%"
              class="text-capitalize rounded-lg"
              height="40"
              elevation="0"
              @click="closeDialog"
            >
              {{ $t('cancel') }}
            </v-btn>
          </v-col>

          <v-col cols="6">
            <v-btn
              color="primary"
              :class="{'btn-theme': true, 'disabled-action': password?.length < 3 }"
              width="100%"
              elevation="0"
              height="40"
              class="white--text text-capitalize rounded-lg"
              @click="$emit('updateRole', password)"
            >
              {{ $t('confirm') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'DeleteConfirmDialog',
  props: {
    value: {
      type: Boolean,
      default: false,
    },

    role: {
      type: Object,
      default: () => ({})
    },
    isNewOwnerAssignment:{
      type: Boolean,
      default: false
    }
  },
  data(){
    return{
      dontShowAgain: false,
      visiblePassword: false,
      validForm: false,
      password: '',
      requiredRules: [
        value => !!value || this.$t('error.requiredField'),
      ],
    }
  },
  computed: {
  
    showDialog: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('input', v)
      }
    },
  },
  methods: {
    closeDialog(){
      this.$emit('close')
    },
  }
}
</script>