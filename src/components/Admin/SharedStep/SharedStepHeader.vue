<template>
  <v-card
    class="white py-6 px-6"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div class="d-flex align-center justify-space-between">
      <h2 v-if="!skeletonLoaderState">
        {{ $t('sharedStepPage.title') }}
      </h2>
      <v-skeleton-loader
        v-else
        height="36"
        width="140"
        type="heading"
      />
      <template v-if="skeletonLoaderState">
        <v-skeleton-loader
          class="rounded-lg primary"
          height="40"
          width="150"
          type="button"
        />
      </template>
      <v-tooltip 
        v-else
        bottom
        :disabled="writeStep" 
      >
        <template #activator="{ on, attrs }">
          <div 
            v-bind="attrs" 
            v-on="on"
          >
            <v-btn
              :disabled="!writeStep"
              color="primary"
              depressed
              :class="{ 'text-capitalize btn-theme rounded-lg': true, 'disabled-action': isProjectArchived, 'd-none': isShareStepArchived }"
              height="40px"
              @click="!isProjectArchived && $emit('create-shared-step')"
            >
              {{ $t('sharedStepPage.createSharedStep') }} <v-icon
                class="ml-1" 
                size="xs"
              >
                mdi-plus
              </v-icon>
            </v-btn>
          </div>
        </template>
        <span>
          {{
            $t("sharedStepPage.noPermissionToDo", { action: $t("create").toLowerCase() })
          }}
        </span>
      </v-tooltip>
    </div>
    <div
      v-if="!skeletonLoaderState"
      class="mt-4 d-flex"
    >
      <v-chip
        label
        width="200px"
        :class="{ 'blue--text': status === 'active' }"
        :color="status === 'active' ? 'blue-light' : 'gray-light'"
        @click="updateStatus('active')"
      >
        <div class="font-weight-bold px-2">
          {{ $t('active') }}
          <span class="ml-2">{{ activeCount }}</span>
        </div>
      </v-chip>

      <v-chip
        class="ml-2"
        label
        width="200px"
        :class="{ 'blue--text': status === 'archived' }"
        :color="status === 'archived' ? 'blue-light' : 'gray-light'"
        @click="updateStatus('archived')"
      >
        <div class="font-weight-bold px-2">
          {{ $t('archived') }}
          <span class="ml-2">{{ archivedCount }}</span>
        </div>
      </v-chip>
    </div>
    <div
      v-else
      class="py-4 d-flex"
    >
      <v-skeleton-loader
        class="rounded-sm d-flex gap-2 chip-primary"
        height="32"
        width="200"
        type="button@2"
      />
    </div>
  </v-card>
</template>

<script>
import handleLoading from '@/mixins/loader.js'

export default {
  name: 'SharedStepHeader',
  mixins: [handleLoading],

  props: {
    status: String,
    activeCount: Number,
    archivedCount: Number,
    writeStep: {
      type: Boolean,
      default: false
    },
    isProjectArchived: {
      type: Boolean,
    },
  },
  computed: {
    isShareStepArchived() {
      return this.status == 'archived';
    },
  },
  methods: {
    updateStatus(value) {
      this.$emit('update-status', value);
      this.$emit('close');
    },
  },
};
</script>
