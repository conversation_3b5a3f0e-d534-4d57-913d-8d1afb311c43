<template>
  <div>
    <v-sheet
      v-if="!skeletonLoaderState"
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-4">{{ $t('filters') }} <v-icon>mdi-filter-variant</v-icon></span>
    </v-sheet>
    <v-skeleton-loader
      v-else
      class="rounded-lg"
      height="40"
      width="95"
      type="button"
    />
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('filters') }}
            </h2>
            <v-btn
              icon
              @click="close()"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>

          <div class="mt-6 text-start">
            <div class="text-left">
              <v-label class="fs-14px text-theme-label font-weight-medium">
                {{ $t('sharedStepPage.numberOfSteps') }}
              </v-label>
            </div>
            <v-range-slider
              v-model="stepsNum"
              class="slider-theme mt-4"
              color="blue"
              track-color="#F2F4F7"
              thumb-color="#FFFFFF"
              :min="stepRangeMin"
              :max="stepRangeMax"
              hide-details
            />
            <div class="d-flex align-center">
              <v-text-field
                v-model.number="stepsNum[0]"
                background-color="#F9F9FB"
                class="field-theme mr-0"
                height="38"
                type="number"
                :min="stepRangeMin"
                :max="stepRangeMax"
                hide-details
                dense
              />
              <v-icon class="mx-4">
                mdi-minus
              </v-icon>
              <v-text-field
                v-model.number="stepsNum[1]"
                background-color="#F9F9FB"
                class="field-theme mr-0"
                height="38"
                type="number"
                :min="stepRangeMin"
                :max="stepRangeMax"
                hide-details
                dense
              />
            </div>
          </div>

          <div class="mt-6 text-start">
            <div class="text-left">
              <v-label class="fs-14px text-theme-label font-weight-medium">
                {{ $t('sharedStepPage.referencedBy') }}
              </v-label>
            </div>
            <v-range-slider
              v-model="referencesNum"
              class="slider-theme mt-4"
              color="blue"
              track-color="#F2F4F7"
              thumb-color="#FFFFFF"
              :min="referenceRangeMin"
              :max="referenceRangeMax"
              hide-details
            />
            <div class="d-flex align-center">
              <v-text-field
                v-model.number="referencesNum[0]"
                background-color="#F9F9FB"
                class="field-theme mr-0"
                height="38"
                type="number"
                :min="referenceRangeMin"
                :max="referenceRangeMax"
                hide-details
                dense
              />
              <v-icon class="mx-4">
                mdi-minus
              </v-icon>
              <v-text-field
                v-model.number="referencesNum[1]"
                background-color="#F9F9FB"
                class="field-theme mr-0"
                height="38"
                type="number"
                :min="referenceRangeMin"
                :max="referenceRangeMax"
                hide-details
                dense
              />
            </div>
          </div>
        </v-card-text>
      </v-card>

      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          full-width
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="clearAll"
        >
          {{
            $t('clearAll')
          }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          full-width
          elevation="0"
          @click="apply"
        >
          {{
            $t('apply')
          }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import handleLoading from '@/mixins/loader.js'
export default {
  name: 'SharedStepFilterDialog',
  mixins: [handleLoading],
  data() {
    return {
      tagsPanel: 0,
      showDialog: false,
      // Dynamic ranges will be calculated
      stepRangeMin: 1,
      stepRangeMax: 20,
      referenceRangeMin: 0,
      referenceRangeMax: 100,
      // Current filter values
      stepsNum: [1, 20],
      referencesNum: [0, 100],
    };
  },

  watch: {
    showDialog(value) {
      if (!value) {
        return;
      }
    },
    
    // Validate range inputs
    stepsNum: {
      handler(newVal) {
        if (newVal[0] > newVal[1]) {
          this.stepsNum = [newVal[1], newVal[0]];
        }
        if (newVal[0] < this.stepRangeMin) {
          this.stepsNum[0] = this.stepRangeMin;
        }
        if (newVal[1] > this.stepRangeMax) {
          this.stepsNum[1] = this.stepRangeMax;
        }
      },
      deep: true
    },
    
    referencesNum: {
      handler(newVal) {
        if (newVal[0] > newVal[1]) {
          this.referencesNum = [newVal[1], newVal[0]];
        }
        if (newVal[0] < this.referenceRangeMin) {
          this.referencesNum[0] = this.referenceRangeMin;
        }
        if (newVal[1] > this.referenceRangeMax) {
          this.referencesNum[1] = this.referenceRangeMax;
        }
      },
      deep: true
    }
  },

  mounted() {
    this.clearAll();
  },

  methods: {
  

    apply() {
      const isStepsFiltered = this.stepsNum[0] !== this.stepRangeMin || this.stepsNum[1] !== this.stepRangeMax;
      const isReferencesFiltered = this.referencesNum[0] !== this.referenceRangeMin || this.referencesNum[1] !== this.referenceRangeMax;
      
      if (isStepsFiltered || isReferencesFiltered) {
        this.$emit('update-filter', false, 
          isStepsFiltered ? this.stepsNum : [], 
          isReferencesFiltered ? this.referencesNum : []
        );
      } else {
        this.$emit('update-filter', true, [], []);
      }
      this.showDialog = false;
    },

    clearAll(){
      this.stepsNum = [this.stepRangeMin, this.stepRangeMax];
      this.referencesNum = [this.referenceRangeMin, this.referenceRangeMax];
      this.$emit('update-filter', true, [], []);
    },

    close() {
      this.showDialog = false;
    },
  },
};
</script>
