<template>
  <v-dialog
    v-model="showDialog"
    class="test-cases-filter-drawer dialog-theme"
    transition="slide-x-transition"
    attach
    fullscreen
    width="485px"
  >
    <v-card>
      <v-card-text class="black--text">
        <div class="d-flex align-center justify-space-between pt-6">
          <h2 class="black--text">
            {{ isEditMode ? $t('customFieldPage.editCustomField') : $t('customFieldPage.createCustomField') }}
          </h2>
          <v-btn
            icon
            @click="showDialog = false"
          >
            <v-icon color="black">
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <v-form
          ref="form"
          v-model="validForm"
          lazy-validation
          class="mt-10"
        >
          <div>
            <div class="d-flex justify-start align-center flex-column">
              <v-checkbox
                v-model="customField.entityTypes"
                multiple
                class="field-theme checkbox-align-start mt-0"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                :value="entityTypeNames.testCase"
                :rules="entityTypeRules"
              >
                <template #label>
                  <div class="d-flex flex-column">
                    <span class="fs-14px text-theme-label font-weight-medium">{{ entityTypeLabels[entityTypeNames.testCase] }}</span>
                    <span class="fs-14px text-theme-secondary">{{ $t('testFormatDescription') }}</span>
                  </div>
                </template>
              </v-checkbox>
              <v-checkbox
                v-model="customField.entityTypes"
                multiple
                class="field-theme checkbox-align-start mt-0"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                :value="entityTypeNames.testResult"
                :rules="entityTypeRules"
              >
                <template #label>
                  <div class="d-flex flex-column">
                    <span class="fs-14px text-theme-label">{{ entityTypeLabels[entityTypeNames.testResult] }}</span>
                    <span class="fs-14px text-theme-secondary">{{ $t('resultFormatDescription') }}</span>
                  </div>
                </template>
              </v-checkbox>
            </div>
            <div>
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('name') }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
              </div>
              <v-text-field
                v-model="customField.name"
                type="text"
                class="rounded-lg field-theme"
                background-color="#F9F9FB"
                dense
                height="38px"
                :placeholder="$t('name')"
                :rules="requiredRules"
              />
            </div>
            <div>
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('descriptionLabel') }}
                </v-label>
              </div>
              <v-textarea
                v-model="customField.description"
                auto-grow
                class="field-theme rounded-lg"
                background-color="#F9F9FB"
                :placeholder="$t('descriptionLabel')"
                hide-details
              />
            </div>
            <div>
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('customFieldPage.dataType') }} <strong class="red--text text--lighten-1">*</strong>
                </v-label>
              </div>
              <v-select
                v-model="customField.type"
                :items="dataTypes"
                item-text="name"
                item-value="type"
                class="rounded-lg pt-0 field-theme custom-prepend mh-38px"
                background-color="#F9F9FB"
                :placeholder="$t('customFieldPage.chooseDataType')"
                :rules="requiredRules"
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true }"
              />
            </div>
          </div>

          <template v-if="isAbleToAddItem">
            <OptionItems
              :type="customField.type"
              :items="customField.options"
              @remove-item="removeOptionItem"
            />

            <v-col
              v-if="isAbleToAddItem"
              cols="12"
              class="text-right pt-0"
            >
              <v-btn
                color="primary"
                class="white--text text-capitalize btn-theme"
                depressed
                height="38px"
                elevation="0"
                @click="onAddItem()"
              >
                {{ $t('add') }}
                <v-icon>
                  mdi-plus
                </v-icon>
              </v-btn>
            </v-col>
          </template>
        </v-form>
      </v-card-text>
    </v-card>
    <div class="actions-container d-flex justify-space-between">
      <v-btn
        width="204.5px"
        color="#F2F4F7"
        full-width
        height="40"
        :depressed="true"
        class="text-capitalize btn-theme"
        elevation="0"
        @click="onCancel()"
      >
        {{ $t('cancel') }}
      </v-btn>
      <v-btn
        width="204.5px"
        class="btn-theme white--text text-capitalize"
        height="40"
        color="primary"
        :depressed="true"
        full-width
        elevation="0"
        :disabled="!validForm"
        @click="onCreate()"
      >
        {{ isEditMode ? $t('save') : $t('create') }}
      </v-btn>
    </div>
  </v-dialog>
</template>

<script>
import { dataTypes } from '@/constants/customField.js';
import OptionItems from '@/components/Admin/CustomField/OptionItems.vue';
import { entityTypeLabels, entityTypeNames } from '@/constants/templates';

export default {
  name: 'CreateUpdateCustomField',

  components: {
    OptionItems,
  },

  props: {
    value: {
      type: Boolean,
      default: false,
    },

    data: {
      type: Object,
      default: () => ({})
    },
  },

  data () {
    return {
      requiredRules: [
        value => !!value || this.$t('error.requiredField'),
      ],
      entityTypeRules: [
        () => (this.customField.entityTypes && this.customField.entityTypes.length > 0) || this.$t('atLeastOneOptionSelected'),
      ],
      validForm: false,
      dataTypes: dataTypes,
      customField: {
        uid: '',
        name: '',
        type: '',
        entityTypes: [],
        source: '',
        options: [],
        description: '',
      },
      entityTypeLabels: entityTypeLabels,
      entityTypeNames: entityTypeNames,
    }
  },

  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(v) {
        this.$emit('input', v)
      }
    },

    isEditMode() {
      return !!this.customField.uid
    },

    isAbleToAddItem() {
      if (!this.customField.type) {
        return false
      }

      const dataType = dataTypes.find(item => item.type === this.customField.type)

      return dataType?.isOptionsRequired
    }
  },

  watch: {
    showDialog(value) {
      if (!value) {
        return
      }

      this.customField = {
        uid: this.data.uid || '',
        name: this.data.name || '',
        type: this.data.type || '',
        source: this.data.source || '',
        options: this.data.options || [],
        entityTypes: this.data.entityTypes || [],
        description: this.data.description || '',
      }

      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetValidation()
        }
      })

    },

    'customField.type': {
      handler: function () {
        if (this.isEditMode && this.customField.type === this.data.type) {
          this.customField.options = this.data.options
        } else {
          this.customField.options = []
        }
      }
    }
  },

  methods: {
    onAddItem() {
      this.customField.options.push('')
    },

    removeOptionItem(index) {
      this.customField.options.splice(index, 1)
    },

    onCreate() {
      const isValidForm = this.$refs.form.validate()

      if (!isValidForm) {
        return
      }

      this.$emit(this.isEditMode ? 'update-custom-field' : 'create-custom-field', this.customField)
    },

    onCancel() {
      this.$emit('close-dialog')
    }
  }
}
</script>
