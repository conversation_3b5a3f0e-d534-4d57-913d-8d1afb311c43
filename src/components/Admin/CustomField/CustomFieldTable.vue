<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      class="custom-table table-fixed mt-6 data-table-style"
      :headers="headers"
      :items="items"
      :item-key="itemKey"
      v-resize-columns="{ type: 'customFields' }"
      hide-default-footer
      disable-pagination
    >
      <template #[`item.name`]="{ item }">
        <v-tooltip
          bottom
          left
          max-width="485px"
          :disabled="!isTruncated"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <div 
              :ref="'customFieldName_' + item.uid"
              class="custom-attribute text-truncate font-weight-bold cursor-pointer"
              v-bind="attrs"
              v-on="on"
              @mouseover="checkTruncate(item.uid, 'customFieldName')"
            >
              {{ item.name }}
            </div>
          </template>
          <span>{{ item.name }}</span>
        </v-tooltip>
      </template>

      <template #[`item.type`]="{ item }">
        <span>{{ getTypeLabel(item.type) }}</span>
      </template>

      <template #[`item.entityType`]="{ item }">
        <span v-if="Array.isArray(item.entityTypes)">{{ item.entityTypes.map(entityType => entityTypeLabels[entityType]).join(', ') }}</span>
        <span v-else>
          <v-icon>
            mdi-minus
          </v-icon>
        </span>
      </template>

      <template #[`item.createdAt`]="{ item }">
        <span>{{ formatCreatedAt(item.createdAt) }}</span>
      </template>

      <template #[`item.uid`]="{ item }">
        <div class="d-flex justify-end">
          <v-tooltip 
            bottom 
            :disabled="writeCustomField"
          >
            <template #activator="{ on, attrs }">
              <div 
                v-bind="attrs" 
                v-on="on"
              >
                <v-btn
                  icon
                  color="primary"
                  :disabled="!writeCustomField"
                  :class="{ 'disabled-action': isProjectArchived }"
                  @click="!isProjectArchived && $emit('edit', item)"
                >
                  <EditIcon />
                </v-btn>
              </div>
            </template>
            <span>
              {{ $t('customFieldPage.noPermissionToDo', { action: $t('edit').toLowerCase() }) }}
            </span>
          </v-tooltip>
          <v-tooltip 
            bottom 
            :disabled="deleteCustomField"
          >
            <template #activator="{ on, attrs }">
              <div 
                v-bind="attrs" 
                v-on="on"
              >
                <v-btn
                  icon
                  color="primary"
                  :disabled="!deleteCustomField"
                  :class="{ 'disabled-action': isProjectArchived }"
                  @click="!isProjectArchived && $emit('delete', item)"
                >
                  <DeleteIcon />
                </v-btn>
              </div>
            </template>
            <span>
              {{ $t('customFieldPage.noPermissionToDo', { action: $t('delete').toLowerCase() }) }}
            </span>
          </v-tooltip>
        </div>
      </template>
    </v-data-table>
    <template v-else>
      <CustomFieldTableSkeleton class="mt-6" />
    </template>
  </div>
</template>

<script>
import EditIcon from '@/assets/svg/edit.svg';
import DeleteIcon from '@/assets/svg/delete.svg';
import { dataTypes } from '@/constants/customField.js';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import projectStatus from '@/mixins/projectStatus';
import handleLoading from '@/mixins/loader.js';
import CustomFieldTableSkeleton from '@/components/Skeletons/Admin/CustomField/CustomFieldTableSkeleton.vue';
import { entityTypeLabels } from '@/constants/templates';

export default {
  components: {
    EditIcon,
    DeleteIcon,
    CustomFieldTableSkeleton,
  },
  mixins: [projectStatus, handleLoading],
  props: {
    headers: Array,
    itemKey: String,
    items: Array,
    writeCustomField: {
      type: Boolean,
      default: false
    },
    deleteCustomField: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },

  data() {
    return {
      isTruncated: false,
      entityTypeLabels: entityTypeLabels,
    };
  },

  methods: {
    getTypeLabel(type) {
      return dataTypes.find(item => item.type === type)?.name || ''
    },

    formatCreatedAt(createdAt) {
      return this.formatDate(createdAt)
    },

    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
  }
}
</script>
