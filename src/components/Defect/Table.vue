<template>
  <div>
    <v-data-table
      v-if="!skeletonLoaderState"
      v-resize-columns="{ type: 'defect' }"
      :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
      :class="`custom-table ${tableClass} mt-6`"
      :headers="headers"
      :items="items"
      :item-key="itemKey"
      :fixed-header="true"
      height="auto"
      hide-default-footer
      disable-pagination
      @click:row="handleRowClick"
    >
      <template #[`item.id`]="{ item }">
        <span v-if="integrationType === 'Github'">#{{ item.id || item.number }}</span>
        <span v-else>{{ item.id || item.number }}</span>
      </template>

      <template #[`item.name`]="{ item }">
        <v-tooltip
          bottom
          left
          max-width="485px"
          :disabled="!isTruncated"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <div 
              :ref="'defectName_' + item.uid"
              class="custom-attribute text-truncate cursor-pointer"
              :class="{ 'font-weight-bold': integrationType == 'Jira' }"
              style="max-width: 100%; overflow: hidden;"
              v-bind="attrs"
              v-on="on"
              @mouseover="checkTruncate(item.uid, 'defectName')"
            >
              {{ item.name }}
            </div>
          </template>
          <span class="cursor-pointer">{{ item.name }}</span>
        </v-tooltip>
      </template>
      <template #[`item.priority`]="{ item }">
        <v-tooltip
          bottom
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <v-chip
              v-if="item.priority"
              label
              class="status-chip"
              :style="{
                color: getPriorityColor(item.priority, priorities),
                backgroundColor: `${getPriorityColor(item.priority, priorities)}10`,
              }"
              v-bind="attrs"
              v-on="on"
            >
              <div class="px-2 font-weight-bold">
                {{ getPriorityName(item.priority, priorities) }}
              </div>
            </v-chip>
            <v-chip
              v-else
              label
              class="status-chip"
              v-bind="attrs"
              v-on="on"
            >
              <div class="px-2 font-weight-bold">
                {{ $t('none') }}
              </div>
            </v-chip>
          </template>
          <span>{{ item.priority ? getPriorityName(item.priority, priorities) : $t('none') }}</span>
        </v-tooltip>
      </template>

      <template #[`item.status`]="{ item }">
        <v-tooltip
          bottom
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <v-chip
              v-if="item.status"
              label
              class="status-chip"
              :style="{
                color: getStatusColor(item.status, statuses),
                backgroundColor: `${getStatusColor(item.status, statuses)}10`,
              }"
              v-bind="attrs"
              v-on="on"
            >
              <div class="px-2 font-weight-bold">
                {{ getStatusName(item.status, statuses) }}
              </div>
            </v-chip>
            <v-chip
              v-else
              label
              class="status-chip"
              :style="{
                color: item.customFields?.state === 'open' ? '#008000' : '#f2284e',
                backgroundColor: item.customFields?.state === 'open' ? '#00800015' : '#f2284e15',
              }"
              v-bind="attrs"
              v-on="on"
            >
              <div class="px-2 font-weight-bold">
                {{ item.customFields?.state }}
              </div>
            </v-chip>
          </template>
          <span>{{ item.status ? getStatusName(item.status, statuses) : item.customFields?.state }}</span>
        </v-tooltip>
      </template>

      <template #[`item.integration`]="{ item }">
        <span class="text-capitalize">{{ item.integration }}</span>
        <span
          v-if="item.integrationStatus === 'inactive' || item.integrationStatus === 'error'"
          class="ml-1 warning-icon"
        >
          <v-tooltip
            bottom
            persistent
            :open-on-hover="true"
            :open-on-click="false"
            content-class="tooltip-with-arrow"
          >
            <template #activator="{ on, attrs }">
              <v-btn
                v-bind="attrs"
                icon
                v-on="on"
              >
                <WarningIcon />
              </v-btn>
            </template>
            <div class="tooltip-content">
              {{ getErrorMessage(item.integrationStatus) }}
            </div>
          </v-tooltip>
        </span>
      </template>

      <template #[`item.linkedExecutions`]="{ item }">
        <DefectExecutionSkeleton
          :key="item.uid"
          :endpoint-func="() => getDefectExecutions(item.uid)"
          :defect-uid="item.uid"
        />
      </template>

      <template #[`item.updated_at`]="{ item }">
        <span>{{ formatUpdatedAt(item.updatedAt) }}</span>
      </template>
    
      <template #[`item.uid`]="{ item }">
        <div class="d-flex justify-center">
          <div class="d-flex gap-2">
            <v-tooltip
              v-for="(menuItem, i) in menuItems"
              :key="i"
              bottom
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <div
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-btn
                    v-if="menuItem.title === 'Edit'"
                    icon
                    color="primary"
                    :disabled="!writeDefect"
                    @click.stop="handleActions(menuItem.title, item)"
                  >
                    <EditIcon />
                  </v-btn>
                  <v-btn
                    v-else-if="menuItem.title === 'Close'"
                    icon
                    color="error"
                    :disabled="!writeDefect"
                    @click.stop="handleActions(menuItem.title, item)"
                  >
                    <CancelIcon />
                  </v-btn>
                  <v-btn
                    v-else-if="menuItem.title === 'Reopen'"
                    icon
                    color="success"
                    :disabled="!writeDefect"
                    @click.stop="handleActions(menuItem.title, item)"
                  >
                    <ReOpenIcon />
                  </v-btn>
                </div>
              </template>
              <span>
                {{ writeDefect ? menuItem.title : $t('noPermissionToDo', { action: menuItem.title.toLowerCase(), type: $t('defect') }) }}
              </span>
            </v-tooltip>
          </div>
        </div>
      </template>

      <template #[`item.repository`]="{ item }">
        <v-tooltip
          bottom
          left
          max-width="485px"
          :disabled="!isRepositoryTruncated"
          content-class="tooltip-theme"
        >
          <template #activator="{ on, attrs }">
            <div
              :ref="'defectRepository_' + item.uid"
              class="text-truncate"
              style="max-width: 100%; overflow: hidden;"
              v-bind="attrs"
              v-on="on"
              @mouseover="checkRepositoryTruncate(item.uid, 'defectRepository')"
            >
              {{ item.repository }}
            </div>
          </template>
          <span>{{ item.repository }}</span>
        </v-tooltip>
      </template>

      <template #[`item.labels`]="{ item }">
        <div
          v-if="item.customFields?.tags?.length"
          class="d-flex align-center flex-wrap"
        >
          <!-- If 3 or fewer tags, just show them -->
          <template v-if="item.customFields.tags.length <= 3">
            <v-chip
              v-for="(tag, index) in item.customFields.tags"
              :key="index"
              class="custom-chip-theme fs-14px ma-1"
              :style="{
                backgroundColor: `${tag.color}10`,
                color: tag.color,
                border: `1px solid ${tag.color}`,
                fontWeight: 'bold',
              }"
            >
              {{ tag.name }}
            </v-chip>
          </template>

          <!-- If more than 3 tags, show first 3 and dropdown -->
          <v-menu
            v-else
            offset-y
            content-class="tags-menu"
          >
            <template #activator="{ on, attrs }">
              <div
                v-bind="attrs"
                v-on="on"
              >
                <v-chip
                  v-for="(tag, index) in item.customFields.tags.slice(0, 3)"
                  :key="index"
                  class="custom-chip-theme ma-1"
                  :style="{
                    backgroundColor: `${tag.color}10`,
                    color: tag.color,
                    border: `1px solid ${tag.color}`,
                    fontWeight: 'bold',
                  }"
                >
                  {{ tag.name }}
                </v-chip>

                <v-tooltip
                  bottom
                  left
                  max-width="485px"
                  content-class="tooltip-theme"
                >
                  <template #activator="{ on, attrs }">
                    <span
                      class="custom-attribute text-truncate font-weight-medium"
                      style="color: #2196F3;"
                      v-bind="attrs"
                      v-on="on"
                    >
                      +{{ item.customFields?.tags.length - 3 }}
                    </span>
                  </template>
                  <v-chip
                    v-for="(tag, index) in item.customFields?.tags.slice(3)"
                    :key="index"
                    class="custom-chip-theme ma-1"
                    :style="{
                      backgroundColor: `${tag.color}10`,
                      color: tag.color,
                      border: `1px solid ${tag.color}`,
                      fontWeight: 'bold',
                    }"
                  >
                    {{ tag.name }}
                  </v-chip>
                </v-tooltip>
              </div>
            </template>
          </v-menu>
        </div>
      </template>
    </v-data-table>
    <template v-else>
      <DefectTableSkeleton class="mt-6" />
    </template>
  </div>
</template>

<script>
import EditIcon from '@/assets/svg/edit.svg';
import CancelIcon from '@/assets/svg/cancel.svg';
import WarningIcon from '@/assets/svg/warning.svg';
import ReOpenIcon from '@/assets/svg/reopen.svg';
import handleLoading from '@/mixins/loader.js';
import DefectTableSkeleton from '@/components/Skeletons/Defect/DefectTableSkeleton.vue';
import DefectExecutionSkeleton from '@/components/Skeletons/Defect/DefectExecutionSkeleton.vue';
import { useDefectsIndex } from '@/composables/modules/defect/index';

export default {
  components: {
    EditIcon,
    CancelIcon,
    WarningIcon,
    ReOpenIcon,
    DefectTableSkeleton,
    DefectExecutionSkeleton,
  },
  mixins: [handleLoading],
  props: {
    headers: Array,
    itemKey: String,
    items: Array,
    totalItems: {
      type: Number,
      default: 0,
    },
    page: {
      type: Number,
      default: 1,
    },
    itemsPerPage: {
      type: Number,
      default: 10,
    },
    priorities: {
      type: Array,
      required: true,
    },
    statuses: {
      type: Array,
      required: true,
    },
    integrationType: {
      type: String,
      required: true,
    },
    activeState: {
      type: String,
      required: true,
    },
    writeDefect: {
      type: Boolean,
      default: false,
    },
    tableClass: {
      type: String,
      default: 'table-fixed data-table-style',
    },
    shouldHaveOverflow: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const {
      formatUpdatedAt,
      getPriorityColor,
      getStatusColor,
      getErrorMessage,
      getPriorityName,
      getStatusName,
      getMenuItems,
      getDefectExecutions,
    } = useDefectsIndex();

    return {
      formatUpdatedAt,
      getPriorityColor,
      getStatusColor,
      getErrorMessage,
      getPriorityName,
      getStatusName,
      getMenuItems,
      getDefectExecutions,
    };
  },
  data() {
    return {
      isTruncated: false,
      isRepositoryTruncated: false,
    };
  },
  computed: {
    menuItems() {
      return this.getMenuItems(this.integrationType, this.activeState);
    }
  },

  methods: {
    handleActions(actionType, item) {
      if (actionType === 'Edit') {
        this.$emit('edit', item);
      } else if (actionType === 'Close') {
        this.$emit('close', item);
      } else if (actionType === 'Reopen') {
        this.$emit('reopen', item);
      }
    },
    handleRowClick(event) {
      this.$emit('view', event);
    },
    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
    checkRepositoryTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isRepositoryTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
  },
};
</script>

<style scoped>
.warning-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 16px;
  width: 16px;
}

.warning-icon :deep(svg) {
  color: #f2284e;
}

.label-container {
  min-height: 32px;
  padding: 4px;
  border-radius: 8px;
  background-color: #f9fafb;
  cursor: pointer;
}

.custom-chip-theme {
  height: 24px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.more-count {
  color: #666;
  font-size: 15px;
  font-weight: 500;
}

.tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
:deep(.tags-menu) {
  background-color: #f9fafb !important;
  border-radius: 12px !important;
  overflow: hidden;
}

:deep(.tags-menu-list) {
  background-color: #f9fafb !important;
  border-radius: 12px !important;
}

.cursor-pointer {
  cursor: pointer;
}

:deep(.v-list-item__title) {
  width: 100%;
  
}

/* Override any default padding on the data table */
:deep(.v-data-table) {
  padding-left: 0 !important;
  margin-left: 0 !important;
}

:deep(.v-data-table__wrapper) {
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* Ensure table content aligns with header */
:deep(.v-data-table table) {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* Ensure the table container has consistent padding with header */
.v-data-table {
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* Override any Vuetify default table padding */
:deep(.v-data-table .v-data-table__wrapper) {
  padding: 0 !important;
  margin: 0 !important;
}

:deep(.v-data-table table) {
  padding: 0 !important;
  margin: 0 !important;
}

:deep(.v-data-table th),
:deep(.v-data-table td) {
  padding-left: 0 !important;
}

/* Override any default table cell padding */
:deep(.v-data-table td:first-child) {
  padding-left: 0 !important;
}

:deep(.v-data-table th:first-child) {
  padding-left: 0 !important;
}

/* Force table alignment to match header */
:deep(.v-data-table) {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

:deep(.v-data-table__wrapper) {
  margin-left: 0 !important;
  padding-left: 0 !important;
  max-height: 60vh !important;
  overflow-y: auto !important;
}

:deep(.v-data-table table) {
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* Ensure fixed header works properly */
:deep(.v-data-table.v-data-table--fixed-header .v-data-table__wrapper) {
  overflow-y: auto !important;
  max-height: 60vh !important;
}
</style>