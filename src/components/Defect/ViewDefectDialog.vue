<template>
  <div>
    <div class="defect-side-panel">
      <div class="card bg-white rounded-lg pa-6">
        <!-- Header -->
        <div class="d-flex justify-space-between align-center mb-4">
          <!-- Left: Edit Button -->
          <v-btn
            class="text-capitalize bg-white f-color-blue px-0 toggle-btn btn-plain-theme"
            depressed
            plain
            :ripple="false"
            @click="handleEditClick"
          >
            <div class="d-flex align-center">
              <EditBlueIcon />
              <span class="text-theme-primary fs-16px ml-2">{{ $t('edit') }}</span>
            </div>
          </v-btn>
          
          <!-- Right: Navigation and Close -->
          <div class="d-flex align-center">
            <v-btn
              icon
              small
              class="mr-2"
              :disabled="isSelectedDefectFirstIndex"
              :class="{ 'disabled-action': isSelectedDefectFirstIndex }"
              @click="viewPreviousDefect"
            >
              <v-icon color="black">
                mdi-arrow-left
              </v-icon>
            </v-btn>
            <v-btn
              icon
              small
              class="mr-2"
              :disabled="isSelectedDefectLastIndex"
              :class="{ 'disabled-action': isSelectedDefectLastIndex }"
              @click="viewNextDefect"
            >
              <v-icon color="black">
                mdi-arrow-right
              </v-icon>
            </v-btn>
            <v-btn
              icon
              @click="showDialog = false"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
        </div>

        <!-- Content -->
        <div class="defect-panel-content">
          <v-tooltip
            bottom
            left
            max-width="485px"
            :disabled="!isNameTruncated"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <h2 
                :ref="'defectName_' + data.uid"
                class="text-theme-label mb-4 text-truncate"
                style="max-width: 100%; overflow: hidden;"
                v-bind="attrs"
                v-on="on"
                @mouseover="checkNameTruncate(data.uid, 'defectName')"
              >
                {{ data.name }}
              </h2>
            </template>
            <span>{{ data.name }}</span>
          </v-tooltip>

          <!-- Tabs -->
          <v-tabs
            v-model="activeTab"
            class="mb-4"
            background-color="transparent"
          >
            <v-tab class="text-none font-weight-medium">
              {{ $t('defect.overview') }}
            </v-tab>
            <v-tab class="text-none font-weight-medium">
              {{ $t('comments') }}
            </v-tab>
          </v-tabs>

          <v-tabs-items v-model="activeTab">
            <!-- Overview Tab -->
            <v-tab-item>
              <div>
                <h4 class="fw-semibold fs-16px text-theme-label text-start">
                  {{ $t('defect.overview') }}
                </h4>

                <v-list class="list-theme d-flex flex-column">
                  <v-list-item class="px-0">
                    <template>
                      <div class="d-flex flex-grow-1">
                        <div
                          v-if="selectedIntegration == 'Jira'"
                          class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                          style="background-color: #f9fafb"
                        >
                          <h5 class="align-left">
                            {{ $t('defect.issueType') }}
                          </h5>
                          <v-tooltip
                            bottom
                            left
                            max-width="485px"
                            :disabled="!isIssueTypeTruncated"
                            content-class="tooltip-theme"
                          >
                            <template #activator="{ on, attrs }">
                              <div 
                                :ref="'issueType_' + data.uid"
                                class="align-left contents fw-semibold fs-14px text-truncate"
                                style="max-width: 100%; overflow: hidden;"
                                v-bind="attrs"
                                v-on="on"
                                @mouseover="checkIssueTypeTruncate(data.uid, 'issueType')"
                              >
                                {{ data.issueType || $t('none') }}
                              </div>
                            </template>
                            <span>{{ data.issueType || $t('none') }}</span>
                          </v-tooltip>
                        </div>
                        <div
                          v-else
                          class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                          style="background-color: #f9fafb"
                        >
                          <h5 class="align-left">
                            {{ $t('defect.creator') }}
                          </h5>
                          <v-tooltip
                            bottom
                            left
                            max-width="485px"
                            :disabled="!isCreatorTruncated"
                            content-class="tooltip-theme"
                          >
                            <template #activator="{ on, attrs }">
                              <div 
                                :ref="'creator_' + data.uid"
                                class="align-left contents fw-semibold fs-14px text-truncate"
                                style="max-width: 100%; overflow: hidden;"
                                v-bind="attrs"
                                v-on="on"
                                @mouseover="checkCreatorTruncate(data.uid, 'creator')"
                              >
                                {{ data.creator || $t('none') }}
                              </div>
                            </template>
                            <span>{{ data.creator || $t('none') }}</span>
                          </v-tooltip>
                        </div>
                        <div
                          class="block rounded-lg px-3 py-2 w-50 ml-2 mh-56px"
                          style="background-color: #f9fafb"
                          @click="openUrl(data.webUrl)"
                        >
                          <div class="d-flex justify-space-between align-center">
                            <h5 class="mb-0">
                              {{ $t(`defect.viewDefect.serviceId`, { service: selectedIntegration }) }}
                            </h5>
                            <v-icon
                              class="external-link-icon"
                              size="16"
                            >
                              mdi-arrow-top-right
                            </v-icon>
                          </div>
                          <div class="align-left contents fw-semibold fs-14px">
                            <span v-if="selectedIntegration === 'Github'">#{{ data.externalId || $t('none') }}</span>
                            <span v-else>{{ data.externalId || $t('none') }}</span>
                          </div>
                        </div>
                      </div>
                    </template>
                  </v-list-item>

                  <v-list-item
                    v-if="!expandOverviewPanel"
                    class="px-0 mt-2"
                  >
                    <template>
                      <div
                        v-if="selectedIntegration == 'Jira'"
                        class="d-flex flex-grow-1"
                      >
                        <div
                          class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                          style="background-color: #f9fafb"
                        >
                          <h5 class="align-left">
                            {{ $t('status') }}
                          </h5>

                          <v-chip
                            v-if="data.status"
                            label
                            class="status-chip"
                            :style="{
                              color: getStatusColor(data.status, statuses),
                              backgroundColor: `${getStatusColor(data.status, statuses)}10`,
                            }"
                          >
                            <div class="px-2 font-weight-bold">
                              {{ getStatusName(data.status, statuses) }}
                            </div>
                          </v-chip>
                          <v-chip
                            v-else
                            label
                            class="status-chip"
                            :style="{
                              color: data.customFields?.state === 'open' ? '#008000' : '#f2284e',
                              backgroundColor: data.customFields?.state === 'open' ? '#00800015' : '#f2284e15',
                            }"
                          >
                            <div class="px-2 font-weight-bold">
                              {{ data.customFields?.state }}
                            </div>
                          </v-chip>
                        </div>
                        <div
                          class="block rounded-lg px-3 py-2 w-50 mr-2 mh-56px"
                          style="background-color: #f9fafb"
                        >
                          <h5 class="align-left">
                            {{ $t('priority') }}
                          </h5>
                          <v-chip
                            v-if="data.priority"
                            label
                            class="status-chip"
                            :style="{
                              color: getPriorityColor(data.priority, priorities),
                              backgroundColor: `${getPriorityColor(data.priority, priorities)}10`,
                            }"
                          >
                            <div class="px-2 font-weight-bold">
                              {{ getPriorityName(data.priority, priorities) }}
                            </div>
                          </v-chip>
                          <v-chip
                            v-else
                            label
                            class="status-chip"
                          >
                            <div class="px-2 font-weight-bold">
                              {{ $t('none') }}
                            </div>
                          </v-chip>
                        </div>
                      </div>
                      <div
                        v-else
                        class="block rounded-lg px-3 py-2 mt-2"
                        style="background-color: #f9fafb; width: 100%"
                        @click="openUrl(`https://github.com/${data.projectName}/issues`)"
                      >
                        <div class="d-flex justify-space-between align-center">
                          <h5 class="align-left">
                            {{
                              $t('defect.viewDefect.serviceProject', {
                                service: selectedIntegration,
                                entity: entity[selectedIntegration.toLowerCase()],
                              })
                            }}
                          </h5>
                          <v-icon
                            class="external-link-icon"
                            size="16"
                          >
                            mdi-arrow-top-right
                          </v-icon>
                        </div>
                        <v-tooltip
                          bottom
                          left
                          max-width="485px"
                          :disabled="!isProjectNameTruncated"
                          content-class="tooltip-theme"
                        >
                          <template #activator="{ on, attrs }">
                            <div 
                              :ref="'projectName_' + data.uid"
                              class="align-left contents fw-semibold fs-14px text-truncate"
                              style="max-width: 100%; overflow: hidden;"
                              v-bind="attrs"
                              v-on="on"
                              @mouseover="checkProjectNameTruncate(data.uid, 'projectName')"
                            >
                              {{ data.projectName || $t('none') }}
                            </div>
                          </template>
                          <span>{{ data.projectName || $t('none') }}</span>
                        </v-tooltip>
                      </div>
                    </template>
                  </v-list-item>
                  <v-list-item
                    v-if="!expandOverviewPanel && selectedIntegration == 'Github'"
                    class="px-0 mt-2"
                  >
                    <template>
                      <div class="d-flex flex-grow-1">
                        <div
                          class="block rounded-lg px-3 py-2 w-50 mr-4 mh-56px"
                          style="background-color: #f9fafb"
                        >
                          <h5 class="align-left">
                            {{ $t('typeLabel') }}
                          </h5>
                          <div class="align-left contents fw-semibold fs-14px">
                            Bug
                          </div>
                        </div>

                        <div
                          class="block rounded-lg px-3 py-2 w-50 mh-56px"
                          style="background-color: #f9fafb"
                        >
                          <h5 class="align-left">
                            {{ $t('lastUpdate') }}
                          </h5>
                          <div class="align-left contents fw-semibold fs-14px">
                            {{ formatDate(data.updatedAt) }}
                          </div>
                        </div>
                      </div>
                    </template>
                  </v-list-item>
                  <v-list-item
                    v-if="!expandOverviewPanel"
                    class="px-0"
                  >
                    <template>
                      <div
                        class="block rounded-lg px-3 py-2 mt-2"
                        style="background-color: #f9fafb; width: 100%"
                      >
                        <h5 class="align-left">
                          {{ $t('defect.viewDefect.labels') }}
                        </h5>
                        <div
                          v-if="data.customFields?.labels.length > 0 || data.customFields?.tags.length > 0"
                          class="tags-grid"
                        >
                          <v-chip
                            v-for="(tag, index) in selectedIntegration == 'Jira'
                              ? data.customFields?.labels.slice(0, 4)
                              : data.customFields?.tags.slice(0, 4)"
                            :key="index"
                            class="custom-chip-theme fs-12px ma-1"
                            style="height: 24px;"
                            :style="
                              selectedIntegration == 'Github'
                                ? {
                                  backgroundColor: `${selectedIntegration == 'Github' ? tag.color : '#42526E'}10`,
                                  color: selectedIntegration == 'Github' ? tag.color : '#42526E',
                                  border: `1px solid ${selectedIntegration == 'Github' ? tag.color : '#42526E'}`,
                                  fontWeight: 'bold',
                                }
                                : { border: 'none' }
                            "
                            :label="selectedIntegration == 'Github' ? false : true"
                          >
                            {{ selectedIntegration == 'Github' ? tag.name : tag }}
                          </v-chip>
                          <v-tooltip
                            bottom
                            left
                            max-width="485px"
                            content-class="tooltip-theme"
                          >
                            <template #activator="{ on, attrs }">
                              <span
                                v-if="(selectedIntegration == 'Jira' && data.customFields?.labels.length > 4) || 
                                  (selectedIntegration == 'Github' && data.customFields?.tags.length > 4)"
                                class="custom-attribute text-truncate text-theme-table-text"
                                v-bind="attrs"
                                v-on="on"
                              >
                                +{{ selectedIntegration == 'Jira' 
                                  ? data.customFields?.labels.length - 4 
                                  : data.customFields?.tags.length - 4 }}
                              </span>
                            </template>
                            <v-chip
                              v-for="(tag, index) in selectedIntegration == 'Jira'
                                ? data.customFields?.labels.slice(4)
                                : data.customFields?.tags.slice(4)"
                              :key="index"
                              class="custom-chip-theme ma-1"
                              :style="
                                selectedIntegration == 'Github'
                                  ? {
                                    backgroundColor: `${selectedIntegration == 'Github' ? tag.color : '#42526E'}10`,
                                    color: selectedIntegration == 'Github' ? tag.color : '#42526E',
                                    border: `1px solid ${selectedIntegration == 'Github' ? tag.color : '#42526E'}`,
                                    fontWeight: 'bold',
                                  }
                                  : { border: 'none' }
                              "
                              :label="selectedIntegration == 'Github' ? false : true"
                            >
                              {{ selectedIntegration == 'Github' ? tag.name : tag }}
                            </v-chip>
                          </v-tooltip>
                        </div>
                        <div v-else>
                          {{ $t('none') }}
                        </div>
                      </div>
                    </template>
                  </v-list-item>
             
                  <v-list-item
                    v-if="!expandOverviewPanel && selectedIntegration == 'Jira'"
                    class="px-0"
                  >
                    <div class="d-flex flex-grow-1">
                      <div
                        class="block rounded-lg px-3 py-2 mt-3 w-50 mr-2 mh-56px"
                        style="background-color: #f9fafb; width: 100%"
                      >
                        <h5 class="align-left">
                          {{ $t('defect.parentTicket') }}
                        </h5>
                        <div class="align-left contents fw-semibold fs-14px">
                          {{ data.customFields?.parent?.key || $t('none') }}
                        </div>
                      </div>
                    </div>
                  </v-list-item>
                  <v-list-item
                    v-if="!expandOverviewPanel && selectedIntegration == 'Jira'"
                    class="px-0"
                  >
                    <div class="d-flex flex-grow-1">
                      <div
                        v-if="selectedIntegration == 'Jira'"
                        class="block rounded-lg px-3 py-2 mt-3 w-50 mr-2 mh-56px"
                        style="background-color: #f9fafb"
                      >
                        <h5 class="align-left">
                          {{ $t('defect.fixVersions') }}
                        </h5>
                        <div class="align-left contents fw-semibold fs-14px">
                          {{ data.customFields?.fixVersions || $t('none') }}
                        </div>
                      </div>
                      <div
                        v-if="selectedIntegration == 'Jira'"
                        class="block rounded-lg px-3 py-2 mt-3 w-50 mr-2 mh-56px"
                        style="background-color: #f9fafb"
                      >
                        <h5 class="align-left">
                          {{ $t('defect.restrictTo') }}
                        </h5>
                        <div class="align-left contents fw-semibold fs-14px">
                          {{ data.customFields?.restrictTo || $t('none') }}
                        </div>
                      </div>
                    </div>
                  </v-list-item>
            
                  <v-list-item
                    v-if="!expandOverviewPanel && selectedIntegration == 'Jira'"
                    class="px-0"
                  >
                    <div class="d-flex flex-grow-1">
                      <div
                        v-if="selectedIntegration == 'Jira'"
                        class="block rounded-lg px-3 py-2 mt-3 w-50 mr-2 mh-56px"
                        style="background-color: #f9fafb"
                      >
                        <h5 class="align-left">
                          {{ $t('defect.assignee') }}
                        </h5>
                        <div class="align-left contents fw-semibold fs-14px">
                          {{ data.customFields?.assignee?.displayName || $t('none') }}
                        </div>
                      </div>
                  
                      <div
                        v-if="selectedIntegration == 'Jira'"
                        class="block rounded-lg px-3 py-2 mt-3 w-50 mr-2 mh-56px"
                        style="background-color: #f9fafb"
                      >
                        <h5 class="align-left">
                          {{ $t('lastUpdate') }}
                        </h5>
                        <div class="align-left contents fw-semibold fs-14px">
                          {{ formatDate(data.updatedAt) }}
                        </div>
                      </div>
                    </div>
                  </v-list-item>

                  <!-- Attachments Section -->
                  <div class="mt-4">
                    <div class="d-flex justify-space-between align-center mb-3">
                      <span class="fw-semibold fs-16px text-theme-label">
                        {{ $t("attachments") }}
                      </span>
                    </div>

                    <template v-if="isLoadingAttachments">
                      <div class="d-flex gap-2">
                        <v-skeleton-loader
                          type="image"
                          width="120"
                          height="120"
                          class="rounded"
                        />
                        <v-skeleton-loader
                          type="image"
                          width="120"
                          height="120"
                          class="rounded"
                        />
                      </div>
                    </template>
                    <div
                      v-if="defectAttachments.length > 0"
                      class="defect-attachments-wrapper"
                    >
                      <v-slide-group class="custom-carousel">
                        <v-slide-item
                          v-for="(file, index) in defectAttachments"
                          :key="index"
                          class="preview-container"
                        >
                          <v-card outlined>
                            <template v-if="isImage(file)">
                              <div class="position-relative">
                                <v-tooltip bottom>
                                  <template #activator="{ on, attrs }">
                                    <a
                                      :href="filePreview(file)"
                                      target="_blank"
                                      class="cursor-pointer"
                                      v-bind="attrs"
                                      v-on="on"
                                    >
                                      <img
                                        :src="filePreview(file)"
                                        class="preview-image"
                                      >
                                    </a>
                                  </template>
                                  <span>{{ file.name }}</span>
                                </v-tooltip>
                                <v-btn
                                  icon
                                  small
                                  class="download-btn"
                                  @click="downloadAttachment(file)"
                                >
                                  <DownloadIcon />
                                </v-btn>
                              </div>
                            </template>
                            <template v-else-if="isVideo(file)">
                              <div class="h-full">
                                <v-tooltip bottom>
                                  <template #activator="{ on, attrs }">
                                    <a 
                                      :href="filePreview(file)" 
                                      target="_blank" 
                                      rel="noopener noreferrer"
                                      class="cursor-pointer"
                                      v-bind="attrs"
                                      v-on="on"
                                    >
                                      <video
                                        class="preview-image"
                                        :src="filePreview(file)"
                                      />
                                    </a>
                                  </template>
                                  <span>{{ file.name }}</span>
                                </v-tooltip>
                                <v-btn
                                  icon
                                  small
                                  class="download-btn"
                                  @click="downloadAttachment(file)"
                                >
                                  <DownloadIcon />
                                </v-btn>
                              </div>
                            </template>
                            <template v-else>
                              <v-tooltip bottom>
                                <template #activator="{ on, attrs }">
                                  <a 
                                    :href="filePreview(file)" 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    class="cursor-pointer"
                                    v-bind="attrs"
                                    v-on="on"
                                  >
                                    <div class="file-placeholder">
                                      {{ $t("file") }}: {{ file.name || file.type }}
                                    </div> 
                                  </a>
                                </template>
                                <span>{{ file.name }}</span>
                              </v-tooltip>
                              <v-btn
                                icon
                                small
                                class="download-btn"
                                @click="downloadAttachment(file)"
                              >
                                <DownloadIcon />
                              </v-btn>
                            </template>
                          </v-card>
                        </v-slide-item>
                      </v-slide-group>
                    </div>
                    <template v-else>
                      <div class="font-weight-medium text-theme-secondary fs-14px text-center">
                        {{ $t('noAttachments') }}
                      </div>
                    </template>
                  </div>

                  <div class="mt-4">
                    <h4
                      v-if="!expandOverviewPanel"
                      class="fw-semibold fs-16px text-theme-label mb-3 text-left"
                    >
                      {{ $t('description') }}
                    </h4>
                    <template v-if="!expandOverviewPanel">
                      <div v-if="data.description">
                        <div 
                          class="bg-gray-theme tiptap-theme readonly-editor"
                          v-html="sanitizeHTML(data.description)"
                        />
                      </div>
                    </template>
                  </div>

                  <!-- Executions Section -->
                  <div class="mt-4">
                    <h4 class="fw-semibold fs-16px text-theme-label mb-3 text-left">
                      Test executions
                    </h4>
                
                    <!-- Loading state for executions -->
                    <div
                      v-if="isLoadingExecutions"
                      class="executions-loading"
                    >
                      <v-skeleton-loader
                        type="list-item-three-line"
                        class="mb-2"
                      />
                      <v-skeleton-loader
                        type="list-item-three-line"
                        class="mb-2"
                      />
                    </div>
                
                    <!-- Executions list -->
                    <template v-else-if="defectExecutions.length > 0">
                      <div class="executions-simple-list">
                        <div
                          v-for="(execution, index) in defectExecutions"
                          :key="index"
                          class="execution-simple-item"
                        >
                          <span class="execution-simple-name">
                            {{ execution.executionName || execution.name || execution.caseName || 'Unknown execution' }}
                          </span>
                          <span class="execution-simple-run">
                            {{ execution.testRunName || execution.runName || execution.testRun || 'Unknown run' }}
                          </span>
                          <!-- Debug: {{ execution }} -->
                        </div>
                      </div>
                    </template>
                
                    <!-- No executions message -->
                    <template v-else>
                      <div class="font-weight-medium text-theme-secondary fs-14px text-center">
                        No executions
                      </div>
                    </template>
                  </div>



                  <h4
                    v-if="expandOverviewPanel"
                    class="f-color-blue toggle-btn text-start text-color"
                    @click="expandOverviewPanel = !expandOverviewPanel"
                  >
                    {{ $t('defect.showMore') }}
                  </h4>
                  <h4
                    v-else
                    class="f-color-blue toggle-btn text-start text-color"
                    @click="expandOverviewPanel = !expandOverviewPanel"
                  >
                    {{ $t('defect.showLess') }}
                  </h4>
                </v-list>
              </div>
            </v-tab-item>
            
            <!-- Comments Tab -->
            <v-tab-item>
              <div class="pa-4 text-center text--secondary">
                {{ $t('noCommentsAvailable') }}
              </div>
            </v-tab-item>
          </v-tabs-items>
        </div>
      </div>
    </div>

    <!-- Image Preview Dialog -->
    <v-dialog
      v-model="showPreview"
      fullscreen
      hide-overlay
      transition="dialog-bottom-transition"
      content-class="image-preview-dialog"
    >
      <div class="preview-overlay">
        <div class="preview-toolbar">
          <v-btn
            icon
            class="close-btn"
            @click="showPreview = false"
          >
            <v-icon color="white">
              mdi-close
            </v-icon>
          </v-btn>
        </div>

        <div class="preview-content">
          <img
            v-if="selectedAttachment && isImageFile(selectedAttachment.fileType)"
            :src="selectedAttachment.previewUrl"
            :alt="selectedAttachment.name"
            class="preview-image"
          >
        </div>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import colorPreferencesMixin from '@/mixins/colorPreferences';
import { useDefectsIndex } from '@/composables/modules/defect/index';
import makeAttachmentService from '@/services/api/attachment';
import { showErrorToast } from '@/utils/toast';
import { getExtension, generateDateUid } from "@/utils/util";
import axios from 'axios';
import DownloadIcon from "@/assets/svg/download.svg";
import EditBlueIcon from '@/assets/svg/edit-blue.svg';

export default {
  name: 'ViewDefectDialog',
  components: {
    DownloadIcon,
    EditBlueIcon,
  },
  mixins: [colorPreferencesMixin],

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    priorities: {
      type: Array,
      default: () => [],
    },
    statuses: {
      type: Array,
      default: () => [],
    },
    selectedIntegration: {
      type: String,
      default: '',
    },
    isSelectedDefectFirstIndex: {
      type: Boolean,
      default: true,
    },
    isSelectedDefectLastIndex: {
      type: Boolean,
      default: true,
    },
  },

  setup() {
    const {
      getDefectExecutions,
      getDefectAttachments,
      getDefectRuns,
      sanitizeHTML,
      formatDate,
      getPriorityName,
      getStatusName,
      getPriorityColor,
      getStatusColor,
    } = useDefectsIndex();

    return {
      getDefectExecutions,
      getDefectAttachments,
      getDefectRuns,
      sanitizeHTML,
      formatDate,
      getPriorityName,
      getStatusName,
      getPriorityColor,
      getStatusColor,
    };
  },

  data() {
    return {
      activeTab: 0, // Default to Overview tab
      expandOverviewPanel: false,
      commentsPanel: 0,
      newComment: '',
      showTiptap: false,
      entity: {
        jira: 'Project',
        github: 'Repository',
      },
      showPreview: false,
      selectedAttachment: null,
      
      // Loading states for async data
      isLoadingExecutions: false,
      isLoadingAttachments: false,
      
      // Async data
      defectExecutions: [],
      defectAttachments: [],
      
      // Truncation states
      isNameTruncated: false,
      isCreatorTruncated: false,
      isIssueTypeTruncated: false,
      isProjectNameTruncated: false,
    };
  },

  computed: {
    showDialog: {
      get() {
        return this.value;
      },
      set(v) {
        this.$emit('close-dialog', v);
      },
    },
  },
  watch: {
    // Simple watcher on data like cases - load fresh data whenever defect changes
    data: {
      handler(newData, oldData) {
        if (newData?.uid && newData.uid !== oldData?.uid) {
          // Clear previous data and load fresh
          this.defectExecutions = [];
          this.defectAttachments = [];
          this.loadAsyncData();
        }
      },
      immediate: true
    },
  },
  methods: {
    handleEditClick() {
      // Close the view panel first
      this.showDialog = false;
      // Then emit edit event to parent component to open edit panel
      this.$emit('edit', this.data);
    },
    
    // Truncation check methods
    checkNameTruncate(uid, columnName) {
      this.$nextTick(() => {
        const element = this.$refs[`${columnName}_${uid}`];
        if (element) {
          this.isNameTruncated = element.scrollWidth > element.clientWidth;
        }
      });
    },
    
    checkCreatorTruncate(uid, columnName) {
      this.$nextTick(() => {
        const element = this.$refs[`${columnName}_${uid}`];
        if (element) {
          this.isCreatorTruncated = element.scrollWidth > element.clientWidth;
        }
      });
    },
    
    checkIssueTypeTruncate(uid, columnName) {
      this.$nextTick(() => {
        const element = this.$refs[`${columnName}_${uid}`];
        if (element) {
          this.isIssueTypeTruncated = element.scrollWidth > element.clientWidth;
        }
      });
    },
    
    checkProjectNameTruncate(uid, columnName) {
      this.$nextTick(() => {
        const element = this.$refs[`${columnName}_${uid}`];
        if (element) {
          this.isProjectNameTruncated = element.scrollWidth > element.clientWidth;
        }
      });
    },
    
    async loadAsyncData() {
      if (!this.data?.uid) {
        return;
      }
      
      // Load executions
      this.isLoadingExecutions = true;
      try {
        const response = await this.getDefectExecutions(this.data.uid);
        this.defectExecutions = response?.data || [];
      } catch (error) {
        console.error('Failed to fetch defect executions:', error);
        this.defectExecutions = [];
      } finally {
        this.isLoadingExecutions = false;
      }
      
      // Load attachments
      this.isLoadingAttachments = true;
      try {
        const response = await this.getDefectAttachments(this.data.uid);
        this.defectAttachments = response?.data || [];
      } catch (error) {
        console.error('Failed to fetch defect attachments:', error);
        this.defectAttachments = [];
      } finally {
        this.isLoadingAttachments = false;
      }
    },


    // Navigation methods
    viewPreviousDefect() {
      if (!this.isSelectedDefectFirstIndex) {
        this.$emit('view-previous-defect');
      }
    },

    viewNextDefect() {
      if (!this.isSelectedDefectLastIndex) {
        this.$emit('view-next-defect');
      }
    },



    getExecutionStatusColor(status) {
      switch (status) {
        case 'passed':
          return 'success';
        case 'failed':
          return 'error';
        case 'skipped':
          return 'warning';
        default:
          return 'info';
      }
    },

    getExecutionStatusName(status) {
      switch (status) {
        case 'passed':
          return this.$t('passed');
        case 'failed':
          return this.$t('failed');
        case 'skipped':
          return this.$t('skipped');
        default:
          return status;
      }
    },

    openUrl(url) {
      if (url) {
        window.open(url, '_blank');
      }
    },

    // Helper methods for custom attachment display
    isImage(file) {
      return file?.type?.startsWith("image/") || file?.fileType?.startsWith("image/");
    },
    isVideo(file) {
      return file?.type?.startsWith("video/") || file?.fileType?.startsWith("video/");
    },
    filePreview(file) {
      const url = file.previewUrl || file.url;
      if (!url) {
        return URL.createObjectURL(file);
      }
      return url;
    },

    async downloadAttachment(file) {
      try {
        const attachmentService = makeAttachmentService(this.$api);
        
        const params = {
          download: true,
        };
        
        const response = await attachmentService.getAttachmentUrl(
          this.$route.params.handle,
          'defects',
          file.uid,
          params
        );
        
        const blob = await axios.get(response.data, { responseType: 'arraybuffer' }).then((res) => res.data);
        
        const uid = generateDateUid();
        const filename = file.name || `${uid}.${getExtension(file.type || file.fileType)}`;
        
        const url = URL.createObjectURL(new Blob([blob]));
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } catch (err) {
        console.error('❌ Download error:', err);
        showErrorToast(this.$swal, this.$t('toast.downloadError', { item: 'Attachment' }), {}, err?.response?.data);
      }
    },
  },
};
</script>

<style scoped>
/* Side Panel Styles */
.defect-side-panel {
  /* Remove height constraint to allow natural flow */
  height: auto;
}

.defect-panel-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
}

.defect-panel-content {
  /* Remove fixed height and internal scroll to match parent scroll behavior */
  height: auto;
  overflow-y: visible;
}

.cursor-pointer {
  cursor: pointer;
}

.text-caption {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
}
.text-color {
  color: #0000ff;
}
:deep(.tiptap-theme) {
  background-color: #f9f9fb;
  border-radius: 8px;
  padding: 12px;
}

:deep(.tiptap-theme .ProseMirror) {
  min-height: unset;
  pointer-events: none;
}

:deep(.tiptap-theme p) {
  margin-bottom: 0;
  line-height: 1.2;
}
.description-content {
  text-align: left;
  white-space: pre-wrap;
  font-family: inherit;
  padding: 12px;
}

.description-content :deep(ul) {
  list-style-type: disc;
  padding-left: 20px;
  margin: 4px 0;
}

.description-content :deep(li) {
  display: list-item;
  padding: 0;
  margin: 0;
}

/* Remove the flex styling that was causing alignment issues */
.description-content :deep(li::before),
.description-content :deep(li::marker) {
  display: none;
}

/* Ensure proper text wrapping */
.description-content :deep(p) {
  margin: 0;
  margin-top: 24px;
  padding: 0;
  text-align: left;
}

/* 
 * Note: v-html is used for rendering sanitized ProseMirror content.
 * The content is structured and controlled, coming from a trusted source.
 */

.block {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

/* Attachment overlay styles */
.attachment-item {
  position: relative;
}

.attachment-item .attachment-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 8px;
}

.attachment-item:hover .attachment-overlay {
  opacity: 1;
}

.attachment-preview {
  transition: transform 0.2s ease;
}

.attachment-item:hover .attachment-preview {
  transform: scale(1.02);
}

.block:hover {
  background-color: #f2f4f7;
}

.external-link-icon {
  color: #344054 !important;
}

h5 {
  color: #667085;
  margin-bottom: 4px;
  font-weight: 500;
  font-size: 12px;
}

.contents {
  color: #344054;
}

.justify-space-between {
  display: flex;
  justify-content: space-between;
  width: 100%;
}



.preview-overlay {
  background: rgba(0, 0, 0, 0.9);
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.preview-toolbar {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.preview-image {
  max-width: 95%;
  max-height: 85vh;
  object-fit: contain;
  border-radius: 4px;
}

.close-btn {
  background: rgba(255, 255, 255, 0.1) !important;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* Smooth transition */
.dialog-bottom-transition-enter-active,
.dialog-bottom-transition-leave-active {
  transition: all 0.3s ease;
}

.dialog-bottom-transition-enter,
.dialog-bottom-transition-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

/* Navigation button styles */
.disabled-action {
  opacity: 0.5;
  cursor: not-allowed;
}

.disabled-action:hover {
  opacity: 0.5;
}

video.attachment-preview {
  background: #f9fafb;
}

.file-type-container {
  display: flex;
  align-items: center;
  padding: 8px;
}

.attachment_file-type {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.attachment_file-details {
  flex: 1;
}

.file-extension {
  font-size: 0.875rem;
  font-weight: 600;
  color: #344054;
}

.executions-simple-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.execution-simple-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #F2F4F7;
}

.execution-simple-item:last-child {
  border-bottom: none;
}

.execution-simple-name {
  font-size: 14px;
  color: #101828;
  font-weight: 500;
}

.execution-simple-run {
  font-size: 14px;
  color: #667085;
  font-weight: 400;
}

.execution-item {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #eaecf0;
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.execution-title {
  display: flex;
  align-items: center;
}

.execution-name {
  font-weight: 500;
  font-size: 14px;
  color: #344054;
  margin-left: 8px;
}

.execution-status {
  display: flex;
  align-items: center;
}

.execution-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.execution-label {
  font-size: 12px;
  color: #667085;
  margin-right: 8px;
}

.execution-value {
  font-size: 14px;
  color: #344054;
  font-weight: 500;
}

.executions-loading,
.attachments-loading {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachments-loading .v-skeleton-loader {
  border-radius: 8px;
}
</style>

<style>
/* GitHub Markdown Styling */
.markdown-body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  text-align: left;
}

.markdown-body pre {
  background: #f6f8fa;
  padding: 10px;
  border-radius: 5px;
  overflow-x: auto;
}

.markdown-body code {
  background: #f6f8fa;
  padding: 2px 5px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.markdown-body blockquote {
  border-left: 4px solid #ccc;
  padding-left: 10px;
  color: #666;
}

.markdown-body table {
  border-collapse: collapse;
  width: 100%;
}

.markdown-body th,
.markdown-body td {
  border: 1px solid #ddd;
  padding: 8px;
}

.markdown-body th {
  background: #f6f8fa;
  font-weight: bold;
}
.more-count {
  color: #666;
  font-size: 15px;
  font-weight: 500;
}

/* Position download button to right corner */
.defect-attachments-wrapper .download-btn {
  right: 0.5rem !important;
}

</style>
