<template>
  <v-card
    class="white py-6 px-6"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div class="d-flex align-center justify-space-between">
      <h2 v-if="!skeletonLoaderState">
        {{ $t('defects') }}
      </h2>
      <v-skeleton-loader
        v-else
        height="36"
        width="140"
        type="heading"
      />
      
      <!-- Integration Dropdown and Create Defect Button -->
      <div class="d-flex align-center">
        <span class="integration-label mr-2">
          {{ $t('integrations.name') }}:
        </span>
     
        <v-select
          v-model="localIntegration"
          :items="integrations"
          dense
          background-color="#F9F9FB"
          class="integration-select rounded-lg field-theme mr-3"
          hide-details
          append-icon="mdi-chevron-down"
          :menu-props="{ offsetY: true }"
          @change="onIntegrationChange"
        />

        <!-- Create Defect Button - Commented out for now -->
        <!--
        <v-btn
          v-if="!skeletonLoaderState"
          color="primary"
          class="create-defect-btn"
          elevation="0"
          @click="onCreateDefect"
        >
          <span class="white--text">Create defect</span>
          <v-icon class="white--text ml-1">mdi-plus</v-icon>
        </v-btn>
        -->
      </div>
    </div>
    <div
      v-if="!skeletonLoaderState"
      class="d-flex align-center justify-start ml-0 py-4"
    >
      <v-chip
        :class="{ 'blue--text': activeState === 'active',
                  'fw-semibold': activeState === 'active',
                  'font-weight-medium': activeState !== 'active' }"
        width="200px"
        :color="activeState === 'active' ? '#e6ecff' : '#f9fafb'"
        label
        @click="setActiveState('active')"
      >
        <div class="px-2">
          {{ $t('defect.active') }} <span class="ml-2">{{ openDefects }}</span>
        </div>
      </v-chip>
      <div class="ml-2">
        <v-chip
          :class="{ 'blue--text': activeState === 'closed',
                    'fw-semibold': activeState === 'closed',
                    'font-weight-medium': activeState !== 'closed' }"
          width="200px"
          :color="activeState === 'closed' ? '#e6ecff' : '#f9fafb'"
          label
          @click="setActiveState('closed')"
        >
          <div class="px-2">
            {{ $t('defect.closed') }} <span class="ml-2">{{ closedDefects }}</span>
          </div>
        </v-chip>
      </div>
    </div>
    <div
      v-else
      class="py-4 d-flex"
    >
      <v-skeleton-loader
        class="rounded-sm d-flex gap-2 chip-primary"
        height="32"
        width="200"
        type="button@2"
      />
    </div>
  </v-card>
</template>

<script>
import handleLoading from '@/mixins/loader.js'
import { mapActions } from 'vuex'

export default {
  name: 'DefectHeader',
  mixins: [handleLoading],
  props: {
    activeState: {
      type: String,
      required: true
    },
    openDefects: {
      type: Number,
      required: true
    },
    closedDefects: {
      type: Number,
      required: true
    },
    integrations: {
      type: Array,
      default: () => []
    },
    selectedIntegration: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      localIntegration: this.selectedIntegration
    }
  },
  watch: {
    selectedIntegration(newValue) {
      this.localIntegration = newValue;
    }
  },
  methods: {
    ...mapActions('headers', ['initializeHeaders']),
    setActiveState(state) {
      this.$emit('update-state', state);
    },
    onIntegrationChange(integration) {
      this.localIntegration = integration;
      this.$emit('integration-change', integration);
    },
    onCreateDefect() {
      this.$emit('create-defect');
    }
  }
}
</script>

<style scoped>
.fw-semibold {
  font-weight: 600 !important;
}

.font-weight-medium {
  font-weight: 500 !important;
}

.integration-select {
  width: 120px !important; /* Compact width */
}

.create-defect-btn {
  background-color: #0c2ff3 !important;
  border-radius: 8px !important;
  text-transform: none !important;
  font-weight: 500 !important;
  padding: 8px 16px !important;
  min-width: auto !important;
}

.create-defect-btn:hover {
  background-color: #0a28d9 !important;
}

.integration-label {
  color: #667085; /* Silver/gray color */
  font-size: 14px;
  font-weight: 400;
}

:deep(.v-select__selections) {
  min-height: 32px !important; /* Smaller height */
  display: flex;
  align-items: center;
  justify-content: center; /* Center the text */
}

:deep(.v-input__slot) {
  min-height: 32px !important; /* Smaller height */
  border: 1px solid #D0D5DD !important;
}

:deep(.v-select__selection) {
  margin: 0;
  padding: 0;
  color: #344054;
  font-size: 14px;
  text-align: center; /* Center the text */
}

:deep(.v-input__append-inner) {
  margin-top: 4px !important; /* Adjust for smaller height */
}
</style>