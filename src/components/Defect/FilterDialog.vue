<template>
  <div>
    <v-sheet
      v-if="!skeletonLoaderState"
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40px"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-3 py-2 d-flex flex-row ml-2">{{ $t('filters') }}
        <span class="fs-small">
          <span
            v-if="filterCount"
            class="ml-2"
          >({{ filterCount }})</span> </span><v-icon
          size="16px"
          class="ml-2"
        >mdi-filter-variant</v-icon></span>
    </v-sheet>
    <v-skeleton-loader
      v-else
      class="rounded-lg"
      height="40"
      width="95"
      type="button"
    />
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('filters') }}
            </h2>
            <v-btn
              icon
              @click="close()"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>

          <v-expansion-panels
            v-if="selectedIntegration === 'Jira'"
            v-model="datePanel"
            flat
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                {{ $t('lastUpdate') }}
              </v-expansion-panel-header>
              <v-expansion-panel-content class="panel-content-theme">
                <div class="d-flex align-center">
                  <v-menu
                    v-model="showStartDateCalendar"
                    :close-on-content-click="false"
                    max-width="290"
                  >
                    <template #activator="{ on, attrs }">
                      <v-text-field
                        v-model="startDate"
                        dense
                        prepend-inner-icon="mdi-calendar"
                        readonly
                        hide-details
                        v-bind="attrs"
                        clearable
                        :placeholder="$t('customFieldPage.startDate')"
                        height="38"
                        background-color="#F9F9FB"
                        class="text-field mt-0 field-theme pa-0 rounded-lg custom-prepend"
                        v-on="on"
                      >
                        <template #prepend-inner>
                          <calendarBlueIcon />
                        </template>
                      </v-text-field>
                    </template>
                    <v-date-picker
                      v-model="startDate"
                      :max="today"
                      @input="showStartDateCalendar = false"
                    />
                  </v-menu>
                  <div class="mx-4 d-flex align-center">
                    <MinusLineIcon />
                  </div>
                  <v-menu
                    v-model="showEndDateCalendar"
                    :close-on-content-click="false"
                    max-width="290"
                  >
                    <template #activator="{ on, attrs }">
                      <v-text-field
                        v-model="endDate"
                        dense
                        filled
                        prepend-inner-icon="mdi-calendar"
                        readonly
                        v-bind="attrs"
                        clearable
                        background-color="#F9F9FB"
                        class="text-field mt-0 field-theme pa-0 rounded-lg custom-prepend"
                        :placeholder="$t('customFieldPage.endDate')"
                        height="38"
                        hide-details
                        v-on="on"
                      >
                        <template #prepend-inner>
                          <calendarBlueIcon />
                        </template>
                      </v-text-field>
                    </template>
                    <v-date-picker
                      v-model="endDate"
                      :max="today"
                      :min="startDate || undefined"
                      @input="showEndDateCalendar = false"
                    />
                  </v-menu>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
          <!-- <v-expansion-panels v-if="selectedIntegration === 'Jira'" v-model="priorityPanel" flat>
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                {{ $t('Issue Type') }}
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div v-for="(item, index) in priorities" :key="index">
                  <v-checkbox
                    v-model="selectedPriorities"
                    :value="item.id"
                    color="blue"
                    class="field-theme ma-0"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ item.name }}</span>
                    </template>
                  </v-checkbox>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels> -->
          <v-expansion-panels
            v-if="selectedIntegration === 'Jira'"
            v-model="priorityPanel"
            flat
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                {{ $t('priority') }}
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div
                  v-for="(item, index) in priorities"
                  :key="index"
                >
                  <v-checkbox
                    v-model="selectedPriorities"
                    :value="item.id"
                    color="blue"
                    class="field-theme ma-0"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                  >
                    <template #label>
                      <v-chip
                        label
                        class="status-chip my-1"
                        :style="{
                          color: getPriorityColor(item.id),
                          backgroundColor: `${getPriorityColor(item.id)}15`,
                        }"
                      >
                        <div class="px-2 font-weight-bold">
                          {{ item.name }}
                        </div>
                      </v-chip>
                    </template>
                  </v-checkbox>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>

          <v-expansion-panels
            v-if="selectedIntegration === 'Jira'"
            v-model="statusPanel"
            flat
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                {{ $t('status') }}
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div
                  v-for="(item, index) in statuses"
                  :key="index"
                >
                  <v-checkbox
                    v-model="selectedStatuses"
                    :value="item.id"
                    color="blue"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                  >
                    <template #label>
                      <!-- <span class="fs-14px text-theme-label" :style="{ color: item.color }">
                        {{ item.name }}
                      </span> -->

                      <v-chip
                        label
                        class="status-chip my-1"
                        :style="{
                          color: getStatusColor(item.id),
                          backgroundColor: `${getStatusColor(item.id)}15`,
                        }"
                      >
                        <div class="px-2 font-weight-bold">
                          {{ item.name }}
                        </div>
                      </v-chip>
                    </template>
                  </v-checkbox>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>

          <!-- Tags Filter -->
          <v-expansion-panels
            v-if="selectedIntegration === 'Github'"
            v-model="tagsPanel"
            flat
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                {{ $t('labels') }}
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div>
                  <v-checkbox
                    v-for="tag in tags"
                    :key="tag.uid"
                    v-model="selectedTags"
                    :value="tag.uid"
                    color="blue"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                  >
                    <template #label>
                      <v-chip
                        class="custom-chip-theme ma-1"
                        style="height: 24px;"
                        :style="{
                          backgroundColor: `${tag.customFields?.color}15`,
                          color: tag.customFields?.color,
                          border: `1px solid ${tag.customFields?.color}`,
                          fontWeight: 'bold',
                        }"
                      >
                        {{ tag.name }}
                      </v-chip>
                    </template>
                  </v-checkbox>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>

          <v-expansion-panels
            v-if="selectedIntegration === 'Github'"
            v-model="datePanel"
            flat
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                {{ $t('lastUpdate') }}
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-row>
                  <v-col cols="6">
                    <v-menu
                      v-model="showStartDateCalendar"
                      :close-on-content-click="false"
                      max-width="290"
                    >
                      <template #activator="{ on, attrs }">
                        <v-text-field
                          v-model="startDate"
                          dense
                          prepend-inner-icon="mdi-calendar"
                          readonly
                          v-bind="attrs"
                          clearable
                          :placeholder="$t('customFieldPage.startDate')"
                          height="38"
                          background-color="#F9F9FB"
                          class="text-field mt-0 field-theme pa-0 rounded-lg custom-prepend"
                          v-on="on"
                        >
                          <template #prepend-inner>
                            <calendarBlueIcon />
                          </template>
                        </v-text-field>
                      </template>
                      <v-date-picker
                        v-model="startDate"
                        :max="today"
                        @input="showStartDateCalendar = false"
                      />
                    </v-menu>
                  </v-col>

                  <v-col cols="6">
                    <v-menu
                      v-model="showEndDateCalendar"
                      :close-on-content-click="false"
                      max-width="290"
                    >
                      <template #activator="{ on, attrs }">
                        <v-text-field
                          v-model="endDate"
                          dense
                          filled
                          prepend-inner-icon="mdi-calendar"
                          readonly
                          v-bind="attrs"
                          clearable
                          background-color="#F9F9FB"
                          class="text-field mt-0 field-theme pa-0 rounded-lg custom-prepend"
                          :placeholder="$t('customFieldPage.endDate')"
                          height="38"
                          hide-details
                          v-on="on"
                        >
                          <template #prepend-inner>
                            <calendarBlueIcon />
                          </template>
                        </v-text-field>
                      </template>
                      <v-date-picker
                        v-model="endDate"
                        :max="today"
                        :min="startDate || undefined"
                        @input="showEndDateCalendar = false"
                      />
                    </v-menu>
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="clearAll"
        >
          {{ $t('clearAll') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          @click="apply"
        >
          {{ $t('apply') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import { formatDate } from '@/utils/util';
import calendarBlueIcon from '@/assets/svg/calendar-blue.svg';
import handleLoading from '@/mixins/loader.js';
import MinusLineIcon from '@/assets/svg/minus-line.svg';

export default {
  name: 'DefectFilterDialog',
  components: {
    calendarBlueIcon,
    MinusLineIcon
  },
  mixins: [handleLoading],
  props: {
    data: {
      type: Object,
      default: () => ({
        priorities: [],
        statuses: [],
        startDate: null,
        endDate: null,
      }),
    },
    priorities: {
      type: Array,
      default: () => [],
    },
    statuses: {
      type: Array,
      default: () => [],
    },
    filterCount: {
      type: Number,
      default: 0,
    },
    tags: {
      type: Array,
      default: () => [],
    },
    selectedIntegration: {
      type: String,
      default: 'Jira',
    },
  },
  data() {
    return {
      showDialog: false,
      priorityPanel: 0,
      selectedPriorities: [],
      statusPanel: 0,
      selectedStatuses: [],
      datePanel: 0,
      showStartDateCalendar: false,
      startDate: null,
      showEndDateCalendar: false,
      endDate: null,
      selectedTags: [],
      tagsPanel: 0,
    };
  },

  computed: {
    today() {
      return formatDate(new Date(), 'yyyy-MM-dd');
    },
  },
  watch: {
    showDialog(value) {
      if (!value) {
        return;
      }
      this.selectedPriorities = this.data.priorities || [];
      this.selectedStatuses = this.data.statuses || [];
      this.startDate = this.data.startDate;
      this.endDate = this.data.endDate;
      this.selectedTags = this.data.tags || [];
    },
    startDate(newStartDate) {
      if (newStartDate && this.endDate && newStartDate > this.endDate) {
        this.endDate = null;
      }
    }
  },
  methods: {
    apply() {
      this.$emit('update-filter-condition', {
        priorities: this.selectedPriorities,
        statuses: this.selectedStatuses,
        startDate: this.startDate,
        endDate: this.endDate,
        tags: this.selectedTags,
      });
      this.showDialog = false;
    },

    clearAll() {
      this.selectedPriorities = [];
      this.selectedStatuses = [];
      this.startDate = null;
      this.endDate = null;
      this.selectedTags = [];
    },

    close() {
      this.showDialog = false;
    },
    getStatusColor(statusId) {
      const status = this.statuses.find((s) => Number(s.id) === Number(statusId));
      return status?.color || '#0c111d';
    },
    getPriorityColor(priorityId) {
      const priority = this.priorities.find((p) => Number(p.id) === Number(priorityId));
      return priority?.color || '#0c111d';
    },
  },
};
</script>

<style scoped>
.custom-chip-theme {
  height: 28px !important;
  font-size: 15px !important;
  font-weight: 500 !important;
}

.field-theme ::v-deep .v-input__slot {
  border: none !important;
}

.v-input--selection-controls {
  margin: 0 !important;
  padding: 0 !important;
}
</style>