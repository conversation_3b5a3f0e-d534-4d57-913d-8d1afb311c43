<template>
  <div>
    <v-dialog
      v-model="createDefectDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      persistent
      width="485px"
    >
      <!-- Show loader when loading -->
      <template v-if="isLoading">
        <v-card>
          <v-overlay
            :value="true"
            absolute
          >
            <v-progress-circular
              indeterminate
              size="64"
            />
          </v-overlay>
        </v-card>
      </template>

      <!-- Show empty state when no integrations -->
      <template v-else-if="totalIntegrations === 0">
        <v-card class="d-flex flex-column fill-height">
          <!-- Header -->
          <div class="px-4 pt-6 pb-4">
            <div class="d-flex align-center justify-space-between">
              <h2 class="black--text">
                {{
                  actionSelected === 'Create new defect'
                    ? $t('defect.createNewDefectDialog.title')
                    : $t('defect.createNewDefectDialog.linkTitle')
                }}
              </h2>
              <v-btn
                icon
                @click="closeDrawer"
              >
                <v-icon color="black">
                  mdi-close
                </v-icon>
              </v-btn>
            </div>
          </div>
          <v-card-text class="d-flex flex-column align-center justify-center flex-grow-1">
            <h3 class="mb-4">
              {{ $t('defect.createNewDefectDialog.noIntegrations') }}
            </h3>
            <p class="mb-6 text-grey">
              {{ $t('defect.createNewDefectDialog.addIntegrationMessage') }}
            </p>
          </v-card-text>
        </v-card>
      </template>

      <!-- Show normal form when integrations exist -->
      <template v-else>
        <v-card class="d-flex flex-column fill-height">
          <v-card-text
            v-if="saveLoading"
            class="flex-grow-1 pa-0"
          >
            <!-- Header -->
            <div class="px-4 pt-6 pb-4">
              <div class="d-flex align-center justify-space-between">
                <h2 class="black--text">
                  {{
                    actionSelected === 'Create new defect'
                      ? $t('defect.createNewDefectDialog.title')
                      : $t('defect.createNewDefectDialog.linkTitle')
                  }}
                </h2>
                <v-btn
                  icon
                  @click="closeDrawer"
                >
                  <v-icon color="black">
                    mdi-close
                  </v-icon>
                </v-btn>
              </div>
            </div>

            <div class="d-flex mx-auto justify-center flex-column h-100">
              <v-progress-circular
                class="position-relative"
                :size="150"
                width="6"
                color="primary"
                indeterminate
              >
                <div class="svg-container">
                  <UploadCloud class="upload-cloud-icon" />
                </div>
              </v-progress-circular>
              <p class="title font-weight-regular text-center mt-4 mx-auto">
                {{ $t('defect.createNewDefectDialog.uploadingData', { service: selectedIntegration.service }) }}...
              </p>
            </div>
          </v-card-text>
          <v-card-text
            v-else
            class="flex-grow-1 pa-0"
          >
            <!-- Header -->
            <div class="px-4 pt-6 pb-1">
              <div class="d-flex align-center justify-space-between">
                <h2 class="black--text">
                  {{
                    actionSelected === 'Create new defect'
                      ? $t('defect.createNewDefectDialog.title')
                      : $t('defect.createNewDefectDialog.linkTitle')
                  }}
                </h2>
                <v-btn
                  icon
                  @click="closeDrawer"
                >
                  <v-icon color="black">
                    mdi-close
                  </v-icon>
                </v-btn>
              </div>
            </div>
            <!-- Service selection section -->
            <div class="px-4 mb-6">
              <div class="d-flex align-center">
                <span class="text-grey-darken-1 text-body-2">
                  {{ $t('defect.createNewDefectDialog.projectIntegration') }}: {{ getServiceName(selectedService) }}
                </span>

                <span
                  v-if="services.length > 1"
                  class="text-primary cursor-pointer text-body-2 ml-2"
                  style="color: #0c2ff3; font-weight: 500"
                  @click="toggleService"
                >
                  {{ $t('defect.createNewDefectDialog.change') }}
                </span>
              </div>
            </div>

            <!-- Scrollable Form Content -->
            <div class="form-wrapper">
              <v-form class="px-4">
                <div>
                  <!-- INTEGRATION SELECT -->
                  <div class="d-flex flex-column mb-6">
                    <div class="text-left">
                      <v-label class="fs-14px text-theme-label font-weight-medium">
                        {{ $t('defect.createNewDefectDialog.integrationLabel') }} <span
                          v-if="actionSelected === 'Create new defect'"
                          class="required-asterisk"
                        >*</span>
                      </v-label>
                    </div>
                    <v-select
                      v-model="selectedIntegration.integrationUid"
                      type="text"
                      dense
                      background-color="#F9F9FB"
                      :placeholder="$t('defect.createNewDefectDialog.selectIntegration')"
                      class="rounded-lg field-theme custom-prepend mh-38px"
                      hide-details
                      append-icon="mdi-chevron-down"
                      :items="integrationsForSelect"
                      item-text="title"
                      item-value="value"
                      :menu-props="{ offsetY: true }"
                      @change="onIntegrationSelected"
                    />
                  </div>

                  <!-- DEFECT NAME/SUMMARY (hide for custom integrations) -->
                  <div
                    v-if="actionSelected === 'Create new defect' && selectedService !== 'custom'"
                    class="d-flex flex-column mb-6"
                  >
                    <div class="text-left">
                      <v-label class="fs-14px text-theme-label font-weight-medium">
                        {{ selectedService === 'github' ? $t('defect.createNewDefectDialog.nameLabel') : $t('defect.createNewDefectDialog.summary') }}
                        <span class="required-asterisk">*</span>
                      </v-label>
                    </div>
                    <v-text-field
                      v-model="defectName"
                      type="text"
                      dense
                      background-color="#F9F9FB"
                      :placeholder="selectedService === 'github' ? $t('defect.createNewDefectDialog.enterName') : $t('defect.createNewDefectDialog.summary')"
                      class="rounded-lg field-theme custom-prepend mh-38px"
                      hide-details
                    />
                  </div>

                  <!-- DEFECT DESCRIPTION (hide for custom integrations) -->
                  <div
                    v-if="actionSelected === 'Create new defect' && selectedService !== 'custom'"
                    class="d-flex flex-column mb-6"
                  >
                    <div class="text-left">
                      <v-label class="fs-14px text-theme-label font-weight-medium">
                        {{ $t('defect.createNewDefectDialog.descriptionLabel') }} <span class="required-asterisk">*</span>
                      </v-label>
                    </div>
                    <TiptapEditor
                      v-model="defectDescription"
                      class="defect-description-editor"
                    />
                  </div>



                  <!-- DEFECT SELECTION (only for linking) - Jira/GitHub -->
                </div>

                <!-- GITHUB LABELS -->
                <div
                  v-if="selectedService === 'github' && actionSelected === 'Create new defect'"
                  class="d-flex flex-column mb-6"
                >
                  <div class="text-left">
                    <v-label class="fs-14px text-theme-label font-weight-medium">
                      {{ $t('defect.createNewDefectDialog.tagsLabel') }}
                    </v-label>
                  </div>
                  <v-select
                    v-model="selectedIntegration.tags"
                    :items="githubTags"
                    type="text"
                    dense
                    background-color="#F9F9FB"
                    :placeholder="$t('defect.createNewDefectDialog.selectTags')"
                    class="rounded-lg field-theme custom-prepend mh-38px"
                    hide-details
                    append-icon="mdi-chevron-down"
                    item-text="name"
                    item-value="name"
                    :menu-props="{
                      offsetY: true,
                      closeOnClick: false,
                      closeOnContentClick: false,
                    }"
                    multiple
                    chips
                    deletable-chips
                  >
                    <!-- Add Select All option -->
                    <template #prepend-item>
                      <v-list-item
                        ripple
                        @click="toggleSelectAll"
                      >
                        <v-list-item-action>
                          <v-checkbox
                            :input-value="isAllSelected"
                            :indeterminate="isIndeterminate"
                            class="field-theme"
                            :ripple="false"
                            off-icon="icon-checkbox-off"
                            on-icon="icon-checkbox-on"
                            indeterminate-icon="icon-indeterminate"
                            :hide-details="true"
                            dense
                          />
                        </v-list-item-action>
                        <v-list-item-content>
                          <v-list-item-title>
                            <span class="fs-14px text-theme-label">{{ $t('selectAll') }}</span>
                          </v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                      <v-divider />
                    </template>

                    <!-- Selection template for displaying selected tags as chips -->
                    <template #selection="{ item, index }">
                      <v-chip
                        v-if="index < 3"
                        small
                        class="ma-1"
                        close
                        @click:close="removeTag(item.name)"
                      >
                        <span style="color: #3f69cc">{{ item.name }}</span>
                      </v-chip>
                      <span
                        v-if="index === 3"
                        class="text-caption grey--text text-truncate"
                      >
                        (+{{ selectedIntegration.tags.length - 3 }} {{ $t('more') }})
                      </span>
                    </template>

                    <!-- Item template for the dropdown list -->
                    <template #item="{ item, on, attrs }">
                      <v-tooltip
                        bottom
                        max-width="100%"
                        content-class="defect-tooltip"
                        :disabled="!isTextTruncated(item.name)"
                      >
                        <template #activator="{ on: tooltipOn, attrs: tooltipAttrs }">
                          <v-list-item
                            v-bind="{ ...attrs, ...tooltipAttrs }"
                            class="defect-item"
                            v-on="{ ...on, ...tooltipOn }"
                          >
                            <v-list-item-content class="defect-item-content">
                              <v-list-item-title class="defect-item-title">
                                {{ getTruncatedText(item.name) }}
                              </v-list-item-title>
                            </v-list-item-content>
                          </v-list-item>
                        </template>
                        <span>{{ item.name }}</span>
                      </v-tooltip>
                    </template>
                  </v-select>
                </div>

                <!-- INTEGRATION DEFECT COMPONENT (hide for custom integrations) -->
                <IntegrationDefectComponent
                  v-if="selectedService !== 'custom'"
                  ref="integrationDefectComponent"
                  :action-selected="actionSelected"
                  :defects="existingDefects"
                  :current-integration="currentIntegration"
                  :testfiesta-project-uid="testfiestaProjectUid"
                  :selected-service="selectedService"
                  :integration-state="integrationState"
                  :save-repo-project-preference="saveRepoProjectPreference"
                  :restore-repo-project-preference="restoreRepoProjectPreference"
                  :auto-select-single-repo-project="autoSelectSingleRepoProject"
                  :get-saved-jira-org-preference="getSavedJiraOrgPreference"
                  @update:state="updateIntegrationState"
                  @project-changed="onProjectChanged"
                />

                <!-- DEFECT SELECTION (only for linking) - Jira/GitHub -->
                <div
                  v-if="actionSelected === 'Link existing defect' && (selectedIntegration.service === 'jira' || selectedIntegration.service === 'github')"
                  class="d-flex flex-column mb-6"
                >
                  <div class="text-left">
                    <v-label class="fs-14px text-theme-label font-weight-medium">
                      {{
                        $t('defect.createNewDefectDialog.libraryLabel', {
                          service: selectedIntegration.service ? selectedIntegration.service : 'Service',
                        })
                      }}
                    </v-label>
                  </div>

                  <v-select
                    v-model="selectedDefect"
                    :items="filteredDefectsForLinking"
                    :loading="isLoadingDefects"
                    :disabled="isLoadingDefects"
                    item-text="name"
                    item-value="id"
                    dense
                    background-color="#F9F9FB"
                    :placeholder="$t('defect.createNewDefectDialog.selectDefect')"
                    class="rounded-lg field-theme custom-prepend mh-38px"
                    hide-details
                    append-icon="mdi-chevron-down"
                    :menu-props="{ offsetY: true, left: true, maxWidth: '450px !important' }"
                  >
                    <template #prepend-item>
                      <v-text-field
                        v-model="searchQuery"
                        dense
                        background-color="#F9F9FB"
                        :placeholder="$t('defect.createNewDefectDialog.searchPlaceholder')"
                        class="mx-2 mt-2"
                        hide-details
                        clearable
                      >
                        <template #prepend-inner>
                          <v-icon size="18">
                            mdi-magnify
                          </v-icon>
                        </template>
                      </v-text-field>
                    </template>
                    
                    <template #item="{ item, on, attrs }">
                      <v-tooltip
                        bottom
                        max-width="100%"
                        content-class="defect-tooltip"
                      >
                        <template #activator="{ on: tooltipOn, attrs: tooltipAttrs }">
                          <v-list-item
                            v-bind="{ ...attrs, ...tooltipAttrs }"
                            class="defect-item"
                            v-on="{ ...on, ...tooltipOn }"
                          >
                            <v-list-item-content class="defect-item-content">
                              <v-list-item-title class="defect-item-title">
                                {{ getTruncatedText(item.name) }}
                              </v-list-item-title>
                            </v-list-item-content>
                          </v-list-item>
                        </template>
                        <span>{{ item.name }}</span>
                      </v-tooltip>
                    </template>
                  </v-select>
                </div>

                <!-- CUSTOM INTEGRATION MULTI-INPUT -->
                <div
                  v-if="actionSelected === 'Link existing defect' && selectedIntegration.service === 'custom'"
                  class="d-flex flex-column mb-6"
                >
                  <div class="text-left">
                    <v-label class="fs-14px text-theme-label font-weight-medium">
                      {{ $t('defect.createNewDefectDialog.customDefectIds') }}
                    </v-label>
                  </div>
                  <v-combobox
                    v-model="customDefectIds"
                    multiple
                    chips
                    small-chips
                    deletable-chips
                    clearable
                    hide-selected
                    hide-details
                    dense
                    background-color="#F9F9FB"
                    :placeholder="$t('defect.createNewDefectDialog.enterCustomDefectIds')"
                    class="rounded-lg field-theme custom-prepend mh-38px"
                    @keydown.enter.native.stop
                  />
                  <p class="text-caption text-grey mt-1">
                    {{ $t('defect.createNewDefectDialog.customDefectIdsHelp') }}
                  </p>
                </div>
                <div v-if="selectedService !== 'custom'">
                  <!-- ATTACHMENTS -->
                  <div class="defect-attachment-section">
                    <div class="text-left mb-4">
                      <span class="section-label">
                        {{ $t('defect.createNewDefectDialog.attachmentsLabel') }}
                      </span>
                    </div>

                    <div class="upload-container-wrapper">
                      <div
                        class="upload-container"
                        @click="openFileDialog"
                        @drop.prevent="handleDrop"
                        @dragover.prevent
                        @dragenter.prevent
                      >
                        <input
                          ref="fileInputRef"
                          type="file"
                          multiple
                          style="display: none"
                          @change="handleFileChange"
                        >

                        <p class="upload-text">
                          {{ $t('defect.createNewDefectDialog.uploadInstructions') }}
                        </p>

                        <div class="browse-files-btn">
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            class="arrow-icon"
                          >
                            <path
                              d="M3.33337 8H12.6667"
                              stroke="#0C2FF3"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M8.66663 4L12.6666 8L8.66663 12"
                              stroke="#0C2FF3"
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          {{ $t('defect.createNewDefectDialog.browseFiles') }}
                        </div>
                      </div>
                    </div>

                    <!-- File List -->
                    <template v-if="attachments.length > 0">
                      <div class="file-list mt-4">
                        <div
                          v-for="(attachment, index) in attachments"
                          :key="index"
                          class="file-item d-flex align-center justify-space-between py-2 px-3"
                        >
                          <div class="d-flex align-center">
                            <v-icon
                              size="20"
                              class="mr-2"
                              color="grey darken-1"
                            >
                              mdi-file-outline
                            </v-icon>
                            <span class="file-name">{{ attachment.fileName }}</span>
                          </div>
                          <v-btn
                            icon
                            x-small
                            @click="removeAttachment(index)"
                          >
                            <v-icon size="16">
                              mdi-close
                            </v-icon>
                          </v-btn>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </v-form>
            </div>
          </v-card-text>

          <!-- Action Buttons -->
          <div
            v-if="!saveLoading"
            class="actions-container"
          >
            <v-btn
              width="204.5px"
              color="#F2F4F7"
              full-width
              height="40"
              :depressed="true"
              class="text-capitalize btn-theme"
              elevation="0"
              @click="handleCancel"
            >
              {{ $t('cancel') }}
            </v-btn>
            <v-btn
              width="204.5px"
              class="btn-theme text-capitalize"
              height="40"
              color="primary"
              :depressed="true"
              full-width
              elevation="0"
              @click="handleAction"
            >
              <span>{{ actionButtonText }}</span>
            </v-btn>
          </div>
        </v-card>
      </template>
    </v-dialog>
  </div>
</template>

<script>
import IntegrationDefectComponent from '@/components/Integration/IntegrationDefect.vue';
import UploadCloud from '@/assets/svg/upload-cloud.svg';
import TiptapEditor from '@/components/base/TipTapEditor.vue';
import { useCreateDefect } from '@/composables/modules/defect/createDefect';
import { getCurrentInstance, ref } from 'vue';

export default {
  name: 'CreateDefect',
  components: {
    IntegrationDefectComponent,
    UploadCloud,
    TiptapEditor,
  },
  props: {
    selectedProjectKey: {
      type: String,
      default: null,
    },
    execution: {
      type: Object,
      required: true,
    },
    resultUid: {
      type: String,
      default: null,
    },
    actionSelected: {
      type: String,
      required: true,
    },
    createDefectDialog: {
      type: Boolean,
      required: true,
    },
    service: {
      type: String,
      default: null,
    },
  },
  setup(props, context) {
    const createDefect = useCreateDefect(props);
    const instance = getCurrentInstance();
    const fileInputRef = ref(null);
    
    // Initialize data when component is created
    createDefect.initializeData();
    
    // Create wrapper functions that pass emit to composable functions
    const closeDrawer = () => createDefect.closeDrawer(context.emit);
    const onIntegrationSelected = () => createDefect.onIntegrationSelected(context.emit);
    const saveDefect = (ref) => createDefect.saveDefect(ref, context.emit);
    const handleCancel = () => createDefect.handleCancel(context.emit);
    const handleAction = () => {
      const ref = instance?.refs?.integrationDefectComponent;
      return createDefect.handleAction(ref, context.emit);
    };
    const openFileDialog = () => {
      if (fileInputRef.value) {
        fileInputRef.value.click();
      }
    };
    const handleFileChange = (event) => createDefect.handleFileChange(event);
    const handleDrop = (event) => createDefect.handleDrop(event);
    const removeAttachment = (index) => createDefect.removeAttachment(index);
    
    // Add truncation function for dropdown items
    const getTruncatedText = (text) => {
      if (!text) return '';
      
      // For GitHub defects with # prefix, truncate at 60 characters
      if (text.startsWith('#')) {
        return text.length > 60 ? text.substring(0, 60) + '...' : text;
      }
      
      // For other defects (like Jira), check if they have external ID format
      if (text.includes(': ')) {
        return text.length > 65 ? text.substring(0, 65) + '...' : text;
      }
      
      // For plain text without external ID, use a higher threshold
      return text.length > 70 ? text.substring(0, 70) + '...' : text;
    };
    
    return {
      ...createDefect,
      closeDrawer,
      onIntegrationSelected,
      saveDefect,
      handleCancel,
      handleAction,
      openFileDialog,
      handleFileChange,
      handleDrop,
      removeAttachment,
      fileInputRef,
      getTruncatedText,
    };
  },
};
</script>

<style scoped>
.dialog-theme {
  overflow: hidden !important;
}

:deep(.v-dialog--fullscreen) {
  width: 485px !important;
  right: 0 !important;
  left: auto !important;
}

:deep(.v-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.form-wrapper) {
  flex: 1;
  overflow-y: auto;
}

.form-wrapper {
  background-color: white !important;
}
:deep(.form-wrapper .v-form) {
  padding: 0 24px;
  width: 100%;
  background-color: white !important;
  box-sizing: border-box;
}

:deep(.actions-container) {
  position: fixed;
  bottom: 0;
  width: 100%;
  max-width: 485px;
  padding: 16px 24px;
  background-color: white;
  z-index: 1;
  border-top: 1px solid #f2f4f7;
  display: flex;
  justify-content: space-between;
  gap: 16px;
  box-sizing: border-box;
}

:deep(.actions-container .v-btn) {
  flex: 1;
  min-width: 0 !important; /* Prevent button from expanding */
}

:deep(.v-tooltip__content) {
  background-color: #333 !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  word-wrap: break-word !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

:deep(.defect-tooltip) {
  width: 100% !important;
  max-width: 100% !important;
  background-color: #333 !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  word-wrap: break-word !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

:deep(.v-text-field.custom-prepend .v-input__prepend-inner) {
  margin-top: 10px !important;
  margin-right: 8px !important;
}

/* Add to existing styles */
.attachment-list {
  border: 1px solid #eaecf0;
  border-radius: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.attachment-item {
  border-bottom: 1px solid #eaecf0;
  background-color: #f9f9fb;
}

.attachment-item:last-child {
  border-bottom: none;
}

.upload-zone {
  background-color: #f9f9fb;
  border: 1px dashed #e4e7ec;
  border-radius: 8px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-zone:hover {
  border-color: #b2b7c2;
  background-color: #f5f5f7;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-text {
  color: #667085;
  font-size: 14px;
  margin: 0;
}

.selected-files {
  border: 1px solid #e4e7ec;
  border-radius: 8px;
  overflow: hidden;
}

.file-item {
  background-color: #f9fafb;
  border-bottom: 1px solid #e4e7ec;
}

.file-item:last-child {
  border-bottom: none;
}

.file-name {
  font-size: 14px;
  color: #344054;
}

.upload__attachment {
  background-color: #fff;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #eaecf0;
  width: 100%;
  gap: 12px;
  margin-bottom: 8px;
}

.attachment_file-type {
  position: relative;
  display: flex;
  align-items: center;
}

.attachment_file-type .v-icon {
  z-index: 5;
  font-size: 55px !important;
}

.attachment_file-type span {
  position: absolute;
  font-size: 10px;
  padding: 1px 5px;
  border-radius: 2px;
  color: #fff;
  z-index: 6;
}

.attachment_file-details {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}

.attachment_file-details p {
  font-size: 14px;
  font-family: 'Inter', sans-serif;
  color: #344054;
}

.attachment_file-details #fileName {
  white-space: nowrap;
  width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attachment_file-details .attachment_progress {
  gap: 5px;
}

.toggle-btn {
  box-shadow: none !important;
}

.defect-attachment-section {
  margin-bottom: 32px;
}

.section-label {
  font-size: 14px;
  font-weight: 500;
  color: #344054;
  display: block;
}

.upload-container-wrapper {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 10px 8px;
  width: 100%;
  max-width: 421px;
}

.upload-container {
  border: 1px dashed #d0d5dd;
  border-radius: 8px;
  padding: 24px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  background-color: #f9fafb;
  min-height: 120px;
}

.upload-container:hover {
  border-color: #b2b7c2;
  background-color: #f5f5f7;
}

.upload-text {
  color: #667085;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  margin: 0;
  max-width: 300px;
}

.browse-files-btn {
  color: #0c2ff3;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.arrow-icon {
  transform: rotate(-45deg);
  margin-right: 4px;
}

.file-list {
  border: 1px solid #eaecf0;
  border-radius: 8px;
  overflow: hidden;
}

.file-item {
  background-color: #f9fafb;
  border-bottom: 1px solid #e4e7ec;
}

.file-item:last-child {
  border-bottom: none;
}

.file-name {
  font-size: 14px;
  color: #344054;
}

:deep(.custom-chip-theme) {
  background-color: #f2f4f7 !important;
  color: #344054 !important;
  height: 24px !important;
}

:deep(.custom-chip-theme .v-chip__close) {
  opacity: 1;
  font-size: 16px;
}

:deep(.v-select.v-text-field input) {
  max-height: none;
  height: auto;
}

:deep(.v-select__selections) {
  padding: 0 8px;
  flex-wrap: wrap;
}

.h-100 {
  height: 100%;
}
.position-relative {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px; /* Ensures container has height */
}
.svg-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.upload-cloud-icon {
  color: #000000; /* Makes the icon black */
}

.v-select__selections .v-chip:not(.v-chip--active) {
  background: #e6ebf6 !important;
  border: solid 2px #3f69cc80 !important;
  border-radius: 50px !important;
  padding: 2px 4px 2px 8px !important;
  font-size: 14px !important;
}

.v-select__selections .v-chip--select.v-chip--active {
  border-radius: 12px !important;
  border: solid 2px #3f69cc80 !important;
  padding: 2px 4px 2px 8px !important;
  font-size: 14px !important;
}

.required-asterisk {
  color: red;
  font-size: 14px;
  font-weight: bold;
  margin-left: 4px;
}

/* TipTap Editor Styles for Defect Description */
:deep(.defect-description-editor .tiptap-theme) {
  background-color: #F9F9FB;
  border-radius: 8px;
  border: 1px solid #E0E0E0;
}

:deep(.defect-description-editor .tiptap-toolbar) {
  background-color: #F9F9FB;
  border-bottom: 1px solid #E0E0E0;
  border-radius: 8px 8px 0 0;
}

:deep(.defect-description-editor .tiptap-editor) {
  background-color: #F9F9FB;
  border: none;
  border-radius: 0 0 8px 8px;
  min-height: 120px;
}

:deep(.defect-description-editor .tiptap-footer) {
  background-color: #F9F9FB;
  border-top: 1px solid #E0E0E0;
  border-radius: 0 0 8px 8px;
}

:deep(.defect-description-editor .ProseMirror) {
  background-color: #F9F9FB;
  padding: 12px;
  min-height: 100px;
  border: none;
  outline: none;
}

:deep(.defect-description-editor .ProseMirror:focus) {
  outline: none;
  border: none;
}

:deep(.defect-description-editor .tiptap-toolbar button) {
  color: #344054;
  transition: all 0.2s ease;
}

:deep(.defect-description-editor .tiptap-toolbar button:hover) {
  background-color: #E0E0E0;
}

:deep(.defect-description-editor .tiptap-toolbar button.is-active) {
  background-color: #1976D2;
  color: white;
}

/* Enhanced image handling for TipTap editor in Create Defect */
:deep(.defect-description-editor .ProseMirror) {
  overflow: hidden !important;
  word-wrap: break-word !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* Force all content to be contained */
:deep(.defect-description-editor .ProseMirror *) {
  max-width: 100% !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
}

/* Comprehensive image styling for create mode */
:deep(.defect-description-editor .ProseMirror img) {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  display: block !important;
  position: relative !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  border-radius: 8px !important;
  margin: 12px 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  object-fit: contain !important;
  max-height: 400px !important;
  cursor: pointer !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

:deep(.defect-description-editor .ProseMirror img:hover) {
  transform: scale(1.02) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Ensure all content is properly contained */
:deep(.defect-description-editor .ProseMirror p) {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.defect-description-editor .ProseMirror *:not(img)) {
  max-width: 100% !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}

/* Container overflow control */
:deep(.defect-description-editor) {
  overflow: hidden !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

:deep(.defect-description-editor .tiptap-theme) {
  overflow: hidden !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

:deep(.defect-description-editor .tiptap-editor) {
  overflow: hidden !important;
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* Force the editor content to respect boundaries */
:deep(.defect-description-editor .tiptap-editor .ProseMirror) {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* Additional constraints for dialog content */
:deep(.v-dialog__content) {
  overflow: hidden !important;
  max-width: 100% !important;
}

:deep(.v-card__text) {
  overflow: hidden !important;
  max-width: 100% !important;
}

:deep(.v-col) {
  overflow: hidden !important;
  max-width: 100% !important;
}

/* Force all content within the dialog to be contained */
:deep(.v-dialog *) {
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Additional styling for better image display */
:deep(.defect-description-editor .defect-description-image) {
  max-width: 100% !important;
  width: 100% !important;
  height: auto !important;
  border-radius: 8px !important;
  margin: 12px 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  object-fit: contain !important;
  max-height: 400px !important;
  display: block !important;
  position: relative !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  cursor: pointer !important;
  transition: transform 0.2s ease, box-shadow 0.2s ease !important;
}

:deep(.defect-description-editor .defect-description-image:hover) {
  transform: scale(1.02) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Ensure proper text wrapping around images */
:deep(.defect-description-editor .ProseMirror) {
  line-height: 1.5 !important;
}

:deep(.defect-description-editor .ProseMirror h1),
:deep(.defect-description-editor .ProseMirror h2),
:deep(.defect-description-editor .ProseMirror h3),
:deep(.defect-description-editor .ProseMirror h4),
:deep(.defect-description-editor .ProseMirror h5),
:deep(.defect-description-editor .ProseMirror h6) {
  margin: 16px 0 8px 0 !important;
  max-width: 100% !important;
  word-wrap: break-word !important;
}

/* List styling */
:deep(.defect-description-editor .ProseMirror ul),
:deep(.defect-description-editor .ProseMirror ol) {
  max-width: 100% !important;
  word-wrap: break-word !important;
  padding-left: 20px !important;
}

:deep(.defect-description-editor .ProseMirror li) {
  max-width: 100% !important;
  word-wrap: break-word !important;
}

/* Code block styling */
:deep(.defect-description-editor .ProseMirror pre) {
  max-width: 100% !important;
  overflow-x: auto !important;
  word-wrap: break-word !important;
  background: #f6f8fa !important;
  padding: 12px !important;
  border-radius: 6px !important;
  margin: 12px 0 !important;
  border: 1px solid #e1e4e8 !important;
}

:deep(.defect-description-editor .ProseMirror code) {
  background: #f6f8fa !important;
  padding: 2px 6px !important;
  border-radius: 3px !important;
  font-family: 'Courier New', monospace !important;
  font-size: 0.9em !important;
  word-wrap: break-word !important;
}

/* Blockquote styling */
:deep(.defect-description-editor .ProseMirror blockquote) {
  border-left: 4px solid #1976D2 !important;
  padding-left: 16px !important;
  margin: 12px 0 !important;
  color: #666 !important;
  font-style: italic !important;
  max-width: 100% !important;
  word-wrap: break-word !important;
}

/* Link styling */
:deep(.defect-description-editor .ProseMirror a) {
  color: #1976D2 !important;
  text-decoration: underline !important;
  word-break: break-word !important;
  max-width: 100% !important;
}

:deep(.defect-description-editor .ProseMirror a:hover) {
  color: #1565C0 !important;
}

/* Defect selection dropdown styles */
:deep(.defect-item) {
  padding: 8px 16px;
  width: 100%;
  max-width: 100%;
}

:deep(.defect-item-content) {
  padding: 0;
  width: 100%;
  max-width: 100%;
}

:deep(.defect-item-title) {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
  width: 100% !important;
  font-size: 14px;
  line-height: 1.4;
  display: block !important;
}

/* Tooltip styles */
:deep(.defect-tooltip) {
  max-width: 400px !important;
  background-color: #2c3e50 !important;
  color: white !important;
  font-size: 13px !important;
  padding: 6px 10px !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  border: none !important;
}

:deep(.defect-tooltip-text) {
  word-wrap: break-word !important;
  white-space: normal !important;
  line-height: 1.3 !important;
  display: block !important;
}


</style>


