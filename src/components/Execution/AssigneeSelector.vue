<template>
  <div>
    <v-autocomplete
      :value="value"
      :loading="false"
      :items="itemsList"
      item-value="uid"
      item-text="fullName"
      :search-input.sync="search"
      :placeholder="getPlaceholder()"
      cache-items
      :return-object="true"
      append-icon="mdi-chevron-down"
      dense
      @change="updateAssignedUser($event)"
    >
      <template #selection="{ item }">
        <span class="fw-semibold fs-14px">{{ `${ item?.firstName ?? '' } ${ item?.lastName ?? '' }` }}</span>
      </template>
    </v-autocomplete>
  </div>
</template>

<script>
import workspaceService from '@/services/api/workspace';
import makeExecutionsService from '@/services/api/execution';
import { showSuccessToast, showErrorToast } from '@/utils/toast';

export default {
  name: 'AssigneeSelector',
  props: {
    value: {
      type: String,
      default: '',
    },
    execution:{
        type: Object,
        default: () => ({}),
    },
    projectList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      items: [],
      search: '',
      users: [],
    };
  },
  computed: {
    sortedTags() {
      return [...this.items].sort((a, b) => a.name.localeCompare(b.name));
    },
    filteredTags() {
      if (!this.tagSearch) return this.sortedTags;
      const search = this.tagSearch.toLowerCase();
      return this.sortedTags.filter((tag) => tag.name.toLowerCase().includes(search));
    },
    itemsList() {
      return this.items.map(item => ({
        ...item,
        fullName: `${item.firstName} ${item.lastName}`
      }));
    },
  },
  watch: {
      search(val) {
        val && val !== this.select && this.querySelections(val);
      },
  },
  async mounted() {
    await this.getWorkspaceUsers();
  },
  methods: {
    getPlaceholder() {
      // If no regular assignee but we have customFields assignee, show it as placeholder
      if (!this.value && this.execution?.customFields?.assignee?.name) {
        return this.execution.customFields.assignee.name;
      }
      return this.$t('Select Assignee');
    },
    querySelections(v) {
      this.items = this.items.filter((e) => {
        return (e.name || '').toLowerCase().indexOf((v || '').toLowerCase()) > -1;
      });
    },
    isSelected(uid) {
      return this.selectedTags.some((t) => t.uid === uid);
    },
    onCloseTagDialog() {
      this.showCreateTagDialog = false;
      this.tagSearch = '';
    },
    async getWorkspaceUsers() {
      try {
        const makeWorkspaceService = workspaceService(this.$api);
        const handle = this.$route.params.handle;
        const response = await makeWorkspaceService.getWorkspaceUsers(handle);
        this.items = response.data?.users || [];
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: this.$t('users') }, error?.response?.data);
      }
    },
    async updateAssignedUser(val) {
      const payload = {
        assignedTo: val.uid,
      };
      const executionsService = makeExecutionsService(this.$api);
      const key = this.$route.params.key ?? this.projectList.find((p) => p.uid === this.execution?.projectUid).key;
      try {
        const response = await executionsService.updateExecution(
          this.$route.params.handle,
          key,
          this.execution?.uid,
          payload
        );
        if (response.status === 200) {
          // Emit the updated execution data instead of just triggering a refetch
          this.$emit('refetchExecution', response.data);
          showSuccessToast(this.$swal, this.$t('success.executionUpdated', { item: 'Execution' }));
        }
      } catch (err) {
        showErrorToast(
          this.$swal,
          this.$t('error.executionUpdateFailed', { item: 'Execution' }),
          {},
          err?.response?.data
        );
      }
    },
  },
};
</script>

<style scoped lang="scss"></style>
