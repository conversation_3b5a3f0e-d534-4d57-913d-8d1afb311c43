<template>
  <v-container
    class="pa-6 white rounded-lg align-start card app-height-global"
    fluid
  >
    <v-row
      v-if="!isBlankTable"
      justify="start"
      align="start"
    >
      <v-col
        cols="7"
        sm="7"
        class="search-bar-style"
      >
        <v-responsive
          v-if="!skeletonLoaderState"
          class="ma-0"
          max-width="344"
        >
          <v-text-field
            v-model="searchTerm"
            class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
            :placeholder="$t('placeHolder.searchByColumn')"
            clearable
            clear-icon="mdi-close-circle"
            height="40"
            background-color="#F9F9FB"
            @input="onSearchInput"
            @click:clear="onSearchClear"
          >
            <template #prepend-inner>
              <SearchIcon />
            </template>
          </v-text-field>
        </v-responsive>
        <v-skeleton-loader
          v-else
          class="rounded-lg mr-3"
          height="40"
          width="344"
          type="button"
        />
        <TestExecutionFilter @filters="applyFilters" />
      </v-col>
      <v-col
        cols="5"
        sm="5"
        class="search-bar-style setting-btn-style"
      >
        <div class="btn-selector">
          <template>
            <div class="text-center">
              <SettingsMenu
                table-type="testExecution"
              />
            </div>
          </template>
        </div>
      </v-col>
      <v-col
        cols="12"
        sm="12"
        class="breadcrumb-container mt-4"
      >
        <v-breadcrumbs
          v-if="!skeletonLoaderState"
          :items="breadCrumbs"
        >
          <template #item="{ item }">
            <v-breadcrumbs-item v-if="isLastItem(item)">
              <b>{{ item.text }}</b>
            </v-breadcrumbs-item>
            <v-breadcrumbs-item v-else>
              <span style="color: #667085">{{ item.text }}</span>
            </v-breadcrumbs-item>
          </template>
        </v-breadcrumbs>
        <v-skeleton-loader
          v-else
          class="rounded-lg pl-3"
          height="24"
          width="250"
          type="text"
        />
      </v-col>
      <v-col
        cols="12"
        sm="12"
      >
        <v-data-table
          v-if="!executionsLoading"
          id="execution-table"
          v-model="selectedRows"
          :headers="filteredHeaders"
          :items="displayTableData"
          item-key="uid"
          show-select
          class="data-table-style table-fixed"
          :item-class="getItemClass"
          :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
          hide-default-footer
          disable-pagination
          @input="handleSelectTestCases"
          @click:row="handleClick"
        >
          <template #[`header.data-table-select`]="{ props, on }">
            <div class="d-flex justify-start align-center">
              <v-checkbox
                id="remember-me-checkbox"
                class="field-theme"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                indeterminate-icon="icon-indeterminate"
                :input-value="props.value"
                :indeterminate="props.indeterminate"
                @change="on.input"
              />
            </div>
          </template>

          <template #[`item.data-table-select`]="{ isSelected, select }">
            <div class="d-flex justify-start align-center">
              <v-checkbox
                id="remember-me-checkbox"
                class="field-theme"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                :input-value="isSelected"
                @change="select"
                @click.stop
              />
            </div>
          </template>

          <template #[`header.actions`]="{ header }">
            <div class="d-none">
              {{ header.text }}
            </div>
          </template>
          <template #[`item.name`]="{ item }">
            <v-tooltip
              bottom
              left
              max-width="485px"
              :disabled="!isTruncated"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <div 
                  :ref="'executionName_' + item.uid"
                  class="custom-attribute text-truncate fw-semibold text-theme-table-text cursor-pointer"
                  v-bind="attrs"
                  v-on="on"
                  @mouseover="checkTruncate(item.uid, 'executionName')"
                >
                  {{ item.name }}
                  <v-tooltip
                    v-if="item.failedSync"
                    bottom
                  >
                    <template #activator="{ on, attrs }">
                      <button
                        class="red--text text--light-1 font-weight-bold"
                        v-bind="attrs"
                        v-on="on"
                        @click.stop="gotoFailedSyncs(item)"
                      >
                        <v-icon class="red--text">
                          mdi-sync-alert
                        </v-icon>{{ $t('integrations.error.syncFailed') }}
                      </button>
                    </template>
                    <div>Last Sync: 03/10/24 17:34:39.</div>
                    <div class="font-weight-bold blue--text">
                      {{ $t('integrations.error.seeFailedSyncs') }}
                    </div>
                  </v-tooltip>
                </div>
              </template>
              <span>{{ item.name }}</span>
            </v-tooltip>
          </template>
          <template #[`item.assignees`]="{ item: assignee }">
            <div class="d-flex align-center">
              <v-select
                v-model="assignee.assigneeObject"
                :items="filteredAssignees"
                :placeholder="getAssigneePlaceholder(assignee)"
                :item-text="item => `${item.firstName} ${item.lastName}`"
                :item-value="item => `${item.uid}`"
                return-object
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true, left: true, maxWidth: '250px !important' }"
                height="20px"
                hide-details
                flat
                class="rounded-lg  pt-0 mt-0 select-assignee font-weight-regular"
                :class="{'disabled-action': isProjectArchived }"
                @click.stop
                @change="updateExecution('assignedTo', assignee.assigneeObject, assignee, assignee.uid)"
              >
                <template #prepend-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-text-field
                        v-model="searchAssignee"
                        class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
                        :placeholder="$t('search')"
                        height="40"
                        background-color="#F9F9FB"
                        hide-details
                      >
                        <template #prepend-inner>
                          <SearchIcon />
                        </template>
                      </v-text-field>
                    </v-list-item-content>
                  </v-list-item>
                </template>

                <template #no-data>
                  <span class="font-weight-regular fs-14px text-theme-label">{{ $t('noMatchingAssignees') }}</span>
                </template>
                <template #item="{ item, attrs, on }">
                  <v-list-item
                    class="mh-36px cursor-pointer"
                    v-bind="attrs"
                    @click="on.click"
                  >
                    <v-list-item-content class="py-0">
                      <v-list-item-title>
                        <span
                          v-bind="attrs"
                          class="font-weight-regular fs-14px text-theme-label"
                        >{{ item.firstName }} {{ item.lastName }}</span>
                      </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-select>
            </div>
          </template>
          <template #[`item.dueAt`]="{ item }">
            <v-menu
              :close-on-content-click="true"
              :nudge-right="40"
              transition="scale-transition"
              offset-y
              min-width="auto"
              @blur="date = dateFormater(item.dueAt)"
              @click.stop.prevent=""
            >
              <template #activator="{ on, attrs }">
                <div
                  class="pointer font-weight-regular"
                  v-on="on"
                >
                  <v-text-field
                    v-if="item.dueAt"
                    :value="dateFormater(item.dueAt)"
                    hint="MM/DD/YYYY"
                    readonly
                    v-bind="attrs"
                  >
                    <template #append>
                      <v-icon style="cursor: pointer">
                        mdi-chevron-down
                      </v-icon>
                    </template>
                  </v-text-field>
                  <div
                    v-else
                    class="d-flex justify-space-between"
                  >
                    <v-icon class="pointer-cursor">
                      mdi-minus
                    </v-icon>
                    <v-icon class="pointer-cursor">
                      mdi-chevron-down
                    </v-icon>
                  </div>
                </div>
              </template>
              <v-date-picker
                v-model="item.dueAt"
                @input="item.dateSelectorMenu = false"
                @change="updateExecution('dueAt', item.dueAt, item, item.uid)"
              />
            </v-menu>
          </template>
          <template #[`item.id`]="{ item }">
            <span class="custom-attribute font-weight-regular text-theme-table-text cursor-pointer">{{ $route.params.key }}-{{ item.uid }}</span>
          </template>

          <template #[`item.priority`]="{ item: priorityItem }">
            <div class="d-flex align-center">
              <v-select
                v-model="priorityItem.priority"
                :items="priorities"
                item-text="name"
                item-value="id"
                :placeholder="$t('choosePriority') + '...'"
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true, left: true, maxWidth: '215px !important' }"
                height="20px"
                hide-details
                flat
                class="rounded-lg pt-0 mt-0 select-assignee font-weight-regular"
                :class="{'disabled-action': isProjectArchived }"
                @click.stop
                @change="(item) => updateExecution('priority', item, priorityItem.priority, priorityItem.uid)"
              >
                <template #selection="{ item }">
                  <span
                    class="fs-14px fw-semibold"
                    :style="{ color: item.color }"
                  >
                    {{ item.name }}
                  </span>
                </template>

                <template #item="{ item, on, attrs }">
                  <v-list-item
                    class="mh-36px cursor-pointer"
                    v-bind="attrs"
                    @click="on.click"
                  >
                    <v-list-item-content>
                      <v-list-item-title
                        class="fs-14px fw-semibold"
                        :style="{ color: item.color }"
                      >
                        {{ item.name }}
                      </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-select>
            </div>
          </template>


          <template #[`item.status`]="{ item: statusItem }">
            <div class="d-flex align-center">
              <v-select
                v-model="statusItem.status"
                :items="statuses"
                :placeholder="$t('chooseStatus') + '...'"
                item-text="name"
                item-value="id"
                append-icon="mdi-chevron-down"
                :menu-props="{ offsetY: true, left: true, maxWidth: '215px !important' }"
                height="20px"
                hide-details
                flat
                class="rounded-lg  pt-0 mt-0 select-assignee font-weight-regular"
                :class="{'disabled-action': isProjectArchived }"
                @click.stop
                @change="(item) => updateExecution('status', item, statusItem.status, statusItem.uid)"
              >
                <template #selection="{ item }">
                  <span
                    class="fs-14px fw-semibold"
                    :style="{ color: item.color }"
                  >
                    {{ item.name }}
                  </span>
                </template>

                <template #item="{ item, on, attrs }">
                  <v-list-item
                    class="mh-36px cursor-pointer"
                    v-bind="attrs"
                    @click="on.click"
                  >
                    <v-list-item-content>
                      <v-list-item-title
                        class="fs-14px fw-semibold"
                        :style="{ color: item.color }"
                      >
                        {{ item.name }}
                      </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-select>
            </div>
          </template>


          <template #[`item.actions`]="{ item }">
            <div class="d-flex flex-row justify-center">
              <v-menu offset-y>
                <template #activator="{ on }">
                  <v-btn
                    v-if="deleteActivity || writeEntity"
                    icon
                    v-on="on"
                  >
                    <v-icon>mdi-dots-vertical</v-icon>
                  </v-btn>
                </template>
                <v-list
                  dense
                  class="text-left"
                >
                  <v-list-item
                    v-if="deleteActivity"
                    :class="{'disabled-action': isProjectArchived }"
                    @click="$parent.$emit('deleteExecution', item.uid)"
                  >
                    <DeleteIcon />
                    <v-list-item-content class="ml-2 fs-14px text-theme-label">
                      {{ $t('delete') }}
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item
                    :class="{'disabled-action': isProjectArchived }"
                    @click="handleRowEdit(item.testCaseUid, item.testRunUid, item.uid)"
                  >
                    <EditIcon />
                    <v-list-item-content class="ml-2 fs-14px text-theme-label">
                      {{ $t('edit') }}
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </template>
        </v-data-table>
        <TestCasesListSkeleton v-else />
        <Pagination
          v-if="showPagination && !executionsLoading && totalItems > 0"
          :page="currentPage"
          :items-per-page="itemsPerPage"
          :total-pages="totalPages"
          :total-items="totalItems"
          @update:pagination="onUpdatePagination"
        />
      </v-col>
    </v-row>

    <v-col
      v-if="actionBtnShow"
      cols="12"
      sm="12"
      class="round-8 action-btn-wrapper px-6 py-4"
    >
      <v-btn
        width="141px"
        color="#F2F4F7"
        full-width
        height="40"
        depressed
        class="text-capitalize btn-theme mr-3"
        elevation="0"
        @click="onCancelAction"
      >
        {{ $t('cancel') }}
      </v-btn>
      <v-menu
        v-model="menuOpen"
        class="rounded-lg"
        width="196px"
        offset-x
        :nudge-top="48"
        top
        :close-on-content-click="false"
      >
        <template #activator="{ on, attrs }">
          <v-btn
            color="primary"
            class="text-capitalize btn-theme"
            height="40"
            width="141px"
            v-bind="attrs"
            :depressed="true"
            v-on="on"
          >
            {{ $t('actions') }}
            <v-icon size="20">
              {{ menuOpen ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
            </v-icon>
          </v-btn>
        </template>
        <v-list class="actions-list font-inter text-left">
          <v-list-item
            :key="1"
            class="action-btn actions-item d-none"
            :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived }"
          >
            <div class="d-flex align-center">
              <img
                src="@/assets/png/pinata.png"
                alt="pinata"
              >
            </div>
            <v-list-item-title class="pl-3">
              {{ $t('runWithPinata') }}
            </v-list-item-title>
          </v-list-item>

          <v-list-item
            :key="2"
            :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived }"
            @click="handleEditClick()"
          >
            <div class="d-flex align-center">
              <EditIcon />
            </div>
            <v-list-item-title class="pl-3">
              {{ $t('edit') }}
            </v-list-item-title>
          </v-list-item>

          <v-menu
            offset-x
            left
            :close-on-content-click="false"
            max-width="196px !important"
            class="rounded-lg"
          >
            <template #activator="{ on, attrs }">
              <v-list-item
                :key="3"
                class="d-flex justify-space-between"
                :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived }"
                v-bind="attrs"
                :close-on-content-click="false"
                dense
                v-on="on"
              >
                <div class="d-flex align-center">
                  <AssignIcon />
                  <v-list-item-title class="pl-4">
                    {{ $t('assignTo') }}
                  </v-list-item-title>
                </div>
                <div class="d-flex align-center justify-end w-full">
                  <ChevronRightIcon />
                </div>
              </v-list-item>
            </template>

            <v-list>
              <v-subheader>
                <v-text-field
                  v-model="searchAssignee"
                  class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
                  :placeholder="$t('search')"
                  height="40"
                  background-color="#F9F9FB"
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchIcon />
                  </template>
                </v-text-field>
              </v-subheader>
              <v-list-item
                v-for="(assignee, index) in filteredAssignees"
                :key="index"
                class="mh-36px"
                @click="assignExecutionsTo(assignee)"
              >
                <span class="font-weight-regular fs-14px text-theme-label">{{ assignee.firstName }} {{ assignee.lastName }}</span>
              </v-list-item>
            </v-list>
          </v-menu>
          <v-menu
            transition="scale-transition"
            offset-x
            left
            :close-on-content-click="false"
            min-width="auto"
            @click.stop.prevent=""
          >
            <template #activator="{ on, attrs }">
              <v-list-item
                :key="3"
                :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived }"
                v-bind="attrs"
                :close-on-content-click="false"
                dense
                v-on="on"
              >
                <div class="d-flex align-center">
                  <AssignIcon />
                  <v-list-item-title class="pl-4">
                    {{ $t('testruns.changeDueDate') }}
                  </v-list-item-title>
                </div>
                <div class="d-flex align-center justify-end w-full">
                  <ChevronRightIcon />
                </div>
              </v-list-item>
            </template>
            <v-list>
              <v-date-picker
                @change="changeExecutionsDueDateTo($event)"
              />
            </v-list>
          </v-menu>
          <v-list-item
            :key="4"
            :class="{'action-btn actions-item': true, 'disabled-action': isProjectArchived }"
          >
            <div class="d-flex align-center">
              <DeleteIcon />
            </div>
            <v-list-item-title class="pl-3 error--text">
              {{ $t('remove') }}
            </v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </v-col>

    <EditRunDialog
      :is-open="isOpenEditRunDialog"
      :assignees="filteredAssignees"
      @closeDialog="handleCloseEditDialog"
    />
    <ExportDialog
      :is-open="isOpenExportDialog"
      @closeDialog="handleCloseExportDialog"
    />
    <ExecutionConfirmDialog
      :is-open="isOpenConfirmDialog"
      @closeDialog="handleCloseConfirmDialog"
      @confirm="handleConfirm"
    />
    <ConfirmBulkDeleteDialog
      :title="$t('testruns.test_case.bulk_remove.title', { count: selectedRows.length })"
      :is-open="isOpenConfirmBulkDeleteDialog"
      @closeDialog="handleCloseConfirmBulkDeleteDialog"
      @confirm="confirmBulkRemove"
    />
  </v-container>
</template>

<script>
import { debounce } from 'lodash';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import TestExecutionFilter from "@/components/Execution/ExecutionFilter.vue";
import EditRunDialog from '@/components/TestRuns/EditRunDialog.vue';
import ExportDialog from '@/views/Tests/Case/Components/ExportDialog.vue';
import ExecutionConfirmDialog from '@/views/Tests/Case/Components/ConfirmDialog.vue';
import ConfirmBulkDeleteDialog from '@/views/Tests/Case/Components/ConfirmBulkDeleteDialog.vue';
import SearchIcon from '@/assets/svg/search-icon.svg';
import makeCasesService from '@/services/api/case';
import makeTemplateService from '@/services/api/template';
import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import AssignIcon from '@/assets/svg/assign.svg';
import ChevronRightIcon from '@/assets/svg/chevron-right.svg';
import Pagination from '@/components/base/Pagination.vue';
import { createNamespacedHelpers, mapGetters, mapActions } from 'vuex';
import projectStatus from '@/mixins/projectStatus';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import handleLoading from '@/mixins/loader.js'
import TestCasesListSkeleton from '@/components/Skeletons/TestCases/List.vue';
import { entityTypeNames,entityTypes } from '@/constants/templates';
import { showErrorToast } from '@/utils/toast';
import dayjs from 'dayjs';
import { useTestExecutionList } from '@/composables/modules/testRun/executions.js';

const { mapState} = createNamespacedHelpers('user');
let caseService;

export default {
  components: {
    TestExecutionFilter,
    ExportDialog,
    EditRunDialog,
    ExecutionConfirmDialog,
    SearchIcon,
    SettingsMenu,
    ConfirmBulkDeleteDialog,
    DeleteIcon,
    EditIcon,
    AssignIcon,
    ChevronRightIcon,
    TestCasesListSkeleton,
    Pagination
  },
  mixins: [projectStatus, colorPreferencesMixin, handleLoading],
  props: {
    isRepository: Boolean,
    caseItems: Array,
    breadCrumbs: [],
    allowAction: {
      type: Boolean,
      default: true
    },
    assignees:{
      type: Array,
    },
    execution: {
      type: Object
    },
    isDetailCollapsed:{
      type: Boolean,
      default: false
    },
    deleteActivity: {
      type: Boolean,
      default: false
    },
    writeEntity: {
      type: Boolean,
      default: false
    },
    totalItems: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    },
    itemsPerPage: {
      type: Number,
      default: 10
    },
    executionsLoading: {
      type: Boolean,
      default: false
    },
    showPagination: {
      type: Boolean,
      default: true
    },
  },
  setup() {
    // drag-and-drop initialized in mounted with component instance
  },
  data()
  {
    return {
      localPositionsOverride: {},
      // test state for blank page
      isBlankTable: false,
      // test state for blank page
      menuOpen: false,
      isOpenEditRunDialog: false,
      isOpenExportDialog: false,
      isOpenConfirmDialog: false,
      isOpenConfirmBulkDeleteDialog: false,
      isSelectedData: false,
      isAddedTable: false,
      openFilterDrawer: true,
      toggleSelect: false,
      testName: '',
      testTemplate: '',
      selectedRows: [],
      headers: [],
      statuses: [],
      priorities: [],
      searchTerm: '',
      searchAssignee: '',
      filters: null,
      actionBtnShow: false,
      selectedData: [],
      states: [{ text: 'High' }, { text: 'Medium' }, { text: 'Low' }],
      tags: [
        { value: 'test', text: '#test' },
        { value: 'billing', text: '#billing' },
        { value: 'performance', text: '#performance' },
        { value: 'navigation', text: '#navigation' },
      ],
      testTemplates: [],
      templatesGrouped: {
        testCase: [],
        testResult: [],
      },
      isTruncated: false,
      entityTypeNames: entityTypeNames,
      entityTypes: entityTypes,
    };
  },
  computed: {
    ...mapState([ 'currentAccount','user']),
    ...mapGetters({
       currentAccount: 'user/currentAccount',
       dynamicHeaders:'headers/dynamicHeaders'
    }),
    activeRow:{
      get() {
        return this.execution;
      }, set(value){
        this.$emit('update:execution', value);
      }
    },
    filteredAssignees() {
      if (!this.searchAssignee) {
        return this.assignees;
      }
      const searchTermLower = this.searchAssignee.toLowerCase();
      return this.assignees.filter(assignee =>
        assignee.firstName.toLowerCase().includes(searchTermLower) ||
        assignee.lastName.toLowerCase().includes(searchTermLower)
      )
    },
    filteredMenuHeaders() {
      const filtered = this.headers.filter((header) => header.value != 'actions');
      return filtered;
    },
    filteredHeaders() {
      const filtered = this.headers.filter((header) => header.checked);
      return filtered;
    },
    displayTableData() {
      const filtered = this.caseItems.filter((item) => {

        if(this.searchTerm?.length && !item.name.toLowerCase().includes(this.searchTerm.toLowerCase()))
          return false

        if(this.filters && (this.filters.priorities.length || this.filters.tags.length)){
            if(this.filters.priorities && this.filters.priorities.length && !this.filters.priorities.includes(item.priority))
              return false

            if(this.filters.tags && this.filters.tags.length){
              let tagExists = false;
              for(const tag of item.caseFields.tags)
                if(this.filters.tags.includes(tag))
                  tagExists = true;

                return tagExists
              }
          }
          // Check for assignedTo in main field first, then fallback to customFields
          let assignee = null;
          if (item?.assignedTo) {
            assignee = this.assignees.find(a => a?.uid === item?.assignedTo || a?.uid === item?.assignedTo?.uid);
          }

          item.assigneeObject = {
            firstName: assignee?.firstName || '',
            lastName: assignee?.lastName || '',
            uid: assignee?.uid || '',
            // Store custom assignee info for display purposes only
            customAssigneeName: item?.customFields?.assignee?.name || null
          };
          return item
        });
      // Sort by effective position (override > original)
      const effectivePosition = (it) =>
        (this.localPositionsOverride && this.localPositionsOverride[it.uid] != null)
          ? this.localPositionsOverride[it.uid]
          : (it.position ?? 0);
      return filtered.sort((a, b) => effectivePosition(a) - effectivePosition(b));
    },
    totalPages() {
      return Math.ceil(this.totalItems / this.itemsPerPage);
    },
  },
  created() {
    if(!this.dynamicHeaders.testExecution) {
      this.initializeHeaders({ type: 'testExecution' });
    }
    this.headers = this.dynamicHeaders.testExecution;
    caseService = makeCasesService(this.$api);

    this.priorities = this.getPriorities("testCase");
    this.statuses = this.getStatuses("testExecution");

    // Load templates and set default template
    this.init();
    this.debouncedSearch = debounce(this.applySearch, 500)
  },

  async mounted() {
    const { addGlobalDragListeners } = useTestExecutionList(this.$props, null, this);
    if (addGlobalDragListeners) addGlobalDragListeners();
    this.$on('local-positions-override', (mapping) => {
      this.localPositionsOverride = {
        ...this.localPositionsOverride,
        ...(mapping || {})
      };
    });
  },
  beforeDestroy() {
    this.$off('local-positions-override');
  },
  methods: {
    ...mapActions("headers", ['initializeHeaders']),
    async init(){
      try {
        const entityTypeKeys = Object.keys(this.entityTypeNames);
        const templatePromises = entityTypeKeys.map(entityType => 
          this.loadTemplates(entityType)
        );

        const allResults = await Promise.allSettled([
          ...templatePromises,
        ]);

        const templateResults = allResults.slice(0, entityTypeKeys.length);
        templateResults.forEach((result, index) => {
          if (result.status === 'rejected') {
            console.error(`Failed to fetch templates for ${entityTypeKeys[index]}:`, result.reason);
            showErrorToast(this.$swal, 'fetchError', {
              item: `${entityTypeKeys[index]} templates`
            }, result.reason?.response?.data);
          }
        });
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'templates' }, error?.response?.data);
      } 
    },
    dateFormater(date) {
      return dayjs(date).format('MM/DD/YY');
    },
    async loadTemplates(entityType) {
      const searchParams = new URLSearchParams();
      searchParams.set('current_page', 1);
      searchParams.set('per_page', 9999);
      searchParams.set('entityType', entityType)
      try {
        const templateService = makeTemplateService(this.$api);
        const response = await templateService.getTemplates(
          this.$route.params.handle,
          this.$route.params.key,
          searchParams.toString()
        );
        
        this.templatesGrouped[entityType] = response.data?.templates || [];
        this.testTemplates.push(...this.templatesGrouped[entityType]);

        // Find and set the default template if available
        const defaultTemplate = this.testTemplates.find(template => template.isDefault === true);
        if (defaultTemplate) {
          this.testTemplate = defaultTemplate.uid;
        } else if (this.testTemplates.length > 0) {
          // If no default template is found, use the first one
          this.testTemplate = this.testTemplates[0].uid;
        }
      } catch (err) {
        console.error('Error loading templates:', err);
      }
    },
    updateExecution(property, value, item, selectedExecutionId) {
      const updatedItem = {
      ...item,
      uid: item?.uid || item?.assignedTo?.uid,
      value:
        property === 'assignedTo'
        ? item?.assignedTo?.uid
            : value,
      };

      const payload = {
        property,
        value,
        item: updatedItem,
        selectedExecutionId,
      };

      this.$emit('updateExecution', payload);
    },
    handleCloseConfirmBulkDeleteDialog()
    {
      this.isOpenConfirmBulkDeleteDialog = false;
    },
    handleCloseConfirmDialog()
    {
      this.isOpenConfirmDialog = false;
    },
    handleConfirm()
    {
      this.isOpenConfirmDialog = false;
    },
    handleEditClick()
    {
      this.isOpenEditRunDialog = true;
    },
    handleCloseEditDialog()
    {
      this.isOpenEditRunDialog = false;
    },
    handleExportClick()
    {
      this.isOpenExportDialog = true;
    },
    handleCloseExportDialog()
    {
      this.isOpenExportDialog = false;
    },
    isLastItem(item)
    {
      return item.text == this.breadCrumbs[this.breadCrumbs.length - 1].text;
    },
    assignExecutionsTo(assignee)
    {
      let payload = {
        assignedTo : assignee.uid
      }
      this.$emit('updateExecutions', payload)
    },
    changeExecutionsDueDateTo(dueDate)
    {
      let payload = {
        dueAt : dueDate
      }
      this.$emit('updateExecutions', payload)
    },

    getColor(priority)
    {
      if (priority == 'High') {
        return 'text-red';
      } else if (priority == 'Medium') {
        return 'text-yellow';
      } else {
        return 'text-green';
      }
    },
    onCancelAction() {
      this.selectedRows = [];
    },
    applyFilters(filters){
      this.filters = filters;
      this.$emit('applyFilters', filters);
    },
    handleSelectTestCases()
    {
      if (this.selectedRows.length > 0) {
        this.actionBtnShow = true;
      } else {
        this.actionBtnShow = false;
      }
      this.$emit('selectedExecutions', this.selectedRows)
    },
    handleClick(row)
    {
      this.$emit('expandDetail', row);
    },
    getItemClass(item) {
      const activeClass = this.activeRow && item.uid === this.activeRow.uid ? 'active-row' : '';
      const uidClass = `uid-${item.uid}`;
      return `${activeClass} ${uidClass}`.trim();
    },
    addSelectedCases()
    {
      this.selectedData = this.selectedRows;
      this.isSelectedData = true;
      this.isAddedTable = true;
    },
    removeSelectedCases()
    {
      this.isAddedTable = false;
    },
    // quick create test case function
    async quickCreate()
    {

      const payload = {
        name: this.testName,
        parentId: this.$route.params.folderUid || null,
        customFields: {
          tags: [],
          priority: this.priorities[this.priorities.length - 1].value,
        },
      };
      this.$emit('createCase', payload);
      this.testName = '';
      // Keep the template selected for next creation
      // We don't reset testTemplate.value here to maintain the default template selection
    },
    // delete Test case from uid
    async deleteTestCase(uid)
    {
      this.$parent.$emit('caseRemove', uid)
    },
    async handleBulkRemove()
    {
      if (this.selectedRows.length > 0) {
        this.isOpenConfirmBulkDeleteDialog = true;
      }
    },

    async confirmBulkRemove()
    {
      const itemUIDs = this.selectedRows.map((row) => row.uid);
      const payload = {
        ids: itemUIDs,
      };
      this.confirmDialogOpen = false;
      this.$parent.$emit('bulkRemove', payload);
      this.isOpenConfirmBulkDeleteDialog = false;
    },

    async updateCases(selectedPriority, selectedTag)
    {
      const handle = this.currentAccount.handle;
      try {
        const payload = {
          name: this.selectedRows[0].name,
          custom_fields: {
            tags: selectedTag,
            priority: selectedPriority,
          },
        };
        const response = await caseService.updateTestCase(
          handle,
          this.$route.params.key,
          this.selectedRows[0].uid,
          payload
        );
        if (response.status == 200) {
          console.log('Case item updated successfully!');
        } else {
          console.error('Error occured in backend!');
        }
      } catch (err) {
        console.log('Error: ', err);
      }
    },
    handleRowEdit(caseId, testRunUid, ExecutionId)
    {
      this.$router.push({ name: 'EditTestCases', params: { uid: caseId }, query: {redirectTo: 'Runs', runId: testRunUid, executionId: ExecutionId, isExecution: true}});
      // TODO - pass this item via state to avoid extra API calls
      //let caseItem;
      //for (let item in this.caseItems) {
      //  if (this.caseItems[item].uid == uid) {
      //    caseItem = this.caseItems[item]
      //  }
      //}
      //this.$router.push({ name: 'EditTestCases', params: { uid: caseItem } })
    },
    onUpdatePagination(options) {
      this.$emit('update-pagination', options);
    },
    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    },
    getAssigneePlaceholder(assignee) {
      // If no regular assignee but we have custom assignee name, show it as placeholder
      if (!assignee.assigneeObject?.uid && assignee.assigneeObject?.customAssigneeName) {
        return assignee.assigneeObject.customAssigneeName;
      }
      return this.$t('unassign') + '...';
    },
    gotoFailedSyncs(item) {
      this.$router.push({
        name: 'TestRunCaseFailedSync',
        params: {
          key: this.$route.params.key,
          handle: this.$route.params.handle,
          uid: item.uid,
        },
      });
    },
    onSearchInput (value) {
      this.debouncedSearch(value);
    },
    applySearch(value){
      this.$emit('applySearch', value);
    },
    onSearchClear(){
      this.debouncedSearch.cancel(); 
      this.applySearch('');
    }
  },
};
</script>
<style scoped>
.color-red {
  color: #f2284e !important;
}

.f-color-red {
  color: #f2284e !important;
}

.round-8 {
  border-radius: 8px;
}

.round-6 {
  border-radius: 6px;
}

.h-40 {
  height: 40px !important;
}

.btn-selector {
  position: relative;
}

.modal-main-area {
  height: 100%;
  padding: 32px 32px 32px 32px;
}

.dialog-title {
  font-weight: 900 !important;
}

.filter-dialog {
  padding-top: 15px;
}

.dialog-action {
  width: 90%;
  display: flex;
  position: absolute;
  bottom: 25px;
}

.btn-selector .selector-wrapper {
  position: relative;
}

.selector-style {
  position: absolute;
  right: 0;
  left: unset;
  top: 30px;
  min-width: 240px;
}

.modal-btn {
  width: 45%;
}

.f-color-white {
  color: white !important;
}

.text-red {
  color: #ef5350;
}

.text-green {
  color: #66bb6a;
}

.text-yellow {
  color: #ffa726;
}

.align-start {
  align-items: baseline !important;
  font-family: Inter !important;
}

.search-box-style {
  padding-top: 0;
  border-radius: 8px;
}

.search-bar-style {
  display: flex;
  padding-bottom: 0;
  justify-content: flex-start
}

.setting-btn-style {
  display: flex;
  justify-content: flex-end;
}

.setting-btn {
  position: absolute;
  right: 10px;
  width: 40px !important;
  min-width: 40px !important;
}

.breadcrumb-container {
  padding: 0;
}

.breadcrumb-container ul {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 15px;
}

.create-btn {
  display: flex;
  justify-content: space-between;
  padding-top: 0;
  padding-bottom: 0;
}

.create-btn button {
  color: white !important;
  margin-top: 10px;
}

.bottom-input-style {
  margin-top: 0;
  border-radius: 5px;
  margin-right: 10px;
}

.bottom-input-style .v-text-field__slot {
  padding-left: 10px;
}

.bottom-input-style .v-select__selections {
  padding-left: 10px;
}

.data-table-style {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
}

.data-table-style tbody tr {
  height: 50px !important;
  line-height: 0% !important;
  min-height: 50px;
  cursor: pointer;
}

.data-table-style tbody tr:hover,
.data-table-style tbody tr:hover:not(.v-data-table__expanded__content) {
  background-color: #d0d5dd !important;
}

.v-input__prepend-inner {
  padding-left: 10px;
}

.v-list-item__content {
  text-align: start;
}

.v-breadcrumbs__item .normal-font-color {
  color: rgb(93, 101, 121) !important;
  color: red;
}

.search-field .v-input__slot {
  display: flex;
  align-items: center !important;
}

.search-field .v-input__prepend-inner {
  align-self: center;
  margin-top: 0 !important;
  padding-left: 0px;
  padding-right: 8px !important;
}

.text-field .v-input__slot {
  background-color: #f9f9fb !important;
}

.btn-restore {
  width: 100%;
  font-family: Inter;
  font-size: 12px;
  font-weight: 600;
  line-height: 18px;
  text-align: left;
  cursor: pointer;
}

.menu-header {
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  text-align: left;
}

.absolute {
  position: absolute;
}

.bottom-right {
  display: flex;
  justify-content: right;
  right: 24px;
  bottom: 16px;
}

.f-color-blue {
  color: #0c2ff3;
}

.action-btn .v-list-item__title {
  display: flex;
  justify-content: flex-start;
}

.h-100 {
  height: 100%;
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.block {
  display: block;
}

h2.blank-title {
  text-align: center;
}

p.blank-description {
  max-width: 500px;
  text-align: center;
}

.none {
  display: none;
}

.custom-attribute {
  white-space: nowrap;
}

.action-btn-wrapper {
  position: sticky;
    bottom: 0;
    background-color: white;
    align-items: flex-end;
    display: flex;
    justify-content: flex-end;
    z-index: 8;
}

.v-data-table tbody tr.dragging {
  opacity: 0.5;
  cursor: move;
}
</style>
<style>
/* Drop indicators for reordering */
.v-data-table tbody tr.drop-above {
  border-top: 3px solid #1976d2;
}

.v-data-table tbody tr.drop-below {
  border-bottom: 3px solid #1976d2;
}

</style>