<template>
  <div class="drag-indicator">
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.3307 8.33203V4.53203C13.3307 3.41193 13.3307 2.85187 13.1127 2.42405C12.921 2.04773 12.615 1.74176 12.2387 1.55002C11.8109 1.33203 11.2508 1.33203 10.1307 1.33203H5.86406C4.74396 1.33203 4.1839 1.33203 3.75608 1.55002C3.37976 1.74176 3.0738 2.04773 2.88205 2.42405C2.66406 2.85187 2.66406 3.41193 2.66406 4.53203V11.4654C2.66406 12.5855 2.66406 13.1455 2.88205 13.5733C3.0738 13.9497 3.37976 14.2556 3.75608 14.4474C4.1839 14.6654 4.74396 14.6654 5.86406 14.6654H7.9974M9.33073 7.33203H5.33073M6.66406 9.9987H5.33073M10.6641 4.66536H5.33073M9.66406 12.6654L10.9974 13.9987L13.9974 10.9987"
        stroke="#344054"
        stroke-width="1.2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>

    {{ count }} test Execution{{ count !== 1 ? 's' : '' }}
  </div>
</template>

<script>
export default {
  name: 'DragIndicator',
  props: {
    count: {
      type: Number,
      default: 1
    }
  }
}
</script>

<style scoped>
.drag-indicator {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  color: #344054;
  padding: 6px 8px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  pointer-events: none;
}
svg {
  margin-right: 8px;
}
</style> 