<template>
  <div>
    <v-sheet
      v-if="!skeletonLoaderState"
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40px"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-3 py-2 d-flex flex-row">{{ $t('filters') }} <v-icon
        size="16px"
        class="ml-2"
      >mdi-filter-variant</v-icon></span>
    </v-sheet>
    <v-skeleton-loader
      v-else
      class="rounded-lg"
      height="40"
      width="95"
      type="button"
    />
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('filters') }}
            </h2>
            <v-btn
              icon
              @click="showDialog = false"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <v-expansion-panels
            v-model="rolesPanel"
            flat
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                {{ $t('priority') }}
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div
                  v-for="(priority, index) in priorities"
                  :key="index"
                >
                  <v-checkbox
                    v-model="priority.selected"
                    :value="priority.selected"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ priority.name }}</span>
                    </template>
                  </v-checkbox>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
          
          <v-expansion-panels
            v-model="statusPanel"
            flat
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('testruns.create_testrun.status') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-checkbox
                  v-for="(status,index) in statuses"
                  :key="index"
                  v-model="status.selected"
                  :value="status.selected"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :hide-details="true"
                >
                  <template #label>
                    <span class="fs-14px text-theme-label">{{ status.name }}</span>
                  </template>
                </v-checkbox>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>

          <v-expansion-panels
            v-model="tagsPanel"
            flat
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                {{ $t('assignedTo') }}
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-text-field
                  v-model="searchTerm"
                  class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
                  :placeholder="$t('search')"
                  height="40"
                  clearable
                  background-color="#F9F9FB"
                  :hide-details="true"
                  @input="debouncedSearch"
                  @click:clear="onSearchClear"
                >
                  <template #prepend-inner>
                    <SearchIcon />
                  </template>
                </v-text-field>
                <div
                  v-for="(user, index) in userList"
                  :key="index"
                >
                  <v-checkbox
                    :value="isUserSelected(user)"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                    @change="toggleUserSelection(user)"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ `#${user.username}` }}</span>
                    </template>
                  </v-checkbox>
                </div>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          full-width
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="clearFilters"
        >
          {{ $t('clearAll') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          full-width
          elevation="0"
          @click="apply"
        >
          {{ $t('apply') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import { debounce } from 'lodash';
import SearchIcon from '@/assets/svg/search-icon.svg';
import makeUsersService from '@/services/api/user';
import makeTagService from '@/services/api/tag';
import { showErrorToast } from '@/utils/toast';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import handleLoading from '@/mixins/loader.js'

export default {
  name: 'TestExecutionFilter',
  components: {
    SearchIcon
  },
  mixins: [colorPreferencesMixin, handleLoading],
  props: {
    currentFilters: {
      type: Object,
      default: () => ({
        priorities: [],
        tags: []
      })
    }
  },
  data() {
    return {
      rolesPanel: 0,
      projectsPanel: 0,
      tagsPanel: 0,
      rolesMenu: false,
      projectsMenu: false,
      tagsMenu: false,
      showDialog: false,
      priorities: [],
      statusPanel: 0,
      searchTerm: '',
      tags: [],
      selectedTags: [],
      selectedUsers: [],
      statuses: [],
      userList: [],
    };
  },
  computed: { 
    searchedTags(){
      return this.searchTerm.length ? this.tags.filter(tag => tag.name.toLowerCase().includes(this.searchTerm.toLowerCase())) : this.tags
    }
  },

  watch: {
    currentFilters: {
      handler(newFilters) {
        this.syncWithFilters(newFilters);
      },
      deep: true,
      immediate: true
    }
  },
  created(){
    this.debouncedSearch = debounce(this.searchUser, 500)
  },
  async mounted() {
    await this.getUserList('');
    await this.fetchTags();
    this.priorities = this.getPriorities('testCase');
    this.statuses = this.getStatuses('testCase');
    
    this.syncWithFilters(this.currentFilters);
  },

  methods: {
    syncWithFilters(filters) {
      if (this.priorities.length > 0) {
        this.priorities = this.priorities.map(priority => ({
          ...priority,
          selected: false
        }));
        
        if (filters.priorities && filters.priorities.length > 0) {
          this.priorities = this.priorities.map(priority => ({
            ...priority,
            selected: filters.priorities.includes(priority.id)
          }));
        }
      }
      
      this.selectedTags = [];
      
      if (filters.tags && filters.tags.length > 0 && this.tags.length > 0) {
        this.selectedTags = this.tags.filter(tag => 
          filters.tags.includes(tag.name)
        );
      }
    },
    
    apply() {
      const selectedPriorities = this.priorities.filter(priority => priority.selected);
      const selectedStatuses = this.statuses.filter(status => status.selected);
      const filters = {
        priorities: selectedPriorities.map(item => item.id),
        statuses: selectedStatuses.map(item => item.id),
        users: this.selectedUsers.map(user => user.uid),
        tags: this.selectedTags.map(item => item.name),
        tagUids: this.selectedTags.map(item => item.uid), // Add tag UIDs for server-side filtering
        tagObjects: this.selectedTags // Store full tag objects for easier manipulation
      };
      this.$emit('filters', filters);
      this.showDialog = false;
    },

    clearFilters() {
      this.$nextTick(() => {
        this.priorities = this.priorities.map(priority => ({ ...priority, selected: false }));
        this.selectedTags = [];
        this.searchTerm = '';
        
        this.$emit('filters', { priorities: [], tags: [], tagUids: [], tagObjects: [] });
      });
    },

    isSelected(tag) {
      if(this.selectedTags.length === 0) return false;
      return this.selectedTags.some(selectedTag => selectedTag.uid === tag.uid);
    },
    isUserSelected(tag) {
      if(this.selectedUsers.length === 0) return false;
      return this.selectedUsers.some(selectedUser => selectedUser.uid === tag.uid);
    },

    toggleTagSelection(tag) {
      if (this.isSelected(tag)) {
        this.selectedTags = this.selectedTags.filter(
          selectedTag => selectedTag.uid !== tag.uid
        );
      } else {
        this.selectedTags.push(tag);
      }
    },
    toggleUserSelection(user) {
      if (this.isSelected(user)) {
        this.selectedUsers = this.selectedUsers.filter(
          selectedUser => selectedUser.uid !== user.uid
        );
      } else {
        this.selectedUsers.push(user);
      }
    },

    async fetchTags() {
      try {
        const tagService = makeTagService(this.$api);
        const response = await tagService.getTags(this.$route.params.handle, 'cases');
        this.tags = response.data;
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'tags' }, error?.response?.data);
      }
    },
    async getUserList(search = '') {
      const userService = makeUsersService(this.$api);
      try {
        const response = await userService.searchUsers(`%${search}%`);
        this.userList = response.data;
      } catch (err) {
        showErrorToast(this.$swal, this.$t('error.failedToFetchUser'), {}, err?.response?.data);
      }
    },
    async searchUser(val){
        await this.getUserList(val);
    },
    async onSearchClear(){
        await this.getUserList();
    }
  }
};
</script>

