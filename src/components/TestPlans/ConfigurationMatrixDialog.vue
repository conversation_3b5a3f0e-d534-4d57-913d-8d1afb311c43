<template>
  <v-dialog
    v-model="dialog"
    :max-width="560"
  >
    <v-card class="bg-white">
      <v-card-text class="py-8 px-sm-10">
        <v-flex class="d-flex justify-space-between align-center">
          <p
            class="ma-0 font-weight-bold text-h6 text-sm-h5 text-start text-theme-label"
          >
            {{ title }}
          </p>
          <v-icon
            class="mt-1 ml-4 pointer"
            color="#0c111d"
            @click="$emit('close')"
          >
            mdi-close
          </v-icon>
        </v-flex>

        <slot name="content">
          <v-row class="mt-2">
            <v-col cols="6 pr-2">
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('vertical') }}
                </v-label>
              </div>
              <v-select
                v-model="verticalValue"
                type="text"
                dense
                background-color="#F9F9FB"
                :placeholder="$t('testruns.selectPriority')"
                class="rounded-lg field-theme custom-prepend mh-38px"
                hide-details
                append-icon="mdi-chevron-down"
                :items="availableVerticalOptions"
                clear-icon="body-2"
                item-text="name"
                item-value="name"
                :menu-props="{ offsetY: true }"
                return-object
                @change="updateVertical"
              >
                <template #append-item>
                  <div align="center">
                    <v-progress-circular
                      v-if="loadingConfigurations"
                      indeterminate
                      color="primary"
                    />
                  </div>
                  <div
                    v-intersect="{
                      handler: onIntersect,
                      options: {
                        threshold: [0, 0.5, 1.0]
                      }
                    }"  
                  />
                </template>
              </v-select>
            </v-col>
            <v-col cols="6 pl-2">
              <div class="text-left">
                <v-label class="fs-14px text-theme-label font-weight-medium">
                  {{ $t('horizontal') }}
                </v-label>
              </div>
              <v-select
                v-model="horizontalValue"
                type="text"
                dense
                background-color="#F9F9FB"
                :placeholder="$t('testruns.selectPriority')"
                class="rounded-lg field-theme custom-prepend mh-38px"
                hide-details
                append-icon="mdi-chevron-down"
                :items="availableHorizontalOptions"
                clear-icon="body-2"
                item-text="name"
                item-value="name"
                :menu-props="{ offsetY: true }"
                return-object
                @change="updateHorizontal"
              >
                <template #append-item>
                  <div align="center">
                    <v-progress-circular
                      v-if="loadingConfigurations"
                      indeterminate
                      color="primary"
                    />
                  </div>
                  <div
                    v-intersect="{
                      handler: onIntersect,
                      options: {
                        threshold: [0, 0.5, 1.0]
                      }
                    }"  
                  />
                </template>
              </v-select>
            </v-col>
          </v-row>

          <div class="matrix-scroll-container my-5">
            <div class="d-flex flex-row mt-5 align-end vertical-header-container">
              <div class="label-divider" />
              <div
                v-for="(horizontal, horizontalIndex) in horizontalValue?.options"
                :key="horizontalIndex"
                class="d-flex flex-column align-center justify-end vertical-header"
                :class="{ highlight: hoveredColumn === horizontalIndex }"
              >
                <div class="vertical-label text-truncate">
                  {{ horizontal.name }}
                </div>
              </div>
            </div>

            <div
              v-for="(vertical, verticalIndex) in verticalValue?.options"
              :key="verticalIndex"
              class="d-flex flex-row align-center mt-2"
            >
              <div 
                class="horizontal-label text-truncate"
                :class="{ highlight: hoveredRow === verticalIndex }"
              >
                {{ vertical.name }}
              </div>

              <div
                v-for="(horizontal, horizontalIndex) in horizontalValue?.options"
                :key="horizontalIndex"
                class="d-flex align-center justify-center checkbox-cell"
                @mouseover="handleMouseOver(horizontalIndex, verticalIndex)"
                @mouseleave="handleMouseLeave"
              >
                <v-checkbox
                  hide-details
                  :value="isChecked(horizontal.uid, vertical.uid)"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  class="ml-2 ma-0 pa-0 field-theme checkbox-matrix"
                  @change="toggleSelection(horizontal, vertical)"
                />
              </div>
            </div>
          </div>
        </slot>
        <slot name="footer">
          <v-row>
            <v-col cols="6 pr-2">
              <v-btn
                depressed
                color="#F2F4F7"
                height="40"
                class="text-capitalize btn-theme"
                elevation="0"
                width="100%"
                @click="$emit('close')"
              >
                {{ $t('projects.create_project.close_dialog.cancel_button') }}
              </v-btn>
            </v-col>
            <v-col cols="6 pl-2">
              <v-btn
                class="btn-theme"
                height="40"
                color="primary"
                depressed
                elevation="0"
                :disabled="Object.keys(matrix).length === 0"
                width="100%"
                @click="$emit('handleConfirmClick', matrix)"
              >
                {{ btn_label }}
              </v-btn>
            </v-col>
          </v-row>
        </slot>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: {
    value: Boolean,
    loadingConfigurations: Boolean,
    title: {
      type: String,
    },
    color: {
      type: String,
    },
    btn_label: {
      type:String
    },
    configurations: {
      type: Array,
      default: () => [],
    },
    initialMatrix: {
      type: Object,
      default: () => ({})
    },
    selectedConfigurationMatrix: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialog: this.value,
      verticalValue: null,
      horizontalValue: null,
      matrix: {},
      hoveredRow: null,
      hoveredColumn: null,
    };
  },
  computed: {
    configurationData() {
      return this.configurations;
    },
    availableVerticalOptions() {
      return this.configurations.filter(config => 
        !this.horizontalValue || config.uid !== this.horizontalValue.uid
      );
    },
    availableHorizontalOptions() {
      return this.configurations.filter(config => 
        !this.verticalValue || config.uid !== this.verticalValue.uid
      );
    },
    configurationsHasData() {
        return this.configurations.length > 0;
    },
  },
  watch: {
    value(newVal) {
      this.dialog = newVal;
    },
    dialog(newVal) {
      this.$emit('input', newVal);
    },
    selectedConfigurationMatrix: {
      immediate: true,
      handler(newVal) {
        if (Object.keys(newVal).length > 0) {
          this.matrix = { ...newVal };
          
          // Find and set vertical and horizontal values based on matrix keys
          const [firstKey, secondKey] = Object.keys(newVal);
          
          if (firstKey && secondKey) {
            const vertical = this.configurations.find(config => config.uid === firstKey);
            const horizontal = this.configurations.find(config => config.uid === secondKey);
            
            if (vertical && horizontal) {
              this.verticalValue = vertical;
              this.horizontalValue = horizontal;
            }
          }
        }
      }
    }
  },
  methods: {
    updateVertical(value) {
      this.verticalValue = value;
      if (this.horizontalValue?.uid === value?.uid) {
        this.horizontalValue = null;
      }
      // Only reset matrix if no pre-filled data
      if (Object.keys(this.selectedConfigurationMatrix).length === 0) {
        this.matrix = {};
      }
    },
    
    updateHorizontal(value) {
      this.horizontalValue = value;
      if (this.verticalValue?.uid === value?.uid) {
        this.verticalValue = null;
      }
      // Only reset matrix if no pre-filled data
      if (Object.keys(this.selectedConfigurationMatrix).length === 0) {
        this.matrix = {};
      }
    },

    isChecked(horizontalUid, verticalUid) {
      return this.matrix[horizontalUid] && this.matrix[horizontalUid][verticalUid] === true;
    },

    toggleSelection(horizontal, vertical) {
      const horizontalUid = horizontal.uid;
      const verticalUid = vertical.uid;
      if (!this.matrix[horizontalUid]) {
        this.$set(this.matrix, horizontalUid, { [verticalUid]: true });
      } else {
        if (this.matrix[horizontalUid][verticalUid]) {
          this.$delete(this.matrix[horizontalUid], verticalUid);
        } else {
          this.$set(this.matrix[horizontalUid], verticalUid, true);
        }
        if (Object.keys(this.matrix[horizontalUid]).length === 0) {
          this.$delete(this.matrix, horizontalUid);
        }
      }
      this.$emit("update:matrix", this.matrix);
    },

    handleMouseOver(horizontalId, verticalId) {
      this.hoveredRow = verticalId;
      this.hoveredColumn = horizontalId;
    },
    handleMouseLeave() {
      this.hoveredRow = null;
      this.hoveredColumn = null;
    },
    onIntersect(entries){
      if (entries[0].isIntersecting && entries[0].intersectionRatio === 1){
        if (!this.configurationsHasData) return;
        this.$emit('fetchMoreConfigurations');
      }
    }
  },
};
</script>


<style scoped>
.matrix-scroll-container {
  max-height: 300px;
  max-width: 100%;
  overflow: auto; 
}

.horizontal-label-col {
  max-width: 125px;
  white-space: nowrap;
}
.horizontal-label {
  background-color: #F9FAFB;
  border-radius: 8px;
  padding: 4px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #0C111D;
  text-align: left;
  margin-right: 3px;
  transition: background-color 0.3s ease; 
  width: 125px;
  min-width: 125px;
}
.label-divider {
  width:125px;
  min-width:125px;
  margin-right: 3px;
}
.vertical-label-col {
  transform: rotate(-90deg);
  white-space: nowrap;
  width: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  margin: 0 auto;
}
.vertical-header {
  margin: 0 auto;
  height: 100%;
  border-radius: 8px;
  padding: 8px 4px;
  margin: 0 2px;
  background-color: #F9FAFB;
  transition: background-color 0.3s ease;
}
.vertical-header-container {
  height: 125px;
  text-align: start;
}

.vertical-label {
  display: inline-block;
  writing-mode: vertical-rl;
  transform: rotate(180deg);
  font-size: 14px;
  text-transform: none;
  font-weight: 600;
  color: #0C111D;
}

.checkbox-cell {
  background-color: #F9FAFB;
  border-radius: 8px;
  width: 30px;
  height: 30px;
  margin: 0 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-cell .v-input--selection-controls__input {
  margin: 0 !important;
}
.field-theme.checkbox-matrix .v-input--selection-controls__input {
  margin-right: -8px !important;
}
.horizontal-label.highlight {
  background-color: #D0D5DD; 
}

.vertical-header.highlight {
  background-color: #D0D5DD;
}
.matrix-scroll-container {
  scrollbar-width: thin; 
}
.vertical-header-container {
  position: sticky;
  top: 0;
  z-index: 3; 
  background-color: #FFF;
}
.horizontal-label {
  position: sticky;
  left: 0;
  z-index: 2;
}

</style>
