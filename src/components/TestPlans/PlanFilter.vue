<template>
  <div>
    <v-sheet
      v-if="!skeletonLoaderState"
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40px"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-3 py-2 d-flex flex-row">
        {{ $t('filters') }}
        <v-icon
          size="16px"
          class="ml-2"
        >mdi-filter-variant</v-icon>
      </span>
    </v-sheet>
    <v-skeleton-loader
      v-else
      class="rounded-lg"
      height="40"
      width="95"
      type="button"
    />
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('filters') }}
            </h2>
            <v-btn
              icon
              @click="showDialog = false"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>

          <v-expansion-panels
            v-model="priorityPanel"
            flat
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('testruns.create_testrun.priority') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-checkbox
                  v-for="(item, index) in priorities"
                  :key="index"
                  v-model="panel_priority"
                  :value="item.name"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :hide-details="true"
                >
                  <template #label>
                    <span class="fs-14px text-theme-label">{{ item.name }}</span>
                  </template>
                </v-checkbox>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
          <v-expansion-panels
            v-model="statusPanel"
            flat
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('testruns.create_testrun.status') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-checkbox
                  v-for="(item, index) in statuses"
                  :key="index"
                  v-model="panel_status"
                  :value="item.name"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :hide-details="true"
                >
                  <template #label>
                    <span class="fs-14px text-theme-label">{{ item.name }}</span>
                  </template>
                </v-checkbox>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
          <div class="text-start pt-6">
            <div class="text-start mb-2">
              <v-label class="text-theme-label font-weight-medium">
                {{ $t('creationDate') }}
              </v-label>
            </div>
            <div class="d-flex align-center">
              <v-menu
                v-model="isStartDatePickerOpen"
                :close-on-content-click="false"
                max-width="290"
              >
                <template #activator="{ on }">
                  <v-text-field
                    dense
                    single-line
                    class="text-field field-theme mt-0 pa-0 rounded-lg custom-prepend"
                    :value="startDate"
                    background-color="#F9F9FB"
                    readonly
                    height="38"
                    hide-details
                    v-on="on"
                  >
                    <template #prepend-inner>
                      <calendarBlueIcon />
                    </template>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="startDate"
                  @change="isStartDatePickerOpen = false"
                />
              </v-menu>
              <div class="mx-4 font-weight-bold text-h6">
                {{ $t('-') }}
              </div>
              <v-menu
                v-model="isEndDatePickerOpen"
                :close-on-content-click="false"
                max-width="290"
              >
                <template #activator="{ on }">
                  <v-text-field
                    background-color="#F9F9FB"
                    class="text-field mt-0 field-theme pa-0 rounded-lg custom-prepend"
                    :value="endDate"
                    readonly
                    height="40"
                    hide-details
                    v-on="on"
                  >
                    <template #prepend-inner>
                      <calendarBlueIcon />
                    </template>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="endDate"
                  @change="isEndDatePickerOpen = false"
                />
              </v-menu>
            </div>
            <v-expansion-panels
              v-model="tagPanel"
              flat
              class="mb-8"
            >
              <v-expansion-panel>
                <v-expansion-panel-header class="mx-0 px-0">
                  <div class="text-start">
                    <v-label class="text-theme-label font-weight-medium">
                      {{ $t('testruns.create_testrun.tag') }}
                    </v-label>
                  </div>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-text-field
                    v-model="searchTag"
                    :placeholder="$t('search')"
                    background-color="#F9F9FB"
                    class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
                    height="38"
                    dense
                    hide-details
                  >
                    <template #prepend-inner>
                      <SearchIcon />
                    </template>
                  </v-text-field>

                  <v-checkbox
                    v-model="matchAllTags"
                    class="field-theme mt-2"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                    @change="handleMatchAllTags"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label font-weight-medium">{{ $t('Match all') }}</span>
                    </template>
                  </v-checkbox>

                  <v-checkbox
                    v-for="(item, index) in filterTags"
                    :key="index"
                    v-model="panel_tag"
                    :value="item.name"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ item.name }}</span>
                    </template>
                  </v-checkbox>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="clearAll"
        >
          {{ $t('clearAll') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          @click="apply"
        >
          {{ $t('apply') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>

import SearchIcon from '@/assets/svg/search-icon.svg';
import calendarBlueIcon from '@/assets/svg/calendar-blue.svg';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import handleLoading from '@/mixins/loader.js';


export default {
  name: 'PlanFilter',
  components: {
    SearchIcon,
    calendarBlueIcon,
  },
  mixins: [colorPreferencesMixin, handleLoading],
  props: {
    configurations: {
      type: Array,
      default: () => [],
    },
    tags: {
      type: Array,
      default: () => [],
    },
    currentFilters: {
      type: Object,
      default: () => ({}),
    },
    viewType: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      searchTag: null,
      panel_priority: [],
      panel_configuration: [],
      panel_status: [],
      panel_tag: [],
      matchAllTags: false,
      startDate: null,
      endDate: null,
      isStartDatePickerOpen: false,
      isEndDatePickerOpen: false,
      showDialog: false,
      configPanel: 0,
      priorityPanel: 0,
      tagPanel: 0,
      statusPanel: 0,
      statuses: [],
      priorities: [],
    };
  },
  computed: {
    filterTags() {
      if (!this.searchTag) return this.tags;
      const search = this.searchTag.toLowerCase().trim();
      return this.tags?.filter(tag =>
        tag.name.toLowerCase().startsWith(search)
      );
    },
  },
  watch: {
    currentFilters: {
      handler(newFilters) {
        if (!newFilters || !Object.keys(newFilters).length) {
          this.resetAllFilters();
          return;
        }

        if (newFilters && Object.keys(newFilters).length) {
          if (newFilters.ui) {
            this.syncFilters(newFilters.ui);
          } else {
            this.syncFilters(newFilters);
          }
        }
      },
      deep: true,
      immediate: true,
    },
    panel_tag: {
      handler(newTags) {
        if (this.tags && this.tags.length > 0) {
          this.matchAllTags = newTags.length === this.tags.length;
        }
      },
      deep: true,
    },
  },
  created() {
    this.priorities = this.getPriorities('testPlan');
    this.statuses = this.getStatuses('testPlan');
    if (this.currentFilters && Object.keys(this.currentFilters).length) {
      if (this.currentFilters.ui) {
        this.syncFilters(this.currentFilters.ui);
      } else {
        this.syncFilters(this.currentFilters);
      }
    }
  },

  methods: {
    apply() {
      if (!this.hasActiveFilters()) {
        this.$emit('applyFilters', null);
        this.showDialog = false;
        return;
      }

      const enhancedFilters = {
        panel_priority: {
          type: 'array',
          label: this.$t('testruns.create_testrun.priority'),
          value: this.panel_priority.map((name) => {
            const priorityObject = this.priorities.find((p) => p.name === name);
            return {
              name: name,
              id: priorityObject?.id || null,
              uid: priorityObject?.uid || null,
            };
          }),
        },
        panel_status: {
          type: 'array',
          label: this.$t('testruns.create_testrun.status'),
          value: this.panel_status.map((name) => {
            const statusObject = this.statuses.find((s) => s.name === name);
            return {
              name: name,
              id: statusObject?.id || null,
              uid: statusObject?.uid || null,
            };
          }),
        },
        panel_tag: {
          type: 'array',
          label: this.$t('testruns.create_testrun.tag'),
          value: this.panel_tag.map((name) => {
            const tagObject = this.tags.find((t) => t.name === name);
            return {
              name: name,
              id: tagObject?.id || null,
              uid: tagObject?.uid || null,
            };
          }),
        },
        panel_configurations: {
          type: 'array',
          label: this.$t('configurations'),
          value: this.panel_configuration.map((name) => {
            const configObject = this.configurations.find((c) => c.name === name);
            return {
              name: name,
              id: configObject?.id || null,
              uid: configObject?.uid || null,
            };
          }),
        },
        dateRange: {
          type: 'dateRange',
          label: this.$t('creationDate'),
          value: {
            start: this.startDate,
            end: this.endDate,
          },
        },
      };

      const apiFilters = {};

      if (this.panel_priority.length > 0) {
        apiFilters.priorityUids = this.priorities
          .filter((p) => this.panel_priority.includes(p.name))
          .map((p) => p.uid || p.id);
      }

      if (this.panel_status.length > 0) {
        apiFilters.statusUids = this.statuses
          .filter((s) => this.panel_status.includes(s.name))
          .map((s) => s.uid || s.id);
      }

      if (this.panel_tag.length > 0) {
        apiFilters.tagUids = this.tags.filter((t) => this.panel_tag.includes(t.name)).map((t) => t.uid || t.id);
      }

      if (this.startDate) {
        apiFilters.minCreatedAt = this.startDate;
      }

      if (this.endDate) {
        apiFilters.maxCreatedAt = this.endDate;
      }

      this.$emit('applyFilters', {
        ui: enhancedFilters,
        api: apiFilters,
      });

      this.showDialog = false;
    },

    hasActiveFilters() {
      const hasArrayFilters =
        this.panel_priority.length > 0 ||
        this.panel_status.length > 0 ||
        this.panel_tag.length > 0 ||
        this.panel_configuration.length > 0;

      const hasDateFilters = this.startDate !== null || this.endDate !== null;

      return hasArrayFilters || hasDateFilters;
    },

    syncFilters(filters) {
      if (filters.panel_priority?.value) {
        this.panel_priority = filters.panel_priority.value.map((item) => (typeof item === 'object' ? item.name : item));
      } else if (Array.isArray(filters.panel_priority)) {
        this.panel_priority = filters.panel_priority;
      }

      if (filters.panel_status?.value) {
        this.panel_status = filters.panel_status.value.map((item) => (typeof item === 'object' ? item.name : item));
      } else if (Array.isArray(filters.panel_status)) {
        this.panel_status = filters.panel_status;
      }

      if (filters.panel_tag?.value) {
        this.panel_tag = filters.panel_tag.value.map((item) => (typeof item === 'object' ? item.name : item));
      } else if (Array.isArray(filters.panel_tag)) {
        this.panel_tag = filters.panel_tag;
      }

      if (filters.dateRange?.value) {
        this.startDate = filters.dateRange.value.start || null;
        this.endDate = filters.dateRange.value.end || null;
      } else if (filters.dateRange) {
        this.startDate = filters.dateRange.start || null;
        this.endDate = filters.dateRange.end || null;
      }
    },

    clearAll() {
      this.resetAllFilters();
      this.$emit('applyFilters', null);
      this.showDialog = false;
    },

    resetAllFilters() {
      this.panel_priority = [];
      this.panel_status = [];
      this.panel_tag = [];
      this.matchAllTags = false;
      this.startDate = null;
      this.endDate = null;
    },

    handleMatchAllTags() {
      if (this.matchAllTags) {
        this.panel_tag = this.tags.map((tag) => tag.name);
      } else {
        this.panel_tag = [];
      }
    },
  },
};
</script>

<style scoped>
.v-dialog--fullscreen {
  max-height: 100vh !important;
  width: 485px !important;
  right: 0 !important;
  left: auto !important;
}

.v-expansion-panel-content__wrap {
  padding: 0 !important;
}
</style>
