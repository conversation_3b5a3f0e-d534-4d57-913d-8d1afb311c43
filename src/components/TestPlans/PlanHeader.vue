<template>
  <v-card
    class="white py-4 px-6 mb-3"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div
      v-if="isDuplicate"
      class="d-flex flex-row align-baseline justify-space-between"
    >
      <div class="d-flex flex-column">
        <div
          class="d-flex flex-row mb-3 pointer"
          @click="handleBackClick"
        >
          <v-icon color="blue">
            mdi-chevron-left
          </v-icon>
          <p
            class="d-flex-inline justify-center align-center ma-0 blue--text font-weight-bold"
          >
            {{ $t("plans.create.backToCreatePlans") }}
          </p>
        </div>
        <h2>{{ $t("plans.create.rerun") }}</h2>
      </div>
      <div
        class="pointer"
        @click="closeShowAddUsersView"
      >
        <v-icon>mdi-close</v-icon>
      </div>
    </div>
    <div
      v-else
      class="d-flex flex-row align-center justify-space-between"
    >
      <h2
        v-if="!skeletonLoaderState"
        class="text-theme-base"
      >
        {{ $t(title) }}
      </h2>
      <v-skeleton-loader
        v-else
        height="36"
        width="140"
        type="heading"
      />
      <template v-if="!skeletonLoaderState">
        <v-tooltip
          bottom
          :disabled="btn_show"
        >
          <template #activator="{ on, attrs }">
            <div
              v-bind="attrs"
              v-on="on"
            >
              <v-btn
                :disabled="!btn_show"
                color="primary"
                depressed
                :class="{
                  'btn-theme rounded-lg': true,
                  'disabled-action': isProjectArchived,
                }"
                height="40"
                @click="navigateToPlanCreate"
              >
                {{ $t(actionText) }}
                <v-icon
                  class="pl-2"
                  size="medium"
                >
                  mdi-plus
                </v-icon>
              </v-btn>
            </div>
          </template>
          <span>
            {{ $t("plans.noPermissionToDo", { action: $t("create").toLowerCase() }) }}
          </span>
        </v-tooltip>
      </template>
      <v-skeleton-loader
        v-else
        class="rounded-lg primary"
        height="40"
        width="150"
        type="button"
      />
    </div>
    <div class="header-actions d-flex align-center justify-space-between">
      <div class="header-actions_run">
        <div
          v-if="!skeletonLoaderState"
          class="d-flex align-center justify-start ml-0 py-4"
        >
          <v-chip
            :class="{ 'blue--text': filter === 'ongoing' }"
            width="115px"
            :color="filter === 'ongoing' ? '#e6ecff' : '#f9fafb'"
            label
            @click="updateFilter('ongoing')"
          >
            <div class="font-weight-bold px-2">
              {{ active_label }} <span class="ml-2">{{ activeItemCount }}</span>
            </div>
          </v-chip>
          <div class="ml-2">
            <v-chip
              :class="{ 'blue--text': filter === 'archived' }"
              width="115px"
              :color="filter === 'archived' ? '#e6ecff' : '#f9fafb'"
              label
              @click="updateFilter('archived')"
            >
              <div class="font-weight-bold px-2">
                {{ archived_label }} <span class="ml-2">{{ archivedItemCount }}</span>
              </div>
            </v-chip>
          </div>
        </div>
        <div
          v-else
          class="py-4 d-flex"
        >
          <v-skeleton-loader
            class="rounded-sm d-flex gap-2 chip-primary"
            height="32"
            width="200"
            type="button@2"
          />
        </div>
        <slot name="additional-actions" />
      </div>
    </div>
    <ProjectDiscardDialog
      v-model="showConfirmBackDialog"
      :title="
        isDuplicate
          ? $t('plans.duplicate.discussConfirmation.title')
          : $t('plans.create_testrun.close_dialog.title')
      "
      @close="handleCloseClick"
      @handleConfirmClick="closeShowAddUsersView"
    />
  </v-card>
</template>

<script>
import ProjectDiscardDialog from "@/components/Project/ProjectDiscardDialog.vue";
import projectStatus from "@/mixins/projectStatus";
import handleLoading from "@/mixins/loader.js";
export default {
  name: "PlanHeader",
  components: {
    ProjectDiscardDialog,
  },
  mixins: [projectStatus, handleLoading],
  props: {
    isDuplicate: Boolean,
    title: String,
    actionText: String,
    filter: String,
    active_label: String,
    archived_label: String,
    btn_show: Boolean,
    activeItemCount: Number,
    archivedItemCount: Number,
  },
  data() {
    return {
      showConfirmBackDialog: false,
    };
  },

  methods: {
    updateFilter(filter) {
      this.$emit("update-filter", filter);
    },
    handleBackClick() {
      this.showConfirmBackDialog = true;
    },
    closeShowAddUsersView() {
      this.$router.replace({ name: "TestPlanCreate" });
    },
    handleCloseClick() {
      this.showConfirmBackDialog = false;
    },
    navigateToPlanCreate() {
      if (!this.isProjectArchived) {
        this.$router.push({ name: "TestPlanCreate" });
      }
    },
  },
};
</script>
