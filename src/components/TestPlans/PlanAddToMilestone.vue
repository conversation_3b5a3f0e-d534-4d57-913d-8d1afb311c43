<template>
  <div>
    <v-dialog
      v-model="isShowDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
      @click:outside="cancel"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <v-tooltip
              bottom
              max-width="400px"
              :disabled="testPlan?.name?.length < 61"
              content-class="tooltip-theme"
            >
              <template #activator="{ on, attrs }">
                <h2 
                  class="black--text d-flex align-center"
                  v-bind="attrs"
                  v-on="on"
                >
                  {{ $t('link') }} 
                  <span class="custom__tooltip__title mx-1">
                    {{ testPlan.name }}
                  </span>
                  {{ $t('plans.milestone.toMilestones') }}
                </h2>
              </template>
              <span>
                {{ testPlan.name }}
              </span>
            </v-tooltip>
            <v-btn
              icon
              @click="cancel"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <div class="text-start pt-6">
            <div class="mb-1 text-start mt-4">
              {{ $t('plans.milestone.selectMilestones') }}
            </div>
            <div>
              <SearchComponent
                v-model="searchQuery"
                :placeholder="$t('placeHolder.searchByName')"
                class="mr-3"
              />
              <v-checkbox 
                v-for="(item) in filteredItems" 
                :key="item.uid"
                v-model="selectedMilestoneUids" 
                class="field-theme"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                :hide-details="true"
                :value="item.uid" 
                dense 
              >
                <template #label>
                  <span class="fs-14px text-theme-label">{{ item.name }}</span>
                </template>
              </v-checkbox>
            </div> 
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="cancel"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          @click="add"
        >
          {{ linkButtonText }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import makeMilestonesService from '@/services/api/milestone';
import makePlanService from '@/services/api/plan';
import SearchComponent from '@/components/Project/SearchComponent.vue';
import { showSuccessToast, showErrorToast } from '@/utils/toast';
import handleLoading from "@/mixins/loader.js";

let milestoneService;
let planService;
export default {
  name: "PlanAddToMilestone",
  components: {
    SearchComponent
  },
  mixins: [handleLoading],
  props: {
    showDialog: Boolean,
    testPlan: { type: Object, required: true },
    selectedPlans: { type: Array, required: true }
  },
  data() {
    return {
      milestones: [],
      selectedMilestoneUids: [],
      isShowDialog: true,
      searchQuery: '', 
    };
  },
  computed:{
    activeMilestones() {
      return this.milestones.filter((milestone) => !milestone?.archivedAt && !milestone?.deletedAt);
    },
    filteredItems() {
      return this.activeMilestones.filter(milestone => 
        milestone.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    },
    initialMilestoneUids() {
      const plans = this.selectedPlans || [];
      const allUids = plans.flatMap(item => (item.milestones || [])).map(milestone => milestone.uid);
      return allUids.filter((uid, i, arr) => arr.indexOf(uid) === i);
    },
    addedMilestoneUids() {
      return this.selectedMilestoneUids.filter(
        uid => !this.initialMilestoneUids.includes(uid)
      );
    },
    removedMilestoneUids() {
      return this.initialMilestoneUids.filter(
        uid => !this.selectedMilestoneUids.includes(uid)
      );
    },
    selectedPlansCount() {
      return this.selectedPlans?.length ?? 0;
    },
    addedMilestoneUidsCount() {
      return this.addedMilestoneUids?.length ?? 0;
    },
    removedMilestoneUidsCount() {
      return this.removedMilestoneUids?.length ?? 0;
    },
    linkButtonText() {
      return this.removedMilestoneUids.length > 0 ? this.$t('unlink') : this.$t('link');  
    }
  },
  watch: {
    isShowDialog(newValue) {
      this.$emit('change-milestone-drawer-state', newValue)
    },
  },
  created() {
    milestoneService = makeMilestonesService(this.$api);
    planService = makePlanService(this.$api);
  },

  mounted() {
    this.getAllMileStones();
    this.selectedMilestoneUids = this.initialMilestoneUids;
  },

  methods: {
    add() {
      
      const uids = Array.isArray(this.selectedPlans) ? this.selectedPlans.map(item => item.uid) : [this.testPlan.uid];
    
      this.addToMilestone({
        uids: uids,
        cascade: true,
        milestoneUids: this.selectedMilestoneUids
      });
    },
    async addToMilestone(payload){

      const handle = this.$route.params.handle;
      const key = this.$route.params.key;

      const requests = [];
      const actionLabels = [];

      if(payload.uids.length <= 1){
        if(this.addedMilestoneUids?.length){
          requests.push(
            planService.updateTestPlans(
              handle,
              key,
              {
                uids: payload.uids,
                action: 'addMilestones',
                cascade: payload.cascade,
                milestoneUids: this.addedMilestoneUids
              }
            )
          );
          actionLabels.push("linkedWithSuccess");
        }

        if(this.removedMilestoneUids?.length){
          requests.push(
            planService.updateTestPlans(
              handle,
              key,
              {
                uids: payload.uids,
                action: 'removeMilestones',
                cascade: payload.cascade,
                milestoneUids: this.removedMilestoneUids
              }
            )
          );
          actionLabels.push("unlinkedWithSuccess");
        }

      } else {
        
        const addedMilestoneUids   = this.addedMilestoneUids;
        const removedMilestoneUids = this.removedMilestoneUids;
        
        if(addedMilestoneUids?.length){
          requests.push(
            planService.updateTestPlans(
              handle,
              key,
              {
                uids: payload.uids,
                action: 'addMilestones',
                cascade: payload.cascade,
                milestoneUids: addedMilestoneUids
              }
            )
          );
          actionLabels.push("linkedWithSuccess");
        }
        if(removedMilestoneUids?.length){
          requests.push(
            planService.updateTestPlans(
              handle,
              key,
              {
                uids: payload.uids,
                action: 'removeMilestones',
                cascade: payload.cascade,
                milestoneUids: removedMilestoneUids
              }
            )
          );
          actionLabels.push("unlinkedWithSuccess");
        }

        if (requests.length === 0) return;
        
      }

      try {
        this.showSkeletonLoader();
        const results = await Promise.allSettled(requests);

        results.forEach((result, index) => {
          const actionKey = actionLabels[index];

          if (result.status === "fulfilled") {
            showSuccessToast(this.$swal, actionKey, {
              item1: `${ this.selectedPlansCount } ${ this.$t('testPlans') }`,
              item2: `${ actionKey === "linkedWithSuccess" ? this.addedMilestoneUidsCount : this.removedMilestoneUidsCount } ${ this.$t('milestones') }`
            });
          } else {
            showErrorToast(
              this.$swal,
              actionKey === "linkedWithSuccess" ? "linkedWithError" : "unlinkedWithError",
              { 
                item1: `${ this.selectedPlansCount } ${ this.$t('testPlans') }`,
                item2: `${ actionKey === "linkedWithSuccess" ? this.addedMilestoneUidsCount : this.removedMilestoneUidsCount } ${ this.$t('milestones') }`
              },
              result.reason?.response?.data
            );
          }
        });
      } finally {
        this.hideSkeletonLoader();
        this.$emit('refresh-test-plans');
      }
    },
    cancel() {
      this.$emit('change-milestone-drawer-state', false)
    },
    async getAllMileStones(){
      try{
        const response = await milestoneService.getMilestones(
          this.$route.params.handle,
          this.$route.params.key
        );
        if(response.status === 200) {
          this.milestones = response.data.items;
        }
      } catch(err){
        console.log(err)
      }
    },
  },
};
</script>

<style scoped>
.v-dialog--fullscreen {
  max-height: 100vh !important;
  width: 485px !important;
  right: 0 !important;
  left: auto !important;
}

.v-expansion-panel-content__wrap {
  padding: 0 !important;
}
</style>
