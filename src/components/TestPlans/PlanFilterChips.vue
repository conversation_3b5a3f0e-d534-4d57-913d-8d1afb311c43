<template>
  <div
    v-if="hasActiveFilters"
    class="plan-filter-chips py-2"
  >
    <div class="d-flex align-center flex-wrap">
      <div class="mr-3 mb-2">
        <span class="font-weight-medium text-theme-label">{{ $t('results') }}: {{ resultsCount }}</span>
      </div>

      <template v-if="filters.panel_priority && filters.panel_priority.length > 0">
        <v-chip
          v-for="priority in filters.panel_priority"
          :key="`priority-${priority}`"
          class="mr-2 mb-2 chip-theme"
          close
          @click:close="removeFilter('panel_priority', priority)"
        >
          <span class="text-caption">{{ $t('testruns.create_testrun.priority') }}: {{ priority }}</span>
        </v-chip>
      </template>

      <template v-if="filters.panel_status && filters.panel_status.length > 0">
        <v-chip
          v-for="status in filters.panel_status"
          :key="`status-${status}`"
          class="mr-2 mb-2 chip-theme"
          close
          @click:close="removeFilter('panel_status', status)"
        >
          <span class="text-caption">{{ $t('testruns.create_testrun.status') }}: {{ status }}</span>
        </v-chip>
      </template>

      <template v-if="filters.panel_milestone && filters.panel_milestone.length > 0">
        <v-chip
          v-for="milestone in filters.panel_milestone"
          :key="`milestone-${milestone}`"
          class="mr-2 mb-2 chip-theme"
          close
          @click:close="removeFilter('panel_milestone', milestone)"
        >
          <span class="text-caption">{{ $t('testruns.create_testrun.milestone') }}: {{ milestone }}</span>
        </v-chip>
      </template>

      <template v-if="filters.panel_tag && filters.panel_tag.length > 0">
        <v-chip
          v-for="tag in filters.panel_tag"
          :key="`tag-${tag}`"
          class="mr-2 mb-2 chip-theme"
          close
          @click:close="removeFilter('panel_tag', tag)"
        >
          <span class="text-caption">{{ $t('testruns.create_testrun.tag') }}: {{ tag }}</span>
        </v-chip>
      </template>

      <v-chip
        v-if="filters.dateRange && filters.dateRange.start && filters.dateRange.end"
        class="mr-2 mb-2 chip-theme"
        close
        @click:close="resetDateRange('dateRange')"
      >
        <span class="text-caption">
          {{ $t('creationDate') }}: {{ formatDate(filters.dateRange.start) }} -
          {{ formatDate(filters.dateRange.end) }}
        </span>
      </v-chip>

      <v-btn
        small
        text
        color="primary"
        class="ml-2 mb-2 text-capitalize"
        @click="clearAllFilters"
      >
        {{ $t('clearAll') }}
      </v-btn>
    </div>
  </div>
</template>

<script>
  import { useDateFormatter } from '@/composables/utils/dateFormatter';
  import colorPreferencesMixin from '@/mixins/colorPreferences';

  export default {
    name: 'PlanFilterChips',
    mixins: [colorPreferencesMixin],
    props: {
      filters: {
        type: Object,
        required: true,
      },
      resultsCount: {
        type: Number,
        default: 0,
      },
    },
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },
    computed: {
      hasActiveFilters() {
        return (
          (this.filters.panel_priority && this.filters.panel_priority.length > 0) ||
          (this.filters.panel_status && this.filters.panel_status.length > 0) ||
          (this.filters.panel_milestone && this.filters.panel_milestone.length > 0) ||
          (this.filters.panel_tag && this.filters.panel_tag.length > 0) ||
          (this.filters.dateRange && this.filters.dateRange.start && this.filters.dateRange.end)
        );
      },
    },
    methods: {
      isDefaultRange(range, defaultRange) {
        if (!range) return true;
        return range[0] === defaultRange[0] && range[1] === defaultRange[1];
      },
      removeFilter(filterType, value) {
        const updatedFilters = JSON.parse(JSON.stringify(this.filters));

        if (Array.isArray(updatedFilters[filterType])) {
          updatedFilters[filterType] = updatedFilters[filterType].filter((item) => item !== value);
        }

        this.$emit('update-filters', updatedFilters);
      },

      resetFilter(filterType, defaultValue) {
        const updatedFilters = JSON.parse(JSON.stringify(this.filters));

        updatedFilters[filterType] = defaultValue;

        this.$emit('update-filters', updatedFilters);
      },

      resetDateRange(rangeType) {
        const updatedFilters = JSON.parse(JSON.stringify(this.filters));

        updatedFilters[rangeType] = {
          start: null,
          end: null,
        };

        this.$emit('update-filters', updatedFilters);
      },
      clearAllFilters() {
        this.$emit('clear-filters');
      },
    },
  };
</script>

<style scoped>
  .plan-filter-chips {
    background-color: #f9f9fb;
    padding: 8px 12px;
    border-radius: 8px;
  }

  .chip-theme {
    background-color: #f2f4f7 !important;
    color: #344054 !important;
    font-size: 12px;
    height: 28px !important;
    border-radius: 6px !important;
    padding-left: 12px !important;
    padding-right: 12px !important;
    border: none !important;
    box-shadow: none !important;
  }

  .chip-theme :deep(.v-chip__close) {
    margin-left: 6px;
  }
</style>
