<template>
  <v-app-bar
    color="white"
    class="px-4 app-navbar"
    height="90px"
    elevation="0"
    rounded="lg"
  >
    <router-link to="/">
      <img
        src="@/assets/png/logo.png"
        alt="logo"
        class="mr-6"
      >
    </router-link>

    <v-menu
      v-for="link in links"
      :key="link.text"
      open-on-hover
      hoverable
      offset-y
      :close-on-content-click="false"
      :disabled="!link.isDisabled"
    >
      <template #activator="{ on, attrs }">        
        <v-btn
          v-bind="attrs"
          :to="link.isDisabled ? '' : link.to"
          class="text-capitalize topbar text-theme-secondary"
          :class="{ 
            'active-link': link.isActive,
            'opacity-md': link.isDisabled,
          }"
          text
          :ripple="false"
          v-on="on"
          @click="setActive(link.text)"
        >
          <div class="d-flex align-center gap-2">
            <span>{{ $t(link.text) }}</span>
            <WarningIcon v-if="link.hasError || link.isDisabled" />
          </div>
        </v-btn>
      </template>
      <v-card class="tooltip-content">
        <v-card-text>
          <div v-if="link.hasError">
            {{ link.errorMessage }}
          </div>
          <div
            v-else-if="link.isDisabled"
            class="text-theme-label"
          >
            <span>{{ $t('integrations.disabledMessage.prefix') }}</span> 
            <router-link :to="link.actionRoute">
              {{ $t('integrations.disabledMessage.action') }}
            </router-link>
            <span>{{ $t('integrations.disabledMessage.suffix') }}</span>
          </div>
        </v-card-text>
      </v-card>
    </v-menu>
    <v-spacer />
    <v-btn
      text
      link
      target="_blank"
      class="text-capitalize mr-2 text-theme-label"
      @click="goToContact"
    >
      {{ $t('needHelp') }}
    </v-btn>
    <div>
      <Avatar 
        :name="displayUserAccount.name"
        :avatar="displayUserAccount.avatar"
      />
      <Avatar 
        v-if="isOrg"
        :avatar-src="currentAccount.avatarUrl"
        :name="currentAccount.name"
        :is-org="isOrg"
        :size="20"
      />
    </div>

    <v-menu
      v-model="menuOpen"
      offset-y
      min-width="200"
      content-class="shadow-theme rounded-lg"
      class="account-menu"
    >
      <template #activator="{ on, attrs }">
        <div
          class="d-flex align-center pl-4"
          v-bind="attrs"
          v-on="on"
        >
          <div class="d-flex flex-column align-start ml-2">
            <span class="black--text font-weight-medium mr-2">{{ displayUserAccount.name }}</span>
            <span
              v-if="currentAccount?.name != displayUserAccount.name"
              class="caption grey--text lighten-1"
            >
              {{ currentAccount?.name }}</span>
          </div>
          <v-icon>{{ menuOpen ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
        </div>
      </template>
      <v-list>
        <v-subheader class="text-theme-secondary">
          {{ $t('account_menu.personal_account') }}
        </v-subheader>

        <v-list-item @click="selectHandle(displayUserAccount)">
          <v-list-item-title class="d-flex align-center">
            <Avatar 
              :name="displayUserAccount.name"
              :avatar="displayUserAccount.avatar"
              :size="32"
            />
            <div class="d-flex flex-column align-start ml-2 text-theme-table-text">
              <span>{{ displayUserAccount.name }}</span>
            </div>
          </v-list-item-title>
        </v-list-item>

        <v-divider class="divider-theme" />
        <v-subheader v-if="displayableAccounts.length > 0">
          {{ $t('account_menu.switch_workspace') }}
        </v-subheader>
        <v-list
          v-if="displayableAccounts.length > 0"
          max-height="200px"
          class="overflow-auto custom-scrollbar"
        >
          <v-list-item
            v-for="account in displayableAccounts"
            :key="account.handle"
            @click="selectHandle(account)"
          >
            <v-list-item-title class="d-flex align-center">
              <Avatar 
                :name="account.name"
                :avatar-src="account.avatarUrl"
                :size="32"
              />
              <div class="d-flex flex-column align-start ml-2">
                <span>{{ account.name }}</span>
              </div>
            </v-list-item-title>
          </v-list-item>
        </v-list>

        <v-list-item :to="{ name: 'Organizations' }">
          <v-list-item-title class="d-flex align-center">
            <PlusIcon class="mr-2" />
            <span class="text-theme-table-text">{{ $t('account_menu.add_workspace') }}</span>
          </v-list-item-title>
        </v-list-item>

        <v-divider class="divider-theme" />

        <v-list-item @click="onClickSetting">
          <v-list-item-title class="d-flex align-center">
            <GearIcon class="mr-2" />
            <span class="text-theme-table-text">{{ $t('settings') }}</span>
          </v-list-item-title>
        </v-list-item>

        <v-divider class="divider-theme" />

        <v-list-item
          link
          @click="logout"
        >
          <v-list-item-title class="d-flex align-center">
            <LogoutIcon class="mr-2" />
            <span class="text-theme-danger">{{ $t('logout') }}</span>
          </v-list-item-title>
        </v-list-item>
      </v-list>
    </v-menu>
  </v-app-bar>
</template>

<script>
import { createNamespacedHelpers, mapActions as projectMapActions } from 'vuex';

import { mapActions as workspaceMapAction } from 'vuex';

import GearIcon from '@/assets/svg/gear24px.svg';
import PlusIcon from '@/assets/svg/plus24px.svg';
import LogoutIcon from '@/assets/svg/logout24px.svg';
import WarningIcon from '@/assets/svg/warning.svg';
//import BellIcon from '@/assets/svg/bell.svg';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import permissions from '@/mixins/permissions';

const { mapActions, mapGetters, mapState, mapMutations } = createNamespacedHelpers('user');
const { mapGetters: integrationGetters } = createNamespacedHelpers('integration');
const { mapMutations: mapDashboardMutations } = createNamespacedHelpers('dashboard');

import { DB_NAME } from '@/constants/dashboardConstants';

import { useProjectView } from '@/composables/modules/project/index';
import Avatar from '../../base/Avatar.vue';


export default {
  name: 'Appbar',
  components: {
    GearIcon,
    PlusIcon,
    LogoutIcon,
    WarningIcon,
    Avatar
    //BellIcon,
  },
  mixins: [colorPreferencesMixin, permissions],
   data() {
    return {
      links: [],
      menuOpen: false,
    };
  },
  computed: {
    ...mapGetters(['orgs', 'isAuthenticated', 'userName']),
    ...mapState(['user', 'currentAccount']),
    ...integrationGetters(['hasIntegrationError', 'integrationErrorMessage']),
    _readIntegration(){
      return this.authorityTo('read_integration')
    },
    handle() {
      if (this.$route.params?.handle) {
        return this.$route.params.handle;
      } else if (this.currentAccount?.handle) {
        return this.currentAccount?.handle;
      }
      return this.$store.state.user.user.handle;
    },
    isOrg(){
      return this.currentAccount.handle !== this.displayUserAccount.handle
    },
    displayableAccounts() {
      const accounts = [];

      if (!this.currentAccount) {
        return accounts;
      }

      const orgsToDisplay = this.orgs
        .filter((org) => org.handle !== this.currentAccount?.handle)
        .map((org) => ({
          name: org.name,
          handle: org.handle,
          avatarUrl: org.avatarUrl,
          type: org.type,
          roleName: org.roleName,
          uid: org.uid,
        }));

      accounts.push(...orgsToDisplay);

      return accounts;
    },

    displayUserAccount() {
      const userAccount = {
        name: `${this.user?.firstName} ${this.user?.lastName}`,
        handle: this.user?.handle,
        avatar: this.user?.avatar,
        type: 'user',
        roleName: 'owner',
      };
      return userAccount;
    },
  },
   watch: {
    $route(to) {
      this.setActive(to.name);
      this.updateLinks(to.params.handle, to.params.key);
    },
    hasIntegrationError: {
      immediate: true,
      handler(newVal) {
        const integrationLinkIndex = this.links.findIndex((link) => link.text === 'integrations.name');
        if (integrationLinkIndex !== -1) {
          this.$set(this.links, integrationLinkIndex, {
            ...this.links[integrationLinkIndex],
            hasError: newVal,
            errorMessage: this.integrationErrorMessage,
          });
        }
      },
    },
  },
  mounted() {
    this.updateLinks(this.handle);
  },
  

  methods: {
    ...mapActions(['initSession']),
    ...workspaceMapAction('workspace', ['clearAssignTo']),
    ...mapMutations(['setUser', 'setOrgs', 'setCurrentAccount']),
    ...projectMapActions('headers', ['clearAllHeaders']),
    ...mapDashboardMutations(['clearDashboard']),
    updateLinks(handle, key) {
      const { getProject } = useProjectView();
      const selectedProject = getProject();
      let handleName = handle || this.handle;
      const currentPage = this.$router.history.current;
      if (key) {
        currentPage.params.key = key;
      }
      this.links = [
        {
          text: 'projectsMenu',
          to: selectedProject?.key ? { name: 'Cases', params: { handle: handleName, key: selectedProject.key } } : { name: 'ProjectsView', params: { handle: handleName } },
          isActive: true,
        },
        {
          text: 'myWorkspace',
          to: { name: 'Workspace', params: { handle: handleName } },
          isActive: false,
        },
        // Check if the user has the read_dashboard permission for org scope
        {
          text: 'analytics',
          to: { name: 'OrgDashboard', params: { handle: handleName } },
          isActive: false,
        },
        //{
        //  text: 'reports',
        //  to: { name: 'Reports', params: { ...currentPage.params, handle: handle } },
        //  isActive: false
        //},
        {
          text: 'administration',
          to: { name: 'admin', params: { ...currentPage.params, handle: handleName } },
          isActive: false,
        },
      ];
      if (this.currentAccount && this._readIntegration) {
        this.links.push({
          text: 'integrations.name',
          to: { name: 'integrations', params: { handle: handleName } },
          isActive: false,
          hasError: this.hasIntegrationError || false,
          errorMessage: this.integrationErrorMessage,
          isDisabled: this.currentAccount.type != 'org',
          actionRoute: {
            name: 'Organizations'
          }
        });
      }
    },
    setActive(selectedLink) {
      this.links.forEach((link) => {
        link.isActive = link.text === selectedLink;
      });
    },
    goToContact() {
      this.$router.push({
        name: 'contact',
        params: { handle: this.currentAccount?.handle }
      });
    },
    goToSettings() {
      this.$router.push({ name: 'Settings' });
    },
    selectHandle(account) {
        const { setProject } = useProjectView();
      let selectedHandle = {
        handle: account.handle,
        name: account.name,
        type: account.type,
        roleName: account.roleName,
        avatarUrl: account.avatarUrl,
        uid: account.uid,
      };

      const currentPage = this.$router.history.current;
      // Handle settings pages differently
      if (
        currentPage.path.indexOf('settings') >= 0 &&
        (!currentPage.params.handle || selectedHandle.handle === this.$store.state.user.user.handle)
      ) {
        // If we're going to the user settings page or the org page
        if (selectedHandle.handle === this.$store.state.user.user.handle) {
          this.$router.push({
            name: 'Account',
          });
        } else {
          this.$router.push({
            name: 'OrgAccount',
            params: { handle: selectedHandle.handle },
          });
        }
      } else {
        if (currentPage.params?.handle === this.currentAccount.handle) {
          this.$router.push({
            name: 'ProjectsView',
            params: { ...currentPage.params, handle: selectedHandle.handle, key: null, id: null },
          });
        }
      }
      this.setCurrentAccount(selectedHandle);
      
      this.clearAssignTo();
      this.updatePreferences(selectedHandle);
      setProject(null);
    },
    onClickSetting() {
      if (this.currentAccount.type == 'org' || this.currentAccount.isOrg) {
        this.$router.push({
          name: 'OrgAccount',
          params: { handle: this.currentAccount.handle },
        });
      } else {
        this.$router.push({
          name: 'Account',
        });
      }
    },
    logout() {
      this.setCurrentAccount(null);
      this.clearAssignTo()

      this.setUser(null);
      this.setOrgs(null);
      this.clearAllHeaders();
      localStorage.clear();
      this.$router.push('/login').catch(() => {});
      indexedDB.deleteDatabase(DB_NAME);
    },
  }
};
</script>

<style scoped>
/* Custom scrollbar styles for the specific container */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f9f9fb;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #eaecf0;
  border-radius: 6px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #eaecf0;
}

.topbar.v-btn--active {
  color: blue !important;
}
.app-navbar .v-btn--active::before {
  opacity: 0 !important;
}
.app-navbar .v-btn--active {
  font-weight: 600 !important;
}
.account-menu {
  z-index: 19;
}

.topbar .v-icon.error {
  color: #f4284e !important;
}

.topbar .d-flex {
  position: relative;
}

.warning-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 16px;
  width: 16px;
}

.warning-icon i {
  font-size: inherit !important;
}
</style>
