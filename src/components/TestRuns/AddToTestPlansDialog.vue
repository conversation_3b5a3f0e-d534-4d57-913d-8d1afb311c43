<template>
  <div>
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
      @click:outside="$emit('close')"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('linkTestRunsToTestPlans') }}
            </h2>
            <v-btn
              icon
              @click="$emit('close')"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <div class="mt-6">
            <div class="text-left mt-4 mb-1">
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t('testruns.testplan_dialog.content') }}
              </v-label>
            </div>
            <v-text-field
              v-model="searchQuery"
              :placeholder="$t('search')"
              background-color="#F9F9FB"
              class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
              height="38"
              dense
              hide-details
            >
              <template #prepend-inner>
                <SearchIcon />
              </template>
            </v-text-field>
            <template v-if="plansHasData">
              <v-checkbox
                v-for="(plan) in filteredItems"
                :key="plan.uid"
                v-model="selectedPlanUids"
                class="field-theme"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                :hide-details="true"
                :value="plan.uid"
              >
                <template #label>
                  <span class="fs-14px text-theme-label">{{ plan.name }}</span>
                </template>
              </v-checkbox>
            </template>
            <template v-else>
              <div class="text-left">
                <span>
                  {{ $t('projects.no_data_available') }}
                </span>
              </div>
            </template>

            <div class="text-left mt-8">
              <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                {{ $t("createQuickPlan") }} 
              </v-label>
              <v-text-field
                v-model="planName"
                :placeholder="$t('enterName')"
                height="38"
                background-color="#F9F9FB"
                class="field-theme mt-0 pt-1"
              />
            </div>
            <v-btn
              type="submit"
              block
              color="primary"
              :depressed="true"
              class="btn-theme"
              width="100%"
              height="40"
              :loading="createButtonLoading"
              :disabled="!planName.trim()"
              @click="createTestPlan"
            >
              {{ $t("create") }}
            </v-btn>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          full-width
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="cancel"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          full-width
          elevation="0"
          @click="handleAddTestPlan"
        >
          {{ linkButtonText }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import SearchIcon from '@/assets/svg/search-icon.svg';

export default {
    components: {
        SearchIcon,
    },
    props: {
        value: Boolean,
        plans: Array,
        selectedRuns: { type: Array, required: true }
    },
    data() {
        return {
            showDialog: this.value,
            selectedPlanUids: [],
            planName: '',
            createButtonLoading: false,
            searchQuery: '',
        };
    },
    computed: {
        plansHasData() {
            return this.plans.length > 0;
        },
        filteredItems(){
          return this.plans.filter(plan => 
            plan.name.toLowerCase().includes(this.searchQuery.toLowerCase())
          );
        },
        initialPlanUids() {
            const runs = this.selectedRuns || [];
            const allUids = runs.flatMap(item => (item.testPlans || [])).map(plan => plan.uid);
            return allUids.filter((uid, i, arr) => arr.indexOf(uid) === i);
        },
        addedPlanUids(){
          return this.selectedPlanUids.filter(
            uid => !this.initialPlanUids.includes(uid)
          );
        },
        removedPlanUids() {
          return this.initialPlanUids.filter(
            uid => !this.selectedPlanUids.includes(uid)
          );
        },
        selectedRunsCount() {
            return this.selectedRuns?.length ?? 0;
        },
        addedPlanUidsCount() {
            return this.addedPlanUids?.length ?? 0;
        },
        removedPlanUidsCount() {
            return this.removedPlanUids?.length ?? 0;
        },
        linkButtonText(){
          return this.removedPlanUidsCount > 0 ? this.$t('unlink') : this.$t('link');  
        }
    },
    watch: {
        value(newVal) {
            this.showDialog = newVal;
        },
    },
    mounted(){
      this.selectedPlanUids = this.initialPlanUids;
    },
    methods: {
        async createTestPlan() {
            this.$emit('planCreated', this.planName);
            this.planName = '';
        },
        handleAddTestPlan() {
          this.$emit('handleAddTestPlan', {
            selectedPlanUids: this.selectedPlanUids,
            addedPlanUids: this.addedPlanUids,
            removedPlanUids: this.removedPlanUids
          });
        },
        cancel() {
            this.$emit("close");
        },
    }
};
</script>

<style scoped>
.v-dialog--fullscreen {
    max-height: 100vh !important;
    width: 485px !important;
    right: 0 !important;
    left: auto !important;
}

.v-expansion-panel-content__wrap {
    padding: 0 !important;
}
</style>