<template>
  <div>
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
      @click:outside="$emit('close')"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2
              v-if="!isAddConfig"
              class="black--text"
            >
              {{ $t('testruns.duplicateApplyConfig') }}
            </h2>
            <h2
              v-else
              class="black--text"
            >
              {{ $t('configurations.add_configuration') }}
            </h2>
            <v-btn
              icon
              @click="$emit('close')"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <div class="mt-6">
            <v-checkbox
              v-if="!isAddConfig"
              class="field-theme"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
            >
              <template #label>
                <span class="fs-14 text-theme-label">{{ $t('keepAssignedTo') }} </span>
              </template>
            </v-checkbox>

            <div class="toggle-wrapper mt-6">
              <div class="toggle-container">
                <div
                  class="toggle-option"
                  :class="{ active: selectedPlan === 0 }"
                  @click="setPlan(0)"
                >
                  Simple set-up
                </div>
                <div
                  class="toggle-option"
                  :class="{ active: selectedPlan === 1 }"
                  @click="setPlan(1)"
                >
                  Matrix set-up
                </div>
              </div>
            </div>

            <template v-if="selectedPlan === 0">
              <div class="text-left mt-8 mb-3">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium text-capitalize">
                  {{ $t('groups') }}
                </v-label>
              </div>

              <template v-if="configurationsHasData">
                <div align="center">
                  <div
                    v-for="(item, index) in configurationData"
                    :key="index"
                    class="mb-5"
                  >
                    <GroupSelection
                      v-model="selectedConfigurations[item.uid]"
                      :title="item.name"
                      :uid="item.uid"
                      :add-label="$t('add')"
                      :search-placeholder="$t('search')"
                      :items="item.options"
                      :button-loading="buttonLoadingItems"
                      :is-open="showDialog"
                      @addItem="addConfigurationItems"
                      @editItem="editConfigurationItem"
                      @deleteItem="deleteConfigurationItem"
                      @editGroup="editConfigurationGroup"
                      @deleteGroup="deleteConfigurationGroup"
                    />
                  </div>
                  <v-progress-circular
                    v-if="loadingConfigurations"
                    indeterminate
                    color="primary"
                  />
                  <div
                    v-intersect="{
                      handler: onIntersect,
                      options: {
                        threshold: [0, 0.5, 1.0]
                      }
                    }"  
                  >
                        &nbsp;
                  </div>
                </div>
              </template>

              <div class="d-flex justify-start">
                <ButtonWithMenu
                  v-model="addConfigurationMenu"
                  button-color="#D0D5DD"
                  :button-label="$t('plans.create.testRuns.addConfiguration')"
                  :label="$t('name')"
                  :placeholder="$t('testruns.create_testrun.egOSorBrowsers')"
                  :cancel-button-text="$t('cancel')"
                  :apply-button-text="$t('add')"
                  :button-loading="buttonLoading"
                  @cancel="cancelAddConfiguration"
                  @apply="applyAddConfiguration"
                /> 
              </div>
            </template>
            <template v-else>
              <div class="text-left mt-8 mb-3">
                <v-label class="text-left fs-14px mb-4 text-theme-label font-weight-medium text-capitalize">
                  {{ $t('configurationMatrix') }}
                </v-label>

                <div class="d-flex flex-column">
                  <div
                    v-for="(value, key) in formattedConfigurationMatrix"
                    :key="key"
                    class="matrix-item"
                  >
                    <div class="matrix-label">
                      {{ key }}:
                    </div>
                    <div class="matrix-value ml-2">
                      {{ value }}
                    </div>
                  </div>
                </div>
                <div class="d-flex justify-start">
                  <v-btn
                    color="#D0D5DD"
                    width="204.5px"
                    height="40"
                    :depressed="true"
                    class="text-capitalize rounded-lg fw-semibold btn-theme mt-4"
                    elevation="0"
                    @click="showConfigurationMatrixDialog"
                  >
                    <span>{{ Object.keys(formattedConfigurationMatrix).length > 0 ? $t('editMatrix') : $t('addMatrix') }}</span>
                  </v-btn>
                </div>
              </div>
            </template>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          full-width
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="cancel"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          full-width
          elevation="0"
          @click="handleDuplicateAndApplyConfig"
        >
          {{ buttonLabel }}
        </v-btn>
      </div>
    </v-dialog>
    <ConfigurationMatrixDialog
      v-model="configurationMatrixDialog"
      :title="$t('configurationMatrix')"
      :btn_label="$t('createRuns')"
      :configurations="configurationData"
      :selected-configuration-matrix="selectedConfigurationMatrix"
      :loading-configurations="loadingConfigurations"
      color="primary"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmBtnClick"
      @fetchMoreConfigurations="fetchMoreConfigurations"
    />
    <ConfigurationsConfirmDialog
      v-model="showDeleteConfirmDialog"
      :title="$t('configurations.deleteConfirm', { name: selectedConfiguration.name })"
      :description="$t('configurations.deleteDescription', { count: selectedConfiguration.options.length })"
      @cancel="showDeleteConfirmDialog = false"
      @close="showDeleteConfirmDialog = false"
      @delete="confirmDeleteConfigurationGroup"
    />
  </div>
</template>

<script>
import GroupSelection from '@/components/TestRuns/GroupSelection.vue';
import ButtonWithMenu from '@/components/base/Forms/CustomButton/ButtonWithMenu.vue';
import ConfigurationMatrixDialog from '@/components/TestPlans/ConfigurationMatrixDialog.vue';
import ConfigurationsConfirmDialog from '@/views/Admin/Configurations/ConfirmDialog.vue';

export default {
  components: {
    GroupSelection,
    ButtonWithMenu,
    ConfigurationMatrixDialog,
    ConfigurationsConfirmDialog,
  },
  props: {
    value: Boolean,
    configurations: Array,
    buttonLoading: Boolean,
    buttonLoadingItems: Boolean,
    selectedRun: [Object, Array],
    globalConfiguration: Object,
    loadingConfigurations: Boolean,
    lastPage: Number,
    isAddConfig: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showDialog: this.value,
      selectedPlan: 0,
      selectedConfigurations: {},
      initialSelectedConfigurations: {},
      selectedConfigurationMatrix:  { },
      addConfigurationMenu: false,
      newConfigurationName: '',
      configurationMatrixDialog: false,
      showDeleteConfirmDialog: false,
      allConfigurations: [],
      currentPage: 1,
      perPage: 10,
      isEditOrDelete: false,
      selectedConfiguration: {
        uid: '',
        name: '',
        options: []
      },
    };
  },
  computed: {
    configurationData() {
      return this.allConfigurations;
    },
    configurationsHasData() {
      return this.allConfigurations.length > 0;
    },
    addedConfig() {
      const diffs = {};
      for (const [uid, current] of Object.entries(this.selectedConfigurations)) {
        const initial = this.initialSelectedConfigurations[uid] || [];
        const added   = current.filter(v => !initial.includes(v));
        if (added.length) diffs[uid] = added;
      }
      return diffs;
    },
    removedConfig() {
      const diffs = {};
      for (const [uid, current] of Object.entries(this.selectedConfigurations)) {
        const initial = this.initialSelectedConfigurations[uid] || [];
        const removed = initial.filter(v => !current.includes(v));
        if (removed.length) diffs[uid] = removed;
      }
      return diffs;
    },
    selectedConfigurationsLength() {
      return Object.entries(this.selectedConfigurations)
        .filter(([, values]) => values.length > 0) // Only include non-empty arrays
        .map(([uid, values]) => values.map((value) => [`${uid}::${value}`])).length;
    },
    formattedConfigurationMatrix() {
      const result = {};
      for (const [parentUid, selectedValues] of Object.entries(this.selectedConfigurationMatrix)) {
        const parentGroup = this.configurationData.find(group =>
          group.options.some(opt => opt.uid.toString() === parentUid)
        );
        if (!parentGroup) continue;
        const parentOption = parentGroup.options.find(opt => opt.uid.toString() === parentUid);
        if (!parentOption) continue;
        const valueGroup = this.configurationData.find(group =>
          Object.keys(selectedValues).some(valueId =>
            group.options.some(opt => opt.uid.toString() === valueId)
          )
        );
        if (!valueGroup) continue;
        const valueNames = Object.keys(selectedValues).map(valueId => {
          const option = valueGroup.options.find(opt => opt.uid.toString() === valueId);
          return option ? option.name : valueId;
        });
        result[parentOption.name] = valueNames.join(', ');
      }
      return result;
    },
    diffCounts() {
      const counts = {}
      for (const uid in this.selectedConfigurations) {
        const initial = this.initialSelectedConfigurations[uid] || []
        const current = this.selectedConfigurations[uid]      || []
        const added   = current.filter(v => !initial.includes(v)).length
        const removed = initial.filter(v => !current.includes(v)).length
        counts[uid] = { added, removed }
      }
      return counts
    },
    totalAddedCount() {
      return Object.values(this.diffCounts).reduce((sum, { added })   => sum + added,   0)
    },
    totalRemovedCount() {
      return Object.values(this.diffCounts).reduce((sum, { removed }) => sum + removed, 0)
    },
    buttonLabel() {
      return this.totalRemovedCount > 0
        ? this.$t('save') 
        : this.$t('add');
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.showDialog = newVal;
        if (this.selectedRun) {
          if (this.selectedRun?.configuration?.sets) {
            this.selectedPlan = this.selectedRun.configuration.type === 'simple' ? 0 : 1;

            this.selectedRun.configuration.sets.forEach((set) => {
              set.forEach((config) => {
                const [uid, value] = config.split('::');
                if (this.selectedConfigurations[uid]) {
                  this.selectedConfigurations[uid].push(Number(value));
                }
                if (this.selectedConfigurationMatrix[uid]) {
                  this.selectedConfigurationMatrix[uid].push(Number(value));
                }
              });
            });
          } else if (this.selectedRun?.customFields?.configs.length > 0) {
            this.selectedRun.customFields.configs.forEach((config) => {
              const [uid, value] = config.split('::');
              if (this.selectedConfigurations[Number(uid)]) {
                this.selectedConfigurations[uid].push(Number(value));
              }
              if (this.selectedConfigurationMatrix[Number(uid)]) {
                this.selectedConfigurationMatrix[uid].push(Number(value));
              }
            });
          }
        } else if (this.globalConfiguration) {
          this.selectedPlan = this.globalConfiguration.type === 'simple' ? 0 : 1;

          this.globalConfiguration.sets.forEach((set) => {
            set.forEach((config) => {
              const [uid, value] = config.split('::');
              if (this.selectedConfigurations[uid]) {
                this.selectedConfigurations[uid].push(Number(value));
              }
              if (this.selectedConfigurationMatrix[uid]) {
                this.selectedConfigurationMatrix[uid].push(Number(value));
              }
            });
          });
        }

        this.initialSelectedConfigurations =
          JSON.parse(JSON.stringify(this.selectedConfigurations));
      }
    },
    configurations: {
      immediate: true,
      deep: true,
      handler(newVal) {
        newVal.forEach(config => {
          if (!this.selectedConfigurations[config.uid]) {
            this.$set(this.selectedConfigurations, config.uid, []);
          }
        });
        if ( this.isEditOrDelete ){
          this.allConfigurations = newVal;
          this.isEditOrDelete = false;
        } else {
          newVal.forEach(config => {
            const index = this.allConfigurations.findIndex(item => item.uid === config.uid);
            if (index > -1) {
              this.allConfigurations.splice(index, 1, config);
            } else {
              this.allConfigurations.push(config);
            }
          });}
        }
      }
  },
  methods: {
    handleDuplicateAndApplyConfig() {
      let formattedConfigs = [];
      if(this.selectedPlan === 1){
        formattedConfigs = Object.entries(this.selectedConfigurationMatrix)
        .filter(([, values]) => Object.keys(values).length > 0)
        .flatMap(([uid, values]) =>
          Object.keys(values).map(value => [`${uid}::${value}`])
        );
      }
      else{
        formattedConfigs = Object.entries(this.selectedConfigurations)
        .filter(([, values]) => values.length > 0)
        .flatMap(([uid, values]) =>
          values.map(value => [`${uid}::${value}`])
        );
      }

      this.$emit('handleDuplicateAndApplyConfig', {
        type: this.selectedPlan === 0 ? 'simple' : 'matrix',
        sets: formattedConfigs,
        addedConfig: this.addedConfig,
        removedConfig: this.removedConfig,
        addedConfigCount: this.totalAddedCount,
        removedConfigCount: this.totalRemovedCount,
      });

      if (formattedConfigs.length > 0) {  
        // Reset all selections
        this.selectedConfigurations = this.allConfigurations.reduce((acc, item) => {
          acc[item.uid] = [];
          return acc;
        }, {});
      }
    },
    cancel() {
      this.$emit('close');
    },
    setPlan(planType) {
      this.selectedPlan = planType;
    },
    addConfiguration() {
      this.addConfigurationMenu = !this.addConfigurationMenu;
    },
    async applyAddConfiguration(newConfigurationName) {
      this.addConfigurationMenu = !this.addConfigurationMenu;
      this.$emit('addConfiguration', {
        newConfigurationName: newConfigurationName,
        options: [],
      }, this.currentPage * this.perPage);
      this.isEditOrDelete = true;
    },
    addConfigurationItems(item) {
      this.$emit('addConfigurationItems', item, this.currentPage*this.perPage);
    },
    cancelAddConfiguration() {
      this.addConfigurationMenu = false;
    },
    showConfigurationMatrixDialog() {
      this.configurationMatrixDialog = !this.configurationMatrixDialog;
    },
    handleCloseClick() {
      this.configurationMatrixDialog = false;
    },
    handleConfirmBtnClick(matrix) {
      this.selectedConfigurationMatrix = matrix;
      this.configurationMatrixDialog = false;
    },
    editConfigurationItem(data) {
      const group = this.allConfigurations.find((config) => config.uid === data.uid);
      if (!group) return;

      const updatedOptions = group.options.map((option) => {
        
        if (option.uid === data.oldItem.uid) {
          return { ...option, name: data.newItem };
        }
        return option;
      });

      const sendData = {
        name: data.name,
        uid: data.uid,
        items: updatedOptions,
      };

      this.$emit('editConfigurationGroup', sendData, this.currentPage*this.perPage);
    },
    editConfigurationGroup(data) {
      const groupItems = this.allConfigurations.find((config) => config.uid === data.uid)?.options;
      const sendData = {
        ...data,
        items: groupItems,
      };

      this.$emit('editConfigurationGroup', sendData, this.currentPage*this.perPage);
      this.isEditOrDelete = true;

    },
    deleteConfigurationGroup(data) {
      this.selectedConfiguration = {
        uid: data.uid,
        name: data.name,
        options: this.allConfigurations.find(config => config.uid === data.uid)?.options || []
      };
      this.showDeleteConfirmDialog = true;
    },
    confirmDeleteConfigurationGroup() {
      this.$emit('deleteConfigurationGroup', {
        uid: this.selectedConfiguration.uid,
        name: this.selectedConfiguration.name
      }, this.currentPage*this.perPage);
      this.isEditOrDelete = true;
      this.showDeleteConfirmDialog = false;
    },
    deleteConfigurationItem(item) {
      this.$emit('deleteConfigurationItem', item, this.currentPage*this.perPage);
      this.isEditOrDelete = true;
    },
    fetchMoreConfigurations(){
      if (this.configurations.length < this.perPage) return;
      this.currentPage += 1;
      this.$emit('loadMoreConfigurations', this.perPage, this.currentPage);
    },
    onIntersect(entries){
      if (entries[0].isIntersecting && entries[0].intersectionRatio === 1){
        this.fetchMoreConfigurations()
      }
    }
  },
};
</script>

<style scoped>
.matrix-item{
  display: flex;
  justify-content: flex-start
}

.v-expansion-panel-content__wrap {
  padding: 0 !important;
}

.matrix-item {
  background: #F9FAFB;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
}

.matrix-label {
  color: #667085;
  font-size: 14px;
  margin-bottom: 4px;
}

.matrix-value {
  color: #101828;
  font-size: 16px;
  font-weight: 500;
}
</style>
