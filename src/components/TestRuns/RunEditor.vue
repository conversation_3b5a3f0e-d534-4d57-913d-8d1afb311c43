<template>
  <v-card
    class="white pt-4 px-6 pb-0 my-0"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <v-container fluid>
      <div class="d-flex flex-row justify-space-between align-center gap-10 mb-4">
        <div class="d-flex flex-column justify-start">
          <div>
            <div
              class="back-to-projects"
              @click="handleBackClick"
            >
              <v-icon color="blue">
                mdi-chevron-left
              </v-icon>
              <p class="d-flex-inline justify-center align-center ma-0 blue--text font-weight-bold">
                {{ $t('testruns.create_testrun.back_to_testrun') }} 
              </p>
            </div>
          </div>
        </div>
        <div class="d-flex align-center gap-5">
          <div class="d-flex align-start justify-start gap-5">
            <span
              v-if="!skeletonLoaderState"
              class="mr-2 text-theme-secondary"
            >{{ $t('testruns.create_testrun.configurations') }}</span>
            <v-skeleton-loader
              v-else
              class="mr-3"
              height="24"
              width="75"
              type="text"
            />
            <template v-if="!skeletonLoaderState">
              <span
                v-if="item.configs.length > 0"
                class="font-weight-medium"
              >
                {{ item.configs?.map(config => `${config.name}&${config.option}`).join(', ') }}
              </span>
              <span v-else>
                {{ $t('none') }}
              </span>
            </template>
            <v-skeleton-loader
              v-else
              class="mr-3"
              height="24"
              width="75"
              type="text"
            />
          </div>
          <div
            cols="auto"
            class="d-flex flex-row align-center pt-0"
            height="40"
          >
            <span
              v-if="!skeletonLoaderState"
              class="mr-2 text-theme-secondary"
            >{{ $t('testruns.status') }}</span>
            <v-skeleton-loader
              v-else
              class="mr-3"
              height="24"
              width="75"
              type="text"
            />
            <v-select
              v-if="!skeletonLoaderState"
              v-model="item.status"
              type="text"
              dense
              single-line
              filled
              hide-details
              :items="statuses"
              item-text="name"
              item-value="id"
              class="rounded-lg ma-0"
              append-icon="mdi-chevron-down"
              clear-icon="body-2"
              width="90"
              :menu-props="{ offsetY: true }"
              @change="updateFilter"
            />
            <v-skeleton-loader
              v-else
              height="40"
              width="235"
              type="text"
            />
          </div>
        </div>
      </div>
      
      <div class="d-flex flex-column justify-start">
        <div class="mb-1">
          <template v-if="!skeletonLoaderState">
            <v-hover
              v-if="!isEditRun"
            >
              <div class="d-flex flex-row">
                <h2 class="edit-plan">
                  {{ item.name }}
                </h2>
                <button
                  class="ml-3 mt-1"
                  @click="handleEditRunDetail"
                >
                  <PencilIcon />
                </button>
              </div>
            </v-hover>
            <v-responsive v-else>
              <v-text-field
                v-model="item.name"
                dense
                solo
                flat
                class="d-flex rounded-lg my-3 black--text font-weight-bold fs-24px"
                background-color="#F9FAFB"
                hide-details
                @change="updateFilter"
              />
            </v-responsive>
          </template>
          <v-skeleton-loader
            v-else
            class="mb-3"
            height="36"
            width="500"
            type="text"
          />
        </div>
        <div>
          <template v-if="!skeletonLoaderState">
            <p
              v-if="!isEditRun"
              class="edit-plan text-start"
              style="color: #667085; font-size: 14px; line-height: 14px"
            >
              {{ item.description }}
            </p>
            <v-responsive v-else>
              <v-textarea
                v-model="item.description"
                :value="item.description"
                background-color="#F9FAFB"
                class="d-flex rounded-lg mb-3"
                dense
                solo
                flat
                auto-grow
                hide-details
                @change="updateFilter"
              />
            </v-responsive>
          </template>
          <v-skeleton-loader
            v-else
            height="24"
            width="350"
            type="text"
          />
        </div>
      </div>

      <div class="d-flex justify-space-between gap-3">
        <div class="d-flex flex-column w-25">
          <div class="text-left">
            <v-label
              v-if="!skeletonLoaderState"
              class="fs-14px text-theme-label font-weight-medium"
            >
              {{ $t('milestone.create_milestone.dueDate') }}
            </v-label>
            <v-skeleton-loader
              v-else
              class="my-2"
              height="16"
              width="125"
              type="text"
            />
          </div>
          <v-menu
            v-if="!skeletonLoaderState"
            v-model="menuDueDate"
            max-width="290"
            top
            offset-y
          >
            <template #activator="{ on }">
              <div class="calendar-textbox-container">
                <v-text-field
                  dense
                  background-color="#F9F9FB"
                  height="38px"
                  color="blue"
                  class="field-theme rounded-lg"
                  :value="selectedDueDate"
                  placeholder="MM/DD/YY"
                  v-on="on"
                />
                <calendarBlueIcon 
                  class="calendar-icon"  
                  v-on="on"   
                />
              </div>
            </template>
            <v-date-picker
              v-model="selectedDueDate"
              @change="onDateChange"
            />
          </v-menu>
          <v-skeleton-loader
            v-else
            height="36"
            width="100%"
            type="button"
          />
        </div>
        <div class="d-flex flex-column w-25">
          <div class="text-left">
            <v-label
              v-if="!skeletonLoaderState"
              class="fs-14px text-theme-label font-weight-medium"
            >
              {{ $t('testruns.create_testrun.milestone') }}
            </v-label>
            <v-skeleton-loader
              v-else
              class="my-2"
              height="16"
              width="125"
              type="text"
            />
          </div>
          <v-select
            v-if="!skeletonLoaderState"
            v-model="selectedMilestones"
            background-color="#F9F9FB"
            type="text"
            dense
            :placeholder="$t('testruns.milestone_dialog.content')"
            hide-details
            class="rounded-lg field-theme custom-prepend mh-38px"
            append-icon="mdi-chevron-down"
            :items="milestones"
            item-key="uid"
            item-text="name"
            item-value="uid"
            multiple
            clear-icon="body-2"
            :menu-props="{ offsetY: true }"
            @change="updateFilter"
          >
            <template #selection="{ item }">
              <div
                class="d-flex align-center custom-chip-theme mr-1 mb-1"
              >
                <div class="text-theme-label label text-truncate mr-1">
                  {{ item.name }}
                </div>
                <v-icon
                  size="16px"
                  @click="onRemoveSelectedMilestone(item.uid)"
                >
                  mdi-close
                </v-icon>
              </div>
            </template>

            <template #item="{ item, on, attrs }">
              <v-list-item
                :ripple="false"
                v-bind="attrs"
                v-on="on"
              >
                <v-list-item-action>
                  <v-checkbox
                    hide-details
                    :input-value="milestoneSelection(item.uid)"
                    class="field-theme mt-0 pt-0"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                    </template>
                  </v-checkbox>
                </v-list-item-action>
              </v-list-item>
            </template>
          </v-select>
          <v-skeleton-loader
            v-else
            height="36"
            width="100%"
            type="button"
          />
        </div>
        <div class="d-flex flex-column w-25">
          <div class="text-left">
            <v-label
              v-if="!skeletonLoaderState"
              class="fs-14px text-theme-label font-weight-medium"
            >
              {{ $t('testruns.create_testrun.priority') }}
            </v-label>
            <v-skeleton-loader
              v-else
              class="my-2"
              height="16"
              width="125"
              type="text"
            />
          </div>
          <v-select
            v-if="!skeletonLoaderState"
            v-model="item.priority"
            type="text"
            dense
            background-color="#F9F9FB"
            :placeholder="$t('testruns.selectPriority')"
            class="rounded-lg field-theme custom-prepend mh-38px"
            hide-details
            append-icon="mdi-chevron-down"
            :items="priorities"
            clear-icon="body-2"
            item-text="name"
            item-value="id"
            :menu-props="{ offsetY: true }"
            @change="updateFilter"
          />
          <v-skeleton-loader
            v-else
            height="36"
            width="100%"
            type="button"
          />
        </div>
        <div class="d-flex flex-column w-25">
          <div class="text-left">
            <v-label
              v-if="!skeletonLoaderState"
              class="fs-14px text-theme-label font-weight-medium"
            >
              {{ $t('testruns.create_testrun.tags') }}
            </v-label>
            <v-skeleton-loader
              v-else
              class="my-2"
              height="16"
              width="125"
              type="text"
            />
          </div>
          <v-select
            v-if="!skeletonLoaderState"
            v-model="selectedTags"
            background-color="#F9F9FB"
            dense
            :items="tags"
            item-value="uid"
            item-text="name"
            :placeholder="$t('testruns.selectTags')"
            class="rounded-lg field-theme custom-prepend mh-38px"
            append-icon="mdi-chevron-down"
            hide-details="true"
            :menu-props="{ offsetY: true }"
            multiple
            @change="updateFilter"
          >
            <template #selection="{ item }">
              <div
                class="d-flex align-center custom-chip-theme mr-1 mb-1"
              >
                <div class="text-theme-label label text-truncate mr-1">
                  {{ item.name }}
                </div>
                <v-icon
                  size="16px"
                  @click="onRemoveSelectedTags(item.uid)"
                >
                  mdi-close
                </v-icon>
              </div>
            </template>

            <template #item="{ item, on, attrs }">
              <v-list-item
                :ripple="false"
                v-bind="attrs"
                v-on="on"
              >
                <v-list-item-action>
                  <v-checkbox
                    hide-details
                    :input-value="tagsSelection(item.uid)"
                    class="field-theme mt-0 pt-0"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                    </template>
                  </v-checkbox>
                </v-list-item-action>
              </v-list-item>
            </template>
          </v-select>
          <v-skeleton-loader
            v-else
            height="36"
            width="100%"
            type="button"
          />
        </div>
      </div>

      <!-- References Section -->
      <div
        v-if="item.references && item.references.length > 0"
        class="mt-4"
      >
        <h4 class="custom_field_heading">
          {{ $t('references') }}
        </h4>
        <div class="d-flex flex-wrap gap-2">
          <v-tooltip
            v-for="(reference, index) in item.references"
            :key="index"
            bottom
          >
            <template #activator="{ on, attrs }">
              <div
                v-bind="attrs"
                class="reference-chip d-flex align-center justify-space-between w-full px-2 py-1 rounded-lg mr-2 mb-2"
                style="background: #F2F4F7; border: 1px solid #E4E7EC; cursor: pointer; max-width: 200px;"
                v-on="on"
                @click="window.open(reference.externalLink, '_blank')"
              >
                <div
                  class="d-flex align-center"
                  style="min-width: 0; flex: 1;"
                >
                  <span
                    class="fs-12px text-theme-label mr-1 text-truncate"
                    style="min-width: 0; flex: 1; font-weight: 500;"
                  >{{ reference.name }}</span>
                </div>
                <a
                  :href="reference.externalLink"
                  target="_blank"
                  class="reference-link"
                  style="text-decoration: none; color: inherit;"
                  @click.stop
                >
                  <v-icon
                    size="12"
                    class="text-theme-secondary"
                  >
                    mdi-arrow-top-right
                  </v-icon>
                </a>
              </div>
            </template>
            <span>{{ reference.name }}</span>
          </v-tooltip>
        </div>
      </div>
    </v-container>
    <slot
      v-if="!skeletonLoaderState"
      name="additional-actions"
    />
    <RunDiscardDialog
      v-model="showConfirmBackDialog"
      :title="$t('testruns.edit_testrun.title')"
      :content="$t('testruns.edit_testrun.content')"
      :btn_label="$t('testruns.edit_testrun.btn_label')"
      color="danger"
      @close="handleCloseClick"
      @handleConfirmClick="handleConfirmClick"
    />
  </v-card>
</template>

<script>
import RunDiscardDialog from '@/components/TestRuns/RunDiscardDialog.vue';
import makeTagsService from '@/services/api/tag';
import { showErrorToast } from '@/utils/toast';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import handleLoading from '@/mixins/loader.js'
import PencilIcon from '@/assets/svg/pencil.svg';
import calendarBlueIcon from '@/assets/svg/calendar-blue.svg';
import { useDateFormatter } from '@/composables/utils/dateFormatter';

let tagService;
export default {
  name: 'RunEditor',
  components: {
    RunDiscardDialog,
    PencilIcon,
    calendarBlueIcon
  },
  mixins: [colorPreferencesMixin, handleLoading],
  props: {
    item: Object,
    milestones: Array,
  },
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },
  data() {
    return {
      tags: [],
      forms: {
        status: '',
        assign: '',
        milestone: '',
        priority: '',
        tags: ''
      },
      menuDueDate: false,
      showConfirmBackDialog: false,
      selectedTags: [],
      selectedMilestones: [],
      selectedDueDate: null,
      statuses: [],
      priorities: [],
      isEditRun: false,
    };
  },
  computed: {
    milestoneIds() {
      return this.item?.testMilestones?.map((item) => item.uid);
    },
    getTags() {
      return this.item?.tags?.length > 0 ? this.item?.tags.map((item) => item.uid) : [];
    },
    getDueDate() {
      const dueAt = this.item.customFields.dueAt;
      return dueAt ? this.formatDate(dueAt, 'YYYY-MM-DD') : '';
    },
  },
  created() {
    tagService = makeTagsService(this.$api);
    this.priorities = this.getPriorities("testRun").filter(element => !element.archived);
    this.statuses = this.getStatuses("testRun").filter(element => !element.archived);
  },
  async mounted() {
    this.selectedMilestones = this.milestoneIds;
    this.selectedTags = this.getTags;
    this.selectedDueDate = this.getDueDate;
    try {
      this.showSkeletonLoader();
      await this.getAllTags();
    } catch (err) {
      showErrorToast(this.$swal, this.$t('fetchError', { item: 'tags' }), {}, err?.response?.data)
    } finally {
      this.hideSkeletonLoader();
    }
    
  },
  methods: {
    handleEditRunDetail() {
      this.isEditRun = true;
    },
    updateFilter() {
      this.$emit('update-filter', this.item);
      this.$emit('update-tags', this.selectedTags);
      this.$emit('update-milestones', this.selectedMilestones);
    },
    onDateChange(date) {
      this.selectedDueDate = date;
      this.menuDueDate = false;
      this.$emit('update-due-date', date);
    },
    handleBackClick() {
      this.showConfirmBackDialog = true;
    },
    handleCloseClick() {
      this.showConfirmBackDialog = false;
    },
    handleConfirmClick() {
      this.showConfirmBackDialog = false;
      const currentFolderUid = this.$route.params.folderUid;
      if(this.$route.query.redirectTo && this.$route.query.redirectTo === 'TestPlanDetail'){
        this.$router.replace({
          name: this.$route.query.redirectTo,
          params: {...this.$route.params, planId: this.$route.query.planId }
        });
      } else {
        this.$router.replace({ 
          name: 'Runs',
          params: {
          handle: this.$route.params.handle,
          key: this.$route.params.key,
          folderUid: currentFolderUid
        }
       });
      }
    },
    onRemoveSelectedMilestone(uid) {
      const index = this.selectedMilestones.indexOf(uid);
      if (index !== -1) {
        this.selectedMilestones.splice(index, 1);
      }
      this.$emit('update-milestones', this.selectedMilestones);
    },
    milestoneSelection(uid) {
      return this.selectedMilestones ? this.selectedMilestones.some((id) => id === uid) : false;
    },
    onRemoveSelectedTags(uid) {
      const index = this.selectedTags.indexOf(uid);
      if (index !== -1) {
        this.selectedTags.splice(index, 1);
      }
      this.$emit('update-tags', this.selectedTags);
    },
    tagsSelection(uid) {
      return this.selectedTags ? this.selectedTags.some((id) => id === uid) : false;
    },

    async getAllTags() {
      try {
        const response = await tagService.getTags(
          this.$route.params.handle,
          'runs'
        );
        if (response.status === 200) {
          
          this.tags = response.data;
          this.$emit('tags-list', this.tags);

        }
      } catch (err) {
        showErrorToast(this.$swal, this.$t('fetchError', { item: 'tags' }), {}, err?.response?.data)
      } 
    },

  },
};
</script>

<style lang="scss" scoped>
.reference-chip {
  transition: all 0.2s ease;
  cursor: pointer;
}

.reference-chip:hover {
  background: #E4E7EC !important;
  transform: translateY(-1px);
}

.reference-link {
  text-decoration: none;
  color: inherit;
}

.reference-link:hover {
  opacity: 0.8;
}

.custom_field_heading {
  color: #667085;
  font-weight: 400;
  font-size: 13px;
  margin: 12px 0 4px 0px;
}
</style>

<style scoped>
.calendar-icon {
  position: absolute;
  right: 12px;
  top: 9px;
}
.box-shadow-none {
  box-shadow: none;
}

.calendar-textbox-container {
  position: relative;
}
.btn-hide{
  display: none;
}
.btn-show{
  display: block !important;
}
h2{
  font-weight: 900;
}

.custom-header{
  height: 118px;
}
.edit-plan{
  max-width: 476px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.back-to-projects {
  display: flex;
  cursor: pointer;
  width: max-content;
}

.responsive-text-field {
  display: inline-block;
  width: auto;
}

/* .horizontal-margin {
  margin: 0px 10px;
} */

/* .tray-btn-margin {
  min-width: 40px !important;
  width: 40px !important;
  padding: 10px 0px !important;
}

.tray-btn-outline {
  border-radius: 8px;
  box-shadow: none;
} */
</style>
