<template>
  <div>
    <v-form ref="form">
      <v-dialog
        v-model="isOpenStatus"
        class="test-cases-filter-drawer dialog-theme"
        transition="slide-x-transition"
        attach
        fullscreen
        width="485px"
        @click:outside="resetDialog"
      >
        <v-card>
          <v-card-text class="black--text">
            <div class="d-flex align-center justify-space-between pt-6 mb-4">
              <h2 class="black--text">
                {{ $t('edit') }} 
              </h2>
              <v-btn
                icon
                @click="$emit('closeDialog')"
              >
                <v-icon color="black">
                  mdi-close
                </v-icon>
              </v-btn>
            </div>
            <div>
              <div class="select-title mt-4 mb-1">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('priority') }}
                </v-label>
              </div>
              <v-select
                v-model="selectedPriority"
                placeholder="Choose priority"
                append-icon="mdi-chevron-down"
                background-color="#F9F9FB"
                :items="priorities"
                item-text="name"
                item-value="id"
                :rules="requiredRules"
                class="rounded-lg field-theme custom-prepend"
                dense
                height="38" 
                :menu-props="{ offsetY: true }"
              />

              <div class="select-title mt-4 mb-1">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('status') }}
                </v-label>
              </div>
              <v-select
                v-model="selectedStatus"
                :placeholder="$t('chooseStatus')"
                :items="statuses"
                item-text="name"
                item-value="id"
                :rules="requiredRules"
                append-icon="mdi-chevron-down"
                background-color="#F9F9FB"
                class="rounded-lg field-theme custom-prepend"
                dense
                height="38" 
                :menu-props="{ offsetY: true }"
              />

              <div class="select-title mt-4 mb-1">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('assignTo') }}
                </v-label>
              </div>

              <v-select 
                v-model="selectedAssignee"
                :items="filteredAssignees"
                :placeholder="$t('chooseUser')"
                :item-text="item => `${item.firstName} ${item.lastName}`"
                :item-value="item => `${item.uid}`"
                append-icon="mdi-chevron-down"
                dense 
                height="38"
                flat
                background-color="#F9F9FB"
                class="rounded-lg field-theme custom-prepend"
                :menu-props="{ offsetY: true }"
              >
                <template #prepend-item>
                  <v-list-item>
                    <v-list-item-content>
                      <v-text-field 
                        v-model="searchAssignee" 
                        class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
                        :placeholder="$t('search')"
                        height="40"
                        background-color="#F9F9FB"
                        hide-details
                      >
                        <template #prepend-inner>
                          <SearchIcon />
                        </template>
                      </v-text-field>
                    </v-list-item-content>
                  </v-list-item>
                </template>
                  
                <template #no-data>
                  <span class="font-weight-regular fs-16px text-theme-label">{{ $t('noMatchingAssignees') }}</span>
                </template>

                <template #selection="{ item }">
                  <span class="font-weight-regular fs-16px text-theme-label">{{ item.firstName }} {{ item.lastName }}</span>
                </template>

                <template #item="{ item, on, attrs }">
                  <v-list-item class="mh-36px cursor-pointer">
                    <v-list-item-content class="py-0">
                      <v-list-item-title>
                        <span
                          v-bind="attrs"
                          class="font-weight-regular fs-16px text-theme-label"
                          @click="on.click"
                        >{{ item.firstName }} {{ item.lastName }}</span>
                      </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-select>
              
              <div class="select-title mt-4 mb-1">
                <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                  {{ $t('addTags') }}
                </v-label>
              </div>
              <TagSelector
                v-model="tagsData.selectedTags"
                :items="tags"
                class="pt-1 mt-0 rounded-lg field-theme custom-prepend"
              />
              <section class="d-flex flex-column w-full">
                <div class="d-flex w-full justify-space-between align-center">
                  <p class="font-weight-medium ma-0">
                    {{ $t('replaceExistingTags') }}
                  </p>
                  <v-switch
                    v-model="showReplaceTag"
                    inset
                  />
                </div>

                <section
                  v-if="showReplaceTag"
                  class="d-flex w-full flex-column"
                >
                  <div
                    class="d-flex flex-column"
                  >
                    <div class="select-title mt-4 mb-1">
                      <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                        {{ $t('replaceTag') }}
                      </v-label>
                    </div>
                    <TagSelector
                      v-model="tagsData.replaceTag"
                      :items="tags"
                      class="pt-1 mt-0 rounded-lg field-theme custom-prepend"
                    />
                  </div>
                  <div
                    class="d-flex flex-column"
                  >
                    <div class="select-title mb-1 mt-4">
                      <v-label class="text-left fs-14px text-theme-label font-weight-medium">
                        {{ $t('with') }}
                      </v-label>
                    </div>
                    <TagSelector
                      v-model="tagsData.withTag"
                      :items="tags"
                      class="pt-1 mt-0 rounded-lg field-theme custom-prepend"
                    />
                  </div>
                </section>
              </section>
            </div>
          </v-card-text>
        </v-card>
        <div class="actions-container d-flex d-flex justify-space-between">
          <v-btn
            width="204.5px"
            color="#F2F4F7"
            full-width
            height="40"
            depressed
            class="text-capitalize btn-theme"
            elevation="0"
            @click="$emit('closeDialog')"
          >
            {{ $t('cancel') }}
          </v-btn>
          <v-btn
            width="204.5px"
            class="btn-theme text-capitalize"
            height="40"
            color="primary"
            :depressed="true"
            full-width
            elevation="0"
            @click="updateExecutions"
          >
            {{ $t('save') }}
          </v-btn>
        </div>
      </v-dialog>
    </v-form>
  </div>
</template>

<script>
import SearchIcon from '@/assets/svg/search-icon.svg';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import TagSelector from '@/components/base/TagSelector.vue';
import { showErrorToast } from '@/utils/toast';
import makeTagService from '@/services/api/tag';

export default {
  name: 'EditRunDialog',
  components: {
    SearchIcon,
    TagSelector
  },
  mixins: [colorPreferencesMixin],
  props: {
    isOpen: Boolean,
    items: Array,
    assignees: Array
  },
  data() {
    return {
      selectedStatus: null,
      selectedPriority: null,
      selectedAssignee: null,
      isOpenStatus: this.isOpen,
      requiredRules: [
        value => !!value || this.$t('error.requiredField'),
      ],
      searchAssignee: '',
      statuses: [],
      priorities: [],
      tagsData: {
        selectedTags: [],
        replaceTag: [],
        withTag: [],
      },
      showReplaceTag: false,
      tags: [],
    };
  },
  computed: {
    filteredAssignees() {
      return this.assignees?.filter(assignee => {
        return assignee?.firstName?.toLowerCase().includes(this.searchAssignee?.toLowerCase()) ||
          assignee?.lastName?.toLowerCase().includes(this.searchAssignee?.toLowerCase());
      });
    }
  },
  watch: {
    isOpen(newVal) {
      this.isOpenStatus = newVal;
    }
  },
  async created() {
    this.priorities = this.getPriorities("testCase").filter(element => !element.archived);
    this.statuses = this.getStatuses("testExecution").filter(element => !element.archived);
    await this.fetchTags();
  },
  methods: {
    resetDialog() {
      this.$refs.form.reset();
      this.$emit("closeDialog");
    },
    updateExecutions(){
      if(this.$refs.form.validate()){
        const payload = {
          status: this.selectedStatus,
          priority: this.selectedPriority,
          tagUids: this.tagsData.selectedTags.map(tag => tag.uid),
            tagReplacements: [
              {
                existingTagIds: this.tagsData.replaceTag.map(tag => tag.uid),
                newTagIds: this.tagsData.withTag.map(tag => tag.uid)
              }
            ]
            
        }

        this.selectedAssignee && (payload.assignedTo = this.selectedAssignee);

        this.$emit('closeDialog')
        this.$parent.$emit('updateExecutions', payload)
      }
    },
    async fetchTags() {
      const tagService = makeTagService(this.$api);
      try {
        const response = await tagService.getTags(this.$route.params.handle, 'cases');
        if (response.status === 200) {
          this.tags = response.data.map((tag) => ({ uid: tag.uid, name: tag.name }));
        } else {
          showErrorToast(this.$swal, 'fetchError', { item: 'Tags' });
        }
      } catch (error) {
        showErrorToast(this.$swal, 'fetchError', { item: 'Tags' }, error?.response?.data);
      }
    },
  }

};
</script>
<style scoped>
.select-title {
  font-family: Inter;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-align: left;
}
</style>
