<template>
  <div>
    <v-sheet
      v-if="!skeletonLoaderState"
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40px"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-3 py-2 d-flex flex-row">{{ $t('filters') }} <v-icon
        size="16px"
        class="ml-2"
      >mdi-filter-variant</v-icon></span>
    </v-sheet>
    <v-skeleton-loader
      v-else
      class="rounded-lg"
      height="40"
      width="95"
      type="button"
    />
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('filters') }}
            </h2>
            <v-btn
              icon
              @click="showDialog = false"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <v-expansion-panels
            v-model="priorityPanel"
            flat
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('testruns.create_testrun.priority') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-checkbox
                  v-for="(item,index) in priorities"
                  :key="index"
                  v-model="panelPriority"
                  :value="item.id"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :hide-details="true"
                >
                  <template #label>
                    <span class="fs-14px text-theme-label">{{ item.name }}</span>
                  </template>
                </v-checkbox>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>
          <v-expansion-panels
            v-model="statusPanel"
            flat
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('testruns.create_testrun.status') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-checkbox
                  v-for="(item,index) in statuses"
                  :key="index"
                  v-model="panelStatus"
                  :value="item.id"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :hide-details="true"
                >
                  <template #label>
                    <span class="fs-14px text-theme-label">{{ item.name }}</span>
                  </template>
                </v-checkbox>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>

          <v-expansion-panels
            v-model="milestonePanel"
            flat
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('milestones') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-checkbox
                  v-for="(item,index) in milestones"
                  :key="index"
                  v-model="panelMilestone"
                  :value="item.uid"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :hide-details="true"
                >
                  <template #label>
                    <span class="fs-14px text-theme-label">{{ item.name }}</span>
                  </template>
                </v-checkbox>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>

          <div 
            v-for="(config, index) in configurations" 
            :key="index"
            class="mb-5"
          >
            <Selection             
              v-model="selectedConfig"
              :title="config.name"    
              :items="config.options"
            />
          </div>
          
          <div class="text-start pt-6">
            <div class="text-start mb-2">
              <v-label class="text-theme-label font-weight-medium">
                {{ $t('creationDate') }}
              </v-label>
            </div>
            <div class="d-flex align-center">
              <v-menu
                v-model="menu1"
                :close-on-content-click="false"
                max-width="290"
              >
                <template #activator="{ on }">
                  <v-text-field
                    dense
                    single-line
                    class="text-field field-theme mt-0 pa-0 rounded-lg custom-prepend"
                    :value="dateStart"
                    background-color="#F9F9FB"
                    readonly
                    height="38"
                    hide-details
                    v-on="on"
                  >
                    <template #prepend-inner>
                      <calendarBlueIcon />
                    </template>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="dateStart"
                  :max="dateEnd || undefined"
                  @input="menu1 = false"
                />
              </v-menu>
              <div class="mx-4 font-weight-bold text-h6">
                {{ $t('-') }}
              </div>
              <v-menu
                v-model="menu2"
                :close-on-content-click="false"
                max-width="290"
              >
                <template #activator="{ on }">
                  <v-text-field
                    background-color="#F9F9FB"
                    class="text-field mt-0 field-theme pa-0 rounded-lg custom-prepend"
                    :value="dateEnd"
                    readonly
                    height="40"
                    hide-details
                    v-on="on"
                  >
                    <template #prepend-inner>
                      <calendarBlueIcon />
                    </template>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="dateEnd"
                  :min="dateStart || undefined"
                  @input="menu2 = false"
                />
              </v-menu>
            </div>
            <v-expansion-panels
              v-model="tagPanel"
              flat
              class="mb-8"
            >
              <v-expansion-panel>
                <v-expansion-panel-header class="mx-0 px-0">
                  <div class="text-start">
                    <v-label class="text-theme-label font-weight-medium">
                      {{ $t('testruns.create_testrun.tag') }}
                    </v-label>
                  </div>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-text-field
                    v-model="searchTag"
                    :placeholder="$t('search')"
                    background-color="#F9F9FB"
                    class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
                    height="38"
                    dense
                    hide-details
                  >
                    <template #prepend-inner>
                      <SearchIcon />
                    </template>
                  </v-text-field>
                  <v-checkbox
                    v-for="(item,index) in filterTags"
                    :key="index"
                    v-model="panelTag"
                    :value="item.uid"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">{{ item.name }}</span>
                    </template>
                  </v-checkbox>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="clearAll"
        >
          {{ $t('clearAll') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          @click="apply"
        >
          {{ $t('apply') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import SearchIcon from '@/assets/svg/search-icon.svg';
import calendarBlueIcon from '@/assets/svg/calendar-blue.svg';
import Selection from '@/components/TestRuns/Selection.vue';
import colorPreferencesMixin from '@/mixins/colorPreferences';
import handleLoading from '@/mixins/loader.js';

export default {
  name: 'ProjectFilter',
  components: { SearchIcon, calendarBlueIcon, Selection },
  mixins: [colorPreferencesMixin, handleLoading],
  props: {
    configurations: { type: Array, default: () => [] },
    tags: { type: Array, default: () => [] },
    milestones: { type: Array, default: () => [] },
    currentFilters: { type: Object, default: () => ({}) }
  },
  data() {
    return {
      searchTag: null,
      panelPriority: [],
      panelStatus: [],
      selectedConfig: [],
      panelTag: [],
      panelMilestone: [],
      dateStart: null,
      dateEnd: null,
      menu1: false,
      menu2: false,
      showDialog: false,
      priorityPanel: 0,
      statusPanel: 0,
      milestonePanel: 0,
      tagPanel: 0,
      configPanel: 0,
      statuses: [],
      priorities: []
    };
  },
  computed: {
    filterTags() {
      return this.searchTag
        ? this.tags.filter(tag => tag.name.startsWith(this.searchTag))
        : this.tags;
    },
  },
  created() {
    this.priorities = this.getPriorities('testRun');
    this.statuses = this.getStatuses('testRun');
  },
  methods: {
    apply() {
      if (!this.hasActiveFilters()) {
        this.$emit('applyFilters', null);
        this.showDialog = false;
        return;
      }

      const enhancedFilters = {
        panelPriority: {
          type: 'array', label: this.$t('testruns.create_testrun.priority'), value: this.panelPriority.map(id => {
            const obj = this.priorities.find(p => p.id === id);
            return { name: obj.name, id: obj?.id || null, uid: obj?.uid || null }; })
        },
        panelStatus: {
          type: 'array', label: this.$t('testruns.create_testrun.status'), value: this.panelStatus.map(id => {
            const obj = this.statuses.find(s => s.id === id);
            return { name: obj.name, id: obj?.id || null, uid: obj?.uid || null }; })
        },
        panelTag: {
          type: 'array', label: this.$t('testruns.create_testrun.tag'), value: this.panelTag.map(uid => {
            const t = this.tags.find(t => t.uid === uid);
            return { name: t.name, id: t?.id || null, uid: t?.uid || null }; })
        },
        panelMilestone: {
          type: 'array', label: this.$t('milestones'), value: this.panelMilestone.map(uid => {
            const m = this.milestones.find(m => m.uid === uid);
            return { name: m.name, id: m?.id || null, uid: m?.uid || null }; })
        },
        panelConfigurations: {
          type: 'array',
          label: this.$t('configurations'),
          value: this.selectedConfig
            .map(optionUid => {
              const optionItem = this.configurations
                .flatMap(cfg => cfg.options)
                .find(o => o.uid === optionUid);

              return {
                name: optionItem?.name ?? '',
                uid:   optionUid
              };
            })
            .filter(item => item.uid != null)
        },
        dateRange: {
          type: 'dateRange', label: this.$t('creationDate'), value: { start: this.dateStart, end: this.dateEnd }
        }
      };

      const apiFilters = {};
      if (this.panelPriority.length)   apiFilters.priorityUids = this.panelPriority;
      if (this.panelStatus.length)     apiFilters.statusUids = this.panelStatus;
      if (this.panelTag.length)        apiFilters.tagUids = this.panelTag;
      if (this.panelMilestone.length)  apiFilters.milestoneUids = this.panelMilestone;
      if (this.selectedConfig.length) {
        apiFilters.configs = this.selectedConfig
          .map(optionUid => {
            const cfg = this.configurations.find(c =>
              c.options.some(o => o.uid === optionUid)
            );
            if (!cfg) return null;        
            const parentUid = cfg.uid;   
            return `${parentUid}::${optionUid}`;
          })
          .filter(x => x !== null);       
      }

      if (this.dateStart)              apiFilters.fromCreatedAt = dayjs(this.dateStart).format();
      if (this.dateEnd)                apiFilters.toCreatedAt = dayjs(this.dateEnd).format();

      this.$emit('applyFilters', { ui: enhancedFilters, api: apiFilters });
      this.showDialog = false;
    },

    hasActiveFilters() {
      const a = this.panelPriority.length || this.panelStatus.length || this.panelTag.length || this.panelMilestone.length || this.selectedConfig.length;
      const d = this.dateStart || this.dateEnd;
      return a || d;
    },

    syncFilters(filters) {
      this.panelPriority = (filters.panelPriority?.value || filters.panelPriority || []).map(f => typeof f==='object'?f.id:f);
      this.panelStatus   = (filters.panelStatus?.value   || filters.panelStatus   || []).map(f => typeof f==='object'?f.id:f);
      this.panelTag      = (filters.panelTag?.value      || filters.panelTag      || []).map(f => typeof f==='object'?f.uid:f);
      this.panelMilestone= (filters.panelMilestone?.value|| filters.panelMilestone|| []).map(f => typeof f==='object'?f.uid:f);
      this.selectedConfig= (filters.panelConfigurations?.value || filters.panelConfigurations || []).map(f => typeof f==='object'?f.name:f);
      const dr = filters.dateRange?.value || filters.dateRange;
      this.dateStart     = dr?.start || null;
      this.dateEnd       = dr?.end   || null;
    },

    clearAll() {
      this.panelPriority = [];
      this.panelStatus = [];
      this.panelTag = [];
      this.panelMilestone = [];
      this.selectedConfig = [];
      this.dateStart = null;
      this.dateEnd = null;
      this.$emit('applyFilters', {});
      this.showDialog = false;
    }
  }
};
</script>

<style scoped>
.v-dialog--fullscreen {
  max-height: 100vh !important;
  width: 485px !important;
  right: 0 !important;
  left: auto !important;
}

.v-expansion-panel-content__wrap {
  padding: 0 !important;
}
</style>
