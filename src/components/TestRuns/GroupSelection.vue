<template>
  <v-expansion-panels
    v-model="panel"
    flat
  >
    <v-expansion-panel>
      <v-expansion-panel-header
        class="mx-0 px-0"
        disabled
      >
        <template #actions>
          <v-icon 
            @click.stop="panel = panel === 0 ? null : 0" 
          >
            $expand
          </v-icon>
        </template>
        <div class="d-flex justify-space-between align-center">
          <div>
            <v-checkbox
              v-model="parentChecked"
              class="field-theme mt-0"
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
              indeterminate-icon="icon-indeterminate"
              hide-details
              :indeterminate="parentIndeterminate"
              @change="toggleAll"
              @click.native.stop
            >
              <template #label>
                <div class="d-flex align-center justify-space-between w-100">
                  <div
                    v-if="isEditingTitle"
                    class="d-flex align-center"
                  >
                    <v-text-field
                      ref="titleInput"
                      v-model="editedTitle"
                      type="text"
                      background-color="transparent"
                      class="new-folder-input mt-0 pt-0 pa-0 editable"
                      hide-details
                      autofocus
                      height="24px"
                      solo
                      flat
                      dense
                      @blur="saveTitle"
                      @keyup.enter="saveTitle"
                      @click.stop
                    />
                  </div>
                  <span
                    v-else
                    class="fs-14 text-theme-label"
                  >{{ title }}</span>
                  <div
                    class="d-flex align-center"
                  >
                    <v-btn
                      icon
                      x-small
                      class="mr-2"
                      @click.stop="startEditTitle"
                    >
                      <PencilIcon />
                    </v-btn>
                    <v-btn
                      icon
                      x-small
                      @click.stop="deleteGroup"
                    >
                      <DeleteIcon />
                    </v-btn>
                  </div>
                </div>
              </template>
            </v-checkbox>
          </div>
          <ButtonWithMenu
            v-model="addConfigurationMenuItems"
            button-color="primary"
            :button-label="addLabel"
            :label="$t('name')"
            :placeholder="$t('testruns.create_testrun.egOSorBrowsersDescription')"
            :cancel-button-text="$t('cancel')"
            :apply-button-text="$t('add')"
            button-type-text
            :button-loading="buttonLoading"
            @cancel="cancelAddConfigurationItems"
            @apply="applyAddConfigurationItems"
          />
        </div>
      </v-expansion-panel-header>
      <span
        v-if="filteredItemsEmpty && !search"
        class="error--text fs-12px"
      >{{ $t("optionGroupsRequires") }}</span>
      <v-expansion-panel-content v-if="items.length">
        <v-text-field
          v-model="search"
          class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
          :placeholder="searchPlaceholder"
          height="40"
          background-color="#F9F9FB"
          :hide-details="true"
        >
          <template #prepend-inner>
            <SearchIcon />
          </template>
        </v-text-field>
        <v-checkbox
          v-for="(item, index) in filteredItems"
          :key="item.uid"
          v-model="selectedItems"
          :value="item.uid"
          class="field-theme d-flex align-center"
          :ripple="false"
          off-icon="icon-checkbox-off"
          on-icon="icon-checkbox-on"
          :hide-details="true"
          @change="updateParentState"
        >
          <template #label>
            <div class="d-flex align-center justify-space-between w-100">
              <div
                v-if="editingIndex === index"
                class="d-flex align-center"
              >
                <v-text-field
                  ref="itemInput"
                  v-model="editedValue"
                  class="new-folder-input mt-0 pt-0 pa-0 editable "
                  type="text"
                  background-color="transparent"
                  hide-details
                  autofocus
                  height="20px"
                  solo
                  flat
                  dense
                  @blur="saveItem(item)"
                  @keyup.enter="saveItem(item)"
                  @click.stop
                />
              </div>

            
              <span
                v-else
                class="fs-14px text-theme-label"
              >{{ item.name }}</span>
              <div class="d-flex align-center">
                <v-btn
                  icon
                  x-small
                  class="mr-2"
                  @click.stop="startEditItem(item, index)"
                >
                  <PencilIcon />
                </v-btn>
                <v-btn
                  icon
                  x-small
                  @click.stop="deleteItem(item)"
                >
                  <DeleteIcon />
                </v-btn>
              </div>
            </div>
          </template>
        </v-checkbox>
      </v-expansion-panel-content>
      <span
        v-if="filteredItemsEmpty && !!search"
        class="error--text fs-12px"
      >{{ $t("noResults") }}</span>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script>
import SearchIcon from '@/assets/svg/search-icon.svg';
import ButtonWithMenu from '@/components/base/Forms/CustomButton/ButtonWithMenu.vue';
import PencilIcon from '@/assets/svg/pencil-16.svg';
import DeleteIcon from '@/assets/svg/delete-16.svg';

export default {
  components: {
    SearchIcon,
    ButtonWithMenu,
    PencilIcon,
    DeleteIcon,
  },
  props: {
    isOpen: {
      type: Boolean,
      default: false,
    },
    uid: {
      type: Number,
    },
    title: {
      type: String,
    },
    items: {
      type: Array,
      required: true,
    },
    addLabel: {
      type: String,
      default: 'Add',
    },
    searchPlaceholder: {
      type: String,
      default: 'Search',
    },
    value: {
      type: [Array, String],
      default: () => [],
    },
    buttonLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      panel: 0,
      search: '',
      selectedItems: this.value,
      parentChecked: false,
      parentIndeterminate: false,
      addConfigurationMenuItems: false,
      isEditingTitle: false,
      editedTitle: '',
      editingIndex: null,
      editedValue: '',
    };
  },
  computed: {
    filteredItems() {
      if (this.search) {
        return this.items.filter((item) => 
          item.name.toLowerCase().includes(this.search.toLowerCase())
        );
      }
      return this.items;
    },
    filteredItemsEmpty() {
      return this.filteredItems.length === 0;
    },
    configurationItems() {
      return this.items;
    },
  },
  watch: {
    isOpen(newValue) {
      if (!newValue) {
        this.selectedItems = [];
        this.updateParentState();
      } else {
        this.selectedItems = this.value;
        this.updateParentState();
      }
    },
  },
  mounted() {
    this.updateParentState();
  },
  methods: {
    async applyAddConfigurationItems(newConfigurationName) {
      const items = [...this.configurationItems, newConfigurationName];

      this.$emit('addItem', {
        uid: this.uid,
        name: this.title,
        items: items,
        description: '',
      });
    },
    cancelAddConfigurationItems() {
      this.addConfigurationMenuItems = false;
    },
    toggleAll() {
      if (this.parentChecked) {
        this.selectedItems = [...this.filteredItems.map(item => item.uid)];
      } else {
        this.selectedItems = [];
      }
      this.updateParentState();
      this.emitSelectedItems();
    },
    updateParentState() {
      const totalItems = this.filteredItems.length;
      const selectedCount = this.selectedItems.filter((item) => this.filteredItems.map(item => item.uid).includes(item)).length;

      if (selectedCount === totalItems && totalItems > 0) {
        this.parentChecked = true;
        this.parentIndeterminate = false;
      } else if (selectedCount > 0) {
        this.parentChecked = false;
        this.parentIndeterminate = true;
      } else {
        this.parentChecked = false;
        this.parentIndeterminate = false;
      }
      this.emitSelectedItems();
    },
    emitSelectedItems() {
      this.$emit('input', this.selectedItems);
    },
    editItem(item) {
      this.$emit('editItem', {
        uid: this.uid,
        name: this.title,
        item: item,
      });
    },
    deleteItem(item) {
      const items = this.items.filter((i) => i !== item);
      this.search = '';
      this.$emit('deleteItem', {
        uid: this.uid,
        name: this.title,
        items: items,
      });
    },
    editGroup() {
      this.$emit('editGroup', {
        uid: this.uid,
        name: this.title,
      });
    },
    deleteGroup() {
      this.$emit('deleteGroup', {
        uid: this.uid,
        name: this.title,
      });
    },
    startEditTitle() {
      this.isEditingTitle = true;
      this.editedTitle = this.title;
      this.$nextTick(() => {
        if (this.$refs?.titleInput) {
          this.$refs.titleInput?.$el?.focus();
        }
      });
    },
    saveTitle() {
      if (this.editedTitle && this.editedTitle !== this.title) {
        this.$emit('editGroup', {
          uid: this.uid,
          name: this.editedTitle,
        });
      }
      this.isEditingTitle = false;
    },
    startEditItem(item, index) {
      this.editingIndex = index;
      this.editedValue = item.name;
      this.$nextTick(() => {
        if (this.$refs?.itemInput) {
          this.$refs?.itemInput?.$el?.focus();
        }
      });
    },
    saveItem(oldItem) {
      if (this.editedValue && this.editedValue !== oldItem.name) {
        this.$emit('editItem', {
          uid: this.uid,
          name: this.title,
          oldItem: oldItem,
          newItem: this.editedValue
        });
      }
      this.editingIndex = null;
      this.editedValue = '';
    },
  },
};
</script>

<style  lang="scss">

.editable{
  .v-input__slot{
    padding: 0 !important;
    margin: 0 !important;
  }
   .v-text-field__slot {
  padding: 0 !important;
  margin: 0 !important;
}
input{
  padding: 0 !important;
  margin: 0 !important;
}
}

.editable > * {
  border: none !important;
}
.v-text-field {
  margin-top: 0;
  margin-bottom: 0;
}

.v-text-field ::deep .v-input__control {
  min-height: 32px;
}

.v-text-field ::deep .v-input__slot {
  min-height: 32px;
}
</style>


