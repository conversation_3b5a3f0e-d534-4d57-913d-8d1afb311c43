<template>
  <v-container
    class="pa-6 white rounded-lg align-start card app-height-global"
    fluid
  >
    <v-row
      justify="start"
      align="start"
    >
      <v-col
        cols="7"
        sm="7"
        class="search-bar-style"
      >
        <v-responsive
          v-if="!skeletonLoaderState"
          class="ma-0"
          max-width="344"
        >
          <v-text-field
            v-model="searchTerm"
            class="text-field mr-3 mt-0 rounded-lg field-theme custom-prepend pa-0"
            :placeholder="$t('placeHolder.searchByColumn')"
            clearable
            clear-icon="mdi-close-circle"
            height="40"
            background-color="#F9F9FB"
          >
            <template #prepend-inner>
              <SearchIcon />
            </template>
          </v-text-field>
        </v-responsive>
        <v-skeleton-loader
          v-else
          class="rounded-lg mr-3"
          height="40"
          width="344"
          type="button"
        />
        <TestCasesFilter
          v-if="!skeletonLoaderState"
          @filters="applyFilters"
        />
        <v-skeleton-loader
          v-else
          class="rounded-lg mr-3"
          height="40"
          width="80"
          type="button"
        />
      </v-col>
      <v-col
        cols="5"
        sm="5"
        class="search-bar-style setting-btn-style"
      >
        <div class="btn-selector">
          <template>
            <div class="text-center">
              <SettingsMenu table-type="testExecution" />
            </div>
          </template>
        </div>
      </v-col>
      <v-col
        cols="12"
        sm="12"
      >
        <v-data-table
          v-if="!skeletonLoaderState"
          v-model="selectedRows"
          :headers="headers"
          :items="failedSyncs"
          item-key="uid"
          :disabled="true"
          show-select
          class="data-table-style table-fixed"
          :item-class="getItemClass"
          :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
          hide-default-footer
          disable-pagination
          @input="handleSelectTestCases"
        >
          <template #[`header.data-table-select`]="{ props }">
            <div class="d-flex justify-center align-center">
              <iconDots />
              <v-checkbox
                id="select-all-checkbox"
                class="field-theme"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                indeterminate-icon="icon-indeterminate"
                :input-value="props.value"
                :indeterminate="props.indeterminate"
                @change="handleSelectAll"
              />
            </div>
          </template>
          <template #[`item.data-table-select`]="{ isSelected, select }">
            <div class="d-flex justify-center align-center">
              <span
                v-if="isImport && isSelected"
                class="mr-1"
                style="margin-top: 6.5px; cursor: grab;"
              >
                <iconDots /> 
              </span>

              <v-checkbox
                id="remember-me-checkbox"
                class="field-theme"
                :ripple="false"
                off-icon="icon-checkbox-off"
                on-icon="icon-checkbox-on"
                :input-value="isSelected"
                @change="select"
                @click.stop
              /> 
            </div>
          </template>
          <template #[`item.date`]="{ item }">
            <p class="font-weight-regular">
              {{ item.date }}
            </p>
          </template>
          <template #[`item.milestone`]="{ item }">
            <p class="font-weight-regular">
              {{ item.milestone }}
            </p>
          </template>
          <template #[`item.testPlan`]="{ item }">
            <p class="font-weight-regular">
              {{ item.testPlan }}
            </p>
          </template>
          <template #[`item.testRun`]="{ item }">
            <p class="font-weight-regular">
              {{ item.testRun }}
            </p>
          </template>
          <template #[`item.actions`]="{ }">
            <v-icon
              class="mr-2"
              size="24"
            >
              mdi-restore
            </v-icon>
          </template>
        </v-data-table>
        <TestCasesListSkeleton v-else />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import TestCasesFilter from '@/views/Tests/Case/Components/Filter.vue';
import TestCasesListSkeleton from '@/components/Skeletons/TestCases/List.vue';
import SettingsMenu from '@/components/Project/SettingsMenu.vue';
import SearchIcon from '@/assets/svg/search-icon.svg';
export default {
  components: {
    TestCasesListSkeleton,
    SearchIcon,
    SettingsMenu,
    TestCasesFilter
  },
  props: {
    failedSyncs: {
      type: Array,
      required: true,
      default: () => [],
    },
    headers: {
      type: Array,
      required: true,
      default: () => [],
    },
    skeletonLoaderState: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  data() {
    return {
      selectedRows: [],
      isSelectedData: false,
      displayTableData: [],
      selectedData: [],
      searchTerm: '',
    };
  },
  methods: {
    getItemClass(item) {
      return item.isSelected ? 'selected-row' : '';
    },
    handleSelectTestCases() {
      this.isSelectedData = this.selectedRows.length > 0;
      this.selectedData = this.selectedRows.map((row) => row.uid);
      this.$emit('selected-test-cases', this.selectedData);
    },
    applyFilters(filters) {
      this.$emit('filters-applied', filters);
    },
  },
};
</script>
<style lang="scss" scoped>
.v-breadcrumbs {
  padding: 8px 12px;
  margin-bottom: 16px;
  display: flex;
  align-items: start;
}
</style>
<style scoped>
.color-red {
  color: #f2284e !important;
}
.f-color-red {
  color: #f2284e !important;
}
.round-8 {
  border-radius: 8px;
}
.round-6 {
  border-radius: 6px;
}
.h-40 {
  height: 40px !important;
}
.btn-selector {
  position: relative;
}
.modal-main-area {
  height: 100%;
  padding: 32px 32px 32px 32px;
}
.dialog-title {
  font-weight: 900 !important;
}
.filter-dialog {
  padding-top: 15px;
}
.dialog-action {
  width: 90%;
  display: flex;
  position: absolute;
  bottom: 25px;
}
.btn-selector .selector-wrapper {
  position: relative;
}
.selector-style {
  position: absolute;
  right: 0;
  left: unset;
  top: 30px;
  min-width: 240px;
}
.modal-btn {
  width: 45%;
}
.f-color-white {
  color: white !important;
}
.text-red {
  color: #ef5350;
}
.text-green {
  color: #66bb6a;
}
.text-yellow {
  color: #ffa726;
}
.align-start {
  align-items: baseline !important;
  font-family: Inter !important;
}
.search-box-style {
  padding-top: 0;
  border-radius: 8px;
}
.search-bar-style {
  display: flex;
  padding-bottom: 0;
  justify-content: flex-start
}
.setting-btn-style {
  display: flex;
  justify-content: flex-end;
}
.setting-btn {
  position: absolute;
  right: 10px;
  width: 40px !important;
  min-width: 40px !important;
}
.breadcrumb-container {
  padding: 0;
}
.breadcrumb-container ul {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 15px;
}
.create-btn {
  display: flex;
  justify-content: space-between;
  padding-top: 0;
  padding-bottom: 0;
}
.create-btn button {
  color: white !important;
  margin-top: 10px;
}
.bottom-input-style {
  margin-top: 0;
  border-radius: 5px;
  margin-right: 10px;
}
.bottom-input-style .v-text-field__slot {
  padding-left: 10px;
}
.bottom-input-style .v-select__selections {
  padding-left: 10px;
}
.data-table-style {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
}
.data-table-style tbody tr {
  height: 50px !important;
  line-height: 0% !important;
  min-height: 50px;
  cursor: pointer;
}
.data-table-style tbody tr:hover,
.data-table-style tbody tr:hover:not(.v-data-table__expanded__content) {
  background-color: #d0d5dd !important;
}
.v-input__prepend-inner {
  padding-left: 10px;
}
.v-list-item__content {
  text-align: start;
}
.v-breadcrumbs__item .normal-font-color {
  color: rgb(93, 101, 121) !important;
  color: red;
}
.search-field .v-input__slot {
  display: flex;
  align-items: center !important;
}
.search-field .v-input__prepend-inner {
  align-self: center;
  margin-top: 0 !important;
  padding-left: 0px;
  padding-right: 8px !important;
}
.text-field .v-input__slot {
  background-color: #f9f9fb !important;
}
.btn-restore {
  width: 100%;
  font-family: Inter;
  font-size: 12px;
  font-weight: 600;
  line-height: 18px;
  text-align: left;
  cursor: pointer;
}
.menu-header {
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  text-align: left;
}
.absolute {
  position: absolute;
}
.bottom-right {
  display: flex;
  justify-content: right;
  right: 24px;
  bottom: 16px;
}
.f-color-blue {
  color: #0c2ff3;
}
.action-btn .v-list-item__title {
  display: flex;
  justify-content: flex-start;
}
.h-100 {
  height: 100%;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.block {
  display: block;
}
h2.blank-title {
  text-align: center;
}
p.blank-description {
  max-width: 500px;
  text-align: center;
}
.none {
  display: none;
}
.custom-attribute {
  white-space: nowrap;
}
.action-btn-wrapper {
  position: sticky;
    bottom: 0;
    background-color: white;
    align-items: flex-end;
    display: flex;
    justify-content: flex-end;
    z-index: 8;
}
.calendar-textbox-container {
  cursor: pointer; font-weight: 300;
}
</style>