<template>
  <v-card
    class="white py-6 px-6 mt-3"
    rounded="lg"
    elevation="0"
    width="100%"
  >
    <div class="d-flex align-center justify-space-between">
      <h2 v-if="!skeletonLoaderState">
        {{ $t('users') }}
      </h2>
      <v-skeleton-loader
        v-else
        height="36"
        width="140"
        type="heading"
      />
      <template v-if="!skeletonLoaderState">
        <v-btn
          class="btn-theme text-capitalize"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          @click="showDialog = true"
        >
          {{ writeMember ? $t('inviteUser.inviteUser') : $t('inviteUser.requestUser') }} <v-icon
            class="ml-1"
            size="xs"
          >
            mdi-plus
          </v-icon>
        </v-btn>
      </template>
      <v-skeleton-loader
        v-else
        class="rounded-lg primary"
        height="40"
        width="150"
        type="button"
      />
    </div>
    <div
      v-if="!skeletonLoaderState"
      class="mt-4 d-flex"
    >
      <v-chip 
        :class="{ 'blue--text': filter === 'active' }" 
        width="200px" 
        :color="filter === 'active' ? '#e6ecff' : '#f9fafb'"
        label 
        @click="updateFilter('active')"
      >
        <div
          class="px-2"
          :class="{ 'font-weight-bold': filter === 'active' }"
        >
          {{ $t('all') }} <span class="ml-2">{{ activeUserCount }}</span>
        </div>
      </v-chip>
      <div
        v-if="writeMember"
        class="ml-2"
      >
        <v-chip 
          :class="{ 'blue--text': filter === 'pending' }" 
          width="200px" 
          :color="filter === 'pending' ? '#e6ecff' : '#f9fafb'"
          label 
          @click="updateFilter('pending')"
        >
          <div
            class="px-2"
            :class="{ 'font-weight-bold': filter === 'pending' }" 
          >
            {{ $t('pending') }} <span class="ml-2">{{ pendingUserCount }}</span>
          </div>
        </v-chip>
      </div>
      <div class="ml-2">
        <v-chip 
          :class="{ 'blue--text': filter === 'requested' }" 
          width="200px" 
          :color="filter === 'requested' ? '#e6ecff' : '#f9fafb'"
          label 
          @click="updateFilter('requested')"
        >
          <div
            class="px-2"
            :class="{ 'font-weight-bold': filter === 'requested' }" 
          >
            {{ writeMember ? $t('requested') : $t('myRequested') }} <span class="ml-2 red--text">{{ requestedUserCount }}</span>
          </div>
        </v-chip>
      </div>
    </div>
    <div
      v-else
      class="mt-4 d-flex"
    >
      <v-skeleton-loader
        class="rounded-sm d-flex gap-2 chip-primary"
        height="32"
        width="400"
        type="button@4"
      />
    </div>
    <v-dialog
      v-if="inviteVisibility"
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('inviteNewUser') }} 
            </h2>
            <v-btn
              icon
              @click="showDialog = false"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <div class="mt-4">
            <p
              class="text-start text-body-2 text-grey-darken-1"
              v-html="$t('inviteUser.billingDescription')"
            />
          </div>
          <div class="mt-6">
            <div class="text-start">
              <v-label class="text-theme-label font-weight-medium">
                {{ $t('email') }}  <strong class="red--text text--lighten-1">*</strong>
              </v-label>
            </div>
            <v-chip-group column>
              <v-chip
                v-for="(email, index) in emails"
                :key="index"
                close
                class="custom-chip-theme bg-white rounded-lg"
                link
                close-icon="mdi-close"
                @click:close="removeEmail(index)"
              >
                {{ email }}
              </v-chip>
            </v-chip-group>
            <v-form
              class="text-right"
              @submit.prevent="addEmail"
            >
              <v-text-field
                v-model="newEmail"
                type="text"
                dense
                class="text-field field-theme rounded-lg"
                background-color="#F9F9FB"
                height="38"
                :error="showEmailError"
                :rules="emailValidation"
                filled
                clear-icon="body-2"
                :placeholder="$t('email')"
                required
                @click:append="addEmail"
                @keyup.space="addEmail"
              />
              <v-btn
                width="10rem"
                color="primary"
                height="40"
                :depressed="true"
                class="text-capitalize btn-theme rounded-lg"
                elevation="0"
                @click="addEmail"
              >
                <v-icon>
                  mdi-plus
                </v-icon>
                {{ $t('addEmail') }}
              </v-btn>
            </v-form>
          </div>
          <div
            class="mt-4 item"
          >
            <v-checkbox
              v-model="applyExistingRole"
              class="field-theme mb-4"
              hide-details
              :ripple="false"
              off-icon="icon-checkbox-off"
              on-icon="icon-checkbox-on"
            > 
              <template #label>
                <span class="fs-14 text-theme-label">{{ $t('applyScopeOfRights') }}</span>
              </template>
            </v-checkbox>
            <div v-if="applyExistingRole">
              <div class="text-start">
                <v-label class="text-theme-label font-weight-medium">
                  {{ $t('user') }}  <strong class="red--text text--lighten-1">*</strong>
                </v-label>
              </div>
              <v-select
                v-model="selectedUser"
                :items="usersFullName"
                :rules="roleRules"
                :error="showRoleError"
                item-text="fullName"
                item-value="uid"
                background-color="#F9F9FB"
                :placeholder="$t('chooseUser')"
                append-icon="mdi-chevron-down"
                class="rounded-lg field-theme"
                height="38px"
                required
                dense
                :menu-props="{ offsetY: true }"
              />
            </div>
          </div>
          <div>
            <div class="text-start">
              <v-label
                class="text-theme-label font-weight-medium"
                :class="{ 'grey--text': applyExistingRole }"
              >
                {{ projectScope ? $t('role') : $t('defaultRole') }}  <strong class="red--text text--lighten-1">*</strong>
              </v-label>
            </div>
            <v-autocomplete
              v-model="selectedRole"
              :items="roles"
              :rules="applyExistingRole ? [] : roleRules"
              :error="showRoleError"
              item-text="name"
              item-value="uid"
              :background-color="applyExistingRole ? 'grey lighten-3' : '#F9F9FB'"
              :placeholder="$t('chooseRole')"
              append-icon="mdi-chevron-down"
              class="rounded-lg field-theme"
              :disabled="applyExistingRole"
              height="38px"
              required
              dense
              :menu-props="{ offsetY: true }"
              @update:search-input="searchRoles('default', $event)"
              @change="defaultRoleSelected"
            />
          </div>
          <div class="mt-6">
            <div class="text-start">
              <v-label class="text-theme-label font-weight-medium">
                {{ $t('selectProjectsToInvite') }}
              </v-label>
            </div>
          </div>
          <div
            v-if="assignedProjects.length"
            style="background-color: #F2F4F7;"
            class="px-2 py-3 rounded-lg mt-2"
          >
            <div
              v-for="(project, index) in assignedProjects"
              :key="index"
              class="project-container"
            >
              <div
                class="roleProject d-flex justify-space-between align-start"
                style="gap: 12px;"
              >
                <div
                  class="text-start"
                  :style="{
                    width: '40%',
                    flexShrink: '0',
                  }"
                >
                  <v-label
                    class="text-theme-label font-weight-medium fs-14px"
                    :class="{ 'grey--text': applyExistingRole }"
                  >
                    {{ $t('project') }}
                  </v-label>
                  <v-select
                    v-model="project.projectUid"
                    :items="projects"
                    :disabled="project.loading || applyExistingRole"
                    item-text="name"
                    item-value="uid"
                    :placeholder="$t('chooseProject')"
                    append-icon="mdi-chevron-down"
                    class="rounded-lg field-theme"
                    height="38px"
                    :background-color="'#F9F9FB'"
                    required
                    dense
                    :menu-props="{ offsetY: true }"
                    @change="selectProject(project.projectUid, index)"
                  >
                    <template #selection="{ item }">
                      <span>{{ truncateText(item?.name, 12) }}</span>
                    </template>
                    <template #item="{ item, on, attrs }">
                      <v-list-item
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-list-item-title>{{ item.name }}</v-list-item-title>
                      </v-list-item>
                    </template>
                  </v-select>   
                </div>
                <div class="text-start">
                  <v-label
                    class="text-theme-label font-weight-medium fs-14px"
                    :class="{ 'grey--text': applyExistingRole }"
                  >
                    {{ $t('projectRole') }}
                  </v-label>
                  <v-tooltip
                    bottm
                    left
                  >
                    <template #activator="{ on, attrs }">
                      <v-btn
                        icon
                        v-bind="attrs"
                        height="0"
                        small
                        v-on="on"
                      >
                        <HelpIcon />
                      </v-btn>
                    </template>
                    <span>{{ $t('projectLevelTooltip') }}</span>
                  </v-tooltip>
                  <v-autocomplete
                    v-model="project.roleUid"
                    :items="assignedProjects[index].roles"
                    :disabled="project.loading || applyExistingRole"
                    item-text="name"
                    item-value="uid"
                    :placeholder="project.loading ? `${$t('loading')}...` :$t('chooseRole')"
                    append-icon="mdi-chevron-down"
                    class="rounded-lg field-theme"
                    height="38px"
                    :background-color="'#F9F9FB'"
                    required
                    dense
                    :menu-props="{ offsetY: true }"
                    @change="selectRole(project.roleUid, index)"
                    @update:search-input="searchRoles(index, $event)"
                  >
                    <template #item="{ item }">
                      <span>
                        <span
                          class="grey--text"
                          :style="{fontSize: '12px'}"
                        >{{ item.projectUid ? 'Project -' : 'ORG -' }}</span>
                        {{ item.name }}
                      </span>
                    </template>
                  </v-autocomplete>
                  <span
                    v-if="assignedProjects[index].roleIncludeOrgPermissions"
                    class="red--text mb-3"
                    :style="{
                      overflow: 'hidden',
                      display: '-webkit-box',
                      '-webkit-line-clamp': 2,
                      '-webkit-box-orient': 'vertical',
                      'font-size': '12px'
                    }"
                  >{{ $t('roleHasOrgPermissions') }}</span>
                </div>
                
                <div
                  class="delete-action text-end"
                  :style="{
                    'alignSelf': 'center',
                  }"
                >
                  <v-btn
                    height="30"
                    color="danger"
                    class="text-capitalize rounded-lg mr-3 white--text btn-theme"
                    depressed
                    :disabled="project.loading || applyExistingRole"
                    icon
                    @click="deleteProject(index)"
                  >
                    <DeleteIcon />
                  </v-btn>
                </div>
              </div>
            </div>
          </div>
          <div
            class="my-4 text-end"
          >
            <v-tooltip
              top
              left
              max-width="485px"
              content-class="tooltip-theme"
              :disabled="!reachedMaxProjectsLimit"
            >
              <template #activator="{on, attrs}">
                <div
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-btn
                    v-if="!projectScope && !isDefaultOwnerRole"
                    width="150"
                    color="#F2F4F7"
                    height="40"
                    :depressed="true"
                    class="text-capitalize btn-theme"
                    elevation="0"
                    :disabled="applyExistingRole || reachedMaxProjectsLimit"
                    @click="addProject"
                  >
                    <v-icon>mdi-plus</v-icon>
                    {{ $t('addProject') }}
                  </v-btn>
                </div>
              </template>
              <span>
                {{ $t('noProjectLeftToAssign') }}
              </span>
            </v-tooltip>
          </div>
          <div>
            <div class="text-start">
              <v-label class="text-theme-label font-weight-medium">
                {{ $t('tags') }}
                <v-tooltip
                  top
                  left
                >
                  <template #activator="{ on, attrs }">
                    <v-btn
                      icon
                      v-bind="attrs"
                      height="0"
                      small
                      v-on="on"
                    >
                      <HelpIcon />
                    </v-btn>
                  </template>
                  <span>{{ $t('tagTooltip') }}</span>
                </v-tooltip>
              </v-label>
            </div>
            <v-select
              v-model="selectedTags"
              :items="tags"
              dense
              background-color="#F9F9FB"
              append-icon="mdi-chevron-down"
              :menu-props="{ offsetY: true }"
              class="rounded-lg field-theme custom-prepend mh-38px"
              multiple
              item-text="name"
              item-value="uid"
              :placeholder="$t('tagPage.chooseType')"
              persistent-placeholder
            >
              <template #selection="{ item }">
                <div
                  class="d-flex align-center custom-chip-theme ma-1"
                >
                  <div class="text-theme-label label text-truncate mr-1">
                    {{ item.name }}
                  </div>
                  <v-icon
                    size="16px"
                    @click="onRemoveSelectedTags(item.name)"
                  >
                    mdi-close
                  </v-icon>
                </div>
              </template>

              <template #item="{ item, on, attrs }">
                <v-list-item
                  :ripple="false"
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-list-item-action>
                    <v-checkbox
                      hide-details
                      :input-value="tagsSelection(item.name)"
                      class="field-theme mt-0 pt-0"
                      :ripple="false"
                      off-icon="icon-checkbox-off"
                      on-icon="icon-checkbox-on"
                    >
                      <template #label>
                        <span class="fs-14px text-theme-label">{{ `${item.name}` }}</span>
                      </template>
                    </v-checkbox>
                  </v-list-item-action>
                </v-list-item>
              </template>

              <template #append-item>
                <button
                  plain
                  class="btn-nav-back pl-3 py-1"
                  @click="addUserTags"
                >
                  <v-icon color="blue">
                    mdi-plus
                  </v-icon>
                  <span class="d-flex-inline justify-center align-center ma-0 blue--text">
                    {{ $t("createTag") }}
                  </span>
                </button>
              </template>
            </v-select>
            <template v-if="!writeMember">
              <v-label
                class="text-theme-label font-weight-medium fs-14px"
              >
                {{ $t('notes') }}
              </v-label>
              <v-textarea
                v-model="purpose"
                class="rounded-lg field-theme"
                background-color="#F9F9FB"
                :placeholder="$t('inviteUser.purposeOfRequest')"
                rows="3"
                hide-details="auto"
                auto-grow
              />
            </template>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="cancel"
        >
          {{ $t('cancel') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          @click="inviteUsers"
        >
          {{ writeMember ? $t('invite') : $t('request') }}
        </v-btn>
      </div>
    </v-dialog>
    <CreateTagDialog 
      v-model="showCreateTagDialog"
      :data="selectedTag"
      @create-new-tag="createTag"
      @close-dialog="showCreateTagDialog = false"
    />
  </v-card>
</template>

<script>
import DeleteIcon from '@/assets/svg/delete.svg';
import handleLoading from '@/mixins/loader.js'
import validator from 'validator';
import { createNamespacedHelpers } from 'vuex';
import { emailValidationRules } from "@/utils/validation";
import CreateTagDialog from '@/components/User/CreateTagDialog.vue';
import makeTagService from "@/services/api/tag";
import { showSuccessToast, showErrorToast } from '@/utils/toast';
// import ProjectsService from '@/services/api/project';
import makeRoleService from '@/services/api/role';
import HelpIcon from '@/assets/svg/help-icon.svg';
const { mapState } = createNamespacedHelpers('user');
import makeInviteService from '@/services/api/invite'
import { debounce } from 'debounce'
// let makeProjectService

export default {
  components: {
    CreateTagDialog,
    DeleteIcon,
    HelpIcon
  },
  mixins: [handleLoading],
  props: {
    filter: String,
    activeUserCount: Number,
    pendingUserCount: Number,
    requestedUserCount: Number,
    expiredUserCount: Number,
    users: {
      type: Array,
      default: () => {
        return [];
      }
    },
    tags: {
      type: Array,
      default: () => {
        return [];
      }
    },
    writeMember: {
      type: Boolean,
      default: false
    },
    projectScope: {
      type: Boolean
    },
    projects: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      applyExistingRole: false,
      showDialog: false,
      showInvites: true,
      newEmail: '',
      emails: [],
      showRoleError: false,
      showEmailError: false,
      showCreateTagDialog: false,
      selectedTag: {},
      selectedRole: '',
      selectedUser: null,
      assignedProjects: [],
      selectedTags: [],
      emailValidation: emailValidationRules(this),
      autoCompleteTags: [],
      sendingInvite: false,
      roles: [],
      noAccessRoleUid: null,
      roleRules: [
        v => !!v || this.$t('Role is Required')
      ],
      purpose: null
    };
  },
  computed: {
    ...mapState(['currentAccount']),
    username(){
      return this.$store.getters['user/user'].handle;
    },
    usersFullName(){
      return this.users.map(user => {
        return {
          ...user,
          fullName: `${user.firstName} ${user.lastName}`
        }
      });
    },
    inviteVisibility(){
      return this.$route.params.handle !== this.username ? true : false;
    },
    reachedMaxProjectsLimit() {
      return this.assignedProjects.length >= this.projects.length;
    },
    isDefaultOwnerRole(){
      const defaultOwnerRole = this.roles.find(role => role.slug === 'owner' && role.uid === this.selectedRole);
      return defaultOwnerRole
    }
  },
  watch: {
    newEmail: {
      handler() {
        this.updateEmailValidationRules();
      },
    },
    selectedUser: {
      handler(newValue) {
        if (this.applyExistingRole && newValue) {
          const selectedUserObj = this.users.find(user => user.uid === newValue);
          if (selectedUserObj?.overriddenRoles?.length) {
            this.copyOverrideRolesFromUser(selectedUserObj);
          }
        }
      },
    },
    applyExistingRole: {
      handler(newValue) {
        if (!newValue) {
          // Clear assigned projects when unchecking apply existing role
          this.assignedProjects = [];
          this.selectedUser = null;
        }
      },
    },
  },
  async mounted(){
    this.roles = await this.getRoles(this.currentAccount.handle);
    this.noAccessRoleUid = this.roles.find(role => role.slug === 'no_access')?.uid || null;
  },
  methods: {
    updateEmailValidationRules() {
      const isDuplicate = this.emails.includes(this.newEmail);
      
      if (this.emails.length === 0 && !this.newEmail) {
        this.emailValidation = emailValidationRules(this);
      } else if (this.emails.length > 0 && !this.newEmail) {
        this.emailValidation = [];
      } else {
        this.emailValidation = emailValidationRules(this);
        if (isDuplicate) {
          this.emailValidation.push(this.$t('error.dublicateEmail'));
        }
      }
    },
    defaultRoleSelected(){
      if(this.isDefaultOwnerRole)
        this.assignedProjects = []
    },
    async getRoles(handle, offset = 0, prm) {
      const roleService = makeRoleService(this.$api);
      const projectKey = this.$route.params.key;
      try {
        const params = {
          includePermissions: true,
          ...(this.projectScope ? {includeOrgRoles: true} : {}),
          offset,
          ...(prm ? prm : {})
        }
        const response = await roleService.getRoles(handle, projectKey, params);  
        return response.data?.items || [];

      } catch (err) {
        this.redirectOnError(err.response.status);
        showErrorToast(this.$swal, 'fetchError', { item: 'roles' }, err?.response?.data);
      }
    },
    searchRoles: debounce(async function (scope, q) {
      const projectUid = scope !== 'default' ? this.assignedProjects[scope].projectUid : null;
      const newRoles = await this.getRoles(this.currentAccount.handle, 0, {
        q,
        ...(projectUid ? { includeProjects: [projectUid] } : {})
      });
      if(scope === 'default')
        this.roles = newRoles;
      else
        this.assignedProjects[scope].roles = newRoles;
    }, 500),    
    async selectProject(projectUid, index){
      const projectExists = this.assignedProjects.filter(project => project.projectUid === projectUid);

      if(projectExists.length > 1){
        this.$nextTick(() => {
          this.assignedProjects[index].projectUid = null;
          this.assignedProjects[index].roleUid = null;
        })
        return showErrorToast(this.$swal, this.$t("projectAlreadyAdded"));
      }

      this.assignedProjects[index].loading = true;
      const projectKey = this.projects.find(project => project.uid === projectUid)?.key;
      const handle = this.$route.params.handle;
      const roleService = makeRoleService(this.$api);
      const params = {
        includePermissions: true,
        includeOrgRoles: true
      }
      await roleService.getRoles(handle, projectKey, params).then(res => this.assignedProjects[index].roles = res.data?.items || [])
      this.assignedProjects[index].loading = false;
    },
    selectRole(roleUid, index){
      if(!roleUid ||!Number.isInteger(index)) return;
        
      const orgPermissions = [
        'write_project', 
        'delete_project', 
        'write_tag', 
        'write_integration', 
        'delete_integration', 
        'write_setting', 
        'write_key', 
        'delete_key', 
        'full_billing', 
        'limited_billing'
      ];
      const findIndex = this.assignedProjects[index].roles.findIndex(role => role.uid === roleUid);
      const role = this.assignedProjects[index]?.roles[findIndex].permissions || [];
      const orgPermissionExists = this.assignedProjects[index]?.roles[findIndex].slug === 'owner' || orgPermissions.some(permission => role.includes(permission));
      this.assignedProjects[index].roleIncludeOrgPermissions = orgPermissionExists
    },
    addUserTags() {
      this.showCreateTagDialog = true;
      this.showDialog = false;
      this.emails = [];
      this.selectedRole = '';
      this.selectedTags = [];
    },
    onRemoveSelectedTags(name) {
      const index = this.selectedTags.findIndex((tagUid) => {
        const tag = this.tags.find(t => t.uid === tagUid);
        return tag && tag.name === name;
      });
      if (index !== -1) {
        this.selectedTags.splice(index, 1);
      }
    },
    tagsSelection(name) {
      if (!this.selectedTags) return false;
      return this.selectedTags.some((tagUid) => {
        const tag = this.tags.find(t => t.uid === tagUid);
        return tag && tag.name === name;
      });
    },
    addProject(){
      if(this.assignedProjects.length == this.projects.length){
        return showErrorToast(this.$swal, this.$t("maxProjectsLimitReached"));
      }

      this.assignedProjects.push({
        projectUid: null,
        roleUid: null,
        roles: [],
        loading: false,
        roleIncludeOrgPermissions: false
      });
    },
    deleteProject(index){
      this.assignedProjects.splice(index, 1);
    },
    toggleScope(){
      this.orgScope = !this.orgScope;
      this.assignedProjects = [];
      if(!this.orgScope){
        this.applyExistingRole = false;
        this.assignedProjects.push({
          projectUid: null,
          roleUid: null,
          roles: [],
          loading: false
        })
      }
    },
    async addEmail() {
      const isDuplicate = this.emails.includes(this.newEmail);

      if (isDuplicate) {
        this.emailValidation.push(this.$t('error.dublicateEmail'));
        return; 
      }

      if (this.newEmail && validator.isEmail(this.newEmail.trim())) {
        this.emails.push(this.newEmail.trim());
        this.newEmail = '';
      } else {
        this.showEmailError = true;
      }
    },
    async createTag(tag) {
      
      this.showCreateTagDialog = false;

      const tagService = makeTagService(this.$api);
    
      try {
        const payload = {
          name: tag.name,
          description: tag.description,
          entityTypes: [tag.entityTypes],
        }

        const response = await tagService.createTag(this.$route.params.handle, payload);
        if(response.status === 200){
          this.$emit('update-tags');
          showSuccessToast(this.$swal, "createSuccess", { item: "Tag" });
          this.showDialog = true;
        }
        
      } catch (err) {
        showErrorToast(this.$swal, "createError", { item: "Tag" }, err?.response?.data);
      } 

    },
    removeEmail(index) {
      this.emails.splice(index, 1);
    },
    updateFilter(filter) {
      this.$emit('update-filter', filter);
    },
    async inviteUsers() {
      // Check if users have assigned more than one role to a single project
      if(this.newEmail)
        await this.addEmail()
    
      if(!this.applyExistingRole && !this.selectedRole)
        return showErrorToast(this.$swal, this.$t("defaultRoleRequired"));

      this.assignedProjects.forEach(p => {
        if(!p.projectUid)
          return showErrorToast(this.$swal, this.$t("projectRequired"));
        if(!p.roleUid)
          return showErrorToast(this.$swal, this.$t("projectRoleRequired"));
      })
      
      const inviteService = makeInviteService(this.$api);
      const projectKey = this.$route.params.key;
      inviteService;
      projectKey;
      if (this.sendingInvite) return;
      let hasError = false;

      if (!this.emails.length) {
        this.showEmailError = true;
        hasError = true;
      }

      if(this.applyExistingRole && !this.selectedUser) {
        hasError = true;
        this.showRoleError = true;
      }

      if (hasError) return;

      const selectedUserObj = this.users.find(user => user.uid === this.selectedUser);
      const sendRoleUid = this.applyExistingRole ? (selectedUserObj?.role?.uid || this.selectedRole) : this.selectedRole;
      let roles = [];
      
      // Copy override roles from existing user if applyExistingRole is checked
      if(this.applyExistingRole && selectedUserObj?.overriddenRoles?.length) {
        // Convert overriddenRoles format to the expected roles format
        roles = selectedUserObj.overriddenRoles.flatMap(role => 
          role.projectUids.map(projectUid => ({
            projectUid: projectUid,
            roleUid: role.roleId
          }))
        );
        console.log('Copying override roles from existing user:', {
          selectedUser: selectedUserObj,
          overriddenRoles: selectedUserObj.overriddenRoles,
          convertedRoles: roles
        });
      } else if(this.projectScope){
        const project = this.projects.find(p => p.key === this.$route.params.key);
        roles = [
          {
            projectUid: project.uid,
            roleUid: sendRoleUid
          }
        ]
      } else if(this.assignedProjects.length){
        roles = this.assignedProjects.map(p => {
          return {
            projectUid: p.projectUid,
            roleUid: p.roleUid
          }
        })
      }
      const payloads = this.emails.map(email => {
        const payload = {
          roleUid: this.projectScope ? this.noAccessRoleUid : sendRoleUid,
          handle: this.$store.state.user.currentAccount.handle,
          userEmail: email,
          tagUids: this.selectedTags,
          ...(!this.writeMember ? { purpose: this.purpose } : {})
        };

        // Include roles if we have project scope, assigned projects, OR if we're copying from existing user
        if(this.projectScope || this.assignedProjects.length || (this.applyExistingRole && selectedUserObj?.overriddenRoles?.length))
          payload.roles = roles;

        console.log('Generated payload for email:', email, payload);
        return payload;
      });
      this.sendingInvite = true;
      try {
        await Promise.all(payloads.map(payload => inviteService.newInvite(payload, projectKey)));
        this.$emit('completed');

        // reset all states
        this.showDialog = false;
        this.emails = [];
        this.selectedRole = '';
        this.selectedTags = [];
      } catch (error) {
        if (!error.response) return this.$emit('showError');

        this.$emit('showError', (error.response?.data.data && error.response?.data.data[0]?.msg) || error.response?.data.message);
      }
      this.sendingInvite = false;
    },
    resetForm() {
      this.emails = [];
      this.newEmail = '';
      this.selectedRole = '';
      this.selectedUser = null;
      this.assignedProjects = [];
      this.selectedTags = [];
      this.applyExistingRole = false;
      this.showRoleError = false;
      this.showEmailError = false;
    },
    cancel() {
      this.showDialog = false;
      this.resetForm();
    },
    truncateText(name, maxLength) {   
      return name?.length > maxLength ? name.substring(0, maxLength) + '...' : name;
    },
    copyOverrideRolesFromUser(user) {
      if (!user || !user.overriddenRoles || !user.overriddenRoles.length) {
        this.assignedProjects = [];
        return;
      }

      // Convert overriddenRoles to assignedProjects format
      this.assignedProjects = user.overriddenRoles.flatMap(role => 
        role.projectUids.map(projectUid => ({
          projectUid: projectUid,
          roleUid: role.roleId,
          roles: [],
          loading: false,
          roleIncludeOrgPermissions: false
        }))
      );
    }
  },
};
</script>


<style scoped>
.item {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
</style>