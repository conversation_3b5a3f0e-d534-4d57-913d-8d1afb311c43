<template>
  <div>
    <v-sheet
      v-if="!skeletonLoaderState"
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40px"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-3 py-2 d-flex flex-row">
        {{ $t('filters') }}
        <v-icon
          size="16px"
          class="ml-2"
        >
          mdi-filter-variant
        </v-icon>
      </span>
    </v-sheet>
    <v-skeleton-loader
      v-else
      class="rounded-lg"
      height="40"
      width="95"
      type="button"
    />
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('filters') }}
            </h2>
            <v-btn
              icon
              @click="showDialog = false"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>
          <div class="mt-4">
            <div class="text-start">
              <v-label class="text-theme-label font-weight-medium">
                {{ $t('numberOfTestRuns') }}
              </v-label>
            </div>
            <v-range-slider
              v-model="testrun"
              :min="0"
              :max="9999"
              class="slider-theme"
              color="blue"
              track-color="#F2F4F7"
              thumb-color="#FFFFFF"
              hide-details
            />
            <div class="d-flex align-center">
              <v-text-field
                v-model="testrun[0]"
                color="blue"
                background-color="#F9F9FB"
                class="field-theme"
                height="38"
                hide-details
              />
              <div class="mx-4 font-weight-bold text-h6">
                -
              </div>
              <v-text-field
                v-model="testrun[1]"
                background-color="#F9F9FB"
                class="field-theme"
                height="38"
                hide-details
              />
            </div>
          </div>

          <div class="mt-4">
            <div class="text-start">
              <v-label class="text-theme-label font-weight-medium">
                {{ $t('numberOfTestCases') }}
              </v-label>
            </div>
            <v-range-slider
              v-model="testcases"
              :min="0"
              :max="9999"
              class="slider-theme"
              color="blue"
              track-color="#F2F4F7"
              thumb-color="#FFFFFF"
              hide-details
            />
            <div class="d-flex align-center">
              <v-text-field
                v-model="testcases[0]"
                background-color="#F9F9FB"
                class="field-theme pt-0"
                height="38"
                hide-details
              />
              <div class="mx-4 font-weight-bold text-h6">
                -
              </div>
              <v-text-field
                v-model="testcases[1]"
                background-color="#F9F9FB"
                class="field-theme pt-0"
                height="38"
                hide-details
              />
            </div>
          </div>

          <div class="text-start pt-6">
            <div class="text-start">
              <v-label class="text-theme-label font-weight-medium">
                {{ $t('Closed Date') }}
              </v-label>
            </div>
            <div class="d-flex align-center">
              <v-menu
                v-model="startDateMenu"
                :close-on-content-click="false"
                max-width="290"
              >
                <template #activator="{ on }">
                  <v-text-field
                    dense
                    class="text-field field-theme mt-0 pa-0 rounded-lg custom-prepend"
                    :value="startDate"
                    background-color="#F9F9FB"
                    height="38"
                    hide-details
                    v-on="on"
                  >
                    <template #prepend-inner>
                      <calendarBlueIcon />
                    </template>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="startDate"
                  @change="startDateMenu = false"
                />
              </v-menu>
              <div class="mx-4 font-weight-bold text-h6">
                -
              </div>
              <v-menu
                v-model="endDateMenu"
                :close-on-content-click="false"
                max-width="290"
              >
                <template #activator="{ on }">
                  <v-text-field
                    background-color="#F9F9FB"
                    class="text-field mt-0 field-theme pa-0 rounded-lg custom-prepend"
                    :value="endDate"
                    height="40"
                    hide-details
                    v-on="on"
                  >
                    <template #prepend-inner>
                      <calendarBlueIcon />
                    </template>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="endDate"
                  @change="endDateMenu = false"
                />
              </v-menu>
            </div>

            <div class="mt-4">
              <div class="text-start">
                <v-label class="text-theme-label font-weight-medium">
                  {{ $t('Progress') }}
                </v-label>
              </div>
              <v-range-slider
                v-model="users"
                class="slider-theme"
                color="blue"
                track-color="#F2F4F7"
                thumb-color="#FFFFFF"
                hide-details
              />
              <div class="d-flex align-center">
                <v-text-field
                  v-model="users[0]"
                  background-color="#F9F9FB"
                  class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
                  height="38"
                  dense
                  hide-details
                />
                <div class="mx-4 font-weight-bold text-h6">
                  -
                </div>
                <v-text-field
                  v-model="users[1]"
                  background-color="#F9F9FB"
                  class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
                  height="38"
                  dense
                  hide-details
                />
              </div>
            </div>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="clearAll"
        >
          {{ $t('clearAll') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          @click="apply"
        >
          {{ $t('apply') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
  import calendarBlueIcon from '@/assets/svg/calendar-blue.svg';
  import handleLoading from '@/mixins/loader.js';

  export default {
    name: 'MilestoneClosedFilter',
    components: {
      calendarBlueIcon,
    },
    mixins: [handleLoading],

    props: {
      currentFilters: {
        type: Object,
        default: () => ({}),
      },
    },

    data() {
      return {
        showDialog: false,
        testrun: [0, 9999],
        testcases: [0, 9999],
        users: [0, 100],
        startDate: null,
        endDate: null,
        startDateMenu: false,
        endDateMenu: false,
      };
    },

    computed: {
      maxTestRun() {
        return Math.max(this.testrun[0] || 0, this.testrun[1] || 9999);
      },
      maxTestCases() {
        return Math.max(this.testcases[0] || 0, this.testcases[1] || 9999);
      },
    },

    watch: {
      currentFilters: {
        handler(newFilters) {
          if (!newFilters || !Object.keys(newFilters).length) {
            this.clearAll();
            return;
          }

          const isDefaultFilters =
            (!newFilters.testRuns || (newFilters.testRuns[0] === 0 && newFilters.testRuns[1] === 9999)) &&
            (!newFilters.testCases || (newFilters.testCases[0] === 0 && newFilters.testCases[1] === 9999)) &&
            (!newFilters.users || (newFilters.users[0] === 0 && newFilters.users[1] === 100)) &&
            (!newFilters.dateRange || !newFilters.dateRange.start || !newFilters.dateRange.end);

          if (isDefaultFilters) {
            this.clearAll();
            return;
          }

          if (newFilters && Object.keys(newFilters).length) {
            this.syncFilters(newFilters);
          }
        },
        deep: true,
        immediate: true,
      },
    },

    created() {
      if (this.currentFilters && Object.keys(this.currentFilters).length) {
        this.syncFilters(this.currentFilters);
      }
    },

    methods: {

      hasActiveFilters() {
        return (
          this.testrun[0] !== 0 ||
          this.testrun[1] !== this.maxTestRun ||
          this.testcases[0] !== 0 ||
          this.testcases[1] !== this.maxTestCases ||
          this.users[0] !== 0 ||
          this.users[1] !== 9999 ||
          this.startDate ||
          this.endDate
        );
      },

      syncFilters(filters) {
        if (filters.testRuns) {
          const rv = filters.testRuns.value ?? filters.testRuns;
          this.testrun = Array.isArray(rv) ? rv : this.testrun;
        }
        if (filters.testCases) {
          const rv = filters.testCases.value ?? filters.testCases;
          this.testcases = Array.isArray(rv) ? rv : this.testcases;
        }
        if (filters.users) {
          const rv = filters.users.value ?? filters.users;
          this.users = Array.isArray(rv) ? rv : this.users;
        }

        if (filters.dateRange) {
          const dr = filters.dateRange.value ?? filters.dateRange;
          this.startDate = dr.start || null;
          this.endDate   = dr.end   || null;
        }
      },

      apply() {

        if (!this.hasActiveFilters()) {
          this.$emit('applyFilters', null);
          this.showDialog = false;
          return;
        }

        const enhancedFilters = {
          testRuns: {
            type: 'range',
            label: this.$t('numberOfTestRuns'),
            value: this.testrun,
            defaultValue: [0, this.maxTestRun]
          },
          testCases: {
            type: 'range',
            label: this.$t('numberOfTestCases'),
            value: this.testcases,
            defaultValue: [0, this.maxTestCases]
          },
          users: {
            type: 'range',
            label: this.$t('users'),
            value: this.users,
            defaultValue: [0, 9999]
          },
          dateRange: {
            type: 'dateRange',
            label: this.$t('customFieldPage.startDate'),
            value: { start: this.startDate, end: this.endDate }
          },
        };

        const apiFilters = {};
        if (this.testrun[0] > 0)        apiFilters.minRunsCount    = this.testrun[0];
        if (this.testrun[1] < this.maxTestRun)
                                        apiFilters.maxRunsCount    = this.testrun[1];
        if (this.testcases[0] > 0)      apiFilters.minCaseCount = this.testcases[0];
        if (this.testcases[1] < this.maxTestCases)
                                        apiFilters.maxCaseCount = this.testcases[1];
        if (this.users[0] > 0)          apiFilters.minProgess       = this.users[0];
        if (this.users[1] < 100)        apiFilters.maxProgess       = this.users[1];

        if (this.startDate)             apiFilters.startDateFrom   = this.startDate;
        if (this.endDate)               apiFilters.startDateTo   = this.endDate;

        this.$emit('applyFilters', { ui: enhancedFilters, api: apiFilters });
        this.showDialog = false;
      },

      clearAll() {
        this.testrun = [0, 9999];
        this.testcases = [0, 9999];
        this.users = [0, 100];
        this.startDate = null;
        this.endDate = null;
      },
    },
  };
</script>

<style scoped>
  .v-dialog--fullscreen {
    max-height: 100vh !important;
    width: 485px !important;
    right: 0 !important;
    left: auto !important;
  }

  .v-expansion-panel-content__wrap {
    padding: 0 !important;
  }
</style>
