<template>
  <div>
    <v-sheet
      v-if="!skeletonLoaderState"
      color="#F2F4F7"
      class="d-flex align-center justify-center pointer"
      height="40px"
      rounded="lg"
      @click="showDialog = true"
    >
      <span class="px-3 py-2 d-flex flex-row">
        {{ $t('filters') }}
        <v-icon
          size="16px"
          class="ml-2"
        >
          mdi-filter-variant
        </v-icon>
      </span>
    </v-sheet>
    <v-skeleton-loader
      v-else
      class="rounded-lg"
      height="40"
      width="95"
      type="button"
    />
    <v-dialog
      v-model="showDialog"
      class="test-cases-filter-drawer dialog-theme"
      transition="slide-x-transition"
      attach
      fullscreen
      width="485px"
    >
      <v-card>
        <v-card-text class="black--text">
          <div class="d-flex align-center justify-space-between pt-6">
            <h2 class="black--text">
              {{ $t('filters') }}
            </h2>
            <v-btn
              icon
              @click="showDialog = false"
            >
              <v-icon color="black">
                mdi-close
              </v-icon>
            </v-btn>
          </div>

          <v-expansion-panels
            v-model="statusPanel"
            flat
            class="mb-5"
          >
            <v-expansion-panel>
              <v-expansion-panel-header class="mx-0 px-0">
                <div class="text-start">
                  <v-label class="text-theme-label font-weight-medium">
                    {{ $t('status') }}
                  </v-label>
                </div>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-checkbox
                  v-for="(item, index) in statuses.filter((status) => status.name.toLowerCase() !== 'closed')"
                  :key="index"
                  v-model="panelStatus"
                  :value="item.id"
                  :label="item.name"
                  class="field-theme"
                  :ripple="false"
                  off-icon="icon-checkbox-off"
                  on-icon="icon-checkbox-on"
                  :hide-details="true"
                >
                  <template #label>
                    <span class="fs-14px text-theme-label">{{ item.name }}</span>
                  </template>
                </v-checkbox>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </v-expansion-panels>

          <div class="text-start pt-6">
            <v-expansion-panels
              v-model="tagPanel"
              flat
              class="mb-8"
            >
              <v-expansion-panel>
                <v-expansion-panel-header class="mx-0 px-0">
                  <div class="text-start">
                    <v-label class="text-theme-label font-weight-medium">
                      {{ $t('testruns.create_testrun.tag') }}
                    </v-label>
                  </div>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-text-field
                    v-model="searchTag"
                    :placeholder="$t('search')"
                    background-color="#F9F9FB"
                    class="text-field mt-0 rounded-lg field-theme custom-prepend pa-0"
                    height="38"
                    dense
                    hide-details
                  >
                    <template #prepend-inner>
                      <SearchIcon />
                    </template>
                  </v-text-field>
                  <v-checkbox
                    v-for="(item, index) in filterTags"
                    :key="index"
                    v-model="panelTag"
                    :value="item.uid"
                    class="field-theme"
                    :ripple="false"
                    off-icon="icon-checkbox-off"
                    on-icon="icon-checkbox-on"
                    :hide-details="true"
                  >
                    <template #label>
                      <span class="fs-14px text-theme-label">#{{ item.name }}</span>
                    </template>
                  </v-checkbox>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
          </div>
        </v-card-text>
      </v-card>
      <div class="actions-container d-flex justify-space-between">
        <v-btn
          width="204.5px"
          color="#F2F4F7"
          height="40"
          :depressed="true"
          class="text-capitalize btn-theme"
          elevation="0"
          @click="clearAll"
        >
          {{ $t('clearAll') }}
        </v-btn>
        <v-btn
          width="204.5px"
          class="btn-theme"
          height="40"
          color="primary"
          :depressed="true"
          elevation="0"
          @click="apply"
        >
          {{ $t('apply') }}
        </v-btn>
      </div>
    </v-dialog>
  </div>
</template>

<script>
  import colorPreferencesMixin from '@/mixins/colorPreferences';
  import handleLoading from '@/mixins/loader.js';
  import SearchIcon from '@/assets/svg/search-icon.svg';

  export default {
    name: 'MilestoneFilter',
    components: {
      SearchIcon,
    },
    mixins: [colorPreferencesMixin, handleLoading],

    props: {
      currentFilters: {
        type: Object,
        default: () => ({}),
      },
      tags: { type: Array, default: () => [] },
    },

    data() {
      return {
        showDialog: false,
        panelStatus: [],
        statuses: [],
        statusPanel: 0,
        searchTag: null,
        tagPanel: 0,
        panelTag: [],
      };
    },
    computed: {
      filterTags() {
        if (!this.searchTag) {
          return this.tags;
        }
        const searchLower = this.searchTag.toLowerCase();
        return this.tags.filter(tag => tag.name.toLowerCase().includes(searchLower));
      },
    },

    watch: {
      currentFilters: {
        handler(newFilters) {

          if (!newFilters || !Object.keys(newFilters).length) {
            this.clearAll();
            return;
          }

          const isDefaultFilters =
            (!newFilters.panelStatus || !newFilters.panelStatus.length) &&
            (!newFilters.panelTag  || !newFilters.panelTag.length) 

          if (isDefaultFilters) {
            this.clearAll();
            return;
          }

          if (newFilters && Object.keys(newFilters).length) {
            this.syncFilters(newFilters);
          }
        },
        deep: true,
        immediate: true,
      },
    },

    created() {
      this.statuses = this.getStatuses('milestone');
      if (this.currentFilters && Object.keys(this.currentFilters).length) {
        this.syncFilters(this.currentFilters);
      }
    },

    methods: {

      hasActiveFilters() {
        return (
          this.panelStatus.length > 0 ||
          this.panelTag.length > 0
        );
      },

        syncFilters(filters) {

          if (filters.panelStatus) {
            const raw = filters.panelStatus.value ?? filters.panelStatus;
            this.panelStatus = Array.isArray(raw) ? raw : [];
          }

          if (filters.panelTag) {
            const raw = filters.panelTag.value ?? filters.panelTag;
            this.panelTag = Array.isArray(raw)
              ? raw.map(item => (typeof item === 'object' ? item.uid : item))
              : [];
          }
        },

      apply() {
        if (!this.hasActiveFilters()) {
          this.$emit('applyFilters', null);
          this.showDialog = false;
          return;
        }

        const enhancedFilters = {
          panelStatus: {
            type: 'array',
            label: this.$t('status'),
            value: this.panelStatus
          },

          panelTag: {
            type: 'array',
            label: this.$t('testruns.create_testrun.tag'),
            value: this.panelTag.map(uid => {
              const tag = this.tags.find(t => t.uid === uid);
              return { uid, name: tag?.name || '' };
            })
          }
        };

        const apiFilters = {};
        if (this.panelStatus.length)   apiFilters.status      = this.panelStatus;
        if (this.panelTag.length)       apiFilters.tagUids        = this.panelTag;

        this.$emit('applyFilters', { ui: enhancedFilters, api: apiFilters });
        this.showDialog = false;
      },

      clearAll() {
        this.panelStatus = [];
        this.panelTag= [];
        this.searchTag = null;
      },
    },
  };
</script>

<style scoped>
  .v-dialog--fullscreen {
    max-height: 100vh !important;
    width: 485px !important;
    right: 0 !important;
    left: auto !important;
  }

  .v-expansion-panel-content__wrap {
    padding: 0 !important;
  }
</style>
