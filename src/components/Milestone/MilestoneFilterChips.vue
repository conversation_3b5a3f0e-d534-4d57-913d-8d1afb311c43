<template>
  <div
    v-if="hasActiveFilters"
    class="py-2"
  >
    <div class="d-flex align-center flex-wrap">
      <div class="mr-3">
        <span 
          class="fw-bold font-weight-medium no-bg pr-2"
          width="300px"
        >{{ $t('results') }}: {{ resultsCount }}</span>
      </div>
      <template v-if="filters.panelStatus?.value && filters.panelStatus?.value.length > 0">
        <v-chip
          v-for="(status, index) in getSelectedStatusNames(filters.panelStatus?.value)"
          :key="`status-${index}`"
          class="mr-2 chip-theme"
          close
          @click:close="removeFilter('panelStatus', index)"
        >
          {{ $t('status') }}: {{ status.name }}
        </v-chip>
      </template>
      <template v-if="filters.panelTag?.value && filters.panelTag?.value.length > 0">
        <v-chip
          v-for="tagObj in filters.panelTag?.value"
          :key="`tag-${tagObj.uid}`"
          class="mr-2 chip-theme"
          close
          @click:close="removeFilter('panelTag', tagObj.uid)"
        >
          {{ tagObj.name }}
        </v-chip>
      </template>

      <v-btn
        class="blue--text text-capitalize fw-semibold font-weight-medium"
        width="100px"
        text
        elevation="0"
        @click="clearAllFilters"
      >
        {{ $t('clearAll') }}
      </v-btn>
    </div>
  </div>
</template>

<script>
  import { useDateFormatter } from '@/composables/utils/dateFormatter';
  import colorPreferencesMixin from '@/mixins/colorPreferences';

  export default {
    name: 'MilestoneFilterChips',
    mixins: [colorPreferencesMixin],
    props: {
      filters: {
        type: Object,
        required: true,
      },
      resultsCount: {
        type: Number,
        default: 0,
      },
      statuses: {
        type: Array,
        default: () => [],
      },
      tags: {
        type: Array,
        default: () => [],
      },
    },
  setup() {
    const { formatDate } = useDateFormatter();
    return { formatDate };
  },
    computed: {
      hasActiveFilters() {
        return (
          (this.filters.panelStatus?.value && this.filters.panelStatus?.value.length > 0) ||
          (this.filters.panelTag?.value && this.filters.panelTag?.value.length > 0)
        );
      },
    },
    methods: {
      getSelectedStatusNames(selectedIds) {
        return this.statuses.filter((status) => selectedIds.includes(status.id));
      },
      getTagName(tagId) {
        const tag = this.tags.find(tag => tag.uid === tagId);
        return tag?.name ?? '';
      },
      isDefaultRange(range, defaultRange) {
        if (!range) return true;
        const [min, max] = Array.isArray(range.value) ? range.value : range;
        return min === defaultRange[0] && max === defaultRange[1];
      },

      removeFilter(filterType, value) {
        const updated = JSON.parse(JSON.stringify(this.filters));
        if (filterType === 'panelStatus') {
          updated.panelStatus?.value.splice(value, 1);
        } else if (filterType === 'panelTag') {
          const idx = updated.panelTag?.value.findIndex(tag => tag.uid === value);
          if (idx !== -1) updated.panelTag?.value.splice(idx, 1);
        }
        this.$emit('update-filters', updated);
      },
      resetFilter(filterType, defaultValue) {
        const updated = JSON.parse(JSON.stringify(this.filters));
        updated[filterType].value = defaultValue;
        this.$emit('update-filters', updated);
      },
      clearAllFilters() {
        this.$emit('clear-filters');
      },
    },
  };
</script>

<style scoped>
  .milestone-filter-chips {
    background-color: #f9f9fb;
    padding: 8px 12px;
    border-radius: 8px;
  }

  .chip-theme {
    background-color: #f2f4f7 !important;
    border-radius: 6px !important;
    padding-left: 12px !important;
    padding-right: 12px !important;
    border: none !important;
    box-shadow: none !important;
    font-weight: 500 !important;
  }

  .chip-theme :deep(.v-chip__close) {
    margin-left: 6px;
  }
</style>
