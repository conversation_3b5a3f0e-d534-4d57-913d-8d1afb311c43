<template>
  <div>
    <div :class="tableContainerClass">
      <v-data-table
        v-if="hasInitiallyLoaded"
        v-resize-columns="{ type: 'milestone' }"
        :header-props="{ 'sort-icon': 'mdi-chevron-down' }"
        :class="tableClass"
        :headers="filteredHeaders"
        :items="filteredItems"
        :item-key="itemKey"
        :item-class="rowClass"
        :value="selectedItems"
        :sort-by.sync="sortBy"
        :sort-desc.sync="sortDesc"
        :server-items-length="totalItems"
        :fixed-header="true"
        :height="shouldHaveOverflow ? '70vh' : 'auto'"
        disable-pagination
        hide-default-footer
        @click:row="viewMilestone"
        @input="onRowClick"
        @update:options="onUpdatePagination"
      >
        <template #[`header.actions`]="{ header }">
          <div class="d-none">
            {{ header.text }}  
          </div>
        </template>
        <template #[`item.name`]="{ item }">
          <v-tooltip
            bottom
            left
            max-width="485px"
            :disabled="!isTruncated"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <div 
                :ref="'milestoneName_' + item.uid"
                class="custom-attribute text-truncate font-weight-bold cursor-pointer"
                v-bind="attrs"
                v-on="on"
                @mouseover="checkTruncate(item.uid, 'milestoneName')"
              >
                {{ item.name }}
              </div>
            </template>
            <span>{{ item.name }}</span>
          </v-tooltip>
        </template>
        <template #[`item.status`]="{ item }">
          <span
            class="align-left contents fw-semibold fs-14px"
            :style="{ color: getStatusColor(item.status, statuses) }"
          >{{ getStatusName(item.status, statuses) }}</span>
        </template>
        <template #[`item.milestones`]="{ item }">
          <span>{{ item.testMilestoneCount || 0 }} {{ $t('milestones') }}</span>
        </template>
        <template #[`item.testplans`]="{ item }">
          <div v-if="!processedMilestoneCache.includes(item.uid) && relationLoadingStates.planCount">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <span v-else-if="(item.planCount || item.planCount === 0) > 0">{{ item.planCount || 0 }} {{ $t('testPlans') }}</span>
          <v-skeleton-loader
            v-else-if="processedMilestoneCache.includes(item.uid)"
            type="text"
            height="16"
            class="w-100"
          />
          <v-icon v-else-if="!relationLoadingStates.planCount">
            mdi-minus
          </v-icon>
        </template>
        <template #[`item.testruns`]="{ item }">
          <div v-if="!processedMilestoneCache.includes(item.uid) && relationLoadingStates.runCount">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <span v-else-if="(item.runCount || item.runCount === 0) > 0">{{ item.runCount || 0 }} {{ $t('testRuns') }}</span>
          <v-skeleton-loader
            v-else-if="processedMilestoneCache.includes(item.uid)"
            type="text"
            height="16"
            class="w-100"
          />
          <v-icon v-else-if="!relationLoadingStates.runCount">
            mdi-minus
          </v-icon>
        </template>
        <template #[`item.testcases`]="{ item }">
          <div v-if="!processedMilestoneCache.includes(item.uid) && relationLoadingStates.caseCount">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <span v-else-if="(item.caseCount || item.caseCount === 0) > 0">{{ item.caseCount || 0 }} {{ $t('testCases') }}</span>
          <v-skeleton-loader
            v-else-if="processedMilestoneCache.includes(item.uid)"
            type="text"
            height="16"
            class="w-100"
          />
          <v-icon v-else-if="!relationLoadingStates.caseCount">
            mdi-minus
          </v-icon>
        </template>
        <template #[`item.startdate`]="{ item }">
          <span class="">{{ dateFormatter.formattedDate(item.startDate) }}</span>
        </template>
        <template #[`item.due_at`]="{ item }">
          <span 
            class="" 
            :class="{ 'red--text': dueDateInfo(item.dueAt).isOverdue }"
          >
            {{ dueDateInfo(item.dueAt).formatted }}
          </span>
        </template>
        <template #[`item.tags`]="{item}">
          <div v-if="!processedMilestoneCache.includes(item.uid) && relationLoadingStates.tag">
            <v-skeleton-loader
              type="text"
              height="16"
              class="w-100"
            />
          </div>
          <v-tooltip
            v-else
            bottom
            left
            max-width="485px"
            :disabled="!Array.isArray(item?.tags) || item?.tags.length < 3"
            content-class="tooltip-theme"
          >
            <template #activator="{ on, attrs }">
              <span
                class="custom-attribute font-weight-regular text-theme-table-text"
                v-bind="attrs"
                v-on="on"
              >
                <template>
                  <div class="text-truncate">
                    <span v-if="Array.isArray(item?.tags) && item?.tags.length > 0">
                      {{ item?.tags.map(tag => `${tag.name}`).join(', ') }}
                    </span>
                    <v-icon v-else>mdi-minus</v-icon>
                  </div>
                </template>
              </span>
            </template> 
            <span>
              {{
                Array.isArray(item?.tags)
                  ? item?.tags.map(tag => `${tag.name}`).join(', ')
                  : ''
              }}
            </span>
          </v-tooltip>
        </template>
        <template #[`item.progress`]="{ item }">
          <ProgressBar
            :executions="item.executionsProgress"
            :percentage="item.percentage"
            :case-count="item.testCasesCount"
          />
        </template>

        <template #[`item.actions`]="{ item }">
          <div class="d-flex flex-row justify-center">
            <v-menu
              left
              offset-y
            >
              <template #activator="{ on }">
                <v-btn
                  icon
                  v-on="on"
                >
                  <v-icon>mdi-dots-vertical</v-icon>
                </v-btn>
              </template>

              <v-list
                dense
                class="text-left"
              >
                <v-tooltip
                  bottom
                  :disabled="writeEntity"
                >
                  <template #activator="{ on, attrs }">
                    <div
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-list-item
                        v-if="item.archivedAt == null"
                        :disabled="!writeEntity"
                        :class="{
                          'pointer': true,
                          'disabled-action': isProjectArchived
                        }"
                        @click="onEdit(item)"
                      >
                        <EditIcon />
                        <v-list-item-content class="ml-2">
                          {{ $t('edit') }}
                        </v-list-item-content>
                      </v-list-item>
                    </div>
                  </template>
                  <span>
                    {{ $t('milestone.noPermissionToDo', { action: $t('edit').toLowerCase()}) }}
                  </span>
                </v-tooltip>
                <v-tooltip
                  v-if="item.archivedAt == null"
                  bottom
                  :disabled="writeEntity"
                >
                  <template #activator="{ on, attrs }">
                    <div
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-list-item
                        :disabled="!writeEntity"
                        :class="{
                          'pointer': true,
                          'disabled-action': isProjectArchived
                        }"
                        @click="onClose(item)"
                      >
                        <CloseIcon />
                        <v-list-item-content class="ml-2">
                          {{ $t('archive') }}
                        </v-list-item-content>
                      </v-list-item>
                    </div>
                  </template>
                  <span>
                    {{ $t('milestone.noPermissionToDo', { action: $t('archive').toLowerCase() }) }}
                  </span>
                </v-tooltip>
                <v-tooltip
                  v-else-if="item.archivedAt != null"
                  bottom
                  :disabled="writeEntity"
                >
                  <template #activator="{ on, attrs }">
                    <div
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-list-item
                        :disabled="!writeEntity"
                        :class="{
                          'pointer': true,
                          'disabled-action': isProjectArchived
                        }"
                        @click="onReOpen(item)"
                      >
                        <ReOpenIcon />
                        <v-list-item-content class="ml-2">
                          {{ $t('unarchive') }}
                        </v-list-item-content>
                      </v-list-item>
                    </div>
                  </template> 
                  <span>
                    {{ $t('milestone.noPermissionToDo', { action: $t('unarchive').toLowerCase() }) }}
                  </span>
                </v-tooltip>
                <v-tooltip
                  bottom
                  :disabled="deleteEntity"
                >
                  <template #activator="{ on, attrs }">
                    <div
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-list-item
                        :disabled="!deleteEntity"
                        :class="{
                          'pointer': true,
                          'disabled-action': isProjectArchived
                        }"
                        @click="onDelete(item)"
                      >
                        <DeleteIcon />
                        <v-list-item-content class="ml-2 error--text">
                          {{ $t('delete') }}
                        </v-list-item-content>
                      </v-list-item>
                    </div>
                  </template>
                  <span>
                    {{ $t('milestone.noPermissionToDo', { action: $t('delete').toLowerCase() }) }}
                  </span>
                </v-tooltip>
              </v-list>
            </v-menu>
          </div>
        </template>

        <template #[`item.users`]="{ item }">
          <td class="d-flex align-center">
            <v-row>
              <div
                v-for="(pic, imgIndex) in item.images"
                :key="imgIndex"
              >
                <v-avatar
                  class="ml-n2 custom_border"
                  size="30"
                >
                  <img :src="pic">
                </v-avatar>
              </div>
              <v-avatar
                v-if="item.showCount"
                class="font-weight-bold gray-ish--text ml-n2"
                color="#ebecf0"
                size="30"
              >
                +{{ item.count }}
              </v-avatar>
            </v-row>
            <div>
              <v-menu
                content-class="custom_ele elevation-0"
                nudge-bottom="35"
                left
              >
                <template #activator="{ on }">
                  <v-btn
                    icon
                    v-on="on"
                  >
                    <v-icon color="gray-ish">
                      mdi-dots-vertical
                    </v-icon>
                  </v-btn>
                </template>
                <v-list dense>
                  <v-list-item
                    class="pointer"
                    @click="onEdit(item)"
                  >
                    <EditIcon />
                    <v-list-item-content class="ml-2">
                      {{ $t('edit') }}
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item
                    v-if="item.status === 'active'"
                    class="pointer"
                    @click="onClose(item)"
                  >
                    <CloseIcon />
                    <v-list-item-content class="ml-2">
                      {{ $t('close') }}
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item
                    v-else-if="item.status === 'archived'"
                    class="pointer"
                    @click="onReOpen(item)"
                  >
                    <ReOpenIcon />
                    <v-list-item-content class="ml-2">
                      {{ $t('re-open') }}
                    </v-list-item-content>
                  </v-list-item>
                  <v-list-item
                    class="pointer"
                    @click="onDelete(item)"
                  >
                    <DeleteIcon />
                    <v-list-item-content class="ml-2 error--text">
                      {{ $t('delete') }}
                    </v-list-item-content>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </td>
        </template>

        <template #no-data>
          <div class="text-center pa-5">
            <p class="text-subtitle-1">
              {{ $t('noMatchingResults') }}
            </p>
          </div>
        </template>
      </v-data-table>
      <template v-else>
        <RunTableSkeleton class="mt-6" />
      </template>
    </div>
    
    <!-- Custom Pagination Component -->
    <Pagination
      v-if="hasInitiallyLoaded && totalItems > 0"
      :page="currentPage"
      :items-per-page="itemsPerPage"
      :total-pages="totalPages"
      :total-items="totalItems"
      @update:pagination="onUpdatePagination"
    />
  </div>
</template>

<script>

import DeleteIcon from '@/assets/svg/delete.svg';
import EditIcon from '@/assets/svg/edit.svg';
import CloseIcon from '@/assets/svg/close.svg';
import ReOpenIcon from '@/assets/svg/re-open.svg';
import { useDateFormatter } from '@/composables/utils/dateFormatter';
import projectStatus from '@/mixins/projectStatus';
import ProgressBar from '@/components/base/ProgressBar'
import colorPreferences from '@/mixins/colorPreferences'
import RunTableSkeleton from '@/components/Skeletons/TestRuns/RunTableSkeleton.vue';
import Pagination from '@/components/base/Pagination.vue';
import { useMilestoneIndex } from '@/composables/modules/milestone/index';
import { useRelations } from '@/composables/utils/relations';
  
export default {
  components: {
    DeleteIcon,
    EditIcon,
    CloseIcon,
    ReOpenIcon,
    ProgressBar,
    RunTableSkeleton,
    Pagination
  },
  mixins: [projectStatus, colorPreferences],
  props: {
    filteredHeaders: Array,
    filteredItems: Array,
    itemKey: String,
    rowClass: Function,
    writeEntity: {
      type: Boolean,
      default: false
    },
    deleteEntity: {
      type: Boolean,
      default: false
    },
    totalItems: {
      type: Number,
      default: 0
    },
    currentPage: {
      type: Number,
      default: 1
    },
    itemsPerPage: {
      type: Number,
      default: 10
    },
    relationsLoading: {
      type: Boolean,
      default: false
    },
    relationLoadingStates: {
      type: Object,
      default: () => ({
        tag: true,
        runCount: true,
        planCount: true,
        caseCount: true
      })
    },
    sortBy: {
      type: Array,
      default: () => []
    },
    sortDesc: {
      type: Array,
      default: () => []
    },

  },
  setup() {
    const dateFormatter = useDateFormatter();
    const { hasInitiallyLoaded } = useMilestoneIndex();
    const { isRelationCached,useIsRelationCached, relationCache, processedMilestoneCache  } = useRelations();
    const dueDateInfo = (date) => {
      return dateFormatter.formatDueDate(date);
    };
    
    return {
      dateFormatter, hasInitiallyLoaded,
      dueDateInfo, isRelationCached,useIsRelationCached, relationCache, processedMilestoneCache

    };
  },
  data() {
    return {
      debounce: false,
      selectedItems: [],
      statuses: [],
      isTruncated: false,
    };
  },
  computed: {
    routeParamsHandle() {
      return this.$route.params.handle;
    },
    routeParamsKey() {
      return this.$route.params.key;
    },
    totalPages() {
      return Math.ceil(this.totalItems / this.itemsPerPage);
    },
    shouldHaveOverflow() {
      // Enable overflow (horizontal scroll) if more than 4 columns
      return this.filteredHeaders && this.filteredHeaders.length > 5;
    },
    tableContainerClass() {
      return this.shouldHaveOverflow ? 'table-scroll-container' : 'table-no-scroll-container';
    },
    tableClass() {
      const baseClasses = 'table-fixed data-table-style mt-6 font-inter';
      return this.shouldHaveOverflow ? `${baseClasses} table-min-width` : baseClasses;
    },
  },



  created(){
    this.statuses = this.getStatuses('milestone')
  },

  methods: {
    onRowClick(item) {
      this.selectedItems = item;
      this.$emit('select-item', this.selectedItems);
    },
    onEdit(item) {
      if (!this.isProjectArchived) {
      this.$emit('edit-item', item);
      }
    },
    onClose(item) {
      if (!this.isProjectArchived) {
      this.$emit('close-item', item);
      }
    },
    onReOpen(item) {
      if (!this.isProjectArchived) {
      this.$emit('reopen-item', item);
      }
    },
    onDelete(item) {
      if (!this.isProjectArchived) {
      this.$emit('delete-item', item);
      }
    },
    toggleStar(item) {
      this.debounce = true;
      this.$emit('toggle-star', item);
    },
    getColor(priority) {
      switch (priority) {
        case "Past due":
          return "font-weight-bold red--text text--lighten-1"
        case "Blocked":
          return "font-weight-bold orange--text text--lighten-1"
        case "Active":
          return "font-weight-bold green--text text--lighten-1"
        case "Upcoming":
          return "font-weight-bold blue--text text--lighten-1"
      }
    },
    viewMilestone(milestone){
      if(milestone.uid)
      this.$router.push({
        name: 'MilestoneView',
        params:{
          id: milestone.uid
        }
      })
    },
    onUpdatePagination(options) {
      // Emit the pagination update to the parent component
      this.$emit('update-pagination', options);
    },

    checkTruncate(uid, columnName) {
      this.$nextTick(() => {
        const el = this.$refs[`${columnName}_${uid}`];
        this.isTruncated = el?.scrollWidth > el?.clientWidth;
      });
    }
  }
};
</script>

<style scoped>
.v-data-table .v-data-table__wrapper tbody tr:nth-child(odd) {
  background-color: #ffffff;
}

.v-data-table-header__icon {
  opacity: 1 !important;
}

.v-data-table .v-data-table__wrapper tbody tr:nth-child(even) {
  background-color: #F9FAFB;
}

.pointer {
  cursor: pointer;
}

.header_text {
  color: #475467;
  font-weight: 700;
}

.custom_color {
  color: #667085;
}

.custom_border {
  border: 2px solid #ffffff;
}
.my-table table {
  table-layout: fixed;
}
.v-data-table table {
  border-collapse: collapse;
}

.v-data-table th {
  border: none !important;
}

.v-data-table td {
  border: none !important;
  cursor: pointer;
}

.v-data-table tr.project-item:hover {
  border: 1px solid #d1e1ff !important;
}

.v-data-table .v-data-table__wrapper tbody tr {
  height: 80px;
}

.v-data-table .v-data-table__wrapper tbody tr td {
  height: 80px !important;
  padding-top: 0;
}

.v-data-table tbody tr:hover:not(.v-data-table__expanded__content) {
  background-color: transparent !important;
}

/* Container styles for proper scrolling */
.table-scroll-container {
  position: relative;
}

.table-no-scroll-container {
  position: relative;
}
</style>