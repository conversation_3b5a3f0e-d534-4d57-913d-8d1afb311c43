function reportError(error, context = {}) {
  const errorInfo = {
    message: error.message,
    stack: error.stack,
    context: context
  };
  
  self.postMessage({
    type: 'error',
    error: errorInfo
  });
  
  return errorInfo;
}

self.addEventListener('error', function(e) {
  reportError(e.error || new Error(e.message), { 
    type: 'global',
    lineno: e.lineno,
    colno: e.colno,
    filename: e.filename
  });
});

self.addEventListener('unhandledrejection', function(e) {
  reportError(e.reason || new Error('Unhandled Promise Rejection'), { 
    type: 'unhandledrejection'
  });
});

self.onmessage = function(e) {
  try {
    
    const { 
      csvFileData, 
      mappedRows, 
      route, 
      selectedTemplateId, 
      selectedTemplateName,
      selectedFolder,
      tagsSeparator
    } = e.data;
    
    processTestCasesWithProgress(
      csvFileData, 
      mappedRows, 
      route, 
      selectedTemplateId, 
      selectedTemplateName,
      selectedFolder,
      tagsSeparator
    );
  } catch (error) {
    reportError(error, { messageEvent: 'onmessage handler' });
  }
};

function processTestCasesWithProgress(csvFileData, mappedRows, route, selectedTemplateId, selectedTemplateName, selectedFolder, tagsSeparator) {
  try {
    const cases = [];

    
    if (!csvFileData || !csvFileData.body || csvFileData.body.length === 0) {

      self.postMessage({ type: 'complete', data: [] });
      return;
    }
    
    const totalItems = csvFileData.body.length;

    const batchSize = 1000; 
    
    const processBatch = function(startIndex) {
      try {
        const endIndex = Math.min(startIndex + batchSize, totalItems);
        
        for (let i = startIndex; i < endIndex; i++) {
          try {
            const item = csvFileData.body[i];
            
            const name = getKeyValue(item, 'name', mappedRows);
            if (!name && mappedRows?.required?.includes('name')) {
              continue;
            }
            
            const testCase = {
              testCaseRef: i + 1,
              id: getKeyValue(item, 'externalId', mappedRows) || `TC-${i + 1}`,
              uid: getKeyValue(item, 'externalId', mappedRows) || `TC-${i + 1}`,
              name: name || `Test Case ${i + 1}`,
              externalId: typeof getKeyValue(item, 'externalId', mappedRows) === 'string' ? getKeyValue(item, 'externalId', mappedRows) : getKeyValue(item, 'externalId', mappedRows)?.toString() || `TC-${i + 1}`,
              priorityText: getKeyValue(item, 'priority', mappedRows) || 'Medium',
              statusText: getKeyValue(item, 'status', mappedRows) || 'Draft',
              tags: getKeyValue(item, 'tags', mappedRows) || [],
              source: 'testfiesta',
              projectKey: route.params.key,
              templateId: selectedTemplateId,
              parentId: selectedFolder || null,
              steps: getStepsValue(item, '_steps', mappedRows) || [],
              
              description: getKeyValue(item, 'description', mappedRows) || '',
              templateName: selectedTemplateName,
              createdBy: 'Import',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              lastUpdatedBy: 'Import',
              testCaseRunRef: i + 1,
              type: 'Manual',
              
              importSource: csvFileData.filename || 'CSV Import'
            };
            
            const tags = getKeyValue(item, 'tags', mappedRows);
            if (tags) {
              testCase.tags = processTags(tags, tagsSeparator);
            }
            
            const externalCreatedAt = getKeyValue(item, 'externalCreatedAt', mappedRows);
            if (externalCreatedAt) {
              testCase.externalCreatedAt = externalCreatedAt;
              testCase.createdAt = externalCreatedAt;
            }
            
            const externalUpdatedAt = getKeyValue(item, 'externalUpdatedAt', mappedRows);
            if (externalUpdatedAt) {
              testCase.externalUpdatedAt = externalUpdatedAt;
              testCase.updatedAt = externalUpdatedAt;
            }
            
            const folderName = getKeyValue(item, 'folder', mappedRows);
            if (folderName) {
              const trimmedFolderName = typeof folderName === 'string' ? folderName.trim() : folderName;
              if (trimmedFolderName && String(trimmedFolderName).length > 0) {
                testCase.parentName = trimmedFolderName;
                testCase._hasFolderAssignment = true;
              } else {
                testCase._hasFolderAssignment = false;
              }
            } else {
              testCase._hasFolderAssignment = false;
            }
            
            if (mappedRows.templateFields) {
              const templateFieldsArray = [];
              
              Object.entries(mappedRows.templateFields).forEach(([fieldId, field]) => {
                try {
                  const fieldValue = item[field.csvHeader];
                  if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
                    templateFieldsArray.push({
                      id: fieldId,
                      name: field.name,
                      dataType: field.dataType || 'text',
                      value: fieldValue
                    });
                  }
                } catch (fieldError) {
                  console.error('Error processing template field', fieldError);
                }
              });
              
              if (templateFieldsArray.length > 0) {
                testCase.customFields = {
                  templateFields: templateFieldsArray
                };
              }
            }
            
            cases.push(testCase);
          } catch (itemError) {
            reportError(itemError, { 
              item: i, 
              context: 'Processing individual item',
              itemData: csvFileData.body[i] ? JSON.stringify(csvFileData.body[i]).substring(0, 100) + '...' : 'undefined'
            });
          }
        }
        
        const progress = Math.min(100, Math.round((endIndex / totalItems) * 100));

        self.postMessage({ 
          type: 'progress', 
          progress: progress,
          processed: endIndex,
          total: totalItems
        });
        
        if (endIndex < totalItems) {
          setTimeout(() => processBatch(endIndex), 0);
        } else {

          self.postMessage({ type: 'complete', data: cases });
        }
      } catch (batchError) {
        reportError(batchError, { 
          startIndex, 
          context: 'Processing batch' 
        });
        
        if (startIndex + batchSize < totalItems) {
          setTimeout(() => processBatch(startIndex + batchSize), 0);
        } else {

          self.postMessage({ type: 'complete', data: cases });
        }
      }
    };

    processBatch(0);
  } catch (error) {
    reportError(error, { context: 'processTestCasesWithProgress' });
    self.postMessage({ type: 'complete', data: [] });
  }
}

function getKeyValue(item, key, reference) {
  try {
    if (!item || !key || !reference || !reference.mappings) {
      return null;
    }
    
    const referenceKey = reference.mappings.find(ref => ref.biddingValue === key)?.csvHeader;
    if (!referenceKey) {
      return null;
    }
    
    return item[referenceKey];
  } catch (error) {
    reportError(error, { key, context: 'getKeyValue' });
    return null;
  }
}

function processTags(tagString, separator) {
  try {
    if (!tagString) return [];
    return tagString.split(separator).map(tag => tag.trim()).filter(tag => tag);
  } catch (error) {
    reportError(error, { tagString, separator, context: 'processTags' });
    return [];
  }
}

function getStepsValue(item, key, reference) {
  try {
    const referenceKey = reference.mappings.find(ref => ref.biddingValue === key)?.csvHeader;
    const stepsString = item[referenceKey];
    
    if (!stepsString) {
      return [];
    }

    const stepObjects = [];
    
    const stepParts = stepsString.split(/\n{2,}/); 

    stepParts.forEach((part, index) => {
      try {
        const [descriptionLine, expectedResultLine, titleLine] = part.split("\n");
        
        const step = {
          description: descriptionLine ? descriptionLine.replace(/^Step Description:\s*/, '') : '-',
          expectedResult: expectedResultLine ? expectedResultLine.replace(/^Expected Result:\s*/, '') : '-',
          title: titleLine ? titleLine.replace(/^Title:\s*/, '') : '-'
        };

        stepObjects.push(step);
      } catch (stepError) {
        reportError(stepError, { stepIndex: index, stepPart: part, context: 'Processing step' });
      }
    });
    
    return stepObjects.length ? stepObjects : [];
  } catch (error) {
    reportError(error, { key, context: 'getStepsValue' });
    return [];
  }
} 