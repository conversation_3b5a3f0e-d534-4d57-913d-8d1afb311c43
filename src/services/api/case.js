export default function makeCasesService(api) {
  return {
    createTestCase: async function (handle, projectKey, data) {
      return api.post(`/${handle}/projects/${projectKey}/cases`, data);
    },
    createTestCases: async function (handle, projectKey, data) {
      return api.post(`/${handle}/projects/${projectKey}/cases/import`, { cases: data });
    },
    getCase: async function (handle, projectKey, caseId) {
      return api.get(`/${handle}/projects/${projectKey}/cases/${caseId}`);
    },
    getCases: async function (handle, projectKey, params = {}) {
      const queryParams = new URLSearchParams();
      
      // Pagination
      if (params.limit) queryParams.append('limit', params.limit);
      if (params.offset) queryParams.append('offset', params.offset);
      
      // Legacy pagination support
      if (params.perPage) queryParams.append('per_page', params.perPage);
      if (params.currentPage) queryParams.append('current_page', params.currentPage);
      
      // Sorting
      if (params.orderBy) queryParams.append('orderBy', params.orderBy);
      if (params.order) queryParams.append('order', params.order);
      
      // Filtering
      if (params.q) queryParams.append('q', params.q);
      if (params.tagUids && params.tagUids.length > 0) {
        params.tagUids.forEach(uid => queryParams.append('tagUids[]', uid));
      }
      if (params.parentUid) queryParams.append('parentUid', params.parentUid);
      if (params.priority) queryParams.append('priority', params.priority);
      if (params.status) queryParams.append('status', params.status);
      
      // Additional options
      if (params.groupByFolder) queryParams.append('groupByFolder', params.groupByFolder);
      
      const queryString = queryParams.toString();
      return api.get(`/${handle}/projects/${projectKey}/cases${queryString ? `?${queryString}` : ''}`);
    },
    searchCases: async function (handle, projectKey, params = {}) {
      const queryParams = new URLSearchParams();
      
      // Search parameters
      if (params.query) queryParams.append('query', params.query);
      if (params.tag) queryParams.append('tag', params.tag);
      if (params.priority) queryParams.append('priority', params.priority);
      
      // Pagination
      if (params.limit) queryParams.append('limit', params.limit);
      if (params.offset) queryParams.append('offset', params.offset);
      
      // Legacy pagination support
      if (params.perPage) queryParams.append('per_page', params.perPage);
      if (params.currentPage) queryParams.append('current_page', params.currentPage);
      
      const queryString = queryParams.toString();
      return api.get(`/${handle}/projects/${projectKey}/cases/search${queryString ? `?${queryString}` : ''}`);
    },
    deleteCase: async function (handle, projectKey, item) {
      return api.delete(`/${handle}/projects/${projectKey}/cases/${item}`);
    },
    deleteCases: async function (handle, projectKey, data) {
      return api.delete(`/${handle}/projects/${projectKey}/cases`, { data });
    },
    updateTestCase: async function (handle, projectKey, item, payload) {
      return api.patch(`/${handle}/projects/${projectKey}/cases/${item}`, payload);
    },
    getCaseExecutions: async function (handle, projectKey, item, perPage = 1000, currentPage = 1) {
      return api.get(`/${handle}/projects/${projectKey}/cases/${item}/executions?per_page=${perPage}&current_page=${currentPage}`);
    },
    getFolderCases: async function (handle, projectKey, folderId, params = {}, fetchAll = false) {
      const queryParams = new URLSearchParams();
      
      // Set folder ID and grouping
      if(!fetchAll){
        queryParams.append('parentUid', folderId);
        queryParams.append('groupByFolder', 'true');
      }
      
      // Pagination
      queryParams.append('limit', params.limit || 1000);
      queryParams.append('offset', params.offset || 0);
      
      // Sorting
      if (params.orderBy) queryParams.append('orderBy', params.orderBy);
      if (params.order) queryParams.append('order', params.order);
      
      // Filtering
      if (params.q || params.searchKey) {
        queryParams.append('q', params.q || params.searchKey);
      }
      if (params.tagUids && params.tagUids.length > 0) {
        params.tagUids.forEach(uid => queryParams.append('tagUids[]', uid));
      }
      if (params.tag) queryParams.append('tag', params.tag);
      if (params.priority) queryParams.append('priority', params.priority);
      if (params.status) queryParams.append('status', params.status);
      
      const queryString = queryParams.toString();
      return api.get(`/${handle}/projects/${projectKey}/cases?${queryString}`);
    },
    getCaseRelations: async function (handle, projectKey, relation, caseUids) {
      return api.get(`/${handle}/projects/${projectKey}/cases/relations`, {
        params: {
          relation,
          caseUids: Array.isArray(caseUids) ? caseUids.join(',') : caseUids
        }
      });
    },
    getProjectCases: async function (handle, projectKey, limit = 1000, offset = 0) {
      return api.get(`/${handle}/projects/${projectKey}/cases?limit=${limit}&offset=${offset}`);
    },
    updateBulkTestCases: async function (handle, projectKey, payload) {
      return api.patch(`/${handle}/projects/${projectKey}/cases`, payload);
    },
    getSignedAttachmentUrl: function({params,payload}){
      return api.post(`/${params.handle}/projects/${params.projectKey}/cases/${params.caseId}/attachments`, payload)
    },
    cleanupAttachments: function({id, params}){
      return api.delete(`/${params.handle}/projects/${params.projectKey}/cases/attachments/${id}/failed`)
    },
    deleteAttachments: function({id, params}){
      return api.delete(`/${params.handle}/projects/${params.projectKey}/cases/attachments/${id}`)
    },
    getTestCasesCount: async function (handle, projectKey) {
      return api.get(`/${handle}/projects/${projectKey}/cases/count`);
    },
    duplicateTestCases: async function(handle, projectKey, data){
      return api.post(`/${handle}/projects/${projectKey}/cases/duplicate`, data);
    },
    getCaseDefects: async function (handle, projectKey, caseId) {
      return api.get(`/${handle}/projects/${projectKey}/cases/${caseId}/defects`);
    }
  };
}