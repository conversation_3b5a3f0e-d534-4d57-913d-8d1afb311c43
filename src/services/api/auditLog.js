
export default function makeAuditLogService(api) {
  return {
    getAuditLogs: async function (handle, queryString = '') {
      return api.get(`/${handle}/audit/logs?${queryString}`);
    }, 
    revertAuditLog: async function (handle, auditLogId) {
      return api.post(`/${handle}/audit/logs/${auditLogId}/revert`);
    },
    exportAuditLogs: async function (handle, payload= {}) {
      return api.post(`/${handle}/audit/logs/export`, { payload });
    },
    getAuditLogFilters: async function (handle) {
      return api.get(`/${handle}/audit/logs/filters`);
    },
  };
}