/**
 *
 * @param {Axios} api
 * @returns
 */
export default function makeInviteService(api) {
  return {
    newInvite: function (data, projectKey) {
      const url = `/${data.handle}${projectKey ? `/projects/${projectKey}` : ''}/invite`;
      return api.post(url, data);
    },
    listInvites: function ({handle, params, projectKey}) {
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/invites`;
      return api.get(url, { params });
    },
    updatePendingInvites: function ({handle, updates, projectKey}) {
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/invites`;
      return api.patch(url, { updates });
    },
    deleteInviteByEmail: function ({handle, email, projectKey}) {
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/invite`;
      return api.delete(url, { data: { email } });
    },
    resendInvite: function ({handle, emails, projectKey}) {
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/invite/resend`;
      return api.post(url, { emails });
    },
    declineRequest: function({handle, inviteUid, projectKey, payload}){
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/request/${inviteUid}/decline`;
      return api.post(url, payload)
    },
    acceptRequest: function({handle, inviteUid, projectKey, payload}){
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/request/${inviteUid}/accept`;
      return api.post(url, payload)
    },
    deleteRequest: function({handle, inviteUid, projectKey}){
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/request/${inviteUid}`;
      return api.delete(url);
    },
    cancelRequest: function({handle, inviteUid, projectKey, payload}){
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/request/${inviteUid}/cancel`;
      return api.post(url, payload)
    },
  }
}