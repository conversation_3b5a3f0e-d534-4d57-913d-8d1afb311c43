

/**
 *
 * @param {Axios} api
 * @returns
 */

export default function makeRoleService(api) {
  return {
    getPermissions: async function(handle){
      return api.get(`/${handle}/permissions`)
    },
    getRoles: async function (handle, key, params) {
      const url = `/${handle}${key ? `/projects/${key}` : ''}/roles`
      return api.get(url, {
        params: {
          ...params
        }
      });
    },
    getRoleMembers: async function (handle, userId) {
      return api.get(`/${handle}/members/${userId}/roles`);
    },
    getRoleDetails: async function (handle, roleId, projectKey, queryParams) {
      // Use admin endpoint for both contexts, pass project as query param
      const url = `/${handle}/roles/${roleId}`;
      const config = {};
      
      // Combine project context with other query parameters
      const allParams = { ...queryParams };
      if (projectKey) {
        allParams.projectKey = projectKey;
      }
      
      if (Object.keys(allParams).length > 0) {
        config.params = allParams;
      }
      
      return api.get(url, config);
    },
    createRole: function (handle ,payload, key) {
      const url = `/${handle}${key ? `/projects/${key}` : ''}/roles`;
      return api.post(url, payload);
    },
    updateRole: function (handle, payload, roleId, key) {
      const url = `/${handle}${key ? `/projects/${key}` : ''}/roles/${roleId}`
      return api.patch(url, payload);
    },
    deleteRole: function (handle ,roleId, key) {
      const url = `/${handle}${key ? `/projects/${key}` : ''}/roles/${roleId}`
      return api.delete(url);
    },
    deleteRoles: function (handle ,payload) {
      const url = `/${handle}/roles/`
      return api.delete(url, { data: payload });
    },
    reAssignRole: function ({handle, roleId, payload, projectKey}) {
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/roles/${roleId}/members/reassign`;
      return api.post(url, payload);
    },
    deleteMembersFromRole: function (handle, roleId, payload) {
      return api.delete(`/${handle}/roles/${roleId}/members`, { data: payload });
    },
    getPermissionScope: function(handle, permission){
      return api.get(`/${handle}/permissions/${permission}`)
    }
  };
}