export default function makeContactService(api) {
  return {
    createContactUsReport: async function (handle, payload) {
      return api.post(`/${handle}/bugReports`, payload);
    },

    uploadContactAttachment: async function ({ params, payload }) {
      return api.post(`/${params.handle}/bugReports/${params.uid}/attachments`, payload);
    },

    sendBugReportEmail: async function ({ params }) {
      return api.post(`/${params.handle}/bugReports/${params.uid}/notify`);
    },

    cleanupFailedContactAttachment: async function ({ params }) {
      return api.delete(`/${params.handle}/bugReports/${params.uid}/attachments/failed`);
    },
  };
}
