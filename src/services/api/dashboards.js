export default function makeDashboardService(api){
  return {
    createDashboard: async function({handle, payload}){
      return api.post(`/${handle}/dashboards`, payload)
    },
    getDashboard: async function({handle, projectKey, params, uid}){
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/dashboards/${uid ?? ''}`;
      return api.get(url, {
        params:{
          ...params
        }
      })
    },
    getDashboardOveview: async function(handle, projectKey, params){
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/dashboards/overview`;
      return api.get(url, {
        params: {
          ...params
        }
      })
    },
    getDashboards: async function(handle, projectKey){
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/dashboards/list`;
      return api.get(url)
    },
    deleteDashboard: async function(handle, id){
      return api.delete(`/${handle}/dashboards/${id}`)
    },
    updateCharts: async function({handle, params, payload, id, projectKey}){
      const url = `/${handle}${projectKey ? `/projects/${projectKey}` : ''}/dashboards/${id}/charts`;
      return api.patch(url, payload, {
        params: {
          ...params
        }
      })
    },
    updateDashboard: async function({handle, id ,payload}){
      return api.patch(`/${handle}/dashboards/${id}`, payload);
    },
    getMissingDashboardChart: async function(handle, params, projectKey, dashboardID, chartID){
      const url = `/${handle}${projectKey ? `/projects/${projectKey}${dashboardID ? `/dashboards/${dashboardID}` : ''}` : `/dashboards/${dashboardID}`}/chart/${chartID}`;
      return api.get(url, {
        params: {
          ...params
        }
      })
    },
  }
}