export default function makeMilestonesService(api) {
  return {
    getMilestones: async function (handle, projectKey, queryParams = {}) {
      const params = { ...queryParams };
      
      // Add sorting support - handle both orderBy (array format) and individual sortBy/sortOrder
      if (params.orderBy && Array.isArray(params.orderBy)) {
        params.orderBy = params.orderBy.join(',');
      }
      
      const queryString = Object.keys(params).map((key) => `${key}=${encodeURIComponent(params[key])}`).join('&');
      return api.get(`/${handle}/projects/${projectKey}/milestones?${queryString}`);
    },
    findMilestone: async function (handle, projectKey, uid) {
      return api.get(`/${handle}/projects/${projectKey}/milestones/${uid}`);
    },
    createMilestone: async function (handle, projectKey, { ...data }) {
      return api.post(`/${handle}/projects/${projectKey}/milestones`, data);
    },
    deleteMilestone: async function (handle, projectKey, item) {
      return api.delete(`/${handle}/projects/${projectKey}/milestones/${item}`);
    },
    updateMilestone: async function (handle, projectKey, item, payload) {
      return api.patch(`/${handle}/projects/${projectKey}/milestones/${item}`, payload);
    },
    addRunsToMilestone: async function (handle, projectKey, uid, payload) {
      return api.patch(`/${handle}/projects/${projectKey}/milestones/${uid}`, payload)
    },
    getMilestoneRelations: async function (handle, projectKey, relation, milestoneUids) {
      return api.get(`/${handle}/projects/${projectKey}/milestones/relations`, {
        params: {
          relation: relation,
          milestoneUids: milestoneUids
        }
      });
    },
    getMilestoneRunsCount: async function (handle, projectKey, uid) {
      return api.get(`/${handle}/projects/${projectKey}/milestones/${uid}/runs/count`);
    },
    getMilestonePlansCount: async function (handle, projectKey, uid) {
      return api.get(`/${handle}/projects/${projectKey}/milestones/${uid}/plans/count`);
    },
    getMilestoneCasesCount: async function (handle, projectKey, uid) {
      return api.get(`/${handle}/projects/${projectKey}/milestones/${uid}/cases/count`);
    },
    searchMilestones: async function (handle, projectKey, query) {
      return api.get(`/${handle}/projects/${projectKey}/milestones/search`, {
        params: { query }
      });
    }
  };
}
