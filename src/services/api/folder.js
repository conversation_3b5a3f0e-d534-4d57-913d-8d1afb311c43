// Note: Folder API
export default function makeFoldersService(api) {
  return {
    getProjectFolders: async function (handle, projectID, { sortBy, sortOrder, orderBy, ...params } = {}) {
      const apiParams = { ...params };

      // Add sorting parameters if provided
      if (orderBy) {
        apiParams.orderBy = Array.isArray(orderBy) ? orderBy.join(',') : orderBy;
      } else if (sortBy) {
        apiParams.sortBy = sortBy;
        if (sortOrder) {
          apiParams.sortOrder = sortOrder;
        }
      }

      return api.get(`/${handle}/projects/${projectID}/folders`, {
        params: apiParams
      });
    },
    getProjectFoldersByTestRun: async function (handle, projectID, testRunId) {
      return api.get(`/${handle}/projects/${projectID}/folders`, {
        params: {
          entityType: 'execution',
          testRunId,
        },
      });
    },
    getRunFolders: async function (handle, projectKey, runId) {
      return api.get(`/${handle}/projects/${projectKey}/runs/${runId}/folders`);
    },
    getFolder: async function (handle, projectKey, folderId, includeChildren = false) {
      return api.get(`/${handle}/projects/${projectKey}/folders/${folderId}`, {
        params: {
          includeChildren: includeChildren ? 'true' : 'false',
        },
      });
    },
    createFolder: async function (handle, projectKey, data) {
      return api.post(`/${handle}/projects/${projectKey}/folders`, data);
    },
    deleteFolder: async function (handle, projectKey, item) {
      return api.delete(`/${handle}/projects/${projectKey}/folders/${item}`);
    },
    updateFolder: async function (handle, projectKey, id, data) {
      return api.patch(`/${handle}/projects/${projectKey}/folders/${id}`, data);
    },
    updateFolders: async function (handle, projectKey, data) {
      return api.patch(`/${handle}/projects/${projectKey}/folders`, data);
    },
    searchFolders: async function (handle, projectKey, query={}) {
      return api.get(`/${handle}/projects/${projectKey}/folders/search`, {
        params: {
          ...query,
        },
      });
    },
  };
}
