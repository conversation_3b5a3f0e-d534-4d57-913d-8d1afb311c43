

/**
 *
 * @param {Axios} api
 * @returns
*/

export default function makeDefectService(api) {
  return {
    getDefects: function (handle, projectKey,params) {
      return api.get(`/${handle}/projects/${projectKey}/defects`,{params});
    },

    getDefect: function (handle, projectKey, uid) {
      return api.get(`/${handle}/projects/${projectKey}/defects/${uid}`);
    },

    createDefect: function (handle, projectKey, data) {
      return api.post(`/${handle}/projects/${projectKey}/defects`, data);
    },

    updateDefect: function (handle, projectKey, { uid, ...data }) {
      return api.patch(`/${handle}/projects/${projectKey}/defects/${uid}`, data);
    },

    addComment: function (handle, projectKey, { uid, comment }) {
      return api.post(`/${handle}/projects/${projectKey}/defects/${uid}/comment`, {
        comment,
      });
    },
    linkDefect: function (handle, projectKey, defectId, data) {
      return api.post(`/${handle}/projects/${projectKey}/defects/${defectId}/link`, data);
    },
    linkExternalDefect: function (handle, projectKey, data) {
      return api.post(`/${handle}/projects/${projectKey}/external/defects/link`, data);
    },
    getDefectPriorities: function (handle, projectKey) {
      return api.get(`/${handle}/projects/${projectKey}/defects/priorities`);
    },
    getDefectStatuses: function (handle, projectKey) {
      return api.get(`/${handle}/projects/${projectKey}/defects/statuses`);
    },
    getDefectStatusScopes: function (handle, projectKey,defectUid) {
      return api.get(`/${handle}/projects/${projectKey}/defects/${defectUid}/statuses`);
    },
    getSignedAttachmentUrl: function({params,payload}){
      return api.post(`/${params.handle}/projects/${params.projectKey}/defects/${params.defectId}/attachments`, payload)
    },
    cleanupAttachments: function({id, params}){
      return api.delete(`/${params.handle}/projects/${params.projectKey}/defects/${params.defectId}/attachments/${id}/failed`)
    },
    deleteAttachments: function({id, params}){
      return api.delete(`/${params.handle}/projects/${params.projectKey}/defects/${params.defectId}/attachments/${id}`)
    },
    getDefectsCount: function (handle, projectKey) {
      return api.get(`/${handle}/projects/${projectKey}/defects/count`);
    },
    getDefectsOpenCount: function (handle, projectKey, integrationService) {
      return api.get(`/${handle}/projects/${projectKey}/defects/open/count`, {
        params: { integrationService }
      });
    },
    getDefectsClosedCount: function (handle, projectKey, integrationService) {
      return api.get(`/${handle}/projects/${projectKey}/defects/closed/count`, {
        params: { integrationService }
      });
    },
    getDefectExecutions: function (handle, projectKey, defectUid) {
      return api.get(`/${handle}/projects/${projectKey}/defects/${defectUid}/executions`);  
    },
    getDefectAttachments: function (handle, projectKey, defectUid) {
      return api.get(`/${handle}/projects/${projectKey}/defects/${defectUid}/attachments`);  
    },
    getDefectRuns: function (handle, projectKey, defectUid) {
      return api.get(`/${handle}/projects/${projectKey}/defects/${defectUid}/runs`);  
    },
    getExecutionDefects: function (handle, projectKey, executionUid) {
      return api.get(`/${handle}/projects/${projectKey}/executions/${executionUid}/defects`);
    }
  };
}
