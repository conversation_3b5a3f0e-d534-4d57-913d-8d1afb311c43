export default function executionsService(api) {
  return {
    getExecution: async function (handle, projectKey, id) {
      return api.get(`/${handle}/projects/${projectKey}/executions/${id}`);
    },
    searchExecutions: async function (handle, projectKey ,text, perPage, currentPage) {
      return api.get(`/${handle}/projects/${projectKey}/executions/search?query=${text}&per_page=${perPage}&current_page=${currentPage}`);
    },
    updateExecution: async function(handle, projectKey, id, data){
      return api.patch(`/${handle}/projects/${projectKey}/executions/${id}`, data);
    },
    updateExecutions: async function(handle, projectKey, data){
      return api.patch(`/${handle}/projects/${projectKey}/executions`, data)
    },
    updateBulkExecutions: async function(handle, data){
      return api.patch(`/${handle}/workspace/executions`, data)
    },
    deleteExecutions: async function (handle, projectKey, id) {
      return api.delete(`/${handle}/projects/${projectKey}/executions/${id}`);
    },
    getSignedAttachmentUrl: function({params,payload}){
      return api.post(`/${params.handle}/projects/${params.projectKey}/executions/${params.executionUid}/attachments`, payload)
    },
    cleanupAttachments: function({id, params}){
      return api.delete(`/${params.handle}/projects/${params.projectKey}/executions/attachments/${id}/failed`)
    },
    deleteAttachments: function({id, params}){
      return api.delete(`/${params.handle}/projects/${params.projectKey}/executions/attachments/${id}`)
    }
  };
}
