

/**
 *
 * @param {Axios} api
 * @returns
*/

export default function makeStorageService(api) {
  return {
    createStorage: function (handle, data) {
      return api.post(`/media/profiles/`, data, {
        headers: {
          'x-owner-handle': handle,
        },
      });
    },
    getRegions: function () {
      return api.get(`/cloud/regions/`);
    },
    fetchAttachmentSize: function (handle) {
      return api.get(`/${handle}/attachments/size`);
    },
    getScheduledTask: function (handle, id) {
      return api.get(`/${handle}/tasks/${id}`)
    },
    getScheduledTaskRun: function (handle, id) {
      return api.post(`/${handle}/tasks/${id}/run`)
    },
    updateStorage: function (handle, data, id) {
      return api.patch(`/media/profiles/${id}`, data, {
        headers: {
          'x-owner-handle': handle,
        },
      });
    },
    getStorages: function (handle) {
      return api.get(`/media/profiles/`, {
        headers: {
          'x-owner-handle': handle,
        },
      });
    },
  }
}
