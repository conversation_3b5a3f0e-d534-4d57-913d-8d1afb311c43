export default function makePlanService(api) {
  return {
    getPlans: async function (handle, projectKey, queryParams) {
      // Convert search parameter to q parameter if it exists
      const params = { ...queryParams };
      if (params.search) {
        params.q = params.search;
        delete params.search;
      }
      
      // Add sorting support - handle both orderBy (array format) and individual sortBy/sortOrder
      if (params.orderBy && Array.isArray(params.orderBy)) {
        params.orderBy = params.orderBy.join(',');
      }
      
      const queryString = Object.keys(params).map((key) => `${key}=${encodeURIComponent(params[key])}`).join('&');
      return api.get(`/${handle}/projects/${projectKey}/plans?${queryString}`);
    },
    createTestPlan: async function (handle, projectKey, data) {
      return api.post(`/${handle}/projects/${projectKey}/plans`, data);
    },
    duplicateTestPlan: async function(handle, projectKey, data){
      return api.post(`/${handle}/projects/${projectKey}/plans/duplicate`, data)
    },


    findTestPlan: async function (handle, projectKey, uid) {
      return api.get(`/${handle}/projects/${projectKey}/plans/${uid}`);
    },
    updateTestPlan: async function (handle, projectKey, uid, data) {
      return api.patch(`/${handle}/projects/${projectKey}/plans/${uid}`, data);
    },
    updateTestPlans: async function (handle, projectKey, data) {
      return api.patch(`/${handle}/projects/${projectKey}/plans/`, data);
    },
    addRunsToTestPlan: async function(handle, projectKey, uid, payload){
      return api.post(`/${handle}/projects/${projectKey}/plans/${uid}/runs`, payload)
    },
    getRunsCountByPlan: async function (handle, projectKey, planUid) {
      return api.get(`/${handle}/projects/${projectKey}/plans/${planUid}/runs/count`);
    },
    getMilestonesCountByPlan: async function (handle, projectKey, planUid) {
      return api.get(`/${handle}/projects/${projectKey}/plans/${planUid}/milestones/count`);
    },
    getPlanRelations: async function (handle, projectKey, relation, planUids) {
      return api.get(`/${handle}/projects/${projectKey}/plans/relations`, {
        params: {
          relation,
          planUids: Array.isArray(planUids) ? planUids.join(',') : planUids
        }
      });
    },
  };
}
