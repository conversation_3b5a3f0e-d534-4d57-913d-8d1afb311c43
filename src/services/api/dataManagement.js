export default function makeDataManagementService(api) {
  return {
    createDataManagement: async function (handle, data) {
      return api.post(`/${handle}/jobs`, data);
    },
    getDataManagement: async function (handle, projectUid) {
      return api.get(`/${handle}/jobs`, {
        params: {
          projectUid,
          frequency: 'daily'
        }
      });
    },    
    updateDataManagement: async function (handle, uid, data) {
      return api.patch(`/${handle}/jobs/${uid}`, data);
    },
    deleteDataManagement: async function (handle, uid) {
      return api.delete(`/${handle}/jobs/${uid}`);
    },
  };
}
