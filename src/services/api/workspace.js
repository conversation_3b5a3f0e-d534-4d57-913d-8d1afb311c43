export default function workspaceService(api) {
  return {
    getWorkspaceExecutions: async function (handle, payload) {
      return api.get(`/${handle}/workspace/executions`, {
        params: {
          ...payload,
        },
      });
    },
    getWorkspaceOverview(handle) {
      return api.get(`/${handle}/workspace/overview`);
    },

    getWorkspaceExecution(handle, projectKey, uid) {
      return api.get(`/${handle}/projects/${projectKey}/executions/${uid}`, {
        params: {
          complete: true,
        },
      });
    },
    getWorkspaceExecutionRelations(handle, relation, executionUids){
      return api.get(`/${handle}/executions/relations`, {
        params: {
          relation,
          executionUids
        },
      });
    },
    getWorkspaceExecutionsCounts(handle, {...params}) {
      return api.get(`/${handle}/executions/count`, {
        params: { 
          status: params.statusArray,
          assignedTo: params.assignedTo,
          projectUids: params.projectUids,
        },
      });
    },
    getProjects(handle) {
      return api.get(`/${handle}/executions/projects`);
    },
    getMilestones(handle, params = {}) {
      return api.get(`/${handle}/executions/milestones`, {
        params: {
          ...params,
        },
      });
    },
    getTestPlans(handle, params = {}) {
      return api.get(`/${handle}/executions/plans`,{
        params: {
          ...params,
        },
      });
    },
    getTestRuns(handle, params = {}) {
      return api.get(`/${handle}/executions/runs`, {
        params: {
          ...params,
        },
      });
    },
    getWorkspaceUsers(handle) {
      return api.get(`/${handle}/executions/users`);
    }
  };
}
