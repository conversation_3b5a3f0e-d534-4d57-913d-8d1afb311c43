export default function makeConfigurationService(api) {
    return {
        getConfigurations: async function (handle, projectKey, perPage, currentPage, sortBy, sortOrder) {
            let url = `/${handle}/projects/${projectKey}/configurations?per_page=${perPage}&current_page=${currentPage}`;
            
            // Add sorting parameters if provided
            if (sortBy) {
                url += `&sortBy=${encodeURIComponent(sortBy)}`;
                if (sortOrder) {
                    url += `&sortOrder=${encodeURIComponent(sortOrder)}`;
                }
            }
            
            return api.get(url);
        },
        createConfigurations: async function (handle, projectKey, data) {
            return api.post(`/${handle}/projects/${projectKey}/configurations`, data);
        },
        deleteConfiguration: async function (handle, projectKey, id) {
            return api.delete(`/${handle}/projects/${projectKey}/configurations/${ id }`);
        },
        updateConfiguration: async function (handle, projectKey, id, data) {
            return api.patch(`/${handle}/projects/${projectKey}/configurations/${ id }`, data);
        },
    };
}
