export default function runsService(api) {
    return {
      getExecutions: async function (handle, projectKey, runId) {
        return api.get(`/${handle}/projects/${projectKey}/runs/${runId}/executions`);
      },
      getRuns: async function (handle, projectKey, { limit = 10, offset = 0, ...params } = {}) {
        return api.get(`/${handle}/projects/${projectKey}/runs`, {
          params: {
            limit,
            offset,
            ...params
          }
        });
      },
      searchRuns: async function (handle, projectKey, text, perPage, currentPage) {
        return api.get(`/${handle}/projects/${projectKey}/runs/search?query=${text}&per_page=${perPage}&current_page=${currentPage}`);
      },
      createTestRun: async function (handle, projectKey, data) {
        return api.post(`/${handle}/projects/${projectKey}/runs`, data);
      },
      duplicateTestRun: async function(handle, projectKey, data){
        return api.post(`/${handle}/projects/${projectKey}/runs/duplicate`, data);
      },
      getTestRunExecutions: async function(handle, projectKey, runId, params = {} ){
        return api.get(`/${handle}/projects/${projectKey}/executions?runUid=${runId}`, {
          params: {
             ...params
          }
        })
      },
      updateTestRun: async function (handle, projectKey, uid, data) {
        return api.patch(`/${handle}/projects/${projectKey}/runs/${uid}`, data);
      },
      updateTestRuns: async function (handle, projectKey, data) {
        return api.patch(`/${handle}/projects/${projectKey}/runs/`, data);
      },
      deleteTestRun: async function (handle, projectKey, uid) {
        return api.delete(`/${handle}/projects/${projectKey}/runs/${uid}`);
      },
      deleteTestRuns: async function (handle, projectKey, data) {
        return api.delete(`/${handle}/projects/${projectKey}/runs`, { data: data });
      },
      getProjectRuns: async function (handle, projectKey, page, limit) {
        return api.get(`/${handle}/projects/${projectKey}/runs?page=${page}&limit=${limit}`);
      },
      getTestRunById: async function (handle, projectKey, runId) {
        return api.get(`/${handle}/projects/${projectKey}/runs/${runId}`);
      },
      getTestRunDetail: async function (handle, projectKey, runId) {
        return api.get(`/${handle}/projects/${projectKey}/runs/${runId}/cases`);
      },
      getTestCaseRuns: async function (handle, projectKey, caseId) {
        return api.get(`/${handle}/projects/${projectKey}/cases/${caseId}/runs`);
      },
      getTestRunCases: async function (handle, projectKey, runId, limit = 1000, offset = 0) {
        return api.get(`/${handle}/projects/${projectKey}/runs/${runId}/cases?limit=${limit}&offset=${offset}`);
      },
      updateTestRunCases: async function (handle, projectKey, runId, data) {
        return api.patch(`/${handle}/projects/${projectKey}/runs/${runId}/cases`, data);
      },
      getTestRunsCount: async function (handle, projectKey) {
        return api.get(`/${handle}/projects/${projectKey}/runs/count`);
      },
      getTestRunCasesCount: async function (handle, projectKey, runId) {
        return api.get(`/${handle}/projects/${projectKey}/runs/${runId}/cases/count`);
      },
      getRunRelations: async function (handle, projectKey, relation, runUids) {
      return api.get(`/${handle}/projects/${projectKey}/runs/relations`, {
        params: {
          relation,
          runUids: Array.isArray(runUids) ? runUids.join(',') : runUids
        }
      });
    },
    bulkAddTestCasesToRuns: async function (handle, projectKey, data) {
      return api.post(`/${handle}/projects/${projectKey}/runs/addCasesToRuns`, data);
    }
    };
}
