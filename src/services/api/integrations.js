export default function makeIntegrationsService(api) {
  return {
    getAvailableIntegrations: async function (handle) {
      return api.get(`/${handle}/integrations/available`);
    },
    getIntegrations: async function (handle, params) {
      return api.get(`/${handle}/integrations?${params}`);
    },
    getOrganizations: async function (handle, integrationUid) {
      return api.get(`/${handle}/integrations/${integrationUid}/orgs`);
    },
    getIntegrationProjects: async function (handle, integrationUid, params) {
      return api.get(`/${handle}/integrations/${integrationUid}/projects?${params}`);
    },
    createIntegration: async function (handle, body) {
      return api.post(`/${handle}/integrations`, body);
    },
    updateIntegration: async function (handle, integrationUid, body) {
      return api.patch(`/${handle}/integrations/${integrationUid}`, body);
    },
    getRedirectUrl: async function (handle, integration, params) {
      return api.get(`/${handle}/integrations/${integration}/redirect?${params}`);
    },
    // soft delete
    deleteIntegration: async function (handle, integrationUid) {
      return api.delete(`/${handle}/integrations/${integrationUid}`);
    },
    // remove integration if user wants to cancel the setup
    removeIntegration: async function (handle, integrationUid) {
      return api.delete(`/${handle}/integrations/${integrationUid}?operation=delete`,{
      });
    },
    getIntegrationData: async function (handle, integrationUid, params) {
      return api.get(`/${handle}/integrations/${integrationUid}/data`,{
        params
      });
    },
    getSignedAttachmentUrl: function({params, payload}) {
      return api.post(
        `/${params.handle}/integrations/${params.integrationUid}/attachments`,payload,
      );
    },
    deleteAttachment: function({id, params}) {
      return api.delete(
        `/${params.handle}/integrations/${params.integrationUid}/attachments/${id}`,
      );
    },
    cleanupAttachments: function({id, params}) {
      return api.delete(
        `/${params.handle}/integrations/${params.integrationUid}/attachments/${id}/failed`,
      );
    },
    reAuthIntegration: async function (handle, integrationUid, body) {
      return api.post(
        `/${handle}/integrations/${integrationUid}/reauthenticate/`, 
        body,
      );
    },
    personalTokenRedirect: async function (handle, service, integrationUid) {
      return api.get(`/${handle}/integrations/${service}/${integrationUid}/personal/redirect`);
    },
    removeIntegrationErrors: async function (handle, integrationUid, body) {
      return api.patch(`/${handle}/integrations/${integrationUid}/errors`, body);
    },
  };
}
