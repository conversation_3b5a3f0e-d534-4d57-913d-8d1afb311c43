/**
 *
 * @param {Axios} api
 * @returns
 */
export default function makeSharedStepService(api) {
  return {
    createSharedStep: function (handle, projectKey, data) {
      return api.post(`/${handle}/projects/${projectKey}/shared-steps`, data);
    },

    getSharedSteps: function (handle, projectKey, params = {}) {
      const {
        limit = 10,
        offset = 0,
        archived = false,
        active = true,
        filters = {}
      } = params;

      const urlParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        archived: archived.toString(),
        active: active.toString(),
      });
      
      // Add optional filter parameters
      if (filters.name) {
        urlParams.append('name', filters.name);
      }
      if (filters.minSteps !== null && filters.minSteps !== undefined) {
        urlParams.append('minSteps', filters.minSteps.toString());
      }
      if (filters.maxSteps !== null && filters.maxSteps !== undefined) {
        urlParams.append('maxSteps', filters.maxSteps.toString());
      }
      if (filters.minReferencedBy !== null && filters.minReferencedBy !== undefined) {
        urlParams.append('minReferencedBy', filters.minReferencedBy.toString());
      }
      if (filters.maxReferencedBy !== null && filters.maxReferencedBy !== undefined) {
        urlParams.append('maxReferencedBy', filters.maxReferencedBy.toString());
      }
      
      return api.get(`/${handle}/projects/${projectKey}/shared-steps?${urlParams.toString()}`);
    },

    deleteSharedStep: function (handle, projectKey, uid) {
      return api.delete(`/${handle}/projects/${projectKey}/shared-steps/${uid}`);
    },

    deleteSharedSteps: function (handle, projectKey, data) {
      return api.delete(`/${handle}/projects/${projectKey}/shared-steps`, { data: data });
    },

    updateSharedStep: function (handle, projectKey, uid, data) {
      return api.patch(`/${handle}/projects/${projectKey}/shared-steps/${uid}`, data);
    },

    updateSharedSteps: function (handle, projectKey, data) {
      return api.patch(`/${handle}/projects/${projectKey}/shared-steps`, data);
    },
  };
}
