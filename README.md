# TestFiesta-Frontend

## Deployments
Deployments and version upgrades are automated in staging.

Version creation is automated in production.  **No actual upgrades/migrations are automated in production.**

In production one must:
- Update the Nginx configmap to update the default version redirect.  See `deploy/cloudbuild-staing.yaml` for the command.
- Reload all frontend-nginx pods with:
```
for pod in $(kubectl get pods -n staging -l=app=frontend-nginx -o json | jq '.items[].metadata.name'); do echo "Reloading ${pod:1:-1}"; kubectl exec -it -n staging ${pod:1:-1} -- bash -c 'nginx -s reload'; done
```

## Project setup
```
yarn install
```

### Compiles and hot-reloads for development
```
yarn serve
```

### Compiles and minifies for production
```
yarn build
```

### Lints and fixes files
```
yarn lint
```

## Project Layout
Much like GitHub, TestFiesta has the concept of both `users` and `orgs`.

`users` can belong to zero or more `orgs`, and in some respects a `user` is sometimes treated like an `org` in that users can also "own" tests and related entities directly. (This is similar to GitHub repositories being "owned" by your personal account instead of being owned by an org.)

Just like on GitHub, users and orgs have usernames (known as `handles`) which are used to scope all ownership and actions. 

For example: Let's assume that there is an `org` with the handle "org1" and a `user` with the handle "user1" who is a member of that `org`.

If thst user performs a `GET /org1/projects` request, it will return all projects owned by `org1`. "user1" can access that endpoint as a member of "org1". However, if he performa a `GET /user1/projects` request, it will return all projects in his personal space that only he has access to.

In order to choose the proper scope of requests, the frontend allows users to select which account they are working under. This account is stored in the `currentAccount` variable in the state. 

In the above example, "user1" would be able to either choose his own personal space (`currentAccount.handle === "user1"`) or that of any org he is a part of (`currentAccount.handle === "org1"`) and all of his requests to the backend will be properly scoped.

_Note: `currentUser` keeps track of the current user and in the above example will always reference user1's account. This means that `currentUser` CAN be equivaent to `currentAccount` but it won't be when users arent working in their private space._
